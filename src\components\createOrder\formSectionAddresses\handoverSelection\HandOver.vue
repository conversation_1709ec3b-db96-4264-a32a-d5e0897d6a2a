<template>
  <div class="mt-6">
    <h5 class="text-h5">{{ headline }}</h5>
    <VRadioGroup
      :model-value="selectedHandOver.selection"
      class="mt-3 pt-0"
      hide-details
      @update:model-value="emitAddress"
    >
      <RadioField
        v-model="HandOverSelection.default"
        :label="defaultLabel"
        :disabled="defaultSelectionDisabled"
      />
      <RadioField
        v-model="HandOverSelection.alternateAddress"
        :label="alternateAddressLabel"
        :disabled="alternateAddressSelectionDisabled"
        class="my-3"
      />
      <AddressCard
        v-if="selectedHandOver.selection === HandOverSelection.alternateAddress"
        v-data-test="'alternate-address'"
        :model-value="selectedAddress"
        :is-customer="false"
        class="mb-6"
        :contact-data="contact"
        :header="addressCardLabel"
        :hide-search-field-label="true"
        :is-editable="true"
        :is-deletable="alternateAddressIsDeletable"
        :smart-proposals-enabled="true"
        :is-shipper-address="isShipperAddress"
        :is-currently-final-address="
          embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.FINAL_DELIVERY_ADDRESS)
        "
        :required="true"
        @update:model-value="updateAlternateAddress"
        @update-contact="updateAlternateContact"
        @delete-address="deleteAddress"
      />
      <RadioField
        v-model="HandOverSelection.port"
        :label="airportSelectionLabel"
        :disabled="airportSelectionDisabled"
        :tooltip="airportSelectionDisabledReason"
      />
      <PortSelection
        v-if="selectedHandOver.selection === HandOverSelection.port"
        v-model="port"
        class="mt-3"
        :disabled="isOrderFromQuote || airportSelectionDisabled"
        :tooltip="airportSelectionDisabledReason"
      />
    </VRadioGroup>
  </div>
</template>

<script setup lang="ts">
import PortSelection from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortSelection.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import RadioField from '@/components/form/RadioField.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import type { HandOverSelectionType, HandOverSelectionValue } from '@/types/hand-over';
import { HandOverSelection } from '@/types/hand-over';
import type { ContactData, OrderAddress } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import type { PropType } from 'vue';
import { computed, watch } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { AddressCardType } from '@/enums';

defineProps({
  headline: {
    type: [String, Object] as PropType<TranslateResult>,
    required: false,
    default: '',
  },
  defaultLabel: {
    type: [String, Object] as PropType<TranslateResult>,
    required: true,
  },
  alternateAddressLabel: {
    type: [String, Object] as PropType<TranslateResult>,
    required: true,
  },
  airportSelectionLabel: {
    type: [String, Object] as PropType<TranslateResult>,
    required: true,
  },
  addressCardLabel: {
    type: [String, Object] as PropType<TranslateResult>,
    required: true,
  },
  defaultSelectionDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  airportSelectionDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  alternateAddressSelectionDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  airportSelectionDisabledReason: {
    type: [String, Object] as PropType<TranslateResult>,
    required: false,
    default: '',
  },
  isShipperAddress: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const addressStore = useCreateOrderAddressesStore();
const embargoStore = useEmbargoStore();
const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
const selectedHandOver = defineModel<HandOverSelectionValue>({
  default: { selection: 'defaultSelection' },
});
const emit = defineEmits([
  'update:alternate-address',
  'update:alternate-contact',
  'delete-address',
]);

const selectedAddress = computed(() => {
  if (selectedHandOver.value.selection === HandOverSelection.alternateAddress) {
    return selectedHandOver.value.address ?? { ...orderAddress() };
  } else {
    return { ...orderAddress() };
  }
});

const contact = computed(() => {
  if (selectedHandOver.value.selection === HandOverSelection.alternateAddress) {
    return selectedHandOver.value.address?.contact ?? { ...contactData() };
  } else {
    return { ...contactData() };
  }
});

const { port, quoteAirAddressesOrigin } = storeToRefs(useCreateOrderAddressesStore());
const { isOrderFromQuote } = storeToRefs(useCreateOrderFormStore());

const updateAlternateAddress = (addressData: OrderAddress) => {
  emit('update:alternate-address', addressData);
};

const updateAlternateContact = (contactData: ContactData) => {
  emit('update:alternate-contact', contactData);
};

const resetPortIfShipperAndConsigneeHandoverUnequalPort = () => {
  if (
    shipperHandOverSelection.value.selection !== HandOverSelection.port &&
    consigneeHandOverSelection.value.selection !== HandOverSelection.port
  ) {
    port.value = undefined;
  }
};

const emitAddress = (value: HandOverSelectionType | null) => {
  switch (value) {
    case HandOverSelection.default:
      selectedHandOver.value = { selection: value };
      break;
    case HandOverSelection.alternateAddress:
      selectedHandOver.value = {
        selection: value,
        address: selectedAddress.value,
      };
      break;
    case HandOverSelection.port:
      selectedHandOver.value = {
        selection: value,
        port: port.value,
      };
      break;
  }

  resetPortIfShipperAndConsigneeHandoverUnequalPort();
};

const deleteAddress = () => {
  emit('delete-address');
};

const alternateAddressIsDeletable = computed(() => {
  return !(
    quoteAirAddressesOrigin.value.shipper === HandOverSelection.alternateAddress ||
    quoteAirAddressesOrigin.value.consignee === HandOverSelection.alternateAddress
  );
});

watch(
  () => port.value,
  () => {
    emitAddress(selectedHandOver.value.selection);
  },
);
</script>
