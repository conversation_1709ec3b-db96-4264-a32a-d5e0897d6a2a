import { storeToRefs } from 'pinia';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';

function convertTimeToISO(time: string) {
  if (!time) {
    return '';
  }

  const { customCollectionTimeSlot } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());

  const newDate = customCollectionTimeSlot.value.collectionDate
    ? new Date(customCollectionTimeSlot.value.collectionDate)
    : new Date();
  const newDateWithTime = new Date(`${newDate.toDateString()} ${time} UTC`);

  return newDateWithTime.toISOString();
}

export default convertTimeToISO;
