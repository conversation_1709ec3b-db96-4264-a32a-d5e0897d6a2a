<template>
  <SectionCard v-if="showCustomer" class="card-forwarding-order">
    <template #headline>
      {{ t('labels.principal_title.text') }}
    </template>
    <VRow>
      <VCol cols="12" sm="6" lg="5" xl="4" class="customer-number-col">
        <SelectAddress
          v-model="customerNumber"
          v-tooltip="{
            text: toolTipText,
            location: 'right',
            disabled: !isPrincipalSelectionWithTooltipVisible,
            attach: $appRoot,
          }"
          :items="customers ?? []"
          :disabled="isCustomerSectionDisabled"
        />
      </VCol>
    </VRow>
  </SectionCard>
</template>

<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import SelectAddress from '@/components/form/SelectAddress.vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const createOrderDataStore = useCreateOrderDataStore();
const { customers } = storeToRefs(createOrderDataStore);

const createOrderFormStore = useCreateOrderFormStore();
const {
  customerNumber,
  isRoadOrderFromQuoteWithDailyPrice,
  isCompleteRoadOrder,
  isPrincipalLocked,
  isOrderSaved,
} = storeToRefs(createOrderFormStore);

const isCustomerSectionDisabled = computed(() => {
  return (
    isRoadOrderFromQuoteWithDailyPrice.value ||
    isCompleteRoadOrder.value ||
    isPrincipalLocked.value ||
    isOrderSaved.value
  );
});

const showCustomer = computed(() => {
  return customers?.value && customers?.value?.length > 1;
});

const isPrincipalSelectionWithTooltipVisible = computed(() => {
  return isPrincipalLocked.value || isCompleteRoadOrder.value || isOrderSaved.value;
});

const toolTipText = computed(() => {
  if (isOrderSaved.value && !isPrincipalLocked.value && !isCompleteRoadOrder.value) {
    return t('labels.cannot_change_saved_order.text');
  }

  return isPrincipalLocked.value
    ? t('labels.cannot_edit_order.text')
    : t('labels.status_complete_tooltip.text');
});
</script>
<style lang="scss" scoped>
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';

.customer-number-col {
  :deep(.v-select) {
    min-width: 360px;
  }
}
</style>
