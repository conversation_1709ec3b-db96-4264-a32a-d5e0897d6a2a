import { useCreateOrderFormStore } from '@/store/createOrder/form';
import {
  MultipleReferenceNumber,
  useCreateOrderOrderReferencesFormStore,
} from '@/store/createOrder/formOrderReferences';
import {
  AirSeaOrderReference,
  isRoadForwardingOrder,
  OrderReferenceType,
  OrderResponseBody,
  RoadOrderReference,
} from '@dfe/dfe-book-api';
import { isArray } from 'lodash';
import { storeToRefs } from 'pinia';
import { Ref } from 'vue';

export function formatReferenceData(
  reference: AirSeaOrderReference & RoadOrderReference,
): MultipleReferenceNumber | undefined {
  if (!reference.id || !reference.referenceValue) return;

  if (typeof reference.loading === 'undefined' && typeof reference.unloading === 'undefined') {
    return {
      id: String(reference.id),
      value: reference.referenceValue,
    };
  } else {
    return {
      id: String(reference.id),
      value: reference.referenceValue,
      loading: reference.loading ?? false,
      unloading: reference.unloading ?? false,
    };
  }
}

export function getReferenceNumbers(
  references: (RoadOrderReference & AirSeaOrderReference)[] | undefined,
  referenceType: OrderReferenceType,
): MultipleReferenceNumber[] | string | undefined {
  if (!references || references.length === 0) return undefined;

  const result: MultipleReferenceNumber[] = [];
  const filteredReferences = references.filter(
    (reference) => reference.referenceType === referenceType,
  );

  if (referenceType === OrderReferenceType.EKAER_NUMBER && filteredReferences.length > 0) {
    return filteredReferences[0].referenceValue;
  }

  if (referenceType === OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT) {
    filteredReferences.forEach((reference) => {
      if (reference.referenceValue) {
        reference.referenceValue = reference.referenceValue.replace('UIT:', '');
      }
    });
  }

  filteredReferences.forEach((reference) => {
    const referenceData = formatReferenceData(reference);
    if (referenceData) {
      result.push(referenceData);
    }
  });

  return result;
}

function writeMultipleReferenceNumbersToStore(
  references: (RoadOrderReference & AirSeaOrderReference)[],
  referenceType: OrderReferenceType,
  storeProperty: Ref<MultipleReferenceNumber[]>,
  furtherReferencesOrder: Ref<OrderReferenceType[]>,
) {
  const referencesData = getReferenceNumbers(references, referenceType);

  if (referencesData?.length && isArray(referencesData)) {
    storeProperty.value = referencesData;
    furtherReferencesOrder.value.push(referenceType);
  }
}

export function useSetReferencesData(editOrderData: OrderResponseBody) {
  const { orderNumber } = storeToRefs(useCreateOrderFormStore());
  const {
    ekaerNumber,
    ekaerNumberNotRequired,
    shipperReference,
    quotationReference,
    dailyPriceReference,
    furtherReferencesOrder,
    invoiceNumbers,
    purchaseOrderNumbers,
    deliveryNoteNumbers,
    otherNumbers,
    markAndNumbers,
    consigneeReferenceNumbers,
    supplierShipmentNumbers,
    providerShipmentNumbers,
    packingListNumbers,
    commercialInvoiceNumbers,
    identificationCodeTransport,
    bookingReference,
  } = storeToRefs(useCreateOrderOrderReferencesFormStore());

  if (editOrderData.orderNumber) {
    orderNumber.value = editOrderData.orderNumber;
  }

  if (isRoadForwardingOrder(editOrderData)) {
    ekaerNumberNotRequired.value = editOrderData.ekaerNotRequired ?? false;
    if (
      editOrderData.references?.some(
        (reference) => reference.referenceType === OrderReferenceType.EKAER_NUMBER,
      )
    ) {
      ekaerNumber.value = <string>(
        getReferenceNumbers(editOrderData.references, OrderReferenceType.EKAER_NUMBER)
      );
    }
  }

  dailyPriceReference.value =
    editOrderData.references?.find(
      (reference) => reference.referenceType === OrderReferenceType.DAILY_PRICE_REFERENCE,
    )?.referenceValue ?? '';

  shipperReference.value =
    editOrderData.references?.find(
      (reference) => reference.referenceType === OrderReferenceType.SHIPPERS_REFERENCE,
    ) ?? shipperReference.value;

  quotationReference.value =
    editOrderData.references?.find(
      (reference) => reference.referenceType === OrderReferenceType.QUOTATION_REFERENCE,
    ) ?? quotationReference.value;

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.INVOICE_NUMBER,
    invoiceNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <RoadOrderReference & AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.PURCHASE_ORDER_NUMBER,
    purchaseOrderNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <RoadOrderReference & AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.DELIVERY_NOTE_NUMBER,
    deliveryNoteNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <RoadOrderReference[]>editOrderData.references,
    OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT,
    identificationCodeTransport,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <RoadOrderReference[]>editOrderData.references,
    OrderReferenceType.BOOKING_REFERENCE,
    bookingReference,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.OTHERS,
    otherNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.MARKS_AND_NUMBERS,
    markAndNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
    consigneeReferenceNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
    supplierShipmentNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
    providerShipmentNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.PACKAGING_LIST_NUMBER,
    packingListNumbers,
    furtherReferencesOrder,
  );

  writeMultipleReferenceNumbersToStore(
    <AirSeaOrderReference[]>editOrderData.references,
    OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
    commercialInvoiceNumbers,
    furtherReferencesOrder,
  );
}
