import { mockServer } from '@/mocks/server';
import { getAddressValidationErrors } from '@/utils/address/getAddressValidationErrors';
import { Severity } from '@dfe/dfe-address-api';
import { initPinia } from '../../../test/util/init-pinia';

describe('handleAddressBookError composable', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {},
    });
  });

  it('should detect individualID error', () => {
    const addressValidationErrors = getAddressValidationErrors({
      type: 'InvalidAddressProblem',
      detail: 'test',
      errorId: 'test',
      status: 400,
      severity: Severity.Moderate,
      traceId: 'test',
      timestamp: 'test',
      oasDiscriminator: 'InvalidAddressProblem',
      title: 'test',
      validationErrors: [{ fields: ['individualId'], description: 'test' }],
    });

    expect(addressValidationErrors).toEqual([
      {
        field: 'labels.individual_id.text',
        description: 'test',
      },
    ]);
  });

  it('should detect postcode error', () => {
    const addressValidationErrors = getAddressValidationErrors({
      type: 'InvalidAddressProblem',
      detail: 'test',
      errorId: 'test',
      status: 400,
      severity: Severity.Moderate,
      traceId: 'test',
      timestamp: 'test',
      oasDiscriminator: 'InvalidAddressProblem',
      title: 'test',
      validationErrors: [{ fields: ['postcode'], description: 'test' }],
    });

    expect(addressValidationErrors).toEqual([
      {
        field: 'labels.postcode_label.text',
        description: 'test',
      },
    ]);
  });
});
