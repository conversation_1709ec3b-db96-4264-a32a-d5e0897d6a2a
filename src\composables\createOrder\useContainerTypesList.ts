import type { Ref } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { ContainerTypesAsOptions } from '@/composables/data/useContainerTypes';

export const useContainerTypesList = (
  lastUsedHeader: TranslateResult | string,
  header: TranslateResult | string,
  options: Ref<ContainerTypesAsOptions>,
) => {
  const { lastUsed, containerTypes = [] } = options.value;

  if (lastUsed?.length) {
    return [
      { header: lastUsedHeader },
      ...lastUsed,
      { divider: true },
      { header },
      ...containerTypes,
    ];
  }

  return containerTypes;
};
