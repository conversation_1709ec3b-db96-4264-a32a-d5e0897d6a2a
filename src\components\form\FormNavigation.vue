<template>
  <div v-if="items" class="navigation pa-0">
    <div class="links">
      <VBtn
        v-for="({ ref: itemRef, text, error }, i) in items"
        :key="text"
        class="link d-flex align-center justify-start px-2 text-none text-decoration-none"
        :class="{
          'is-active': i === activeIndex,
          'has-error': error,
          'text-grey-900': i !== activeIndex && !error,
          'text-body-2': i !== activeIndex,
          'text-h5': i === activeIndex,
        }"
        variant="plain"
        @click.prevent="onLinkClick(itemRef)"
      >
        <span class="text" :class="{ 'text--reduced-width': error }">
          {{ text }}
        </span>
        <MaterialSymbol v-if="error" size="24" class="text-error pl-2">
          <ErrorIcon />
        </MaterialSymbol>
      </VBtn>
    </div>
  </div>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import useFormNavigationActiveIndex from '@/composables/form/useFormNavigationActiveIndex';
import { ClientKey } from '@/types/client';
import { DialogScroll } from '@/types/events';
import type { FormNavigationItem } from '@/types/general';
import ErrorIcon from '@dfe/dfe-frontend-styles/assets/icons/error-24px.svg';
import { useScroll } from '@vueuse/core';
import type { ComponentPublicInstance } from 'vue';
import { inject, onBeforeUnmount, ref, toRefs, unref, watch } from 'vue';

interface Props {
  items?: FormNavigationItem[];
  activeThreshold?: number;
  scrollBehaviour?: ScrollOptions['behavior'];
}

const props = defineProps<Props>();

const client = inject(ClientKey);
const activeIndex = ref(0);
const yDialog = ref(0);
const offsetTop = ref(0);
const { y: yWindow, arrivedState } = useScroll(window);
const { top, bottom } = toRefs(arrivedState);

const onLinkClick = (ref: FormNavigationItem['ref']) => {
  const element: HTMLElement | ComponentPublicInstance | null = unref(ref);

  if (!element) return;
  const el = element instanceof HTMLElement ? element : element.$el;
  el.scrollIntoView({
    behavior: props.scrollBehaviour ?? 'smooth',
    block: 'start',
  });
};

const handleCreateOrderDialogScroll = (evt: DialogScroll) => {
  offsetTop.value = evt.offset.top;
  yDialog.value = unref(evt.scroll.y);
  top.value = evt.scroll.arrivedState.top;
  bottom.value = evt.scroll.arrivedState.bottom;
};

client?.events.on('createOrderDialogScroll', handleCreateOrderDialogScroll);

onBeforeUnmount(() => {
  client?.events.off('createOrderDialogScroll', handleCreateOrderDialogScroll);
});

watch([top, bottom, yWindow, yDialog], ([isTop, isBottom]) => {
  activeIndex.value = useFormNavigationActiveIndex(
    props.items?.map(({ ref }) => ref),
    {
      top: isTop,
      bottom: isBottom,
      offset: {
        top: offsetTop.value,
        bottom: 0,
      },
      threshold: props.activeThreshold ?? 36,
    },
  );
});
</script>

<style lang="scss" scoped>
@use 'sass:map';
@use '@/styles/settings';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/mixins';

.navigation {
  width: vars.$form-navigation-width-md;
  margin-right: map.get(settings.$grid-gutters, 'md');
  flex: 0 0 auto;

  @media #{map.get(settings.$display-breakpoints, 'lg-and-up')} {
    width: vars.$form-navigation-width-lg;
    margin-right: map.get(settings.$grid-gutters, 'lg');
  }

  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    width: vars.$form-navigation-width-xl;
    margin-right: map.get(settings.$grid-gutters, 'xl');
  }
}

.links {
  position: fixed;
  width: inherit;
}

.link {
  opacity: 1;
  border-radius: 0;
  width: 100%;
  min-height: vars.$form-navigation-item-height-base;
  margin-bottom: vars.$form-navigation-item-spacing-y;
  border-left: vars.$form-navigation-item-marker-width solid var(--color-base-grey-400);

  :deep(.v-btn__content) {
    max-width: 100%;
    text-align: left;
    .text {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &:hover {
    background-color: var(--color-base-grey-50);
    color: rgb(var(--v-theme-blue-500)) !important;
    border-left: vars.$form-navigation-item-marker-width solid rgb(var(--v-theme-blue-500));
  }

  &.is-active {
    color: var(--color-base-blue-500) !important;
    border-color: var(--color-base-blue-500);

    &:hover {
      background-color: var(--color-base-grey-50);
      border-color: var(--color-base-blue-700) !important;

      .text {
        color: var(--color-base-blue-700);
      }
    }
  }

  &.has-error {
    color: var(--color-base-red-500) !important;
    border-color: var(--color-base-red-500);

    &.is-active {
      border-color: var(--color-base-red-500) !important;
    }
    &.has-error:not(:active) {
      border-color: var(--color-base-grey-500);
    }

    &:hover {
      border-color: var(--color-base-red-700) !important;
      color: var(--color-base-red-700) !important;

      .text {
        color: var(--color-base-red-700);
      }
    }
  }
}
</style>
