import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import SelectPackingPosition from '@/components/createOrder/formSectionFreight/packingPosition/SelectPackingPosition.vue';
import SelectField from '@/components/form/SelectField.vue';
import * as uuid from '@/utils/createUuid';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { initPinia } from '@test/util/init-pinia';

const uuidSpy = vi.spyOn(uuid, 'createUuid');

describe('SelectPackingPosition.vue', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    initPinia();
    wrapper = mount(SelectPackingPosition, {
      props: {
        modelValue: null,
        'onUpdate:modelValue': (value: number | null) => wrapper.setProps({ modelValue: value }),
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
    uuidSpy.mockClear();
  });

  it('renders select packing position component', () => {
    expect(wrapper.findComponent(SelectField).exists()).toBe(true);
  });

  it('has none option selected by default', () => {
    expect(wrapper.findComponent(SelectField).find('.v-select__selection-text').text()).toEqual(
      'labels.dfe_none.text',
    );
  });

  it('selects correct option for model value', async () => {
    const orderLineStore = useCreateOrderOrderLineFormStore();
    uuidSpy.mockReturnValueOnce(2);
    orderLineStore.addPackingPosition();
    uuidSpy.mockReturnValueOnce(3);
    orderLineStore.addPackingPosition();

    await wrapper.setProps({ modelValue: 2 });
    expect(wrapper.findComponent(SelectField).find('.v-select__selection-text').text()).toEqual(
      '1',
    );

    await wrapper.setProps({ modelValue: 3 });
    expect(wrapper.findComponent(SelectField).find('.v-select__selection-text').text()).toEqual(
      '2',
    );

    await wrapper.setProps({ modelValue: null });
    expect(wrapper.findComponent(SelectField).find('.v-select__selection-text').text()).toEqual(
      'labels.dfe_none.text',
    );
  });
});
