<template>
  <div ref="seaOrderContainer" tabindex="0">
    <div v-if="seaOrderNavigationItems.length > 0">
      <MandatoryFieldsLabel />
      <FormSection
        v-if="isCustomersAvailable"
        ref="seaOrderCustomers"
        v-model="valid.seaOrderCustomers"
        class="nav-scroll-section"
      >
        <SectionCustomers />
      </FormSection>
      <FormSection
        ref="seaOrderAddresses"
        v-model="valid.seaOrderAddresses"
        class="nav-scroll-section"
      >
        <AddressesSectionWithHandOver />
      </FormSection>
      <FormSection
        ref="seaOrderIncoterms"
        v-model="valid.seaOrderIncoterms"
        class="nav-scroll-section"
      >
        <SectionIncoterms />
      </FormSection>
      <FormSection ref="seaOrderFreight" v-model="valid.seaOrderFreight" class="nav-scroll-section">
        <SectionFreight />
      </FormSection>
      <FormSection
        ref="seaOrderCollection"
        v-model="valid.seaOrderCollection"
        class="nav-scroll-section"
      >
        <SectionCollectionAndDelivery />
      </FormSection>
      <FormSection
        ref="seaOrderReferences"
        v-model="valid.seaOrderReferences"
        class="nav-scroll-section"
      >
        <SectionOrderReferences />
      </FormSection>
      <FormSection
        ref="seaOrderDocuments"
        v-model="valid.seaOrderDocuments"
        class="nav-scroll-section"
      >
        <SectionDocuments />
      </FormSection>
    </div>
    <MainFooter @create-order-validate="validateForms" />
  </div>
</template>

<script setup lang="ts">
import AddressesSectionWithHandOver from '@/components/createOrder/formSectionAddresses/handoverSelection/AddressesSectionWithHandOver.vue';
import SectionCollectionAndDelivery from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import SectionCustomers from '@/components/createOrder/formSectionCustomer/collectingOrder/CardMain.vue';
import MainFooter from '@/components/createOrder/formSectionFooter/MainFooter.vue';
import SectionFreight from '@/components/createOrder/formSectionFreight/CardMain.vue';
import SectionIncoterms from '@/components/createOrder/formSectionIncoTerms/CardMain.vue';
import SectionOrderReferences from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import SectionDocuments from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import FormSection from '@/components/form/FormSection.vue';
import MandatoryFieldsLabel from '@/components/MandatoryFieldsLabel.vue';
import { ValidationResult } from '@/composables/createOrder/useCreateOrderValidation';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useValidationDataStore } from '@/store/validation';
import { HandOverSelection } from '@/types/hand-over';
import { identity } from 'lodash';
import { storeToRefs } from 'pinia';
import { ComponentPublicInstance, computed, ref, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const emit = defineEmits(['seaOrderNavigationItems']);

const addressStore = useCreateOrderAddressesStore();
const { shipperHandOverSelection } = storeToRefs(addressStore);

const seaOrderCustomers = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const seaOrderAddresses = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const seaOrderIncoterms = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const seaOrderFreight = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const seaOrderCollection = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const seaOrderReferences = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const seaOrderDocuments = ref<ComponentPublicInstance<typeof FormSection> | null>(null);

const { hasEmbargo } = storeToRefs(useEmbargoStore());
const { formValidationSectionsSea: valid, getFormSectionsValid } =
  storeToRefs(useValidationDataStore());
const createOrderDataStore = useCreateOrderDataStore();
const { isCustomersAvailable } = storeToRefs(createOrderDataStore);

const seaOrderNavigationItems = computed(() =>
  [
    isCustomersAvailable.value
      ? {
          ref: seaOrderCustomers,
          text: t('labels.principal_title.text'),
          error: !valid.value.seaOrderCustomers,
        }
      : undefined,
    {
      ref: seaOrderAddresses,
      text: t('labels.addresses_title.text'),
      error: !valid.value.seaOrderAddresses || hasEmbargo.value,
    },
    {
      ref: seaOrderIncoterms,
      text: t('labels.incoterms_title.text'),
      error: !valid.value.seaOrderIncoterms,
    },
    {
      ref: seaOrderFreight,
      text: t('labels.order_line_title.text'),
      error: !valid.value.seaOrderFreight,
    },
    {
      ref: seaOrderCollection,
      text:
        shipperHandOverSelection.value.selection === HandOverSelection.port
          ? t('labels.port_delivery.text')
          : t('labels.collection_title.text'),
      error: !valid.value.seaOrderCollection,
    },
    {
      ref: seaOrderReferences,
      text: t('labels.order_references.text'),
      error: !valid.value.seaOrderReferences,
    },
    {
      ref: seaOrderDocuments,
      text: t('labels.documents_label.text'),
      error: !valid.value.seaOrderDocuments,
    },
  ].filter(identity),
);

const validateForms = async (validate: (value: ValidationResult) => void) => {
  await Promise.all(
    seaOrderNavigationItems.value.map(async (item) => {
      return await item?.ref?.value?.validate();
    }),
  );

  validate(getFormSectionsValid.value);
};

watchEffect(() => {
  emit('seaOrderNavigationItems', seaOrderNavigationItems.value);
});
</script>
