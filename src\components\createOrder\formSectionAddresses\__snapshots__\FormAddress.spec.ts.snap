// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Addresses FormAddress component > sets AddressCard props isConsigneeAddress 1`] = `
[
  {
    "header": "labels.frequently_used.text",
  },
  {
    "countryCode": "FR",
    "isPostCodeMandatory": true,
    "label": "France",
  },
  {
    "countryCode": "DE",
    "isPostCodeMandatory": true,
    "label": "Germany",
  },
  {
    "countryCode": "US",
    "isPostCodeMandatory": true,
    "label": "Canada",
  },
  {
    "divider": true,
  },
  {
    "header": "labels.more_options.text",
  },
  {
    "countryCode": "AL",
    "isPostCodeMandatory": true,
    "label": "Albania",
  },
  {
    "countryCode": "DZ",
    "isPostCodeMandatory": true,
    "label": "Algeria",
  },
  {
    "countryCode": "AD",
    "isPostCodeMandatory": true,
    "label": "Andorra",
  },
  {
    "countryCode": "AT",
    "isPostCodeMandatory": true,
    "label": "Austria",
  },
  {
    "countryCode": "BE",
    "isPostCodeMandatory": true,
    "label": "Belgium",
  },
  {
    "countryCode": "BA",
    "isPostCodeMandatory": true,
    "label": "Bosnia and Herzegovina",
  },
  {
    "countryCode": "BG",
    "isPostCodeMandatory": true,
    "label": "Bulgaria",
  },
  {
    "countryCode": "HR",
    "isPostCodeMandatory": true,
    "label": "Croatia",
  },
  {
    "countryCode": "CY",
    "isPostCodeMandatory": true,
    "label": "Cyprus",
  },
  {
    "countryCode": "CZ",
    "isPostCodeMandatory": true,
    "label": "Czech Republic",
  },
  {
    "countryCode": "DK",
    "isPostCodeMandatory": true,
    "label": "Denmark",
  },
  {
    "countryCode": "EE",
    "isPostCodeMandatory": true,
    "label": "Estonia",
  },
  {
    "countryCode": "FO",
    "isPostCodeMandatory": true,
    "label": "Faroe Islands",
  },
  {
    "countryCode": "FI",
    "isPostCodeMandatory": true,
    "label": "Finland",
  },
  {
    "countryCode": "GR",
    "isPostCodeMandatory": true,
    "label": "Greece",
  },
  {
    "countryCode": "GL",
    "isPostCodeMandatory": true,
    "label": "Greenland",
  },
  {
    "countryCode": "HU",
    "isPostCodeMandatory": true,
    "label": "Hungary",
  },
  {
    "countryCode": "IS",
    "isPostCodeMandatory": true,
    "label": "Iceland",
  },
  {
    "countryCode": "IE",
    "isPostCodeMandatory": true,
    "label": "Ireland",
  },
  {
    "countryCode": "IT",
    "isPostCodeMandatory": true,
    "label": "Italy",
  },
  {
    "countryCode": "XK",
    "isPostCodeMandatory": true,
    "label": "Kosovo",
  },
  {
    "countryCode": "LV",
    "isPostCodeMandatory": true,
    "label": "Latvia",
  },
  {
    "countryCode": "LI",
    "isPostCodeMandatory": true,
    "label": "Liechtenstein",
  },
  {
    "countryCode": "LT",
    "isPostCodeMandatory": true,
    "label": "Lithuania",
  },
  {
    "countryCode": "LU",
    "isPostCodeMandatory": true,
    "label": "Luxembourg",
  },
  {
    "countryCode": "MK",
    "isPostCodeMandatory": true,
    "label": "Macedonia",
  },
  {
    "countryCode": "MD",
    "isPostCodeMandatory": true,
    "label": "Moldova",
  },
  {
    "countryCode": "ME",
    "isPostCodeMandatory": true,
    "label": "Montenegro",
  },
  {
    "countryCode": "MA",
    "isPostCodeMandatory": true,
    "label": "Morocco",
  },
  {
    "countryCode": "NL",
    "isPostCodeMandatory": true,
    "label": "Netherlands",
  },
  {
    "countryCode": "NO",
    "isPostCodeMandatory": true,
    "label": "Norway",
  },
  {
    "countryCode": "PL",
    "isPostCodeMandatory": true,
    "label": "Poland",
  },
  {
    "countryCode": "PT",
    "isPostCodeMandatory": true,
    "label": "Portugal",
  },
  {
    "countryCode": "RS",
    "isPostCodeMandatory": true,
    "label": "Republik of Serbia",
  },
  {
    "countryCode": "RO",
    "isPostCodeMandatory": true,
    "label": "Romania",
  },
  {
    "countryCode": "SM",
    "isPostCodeMandatory": true,
    "label": "San Marino",
  },
  {
    "countryCode": "SK",
    "isPostCodeMandatory": true,
    "label": "Slovakia",
  },
  {
    "countryCode": "SI",
    "isPostCodeMandatory": true,
    "label": "Slovenia",
  },
  {
    "countryCode": "ES",
    "isPostCodeMandatory": true,
    "label": "Spain",
  },
  {
    "countryCode": "SE",
    "isPostCodeMandatory": true,
    "label": "Sweden",
  },
  {
    "countryCode": "CH",
    "isPostCodeMandatory": true,
    "label": "Switzerland",
  },
  {
    "countryCode": "TN",
    "isPostCodeMandatory": true,
    "label": "Tunisia",
  },
  {
    "countryCode": "TR",
    "isPostCodeMandatory": true,
    "label": "Turkey",
  },
  {
    "countryCode": "GB",
    "isPostCodeMandatory": true,
    "label": "United Kingdom",
  },
  {
    "countryCode": "VA",
    "isPostCodeMandatory": true,
    "label": "Vatican City",
  },
]
`;
