import type {
  AirExportOrder,
  AirImportOrder,
  AirOrder,
  BasicOrder,
  EQDangerousGood,
  RoadCollectionOrder,
  RoadForwardingOrder,
  RoadOrder,
  SeaExportOrder,
  SeaImportOrder,
} from '@dfe/dfe-book-api';
import {
  CollectionOption,
  CustomsType,
  OrderReferenceType,
  OrderStatus,
  OrderType,
  PortType,
  FreightPayerType,
} from '@dfe/dfe-book-api';
import { FurtherAddressTypesList } from '@/enums';
import { freightTerms } from '@/mocks/fixtures/freightTerms';
import { airDeliveryProducts } from '@/mocks/fixtures/deliveryProducts';

export const basicOrder: BasicOrder = {
  orderId: 1,
  customerNumber: '00000001',
  shipperAddress: {
    customerNumber: undefined,
    name: 'Ray Sono AG',
    name2: 'rear annex',
    name3: 'string',
    street: 'Tumblinger Str. 32',
    postcode: '80337',
    city: 'Munich',
    countryCode: 'DE',
    supplement: 'Tel. 0815',
    gln: '4313920192301',
    neutralizeAddress: true,
    addressType: '',
    contact: {
      name: 'string',
      email: 'string',
      telephone: 'string',
      mobile: 'string',
    },
  },
  consigneeAddress: {
    name: 'Ray Sono AG',
    name2: 'rear annex',
    name3: 'string',
    street: 'Tumblinger Str. 32',
    postcode: '80337',
    city: 'Munich',
    countryCode: 'DE',
    supplement: 'Tel. 0815',
    gln: '4313920192301',
    neutralizeAddress: true,
    addressType: '',
  },
  furtherAddresses: [
    {
      name: 'Ray Sono AG',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
      addressType: FurtherAddressTypesList.coverAddressConsignor,
    },
    {
      addressType: FurtherAddressTypesList.deviatingFreightPayer,
      name: 'RE',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    },
    {
      addressType: FurtherAddressTypesList.customsAgent,
      name: 'CB',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    },
    {
      addressType: FurtherAddressTypesList.finalDeliveryAddress,
      name: 'DP',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    },
    {
      addressType: FurtherAddressTypesList.AE,
      name: 'AE',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    },
    {
      addressType: FurtherAddressTypesList.DC,
      name: 'DC',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    },
    {
      addressType: FurtherAddressTypesList.importer,
      name: 'Importer',
      name2: 'Importer rear annex',
      name3: 'Importer string',
      street: 'Importer Str. 32',
      postcode: '80337',
      city: 'ImporterMunich',
      countryCode: 'DE',
      supplement: 'Importer Tel. 0815',
      gln: '4313920192301',
    },
    {
      name: 'LP',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      addressType: FurtherAddressTypesList.loadingPoint,
    },
  ],
  collectionTime: {
    collectionDate: '2022-11-25',
    from: '2022-11-25T15:15:10.478Z',
    to: '2022-11-25T15:15:10.478Z',
  },
  division: undefined,
  deliveryOption: 'st',
  texts: [
    {
      // eslint-disable-next-line
      // @ts-ignore: no spread argument
      active: true,
      id: undefined,
      textType: 'ZU',
      value: 'Additional Text',
    },
  ],
  orderType: OrderType.RoadForwardingOrder,
  orderNumber: '123456789',
  goodsValue: 999,
  goodsCurrency: 'EUR',
  customsType: CustomsType.DACHSER,
  documentIds: [],
  orderStatus: {
    status: OrderStatus.DRAFT,
  },
  clonedOrder: false,
  principalLocked: false,
};

export const roadOrder: RoadOrder = {
  fixDate: undefined,
  tailLiftDelivery: true,
  freightTerm: freightTerms[0],
  orderGroup: '',
  deliveryContact: {
    email: '',
    id: 0,
    mobile: '',
    name: '',
    telephone: '',
  },
  labelPrinted: false,
  transferListPrinted: false,
  frostProtection: false,
  ekaerNotRequired: false,
  product: 'E',
  orderLineItems: [
    {
      id: 1,
      number: 999,
      quantity: 99999,
      packaging: {
        code: 'string',
        description: 'string',
      },
      content: 'string',
      weight: 99999,
      length: 999,
      width: 999,
      height: 999,
      volume: 0,
      loadingMeter: 0,
      goodsGroup: {
        code: 'string',
        quantity: 99999,
      },
      dangerousGoods: [
        {
          id: 1,
          sortingPosition: 1,
          dangerousGoodType: 'EQDangerousGood',
          packaging: {
            code: 'S',
          },
          noOfPackages: 3,
        },
        {
          id: 2,
          sortingPosition: 2,
          dangerousGoodType: 'EQDangerousGood',
          noOfPackages: 4,
        },
      ] as EQDangerousGood[],
    },
  ],
  packingPositions: [
    {
      id: 1,
      quantity: 999,
      packagingType: {
        code: 'string',
        description: 'string',
      },
      lines: [
        {
          id: 2,
          number: 9999,
          quantity: 99999,
          packaging: {
            code: 'string',
            description: 'string',
          },
          content: 'string',
          weight: 99999,
          length: 999,
          width: 999,
          height: 999,
          volume: 0,
          loadingMeter: 0,
          goodsGroup: {
            code: 'string',
            quantity: 99999,
          },
          packingPositionId: 1,
        },
      ],
    },
  ],
  references: [
    {
      id: 1,
      referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
      referenceValue: '111',
    },
    {
      id: 2,
      referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
      referenceValue: '222',
    },
    {
      referenceType: OrderReferenceType.EKAER_NUMBER,
      referenceValue: '987654321',
    },
  ],
};

export const roadForwardingOrder: RoadForwardingOrder = {
  ...roadOrder,
  ...basicOrder,
  selfCollection: false,
  transportName: '',
  manualNumberSscc: 123,
  orderType: OrderType.RoadForwardingOrder,
  palletLocations: 2,
  generatedSsccs: [],
  cashOnDelivery: false,
  cashOnDeliveryAmount: 0,
};

export const roadForwardingOrderInputForSaveTest: RoadForwardingOrder = {
  ...roadForwardingOrder,
  references: [
    {
      referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
      referenceValue: '111',
    },
    {
      referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
      referenceValue: '222',
    },
    {
      referenceType: OrderReferenceType.EKAER_NUMBER,
      referenceValue: '987654321',
    },
    {
      referenceType: OrderReferenceType.DAILY_PRICE_REFERENCE,
      referenceValue: '060',
    },
  ],
};

export const roadCollectionOrder: RoadCollectionOrder = {
  ...roadOrder,
  ...basicOrder,
  differentConsigneeAddress: {
    name: 'Different Consignee',
    name2: '',
    name3: '',
    street: 'Tumblinger Str. 32',
    postcode: '80337',
    city: 'Munich',
    countryCode: 'DE',
    supplement: '',
    gln: '',
    neutralizeAddress: true,
    addressType: '',
  },
  orderType: OrderType.RoadCollectionOrder,
  collectionOption: CollectionOption.NOTIFICATION,
  freightPayer: FreightPayerType.Principal,
  tailLiftCollection: true,
  interpreter: 'COLLECTION_NOT_BEFORE',
};

export const roadCollectionOrderInputForSaveTest: RoadCollectionOrder = {
  ...roadCollectionOrder,
  references: [
    {
      referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
      referenceValue: '111',
    },
    {
      referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
      referenceValue: '222',
    },
    {
      referenceType: OrderReferenceType.EKAER_NUMBER,
      referenceValue: '987654321',
    },
  ],
};

export const airOrder: AirOrder = {
  fromIATA: {
    code: 'MUC',
    name: 'Munich',
    countryCode: 'DE',
    type: PortType.AIRPORT,
  },
  toIATA: {
    code: 'ZRH',
    name: 'Zurich',
    countryCode: 'CH',
    type: PortType.AIRPORT,
  },
  deliverToAirport: false,
  collectFromAirport: false,
  pickupAddress: {
    name: 'Ray Sono AG',
    name2: 'rear annex',
    name3: 'string',
    street: 'Tumblinger Str. 32',
    postcode: '80337',
    city: 'Munich',
    countryCode: 'DE',
    supplement: 'Tel. 0815',
    gln: '4313920192301',
    contact: {
      name: 'string',
      email: 'string',
      telephone: 'string',
      mobile: 'string',
    },
  },
  deliveryAddress: {
    name: 'Ray Sono AG',
    name2: 'rear annex',
    name3: 'string',
    street: 'Tumblinger Str. 32',
    postcode: '80337',
    city: 'Munich',
    countryCode: 'DE',
    supplement: 'Tel. 0815',
    gln: '4313920192301',
    contact: {
      name: 'string',
      email: 'string',
      telephone: 'string',
      mobile: 'string',
    },
  },
  shipperReference: 'string',
  incoTerm: {
    code: 'DAP',
    dachserCode: 'DAP',
    description:
      'Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading.',
    id: 15,
    label: 'DAP – Delivery At Place',
  },
  orderLineItems: [
    {
      id: 1,
      number: 999,
      quantity: 99999,
      packaging: {
        code: 'string',
        description: 'string',
      },
      weight: 99999,
      length: 999,
      width: 999,
      height: 999,
      volume: 0,
      hsCodes: [{ hsCode: '070190', goods: 'Test' }],
    },
  ],
  references: [
    {
      referenceType: OrderReferenceType.QUOTATION_REFERENCE,
      referenceValue: '987654321',
      loading: false,
      unloading: false,
    },
    {
      id: 2,
      referenceType: OrderReferenceType.INVOICE_NUMBER,
      referenceValue: '22',
      loading: true,
      unloading: true,
    },
    {
      id: 1,
      referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
      referenceValue: '11',
      loading: true,
      unloading: false,
    },
    {
      id: 3,
      referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
      referenceValue: '33',
      loading: false,
      unloading: true,
    },
    {
      id: 4,
      referenceType: OrderReferenceType.OTHERS,
      referenceValue: '44',
      loading: false,
      unloading: false,
    },
    {
      id: 5,
      referenceType: OrderReferenceType.MARKS_AND_NUMBERS,
      referenceValue: '55',
      loading: true,
      unloading: true,
    },
    {
      id: 6,
      referenceType: OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      referenceValue: '66',
      loading: false,
      unloading: true,
    },
    {
      id: 7,
      referenceType: OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      referenceValue: '77',
      loading: true,
      unloading: false,
    },
    {
      id: 8,
      referenceType: OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
      referenceValue: '88',
      loading: false,
      unloading: false,
    },
    {
      id: 9,
      referenceType: OrderReferenceType.PACKAGING_LIST_NUMBER,
      referenceValue: '99',
      loading: true,
      unloading: true,
    },
    {
      id: 10,
      referenceType: OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
      referenceValue: '111',
      loading: false,
      unloading: true,
    },
  ],
  customerContactData: {
    name: 'string',
    email: 'string',
    telephone: 'string',
    mobile: 'string',
  },
  product: airDeliveryProducts[0],
};

export const airExportOrder: AirExportOrder = {
  ...basicOrder,
  ...airOrder,
  orderType: OrderType.AirExportOrder,
};

export const airImportOrder: AirImportOrder = {
  ...basicOrder,
  ...airOrder,
  orderType: OrderType.AirImportOrder,
};

export const airExportOrderInputForSaveTest: AirExportOrder = {
  ...airExportOrder,
  references: [
    {
      referenceType: OrderReferenceType.QUOTATION_REFERENCE,
      referenceValue: '987654321',
    },
    {
      referenceType: OrderReferenceType.INVOICE_NUMBER,
      referenceValue: '22',
      loading: true,
      unloading: true,
    },
    {
      referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
      referenceValue: '11',
      loading: true,
      unloading: false,
    },
    {
      referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
      referenceValue: '33',
      loading: false,
      unloading: true,
    },
    {
      referenceType: OrderReferenceType.OTHERS,
      referenceValue: '44',
      loading: false,
      unloading: false,
    },
    {
      referenceType: OrderReferenceType.MARKS_AND_NUMBERS,
      referenceValue: '55',
      loading: true,
      unloading: true,
    },
    {
      referenceType: OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      referenceValue: '66',
      loading: false,
      unloading: true,
    },
    {
      referenceType: OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      referenceValue: '77',
      loading: true,
      unloading: false,
    },
    {
      referenceType: OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
      referenceValue: '88',
      loading: false,
      unloading: false,
    },
    {
      referenceType: OrderReferenceType.PACKAGING_LIST_NUMBER,
      referenceValue: '99',
      loading: true,
      unloading: true,
    },
    {
      referenceType: OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
      referenceValue: '111',
      loading: false,
      unloading: true,
    },
  ],
};

export const seaExportOrder: SeaExportOrder = {
  ...basicOrder,
  fromPort: {
    code: 'DEHAM',
    name: 'Hamburg',
    countryCode: 'DE',
    type: PortType.SEAPORT,
  },
  toPort: {
    code: 'DEMUC',
    name: 'Munich',
    countryCode: 'DE',
    type: PortType.SEAPORT,
  },
  collectFromPort: true,
  deliverToPort: false,
  shipperReference: 'string',
  loading: false,
  unloading: false,
  requestArrangement: false,
  tailLiftCollection: false,
  stackable: false,
  shockSensitive: false,
  orderType: OrderType.SeaExportOrder,
  orderLineItems: [
    {
      id: 1,
      number: 999,
      quantity: 99999,
      packaging: {
        code: 'string',
        description: 'string',
      },
      weight: 99999,
      length: 999,
      width: 999,
      height: 999,
      volume: 0,
      hsCodes: [{ hsCode: '070190', goods: 'Test' }],
    },
  ],
  fullContainerLoads: [
    {
      id: 1,
      sortingPosition: 1,
      containerNumber: 'string',
      containerType: {
        code: 'string',
        description: 'string',
      },
      verifiedGrossMass: 999,
      lines: [
        {
          id: 2,
          number: 9999,
          quantity: 99999,
          packaging: {
            code: 'string',
            description: 'string',
          },
          weight: 99999,
          length: 999,
          width: 999,
          height: 999,
          volume: 0,
          fullContainerLoadId: 1,
        },
      ],
    },
  ],
};

export const seaImportOrder: SeaImportOrder = {
  ...basicOrder,
  fromPort: {
    code: 'DEHAM',
    name: 'Hamburg',
    countryCode: 'DE',
    type: PortType.SEAPORT,
  },
  toPort: {
    code: 'DEMUC',
    name: 'Munich',
    countryCode: 'DE',
    type: PortType.SEAPORT,
  },
  collectFromPort: true,
  deliverToPort: false,
  shipperReference: 'string',
  loading: false,
  unloading: false,
  requestArrangement: false,
  tailLiftCollection: false,
  stackable: false,
  shockSensitive: false,
  orderType: OrderType.SeaImportOrder,
  orderLineItems: [
    {
      id: 1,
      number: 999,
      quantity: 99999,
      packaging: {
        code: 'string',
        description: 'string',
      },
      weight: 99999,
      length: 999,
      width: 999,
      height: 999,
      volume: 0,
      hsCodes: [{ hsCode: '070190', goods: 'Test' }],
    },
  ],
  fullContainerLoads: [
    {
      id: 1,
      sortingPosition: 1,
      containerNumber: 'string',
      containerType: {
        code: 'string',
        description: 'string',
      },
      verifiedGrossMass: 999,
      lines: [
        {
          id: 2,
          number: 9999,
          quantity: 99999,
          packaging: {
            code: 'string',
            description: 'string',
          },
          weight: 99999,
          length: 999,
          width: 999,
          height: 999,
          volume: 0,
          fullContainerLoadId: 1,
        },
      ],
    },
  ],
};
