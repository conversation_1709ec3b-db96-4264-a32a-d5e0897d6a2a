import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import useLoadingPointsWithLabel from '@/composables/createOrder/useLoadingPointsWithLabel';
import { useLoadingPoints } from '@/composables/data/useLoadingPoints';
import { useValidationDataStore } from '@/store/validation';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { storeToRefs } from 'pinia';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';

function useResetCreateOrderFormData() {
  const { data: loadingPoints } = useLoadingPoints();
  const clearFormData = () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { transportType, orderType, preferredCurrency } = createOrderFormStore;
    const { setLoadingPoint } = useLoadingPointsWithLabel(loadingPoints);
    const createOrderDataStore = useCreateOrderDataStore();
    const { fromPortRouting, toPortRouting } = storeToRefs(createOrderDataStore);

    // Workaround for createOrderDataStore.$reset(), because it doesn't reset the values of fromPortRouting and toPortRouting
    fromPortRouting.value = [];
    toPortRouting.value = [];

    createOrderFormStore.$reset();
    createOrderDataStore.$reset();
    useCreateOrderFormAccountingAdditionalServices().$reset();
    useCreateOrderAddressesStore().$reset();
    useCreateOrderFormCollectionAndDeliveryStore().$reset();
    useCreateOrderOrderReferencesFormStore().$reset();
    useCreateOrderTextsStore().$reset();
    useCreateOrderDocumentsStore().$reset();
    useCreateOrderOrderLineFormStore().$reset();
    useErrorBanner().$reset();
    useValidationDataStore().$reset();
    useEmbargoStore().$reset();

    createOrderFormStore.transportType = transportType;
    createOrderFormStore.orderType = orderType;
    createOrderFormStore.preferredCurrency = preferredCurrency;
    setLoadingPoint();
  };

  return {
    clearFormData,
  };
}

export default useResetCreateOrderFormData;
