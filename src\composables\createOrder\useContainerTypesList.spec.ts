import { ref } from 'vue';
import { ContainerTypesAsOptions } from '@/composables/data/useContainerTypes';
import { useContainerTypesList } from '@/composables/createOrder/useContainerTypesList';

const lastUsedHeader = 'Last used';
const header = 'More types';

const emptyContainerTypeOptions = ref([] as ContainerTypesAsOptions);
const containerTypes = [
  { code: 'RE-20', description: '20 Reefer' },
  { code: 'OT-20', description: '20 Open Top' },
];
const lastUsed = [
  { code: 'GP-20', description: '20 Standard' },
  { code: 'OT-40', description: '40 Open Top' },
];

describe('useContainerTypesList composable', () => {
  it('returns an empty array if no container types are available', () => {
    expect(useContainerTypesList(lastUsedHeader, header, emptyContainerTypeOptions));
  });

  it('returns container types', () => {
    expect(useContainerTypesList(lastUsedHeader, header, ref({ containerTypes }))).toEqual(
      containerTypes,
    );
  });

  it('returns packaging options with lastUsed and headers', () => {
    expect(
      useContainerTypesList(lastUsedHeader, header, ref({ containerTypes, lastUsed })),
    ).toEqual([
      { header: lastUsedHeader },
      ...lastUsed,
      { divider: true },
      { header },
      ...containerTypes,
    ]);
  });
});
