import FormNavigation from '@/components/form/FormLabel.vue';
import type { TestUtils } from '../../../test/test-utils';
import { mount } from '@vue/test-utils';

const propsData = {
  for: 'someId',
};

describe('FormLabel', () => {
  let wrapper: TestUtils.VueWrapper<typeof FormNavigation>;

  beforeEach(() => {
    wrapper = mount(FormNavigation, {
      propsData,
      slots: {
        default: 'Label content',
      },
    });
  });

  it('mounts – and shows up with slot content', async () => {
    await wrapper.vm.$nextTick();

    expect(wrapper.get('label').text()).toBe('Label content');
  });

  it('shows required indication (if prop is set)', async () => {
    expect(wrapper.find('span').exists()).toBe(false);

    await wrapper.setProps({ required: true });

    expect(wrapper.find('span').exists()).toBe(true);
  });

  it.each([
    ['text content', 'Label content'],
    ['tagged content', '<span>Label content</span>'],
    ['mixed content', 'Label <span>content</span>'],
  ])('mounts – and shows up with slot: %s', async (_, labelData) => {
    wrapper = mount(FormNavigation, {
      propsData,
      slots: {
        default: labelData,
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.find('label').exists()).toBe(true);
    expect(wrapper.find('label').html()).toContain(labelData);
  });

  it('shows an icon with tooltip if prop is true', async () => {
    expect(wrapper.findComponent({ name: 'v-tooltip' }).exists()).toBe(false);

    await wrapper.setProps({ tooltip: 'Tooltip' });

    expect(wrapper.findComponent({ name: 'v-tooltip' }).exists()).toBe(true);
  });

  it("mounts – without showing up (if slot content isn't set)", async () => {
    wrapper = mount(FormNavigation, {
      propsData,
    });

    await wrapper.vm.$nextTick();

    // Doesn't show without slot content
    expect(wrapper.find('label').exists()).toBe(false);

    wrapper.unmount();
  });
});
