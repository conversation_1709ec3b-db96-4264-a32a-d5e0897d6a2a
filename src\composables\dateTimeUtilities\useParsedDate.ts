import { parse } from 'date-fns';

function isValidDate(date: Date) {
  return !!date.getTime();
}

function useParsedDate(value: string, format: string | string[], referenceDate?: Date) {
  if (value && format?.length) {
    const formats = typeof format === 'string' ? [format] : format;
    let refDate = referenceDate;

    if (!refDate) {
      refDate = new Date();
      refDate.setHours(0);
    }

    for (const format of formats) {
      const date = parse(value, format, refDate);
      if (isValidDate(date)) {
        return date;
      }
    }
  }

  return value;
}

export default useParsedDate;
