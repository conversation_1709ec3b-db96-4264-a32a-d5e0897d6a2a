import isValidDate from '@/utils/isValidDate';

describe('isValidDate util', () => {
  it('returns if the input value is a valid date object', () => {
    expect(isValidDate(new Date())).toBe(true);
    expect(isValidDate(new Date('2022-09-19'))).toBe(true);
    expect(isValidDate(new Date('2022-09-40'))).toBe(false);
    expect(isValidDate(null)).toBe(false);
    expect(isValidDate('2022-09-19')).toBe(false);
    expect(isValidDate(2022)).toBe(false);
  });
});
