import type { InvalidAddressProblem } from '@dfe/dfe-address-api';
import type { AddressValidationError } from '@/types/createOrder';

const getAddressValidationErrors = (error: InvalidAddressProblem) => {
  const errorMessageAddressBook: AddressValidationError[] = [];

  if (!error?.validationErrors) {
    return [];
  }

  error.validationErrors.forEach((error) => {
    if (!error.fields) {
      return;
    }

    error.fields.forEach((field) => {
      errorMessageAddressBook.push({
        field: mapParameterToLabel(field),
        description: error.description ? error.description : '',
      });
    });
  });
  return errorMessageAddressBook;
};

const mapParameterToLabel = (field: string): string => {
  switch (field) {
    case 'individualId':
      return 'labels.individual_id.text';
    case 'postcode':
      return 'labels.postcode_label.text';
  }

  return field;
};

export { getAddressValidationErrors };
