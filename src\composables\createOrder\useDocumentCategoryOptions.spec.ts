import useDocumentCategoryOptions, {
  isValidExtension,
} from '@/composables/createOrder/useDocumentCategoryOptions';
import {
  documentTypes,
  supportedExtensions,
  supportedExtensionsPdfOnly,
} from '@/mocks/fixtures/documentTypes';
import { StoreDocument } from '@/store/createOrder/orderDocuments';

const recentlyUsedHeader = 'Favorites';
const recentlyUsed = [
  { header: recentlyUsedHeader },
  {
    ...documentTypes[4],
    description: `${documentTypes[4].category}: ${documentTypes[4].description}`,
  },
  {
    ...documentTypes[8],
    description: `${documentTypes[8].category}: ${documentTypes[8].description}`,
  },
  {
    ...documentTypes[9],
    description: `${documentTypes[9].category}: ${documentTypes[9].description}`,
  },
  {
    ...documentTypes[10],
    description: `${documentTypes[10].category}: ${documentTypes[10].description}`,
  },
  { divider: true },
];
const documentTypesByCategory = [
  { header: 'document' },
  documentTypes[0],
  documentTypes[6],
  documentTypes[14],
  { divider: true },
  { header: 'document2' },
  documentTypes[1],
  documentTypes[3],
  documentTypes[5],
  { divider: true },
  { header: 'document3' },
  documentTypes[2],
  documentTypes[4],
  documentTypes[7],
  documentTypes[15],
  { divider: true },
  { header: 'document4' },
  documentTypes[8],
  documentTypes[9],
  documentTypes[10],
  { divider: true },
  { header: 'document1' },
  documentTypes[11],
  documentTypes[12],
  documentTypes[13],
  { divider: true },
];

const documentTypesByCategoryWithoutRecentlyUsed = [
  { header: 'document' },
  documentTypes[0],
  documentTypes[6],
  documentTypes[14],
  { divider: true },
  { header: 'document2' },
  documentTypes[1],
  documentTypes[3],
  documentTypes[5],
  { divider: true },
  { header: 'document3' },
  documentTypes[2],
  documentTypes[7],
  documentTypes[15],
  { divider: true },
  { header: 'document1' },
  documentTypes[11],
  documentTypes[12],
  documentTypes[13],
  { divider: true },
];

const documentTypesByCategoryWithoutUnsupportedFiles = [
  { header: 'document' },
  documentTypes[0],
  documentTypes[6],
  documentTypes[14],
  { divider: true },
  { header: 'document2' },
  documentTypes[1],
  documentTypes[3],
  documentTypes[5],
  { divider: true },
  { header: 'document3' },
  documentTypes[2],
  documentTypes[4],
  documentTypes[7],
  documentTypes[15],
  { divider: true },
  { header: 'document1' },
  documentTypes[11],
  documentTypes[12],
  documentTypes[13],
  { divider: true },
];

const pdfDocument: StoreDocument = {
  documentName: 'Placeholder.pdf',
  file: undefined,
  mimeType: 'application/pdf',
  size: 8614,
  progress: 100,
  documentId: 3462,
  uploadStatus: 'success',
  documentType: 'CDORG',
  documentTypeId: 48,
};

const xlsDocument: StoreDocument = {
  documentName: 'Placeholder.pdf',
  file: undefined,
  mimeType: 'application/vnd.ms-excel',
  size: 8614,
  progress: 100,
  documentId: 3462,
  uploadStatus: 'success',
  documentType: 'CDORG',
  documentTypeId: 48,
};

describe('useDocumentCategoryOptions composable', () => {
  it('returns frequently used items', () => {
    expect(
      useDocumentCategoryOptions(
        recentlyUsedHeader,
        documentTypes,
        [documentTypes[4], documentTypes[8], documentTypes[9], documentTypes[10]],
        pdfDocument,
      ),
    ).toEqual([...recentlyUsed, ...documentTypesByCategoryWithoutRecentlyUsed]);
  });

  it('returns items without frequently used if there is no frequently used options', () => {
    expect(useDocumentCategoryOptions(recentlyUsedHeader, documentTypes, [], pdfDocument)).toEqual([
      ...documentTypesByCategory,
    ]);
  });

  it('returns items excluding unsupported extensions', () => {
    expect(
      useDocumentCategoryOptions(recentlyUsedHeader, documentTypes, [], xlsDocument),
    ).toStrictEqual([...documentTypesByCategoryWithoutUnsupportedFiles]);
  });

  describe('isValidExtension helper', () => {
    it('returns true if extension is missing', () => {
      expect(isValidExtension(undefined, supportedExtensions)).toBe(true);
    });

    it('returns false if extension is not supported', () => {
      expect(isValidExtension('application/vnd.ms-excel', supportedExtensionsPdfOnly)).toBe(false);
    });

    it('returns true if extension is supported', () => {
      expect(isValidExtension('application/vnd.ms-excel', supportedExtensions)).toBe(true);
    });

    it('returns true if extension is supported for saved files', () => {
      expect(isValidExtension('.xls', supportedExtensions)).toBe(true);
    });

    it('returns true if there are no supported extensions', () => {
      expect(isValidExtension('application/vnd.ms-excel', [])).toBe(true);
      expect(isValidExtension('application/vnd.ms-excel', undefined)).toBe(true);
    });
  });
});
