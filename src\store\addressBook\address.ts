import type { Address as AddressBookAddress, ContactData } from '@dfe/dfe-address-api';
import { defineStore } from 'pinia';
import type { ContactsType } from '@/enums';
import type { Address, OrderAddress } from '@dfe/dfe-book-api';
import { sortContacts } from '@/utils/address/sortContactData';

export interface AddressesData {
  loading: boolean;
  search: AddressBookAddress[];
}

export interface CreateAddressState {
  addresses: AddressesData;
  contactsDelivery: ContactData[];
  contactsCollection: ContactData[];
  contactsCustomer: ContactData[];
  contactsAlternativePickup: ContactData[];
  contactsAlternativeDelivery: ContactData[];
}

let searchAddressCancelToken: symbol | string | number;

export const useCreateAddressDataStore = defineStore('createAddressData', {
  state: (): CreateAddressState => ({
    addresses: {
      loading: false,
      search: [],
    },
    contactsDelivery: [],
    contactsCollection: [],
    contactsCustomer: [],
    contactsAlternativePickup: [],
    contactsAlternativeDelivery: [],
  }),
  actions: {
    async upsertAddress(address: OrderAddress) {
      const addressForAddressBook: Address = {
        ...address,
        id: address.originAddressId,
      };

      if (!addressForAddressBook.postcode) {
        delete addressForAddressBook.postcode;
      }

      if (!addressForAddressBook.id) {
        delete addressForAddressBook.id;
        delete (addressForAddressBook as OrderAddress).originAddressId;
        return (await this.api.address.v1.addAddressV1(addressForAddressBook as AddressBookAddress))
          .data;
      } else {
        return (
          await this.api.address.v1.updateAddressV1(
            addressForAddressBook.id,
            addressForAddressBook as AddressBookAddress,
          )
        ).data;
      }
    },
    async addContactToAddress(addressId: number, contactData: ContactData) {
      try {
        const contact = { ...contactData };
        delete contact.id;
        const { data } = await this.api.address.v1.addContactToAddressV1(addressId, contact);
        return data.id;
      } catch (error) {
        this.client?.log.error('Failed to add contact to address', 'dfe-book-frontend', error);
        throw error;
      }
    },
    async searchNameInAddressBook(query: string) {
      this.api.book.abortRequest(searchAddressCancelToken);
      this.addresses.loading = true;

      const cancelToken = this.createCancelToken();
      searchAddressCancelToken = cancelToken;

      try {
        const { data } = await this.api.address.v1.searchAddressV1({ query }, { cancelToken });
        this.addresses.search = data;
      } catch (error) {
        this.client?.log.error('Failed to search address', 'dfe-book-frontend', error);
      }

      if (cancelToken === searchAddressCancelToken) {
        this.addresses.loading = false;
      }
    },
    async getAddressContacts(
      addressId: number,
      type: (typeof ContactsType)[keyof typeof ContactsType],
    ) {
      try {
        const { data } = await this.api.address.v1.getContactListOfAddressV1(addressId);
        // Sort the contacts so that isMainContact=true comes first
        const sortedData = sortContacts(data);

        if (type === 'delivery') {
          this.contactsDelivery = sortedData;
        }
        if (type === 'collection') {
          this.contactsCollection = sortedData;
        }
        if (type === 'customer') {
          this.contactsCustomer = sortedData;
        }

        return sortedData;
      } catch (error) {
        this.client?.log.error('Failed to get address contacts', 'dfe-book-frontend', error);
      }
    },
    async updateAddressContact(addressId: number, contactId: number, contact: ContactData) {
      try {
        const { data } = await this.api.address.v1.updateContactDataV1(
          addressId,
          contactId,
          contact,
        );
        return data;
      } catch (error) {
        this.client?.log.error('Failed to update address contact', 'dfe-book-frontend', error);
      }
    },
  },
});
