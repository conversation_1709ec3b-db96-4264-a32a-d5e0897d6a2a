import type { CreationPreferences } from '@dfe/dfe-book-api';
import type { Address, ContactData } from '@dfe/dfe-address-api';

import type VueI18n from 'vue-i18n';
import type { OrderLine4Store } from '@/store/createOrder/orderLine';
import type { Preferences } from '@dfe/dfe-frontend-client';

export type TermType = 'INCOTERM' | 'FREIGHT_TERM';

export type PreferredCurrency = NonNullable<CreationPreferences['preferredCurrency']>;

export type ContactPreferences = {
  phoneNumber?: string;
  mobileNumber?: string;
};

export type TimeFormat = Preferences['timeFormat'];
export type DateFormat = Preferences['dateFormat'];

export type OrderContentResponse = string[];

export type CustomsDeclarationExecutor = 'customer' | 'dachser';

export type FurtherAddressTypes =
  | 'CX'
  | 'N1'
  | 'N2'
  | 'PJ'
  | 'TO'
  | 'UP'
  | 'CB'
  | 'DC'
  | 'DA'
  | 'DP'
  | 'IM'
  | 'CC'
  | 'RE'
  | 'OS'
  | 'FW'
  | 'UC'
  | 'CT'
  | 'LP'
  | 'UK'
  | 'RA'
  | 'EV'
  | 'OY'
  | 'OB'
  | 'WB'
  | 'AE';

export type OrderLineLengthValueObject = {
  length: number;
  width: number;
  value: string;
  menuText: string;
};

export type MeasurementProposalsMenu = Array<
  | OrderLineLengthValueObject
  | {
      header: VueI18n.TranslateResult;
    }
  | {
      divider: boolean;
    }
> | null;

type Key = keyof OrderLine4Store;
type PropertyType = OrderLine4Store[Key];
export type MultipleKeyValueObject = {
  [key in Key]: PropertyType;
};

type ContactDataKey = keyof ContactData;
type ContactDataPropertyType = ContactData[ContactDataKey];
export type multipleContactDataKeyValueObject = {
  [key in ContactDataKey]?: ContactDataPropertyType;
};

type AddressKey = keyof Address;
type AddressPropertyType = Address[AddressKey];
export type multipleAddressKeyValueObject = {
  [key in AddressKey]?: AddressPropertyType;
};

export type VueUploadDocument = {
  id: string;
  readonly fileObject?: boolean;
  name?: string;
  size?: number;
  type?: string;
  active?: boolean;
  error?: Error | string;
  success?: boolean;
  postAction?: string;
  putAction?: string;
  timeout?: number;
  progress?: string;
  speed?: 0;
  xhr?: XMLHttpRequest;
  el?: HTMLInputElement;
  iframe?: HTMLElement;
  file: File;
};

export type AddressValidationError = {
  description: string;
  field: string;
};
