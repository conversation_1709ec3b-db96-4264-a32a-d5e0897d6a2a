@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

$body-font-family: vars.$font-family-base;
$heading-font-family: $body-font-family;

$font-size-root: vars.$font-size-base;

$headings: (
  'h1': (
    'size': vars.$font-size-heading-1,
    'weight': vars.$font-weight-heading,
    'line-height': vars.$line-height-heading-1,
    'letter-spacing': vars.$letter-spacing-heading-1,
    'font-family': $heading-font-family,
    'text-transform': false,
  ),
  'h2': (
    'size': vars.$font-size-heading-2,
    'weight': vars.$font-weight-heading,
    'line-height': vars.$line-height-heading-2,
    'letter-spacing': vars.$letter-spacing-heading-2,
    'font-family': $heading-font-family,
    'text-transform': false,
  ),
  'h3': (
    'size': vars.$font-size-heading-3,
    'weight': vars.$font-weight-heading,
    'line-height': vars.$line-height-heading-3,
    'font-family': $heading-font-family,
    'text-transform': false,
  ),
  'h4': (
    'size': vars.$font-size-heading-4,
    'weight': vars.$font-weight-heading,
    'line-height': vars.$line-height-heading-4,
    'font-family': $heading-font-family,
    'text-transform': false,
  ),
  'h5': (
    'size': vars.$font-size-heading-5,
    'weight': vars.$font-weight-heading,
    'line-height': vars.$line-height-heading-5,
    'font-family': $heading-font-family,
    'text-transform': false,
  ),
  'h6': (
    'size': vars.$font-size-heading-6,
    'weight': vars.$font-weight-heading,
    'line-height': vars.$line-height-heading-6,
    'font-family': $heading-font-family,
    'text-transform': false,
  ),
  'body-1': (
    'size': vars.$font-size-body-1,
    'weight': vars.$font-weight-body,
    'line-height': vars.$line-height-body-1,
    'font-family': $body-font-family,
  ),
  'body-2': (
    'size': vars.$font-size-body-2,
    'weight': vars.$font-weight-body,
    'line-height': vars.$line-height-body-2,
    'font-family': $body-font-family,
  ),
  'body-3': (
    'size': vars.$font-size-body-3,
    'weight': vars.$font-weight-body,
    'line-height': vars.$line-height-body-3,
    'letter-spacing': normal,
    'font-family': $body-font-family,
    'text-transform': false,
  ),
  'caption': (
    'size': vars.$font-size-caption,
    'weight': vars.$font-weight-body,
    'line-height': vars.$line-height-caption,
    'font-family': $body-font-family,
  ),
  'label-1': (
    'size': vars.$font-size-label-1,
    'weight': vars.$font-weight-label,
    'line-height': vars.$line-height-label-1,
    'letter-spacing': normal,
    'font-family': $body-font-family,
    'text-transform': false,
  ),
  'label-2': (
    'size': vars.$font-size-label-2,
    'weight': vars.$font-weight-label,
    'line-height': vars.$line-height-label-2,
    'letter-spacing': normal,
    'font-family': $body-font-family,
    'text-transform': false,
  ),
  'label-3': (
    'size': vars.$font-size-label-3,
    'weight': vars.$font-weight-label,
    'line-height': vars.$line-height-label-3,
    'letter-spacing': normal,
    'font-family': $body-font-family,
    'text-transform': false,
  ),
);
