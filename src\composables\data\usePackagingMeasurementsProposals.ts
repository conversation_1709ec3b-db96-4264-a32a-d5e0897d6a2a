import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import type { MaybeRefOrGetter } from 'vue';
import { computed } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useToString } from '@vueuse/core';
import type { MeasurementProposals } from '@dfe/dfe-book-api';

export const usePackagingMeasurementsProposals = (packagingCode: MaybeRefOrGetter<string>) => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { customerNumber, transportType } = storeToRefs(formStore);

  const packagingRef = useToString(packagingCode);

  return useCustomerQuery<MeasurementProposals>(
    'packagingMeasurementsProposals',
    (customerArgs) => {
      return api.book.customers.getPackagingMeasurementsProposals({
        ...customerArgs,
        packagingCode: packagingRef.value,
      });
    },
    {
      queryKey: [packagingCode],
      enabled: computed(
        () => !!customerNumber.value && !!transportType.value && !!packagingRef.value,
      ),
    },
  );
};
