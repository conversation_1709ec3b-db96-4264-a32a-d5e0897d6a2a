<template>
  <DfeBanner type="info" class="my-4">
    <div class="text-h5">{{ title }}</div>
    <template v-if="orderIsPriceDependent">
      <div class="text-body-2 mt-2">
        {{ t('messages.id6627.description') }}
      </div>
      <VBtn
        v-if="showQuoteCrossLink"
        :href="sanitizeHtml(targetPath)"
        class="mt-2"
        size="small"
        variant="text"
        color="primary"
        @click.prevent="onClick"
      >
        <MaterialSymbol :size="16" left>
          <ChevronRightIcon />
        </MaterialSymbol>
        {{ t('labels.view_quote.text') }}
      </VBtn>
    </template>
    <div v-else class="text-body-2 mt-2">{{ t('messages.id6612.text') }}</div>
  </DfeBanner>
</template>

<script setup lang="ts">
import { Routes } from '@/enums';
import { computed } from 'vue';
import DfeBanner from './DfeBanner.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import ChevronRightIcon from '@dfe/dfe-frontend-styles/assets/icons/chevron_right-16px.svg';
import sanitizeHtml from 'sanitize-html';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import { useHasAccess } from '@dfe/dfe-frontend-user-profile';
import { Segment } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useClient } from '@/composables/useClient';

interface Props {
  title: TranslateResult;
  quoteId?: number | null;
  orderIsPriceDependent?: boolean;
}

const props = defineProps<Props>();

const { client } = useClient();
const { t } = useI18n();
const createOrderFormStore = useCreateOrderFormStore();
const { customerNumber, transportType } = storeToRefs(createOrderFormStore);

const segment = computed(() => (transportType.value == Segment.ROAD ? 'ROAD' : 'ASL'));
const hasQuoteAccess = useHasAccess('general.quote', segment, customerNumber);

const showQuoteCrossLink = computed(() => {
  return hasQuoteAccess.value && props?.quoteId;
});

const targetPath = computed(() => `${Routes.QUOTE}/${String(props.quoteId)}`);

const onClick = () => {
  if (props?.quoteId) {
    client?.events.emit('closeBookDialog', {});
    client?.router.remoteNavigate(targetPath.value, location.pathname);
  }
};
</script>
