import FormDelivery from '@/components/createOrder/formSectionCollectionAndDelivery/FormDelivery.vue';
import { DeliveryOptions, OrderTypes, Product } from '@/enums';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { deliveryOptions } from '@/mocks/fixtures/deliveryOptions';
import { RoadForwardingQuoteInformation, Segment } from '@dfe/dfe-book-api';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { mockServer } from '@/mocks/server';
import CheckboxField from '@/components/form/CheckboxField.vue';
import SwitchField from '@/components/form/SwitchField.vue';
import { nextTick, ref } from 'vue';
import type { Server } from 'miragejs';
import DatePicker from '@/components/form/DatePicker.vue';
import FormContactPerson from '@/components/form/FormContactPerson.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { roadDeliveryProducts } from '@/mocks/fixtures/deliveryProducts';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { addresses } from '@/mocks/fixtures/addresses';
import { resolveDachserPlz } from '@/composables/data/useRoadDeliveryProductsList';
import { customers } from '@/mocks/fixtures/customers';
import { createClientMock } from '@test/util/mock-client';
import { VInput } from 'vuetify/components';

describe('Collection & Delivery - FormDelivery component', () => {
  let wrapper: VueWrapper;
  let server: Server;
  const client = createClientMock();

  beforeAll(() => {
    server = mockServer({
      environment: 'test',
      fixtures: {
        deliveryOptions,
        deliveryProducts: roadDeliveryProducts,
        customers,
      },
    });
  });

  beforeEach(() => {
    wrapper = mount(FormDelivery);
  });

  it('shows delivery option, product field and tail-lift checkbox', async () => {
    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'deliveryOptionField' }).exists()).toBe(true);
    });
    expect(wrapper.findComponent({ ref: 'deliveryProductField' }).exists()).toBe(true);
    expect(wrapper.findComponent(CheckboxField).exists()).toBe(true);
  });

  it.each([
    ['Shows', true],
    ["Doesn't show", false],
  ])(
    '%s delivery self collection for forwarding order depending on customer settings',
    async (_, selfCollection) => {
      server.shutdown();
      server = mockServer({
        environment: 'test',
        fixtures: {
          customerSettings: {
            selfCollection,
          },
          deliveryProducts: roadDeliveryProducts,
        },
      });
      const formStore = useCreateOrderFormStore();
      formStore.customerNumber = '00000001';
      formStore.transportType = Segment.ROAD;
      formStore.orderType = OrderTypes.RoadForwardingOrder;

      await nextTick();

      await vi.waitFor(() => {
        expect(wrapper.findComponent(SwitchField).exists()).toBe(selfCollection);
      });
    },
  );

  it('toggles delivery product banner depending on whether addresses exist', async () => {
    const store = useCreateOrderAddressesStore();

    expect(wrapper.findComponent(DfeBanner).props('value')).toBe(true);

    store.shipperAddress.address.countryCode = addresses[0].countryCode;
    store.shipperAddress.address.postcode = addresses[0].postcode;
    store.consigneeAddress.address.countryCode = addresses[0].countryCode;
    store.consigneeAddress.address.postcode = addresses[0].postcode;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(DfeBanner).props('value')).toBe(false);
  });

  it('shows delivery date picker field for products with fixed delivery date for forwarding order', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const addressStore = useCreateOrderAddressesStore();
    addressStore.shipperAddress.address = addresses[0];
    addressStore.consigneeAddress.address = addresses[0];
    store.deliveryProduct = 'Code_2';

    const storeForm = useCreateOrderFormStore();
    storeForm.orderType = OrderTypes.RoadForwardingOrder;

    await vi.waitFor(
      () => {
        expect(wrapper.findComponent(DatePicker).exists()).toBe(true);
      },
      {
        timeout: 2500,
        interval: 50,
      },
    );
  });

  it('shows delivery date picker field for products with fixed delivery date for collection order', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const addressStore = useCreateOrderAddressesStore();
    addressStore.shipperAddress.address = addresses[0];
    addressStore.consigneeAddress.address = addresses[0];
    store.deliveryProduct = 'Code_2';

    const storeForm = useCreateOrderFormStore();
    storeForm.orderType = OrderTypes.RoadCollectionOrder;

    await vi.waitFor(() => {
      const dateDeliveryPicker = wrapper.find('[data-testid="date-delivery-picker"]');
      expect(dateDeliveryPicker.exists()).toBe(true);
    });
  });

  it('should reset fixDate when product has no fix Date', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const addressStore = useCreateOrderAddressesStore();
    addressStore.shipperAddress.address = addresses[0];
    addressStore.consigneeAddress.address = addresses[0];
    store.deliveryProduct = 'Code_3';
    store.dateDelivery = new Date();

    const storeForm = useCreateOrderFormStore();
    storeForm.orderType = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent(DatePicker).exists()).toBe(false);
      expect(store.dateDelivery).toBe('');
    });
  });

  it('shows contact data component for delivery options and dont show when none is selected', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    storeData.deliveryOptions = deliveryOptions;
    store.deliveryOption = DeliveryOptions.None;
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(FormContactPerson).exists()).toBe(false);
    store.deliveryOption = 'Code_1';

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(FormContactPerson).exists()).toBe(true);

    // Check if the component is hidden when the delivery option is changed to None
    store.deliveryOption = DeliveryOptions.None;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(FormContactPerson).exists()).toBe(false);
  });

  it('disables all price related inputs if there is an Order from Quote (road with daily price)', async () => {
    const deliveryProducts = wrapper.findComponent({
      ref: 'deliveryProductField',
    });
    const deliveryOption = wrapper.findComponent({
      ref: 'deliveryOptionField',
    });
    expect(deliveryProducts.props('disabled')).toBe(false);
    expect(deliveryOption.props('disabled')).toBe(false);

    const store = useCreateOrderFormStore();
    const referencesStore = useCreateOrderOrderReferencesFormStore();
    store.quoteInformation = {
      orderType: OrderTypes.RoadForwardingOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as RoadForwardingQuoteInformation;
    store.orderType = OrderTypes.RoadForwardingOrder;
    referencesStore.dailyPriceReference = '060';
    await wrapper.vm.$nextTick();

    expect(deliveryProducts.props('disabled')).toBe(true);
    expect(deliveryOption.props('disabled')).toBe(true);
  });

  it('deliveryOption is update if deliveryProduct change and the deliveryOption was not in the new list of deliveryOption', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    storeData.deliveryOptions = deliveryOptions;
    store.deliveryOption = DeliveryOptions.None;
    await wrapper.vm.$nextTick();

    store.deliveryProduct = Product.TargoOnSiteFix;

    await wrapper.vm.$nextTick();

    expect(store.deliveryOption).toBe(DeliveryOptions.AutomaticDeliveryBooking);
  });

  it('Exception: The user has firstly chosen a “phone delivery notification” and switch to a targo on-site product. In this case, the “phone delivery booking” is kept.', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    storeData.deliveryOptions = deliveryOptions;
    store.deliveryOption = DeliveryOptions.PhoneDeliveryNotification;
    await wrapper.vm.$nextTick();

    store.deliveryProduct = Product.TargoOnSitePrenium;

    await wrapper.vm.$nextTick();

    expect(store.deliveryOption).toBe(DeliveryOptions.PhoneDeliveryBooking);
  });

  it('show cash on delivery amount if cash on delivery is check', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    const storeOrder = useCreateOrderFormStore();

    storeData.customers = customers;
    storeOrder.customerNumber = '00000001';

    store.cashOnDelivery = true;
    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      const cashOnDeliveryAmount = wrapper.find('[data-test="book-cash-on-delivery-amount-field"]');
      expect(cashOnDeliveryAmount.exists()).toBe(true);
    });
  });

  it('show cash on delivery if we are on Road FW and customer have the option', async () => {
    const storeData = useCreateOrderDataStore();
    const storeOrder = useCreateOrderFormStore();

    storeData.customers = customers;
    storeOrder.customerNumber = '00000001';

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      const cashOnDelivery = wrapper.find('[data-test="book-cash-on-delivery-field"]');
      expect(cashOnDelivery.exists()).toBe(true);
    });
  });

  it('show cash on delivery amount if cash on delivery is check', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    const storeOrder = useCreateOrderFormStore();

    storeData.customers = customers;
    storeOrder.customerNumber = '00000001';

    store.cashOnDelivery = true;

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      const cashOnDelivery = wrapper.find('[data-test="book-cash-on-delivery-field"]');
      expect(cashOnDelivery.exists()).toBe(true);
      const cashOnDeliveryAmount = wrapper.find('[data-test="book-cash-on-delivery-amount-field"]');
      expect(cashOnDeliveryAmount.exists()).toBe(true);
    });
  });

  it('disabled cash on delivery if from quote', async () => {
    const storeData = useCreateOrderDataStore();
    const storeOrder = useCreateOrderFormStore();

    storeData.customers = customers;

    storeOrder.quoteInformation = {
      orderType: OrderTypes.RoadForwardingOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '00000001',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as RoadForwardingQuoteInformation;

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(storeOrder.isOrderFromQuote).toBe(true);
      const cashOnDelivery = wrapper.find('[data-test="book-cash-on-delivery-field"]');
      expect(cashOnDelivery.exists()).toBe(true);
      expect(cashOnDelivery.findComponent(VInput).props('disabled')).toBe(true);
      const cashOnDeliveryAmount = wrapper.find('[data-test="book-cash-on-delivery-amount-field"]');
      expect(cashOnDeliveryAmount.exists()).toBe(true);
      expect(cashOnDeliveryAmount.findComponent(VInput).props('disabled')).toBe(true);
    });
  });

  it('display toast if cash on delivery is checked and we change to a customer without COD', async () => {
    const toast = vi.spyOn(client.toast, 'info');
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    const storeOrder = useCreateOrderFormStore();

    storeData.customers = customers;
    storeOrder.customerNumber = '00000001';
    store.cashOnDelivery = true;

    await wrapper.vm.$nextTick();

    storeOrder.customerNumber = '00000002';

    await vi.waitFor(() => {
      expect(toast).toHaveBeenCalledWith('messages.id7217.text');
    });
  });

  it('deliveryOption is update if deliveryProduct change and the deliveryOption was not in the new list of deliveryOption', async () => {
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    const storeData = useCreateOrderDataStore();
    storeData.deliveryOptions = deliveryOptions;
    store.deliveryOption = DeliveryOptions.None;
    await wrapper.vm.$nextTick();

    store.deliveryProduct = Product.TargoOnSiteFix;

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      const banner = wrapper.find('[data-test="book-banner-warning"]');
      expect(banner.exists()).toBe(false);
    });
  });
});

describe('resolveDachserPlz', () => {
  it('setzt dachserPlz wenn passende Stadt und County gefunden werden', () => {
    const selectableList = [
      {
        label: 'Dublin/Dublin',
        data: {
          town: 'Dublin',
          county: 'Dublin',
          dachserPlz: 'IE999',
        },
      },
    ];

    const townCounty = ref({
      label: 'Dublin/Dublin',
      data: {
        town: 'Dublin',
        county: 'Dublin',
        dachserPlz: '',
      },
    });

    resolveDachserPlz(selectableList, townCounty);

    expect(townCounty.value?.data?.dachserPlz).toBe('IE999');
  });

  it('ändert nichts wenn kein Match gefunden wird', () => {
    const selectableList = [
      {
        label: 'Berlin/Berlin',
        data: {
          town: 'Berlin',
          county: 'Berlin',
          dachserPlz: 'DE123',
        },
      },
    ];

    const townCounty = ref({
      label: 'Dublin/Dublin',
      data: {
        town: 'Dublin',
        county: 'Dublin',
        dachserPlz: '',
      },
    });

    resolveDachserPlz(selectableList, townCounty);

    expect(townCounty.value?.data?.dachserPlz).toBe('');
  });
});
