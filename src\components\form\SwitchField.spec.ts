import SwitchField from '@/components/form/SwitchField.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';

const label = 'Label';
const props = {
  value: true,
};

describe('SwitchField component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(SwitchField, {
      props,
    });
  });

  it('emits an change event on change', async () => {
    const switchField = wrapper.findComponent({ name: 'v-switch' });

    switchField.vm.$emit('update:modelValue', false);

    const event = wrapper.emitted('update:modelValue');

    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([false]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').text()).toEqual('');

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.text()).toEqual(label);
  });
});
