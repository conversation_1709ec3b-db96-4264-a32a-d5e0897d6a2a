import { createI18n } from 'vue-i18n';
import { isEmpty } from 'lodash';
import { useInit } from '@/composables/useInit';
import { Ref, watch } from 'vue';

type MessageSchema = object;

const fallbackLocale = 'en';
const loadedLocales: string[] = [];

export const i18n = createI18n<[MessageSchema], string, false>({
  fallbackLocale,
  locale: fallbackLocale,
  silentTranslationWarn: true,
  missingWarn: process.env.NODE_ENV !== 'test',
});

const setLocale = async (locale: string): Promise<void> => {
  if (loadedLocales.includes(locale)) {
    i18n.global.locale.value = locale;
    updateHtmlLang(locale);
    return;
  }

  try {
    const { api } = useInit();
    const translations = await api.dynamicLabel.v1
      .getTranslations({
        language: locale,
        appIdList: [158],
        includeGeneralData: true,
      })
      .then(({ data }) => data);

    if (isEmpty(translations.labels) && isEmpty(translations.messages)) {
      throw new Error(`No translations for locale ${locale}`);
    }

    i18n.global.setLocaleMessage(locale, translations);
    i18n.global.locale.value = locale;
    loadedLocales.push(locale);
    updateHtmlLang(locale);
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    // Ignore new locale
  }
};

export const useLocale = async (locale: Ref<string>) => {
  await setLocale(locale.value);
  watch(locale, setLocale);
};

function updateHtmlLang(locale: string) {
  const htmlElement = document.querySelector('html');
  htmlElement?.setAttribute('lang', locale);
}
