import type { DFEClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';
import type { Api as BookApi } from '@dfe/dfe-book-api';
import type { Api as AddressApi } from '@dfe/dfe-address-api';
import type { Api as ConfigApi } from '@dfe/dfe-configserver-api-module';
import type { Api as DynamicLabelApi } from '@dfe/dfe-dynamiclabel-api';
import type { ApiSecurityDataFn } from '@/services/api';

export type API = {
  book: BookApi<ApiSecurityDataFn>;
  address: AddressApi<ApiSecurityDataFn>;
  config: ConfigApi<ApiSecurityDataFn>;
  dynamicLabel: DynamicLabelApi<ApiSecurityDataFn>;
};

let _client: DFEClient<Events>;
let _api: API;

export const setInit = (client: DFEClient<Events>, api: API) => {
  _client = client;
  _api = api;
};

export const useInit = () => {
  return {
    client: _client,
    api: _api,
  };
};
