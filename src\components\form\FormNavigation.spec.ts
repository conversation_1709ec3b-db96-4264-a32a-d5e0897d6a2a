import FormNavigation from '@/components/form/FormNavigation.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { ref } from 'vue';

const scrollIntoViewMock = vi.fn();
window.HTMLElement.prototype.scrollIntoView = scrollIntoViewMock;

const mockAddressesEl = document.createElement('div');
const mockFreightEl = document.createElement('div');
const mockDateEl = document.createElement('div');

const props = {
  items: [
    { text: 'Addresses', ref: ref(mockAddressesEl) },
    { text: 'Freight', ref: ref(mockFreightEl), error: true },
    { text: 'Date', ref: ref(mockDateEl) },
  ],
};

describe('FormNavigation', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(FormNavigation, {
      props,
    });
  });

  it('displays navigation items', () => {
    const buttons = wrapper.findAllComponents({ name: 'v-btn' });

    expect(buttons).toHaveLength(props.items.length);

    for (let i = 0; i < props.items.length; i++) {
      expect(buttons.at(i)?.text()).toEqual(props.items[i].text);
    }
  });

  it('calls scrollIntoView on button click', async () => {
    const buttons = wrapper.findAllComponents({ name: 'v-btn' });

    await buttons.at(0)?.trigger('click');
    await buttons.at(1)?.trigger('click');
    await buttons.at(2)?.trigger('click');

    expect(scrollIntoViewMock).toHaveBeenCalledTimes(3);
  });

  it('adds active class to first item by default', () => {
    expect(wrapper.findComponent({ name: 'v-btn' }).classes()).toContain('is-active');
  });

  it('displays error state', () => {
    expect(wrapper.findAll('.material-symbol')).toHaveLength(1);
    expect(wrapper.findAll('.has-error')).toHaveLength(1);
  });
});
