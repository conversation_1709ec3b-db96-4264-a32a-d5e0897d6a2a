import { ContactData } from '@dfe/dfe-address-api';
import { Address, BasicAddress, OrderAddress } from '@dfe/dfe-book-api';

export const contactData = (): ContactData => ({
  id: 0,
  name: '',
  email: '',
  telephone: '',
  mobile: '',
});

export const orderAddress = (): OrderAddress => ({
  id: undefined,
  name: '',
  name2: '',
  name3: '',
  countryCode: undefined,
  street: '',
  street2: '',
  postcode: '',
  city: '',
  supplement: '',
  gln: undefined,
  taxID: undefined,
  contact: undefined,
  originAddressId: undefined,
  lockedByQuote: undefined,
  dropOfLocation: undefined,
  neutralizeAddress: undefined,
});

export const basicAddress = (): BasicAddress => ({
  id: undefined,
  name: '',
  countryCode: '',
  street: '',
  postcode: '',
  city: '',
});

export const loadingPointAddress = (): Address => ({
  id: undefined,
  name: '',
  name2: '',
  name3: '',
  countryCode: '',
  street: '',
  postcode: '',
  city: '',
});
