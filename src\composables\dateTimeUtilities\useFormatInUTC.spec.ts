import {
  useFormatInUTC,
  useTruncatedDateString,
} from '@/composables/dateTimeUtilities/useFormatInUTC';

describe('useFormatInUTC', () => {
  it('should return a UTC date', () => {
    const expectedDate = new Date('2021-01-01T00:00:00.000Z');

    const result = useFormatInUTC('2021-01-01', 'en');
    expect(result).toEqual(expectedDate);
  });

  it('should convert a string of GMT +2 to an ISO date string', () => {
    const result = useTruncatedDateString('2023-02-14T00:00:00.000+0200');
    expect(result).toEqual('2023-02-14');
  });

  it('should convert a string of UTC to an ISO date string', () => {
    const result = useTruncatedDateString('2023-02-14T00:00:00.000Z');
    expect(result).toEqual('2023-02-14');
  });

  it('should convert a string of GMT -2 to an ISO date string', () => {
    const result = useTruncatedDateString('2023-02-14T00:00:00.000-0200');
    expect(result).toEqual('2023-02-14');
  });

  it('should convert a date of GMT +2 to an ISO date string', () => {
    const date = new Date('2023-02-14');

    const result = useTruncatedDateString(date);
    expect(result).toEqual('2023-02-14');
  });
});
