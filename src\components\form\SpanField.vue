<template>
  <div>
    <FormLabel v-if="label" :for="spanFieldId" :required="required">{{ label }}</FormLabel>

    <div v-if="$slots['default']" :id="spanFieldId" class="custom-span">
      <slot />
    </div>

    <span v-else :id="spanFieldId" class="custom-span">{{ value }}</span>
  </div>
</template>

<script setup lang="ts">
import FormLabel from '@/components/form/FormLabel.vue';
import { createUuid } from '@/utils/createUuid';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  id?: string;
  label?: TranslateResult;
  value?: string;
  required?: boolean;
}

const props = defineProps<Props>();

const spanFieldId = props.id ?? `span-field-${createUuid()}`;
</script>

<style lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

.custom-span {
  display: flex;
  align-items: center;
  height: vars.$form-input-height;
}
</style>
