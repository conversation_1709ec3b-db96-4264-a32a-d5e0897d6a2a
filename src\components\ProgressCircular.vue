<template>
  <VProgressCircular
    :class="[`width-${width}`, { indeterminate }]"
    :color="color"
    :indeterminate="indeterminate"
    :size="size"
    :width="width"
    :model-value="value"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  color: string;
  size: string | number;
  indeterminate?: boolean;
  value?: string | number;
}

const props = defineProps<Props>();

const width = computed(() => {
  if (props.size.toString() === '24') {
    return 2;
  }
  return 4;
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;

.indeterminate :deep(.v-progress-circular__info) {
  width: inherit;
  height: inherit;
  border-style: solid;
  border-color: vars.$progress-circular-underlay-stroke;
  border-radius: 50%;
}

.indeterminate.width-2 :deep(.v-progress-circular__info) {
  border-width: 2px;
}

.indeterminate.width-4 :deep(.v-progress-circular__info) {
  border-width: 4px;
}
</style>
