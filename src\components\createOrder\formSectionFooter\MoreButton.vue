<template>
  <div>
    <VMenu min-width="160" class="rounded-lg menu--reset" location="top left" attach>
      <template #activator="{ props }">
        <VBtn v-bind="props" icon max-width="24" variant="text" class="more-btn">
          <MaterialSymbol>
            <more-vert />
          </MaterialSymbol>
        </VBtn>
      </template>
      <VList class="pa-0">
        <VListItem
          v-if="isRoadForwardingOrder"
          v-data-test="'print-labels'"
          class="px-3"
          link
          @click="printLabels()"
        >
          <VListItemTitle>
            {{ t('labels.print_labels.text') }}
          </VListItemTitle>
        </VListItem>

        <TooltipWithSlot
          :show-tooltip="checkIfSaveAsDraftIsDisabled"
          :tooltip-props="{
            location: 'top',
            maxWidth: 240,
            contentClass: 'save-as-draft-tooltip',
          }"
        >
          <template #content>
            <VListItem
              v-if="customerNumber"
              v-data-test="'save-as-draft'"
              class="px-3"
              :disabled="checkIfSaveAsDraftIsDisabled"
              link
              @click="saveDraft"
            >
              <VListItemTitle>
                {{ t('labels.save_as_draft.text') }}
              </VListItemTitle>
            </VListItem>
          </template>
          <template #tooltipContent>
            <span v-if="isRoadOrder" class="text-body-2">{{
              t('labels.disabled_save_draft_road.text')
            }}</span>
            <span v-else class="text-body-2">{{
              t('labels.disabled_save_draft_air_sea.text')
            }}</span>
          </template>
        </TooltipWithSlot>
      </VList>
    </VMenu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import MoreVert from '@dfe/dfe-frontend-styles/assets/icons/more_vert-16px.svg';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderStatus } from '@dfe/dfe-book-api';
import { useI18n } from 'vue-i18n';
import { CreateOrderValidationEmits } from '@/composables/createOrder/useCreateOrderValidation';
import TooltipWithSlot from '@/components/base/tooltips/TooltipWithSlot.vue';

const { t } = useI18n();

interface Emits extends CreateOrderValidationEmits {
  (event: 'save-as-draft'): void;
  (event: 'save-print-labels'): void;
}
const emit = defineEmits<Emits>();

const createOrderFormStore = useCreateOrderFormStore();
const { customerNumber, isRoadForwardingOrder, isRoadOrder, currentOrderStatus } =
  storeToRefs(createOrderFormStore);

const checkIfSaveAsDraftIsDisabled = computed(
  () =>
    currentOrderStatus.value === OrderStatus.COMPLETE ||
    currentOrderStatus.value === OrderStatus.LABEL_PENDING,
);

const saveDraft = () => {
  emit('save-as-draft');
};

const printLabels = () => {
  emit('save-print-labels');
};
</script>

<style lang="scss" scoped>
.more-btn {
  color: var(--color-base-grey-700);
  &:hover {
    color: var(--color-base-grey-900);
    background: transparent;

    :deep(.v-btn__overlay) {
      background: transparent;
    }
  }
}

.menu--reset .v-menu__content {
  contain: unset;
  overflow: visible;
}
.save-as-draft-tooltip {
  transform: translate(0, -100%);
  top: -5px !important;
}

.theme--light.v-btn.v-btn--icon {
  color: var(--color-base-grey-700) !important;
  caret-color: var(--color-base-grey-700) !important;
  &:hover {
    color: var(--color-base-grey-900) !important;
    caret-color: var(--color-base-grey-900) !important;
  }
}
</style>
