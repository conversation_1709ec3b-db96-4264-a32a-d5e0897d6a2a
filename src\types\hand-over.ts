import type { PortItem } from '@/composables/createOrder/usePortSearch';
import type { OrderAddress } from '@dfe/dfe-book-api';

export const HandOverSelection = {
  default: 'defaultSelection',
  alternateAddress: 'alternateAddressSelection',
  port: 'portSelection',
} as const;

export type HandOverSelectionType = (typeof HandOverSelection)[keyof typeof HandOverSelection];

type HandOverValue<T extends HandOverSelectionType> = {
  selection: T;
};

export type HandOverDefaultSelectionValue = HandOverValue<(typeof HandOverSelection)['default']>;
export type HandOverAlternateAddressSelectionValue = HandOverValue<
  (typeof HandOverSelection)['alternateAddress']
> & {
  address: OrderAddress;
};
export type HandOverAirportSelectionValue = HandOverValue<(typeof HandOverSelection)['port']> & {
  port?: PortItem;
};
export type HandOverSelectionValue =
  | HandOverDefaultSelectionValue
  | HandOverAlternateAddressSelectionValue
  | HandOverAirportSelectionValue;
