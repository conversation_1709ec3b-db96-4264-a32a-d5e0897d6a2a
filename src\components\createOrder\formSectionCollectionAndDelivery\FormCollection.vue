<template>
  <div>
    <div>
      <div v-if="isRoadCollectionOrder">
        <SelectField
          v-model="collectionInterpreterOption"
          v-data-test="'collection-interpreter-option'"
          :label="t('labels.date_label.text')"
          :items="selectableCollectionInterpreterOptions"
          item-value="code"
          item-text="description"
          :required="true"
          class="mb-4"
        />
      </div>
      <div>
        <DatePicker
          v-model="collectionDate"
          v-data-test="'collection-date-picker'"
          :label="!isRoadCollectionOrder ? t('labels.date_label.text') : undefined"
          :required="true"
          :min="minAllowedDateCollection"
          :max="maxAllowedDateCollection"
          :disabled="isRoadOrderFromQuoteWithDailyPrice"
          class="mb-4"
        />
      </div>
      <!-- Currently no visible the requirements need to be redefined @see https://dil-itd.atlassian.net/browse/DFE-4399 -->
      <div v-if="isRoadOrder && false">
        <SelectField
          v-if="collectionTimeSlots && collectionTimeSlots?.length"
          ref="collectionTimeSlotField"
          v-model="collectionTimeSlot"
          class="mb-4"
          :items="timeSlotOptions"
          :label="t('labels.time_slot_label.text')"
          :placeholder="t('labels.select_option.text')"
          :item-value="timeSlotItemValue"
        />
      </div>
    </div>
    <div v-if="showCustomTimeFields" class="collection-time ga-4 d-flex">
      <div>
        <TimePicker
          v-model="from"
          :items="customTimeOptions"
          :label="t('labels.from_label.text')"
          :placeholder="t('labels.select_option.text')"
          :default-value="customTimeDefaults.from"
          :required="true"
          :message="t('labels.validation_select_input_required.text')"
        />
      </div>
      <div>
        <TimePicker
          v-model="to"
          :items="customTimeOptions"
          :label="t('labels.to_label.text')"
          :placeholder="t('labels.select_option.text')"
          :default-value="customTimeDefaults.to"
          :required="true"
          :message="t('labels.validation_select_input_required.text')"
        />
      </div>
    </div>
    <div v-if="isRoadCollectionOrder" class="align-center mt-4">
      <div class="d-inline-flex">
        <CheckboxField
          v-model="tailLiftCollection"
          class="pt-1"
          :label="t('labels.taillift_collection.text')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CheckboxField from '@/components/form/CheckboxField.vue';
import DatePicker from '@/components/form/DatePicker.vue';
import SelectField from '@/components/form/SelectField.vue';
import TimePicker from '@/components/form/TimePicker.vue';
import { useCollectionInterpreterOptions } from '@/composables/data/useCollectionInterpreterOptions';
import useAllowedDate from '@/composables/dateTimeUtilities/useAllowedDate';
import useCollectionTimeCustomDefaults from '@/composables/dateTimeUtilities/useCollectionTimeCustomDefaults';
import useCollectionTimeSlotOptions from '@/composables/dateTimeUtilities/useCollectionTimeSlotOptions';
import { useCollectionTimeSlots } from '@/composables/dateTimeUtilities/useCollectionTimeSlots';
import useTimeOptions from '@/composables/dateTimeUtilities/useTimeOptions';
import { CollectionTimeSlots } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import type { CollectionTimeSlot } from '@dfe/dfe-book-api';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import { toRefs } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import type { Ref } from 'vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { timeFormat } = usePreferences();

const { selectableCollectionInterpreterOptions } = useCollectionInterpreterOptions();

const createOrderFormCollectionAndDeliveryStore = useCreateOrderFormCollectionAndDeliveryStore();
const {
  collectionTimeSlot,
  customCollectionTimeSlot,
  tailLiftCollection,
  collectionInterpreterOption,
} = storeToRefs(createOrderFormCollectionAndDeliveryStore);

const { to, from, collectionDate } = toRefs(customCollectionTimeSlot);

const { data: collectionTimeSlots, isLoading: timeslotsLoading } = useCollectionTimeSlots(
  collectionDate as Ref<string | Date>,
);

const timeSlotOptions = useCollectionTimeSlotOptions(collectionTimeSlots, timeFormat.value);

const { minAllowedDateCollection, maxAllowedDateCollection } = useAllowedDate();

const createOrderFormStore = useCreateOrderFormStore();
const { isRoadOrder, isRoadCollectionOrder, isRoadOrderFromQuoteWithDailyPrice } =
  storeToRefs(createOrderFormStore);

const timeSlotItemValue = ({ collectionDate, from, to }: CollectionTimeSlot) => {
  return { collectionDate, from, to };
};

const showCustomTimeFields = computed(() => {
  // if custom time slot is selected or there are no time slots and date is selected and it's not loading
  return (
    (collectionTimeSlot.value?.from === CollectionTimeSlots.Custom &&
      collectionTimeSlot.value?.to === CollectionTimeSlots.Custom) ||
    ((customCollectionTimeSlot.value.collectionDate || collectionTimeSlot.value?.collectionDate) &&
      !timeslotsLoading.value)
  );
});

const customTimeOptions = computed(() => useTimeOptions(timeFormat.value));

const customTimeDefaults = computed(() => useCollectionTimeCustomDefaults(timeFormat.value));
</script>
<style lang="scss" scoped>
@use '@/styles/variables' as vars;
:deep(.v-input) {
  width: vars.$form-cols-width-xl;
}
.collection-time {
  .v-col-xl-6,
  :deep(.v-input) {
    max-width: 190px;
  }
}
</style>
