import type { Ref } from 'vue';
import type { MeasurementProposalsMenu, OrderLineLengthValueObject } from '@/types/createOrder';
import type { MeasurementProposals } from '@dfe/dfe-book-api';
import type { TranslateResult } from 'vue-i18n';

export default function (
  favoritesHeader: TranslateResult | string,
  standardHeader: TranslateResult | string,
  measurementProposals: Ref<MeasurementProposals>,
) {
  const createOptions = (
    x: { length: number; width: number },
    isStandard = false,
  ): OrderLineLengthValueObject => {
    return {
      ...x,
      menuText: `${x.length} cm x <span class="text--light">${x.width} cm</span>`,
      value: isStandard ? `${x.length}-${x.width}--STANDARD` : `${x.length}-${x.width}`,
    };
  };

  const measurementProposalsStandard = measurementProposals.value.standard;
  const measurementProposalsFrequentlyUsed = measurementProposals.value.frequentlyUsed;
  const standardOption = measurementProposalsStandard
    ? [measurementProposalsStandard].map((x) => createOptions(x, true))
    : [];
  const freqUsedOptions = measurementProposalsFrequentlyUsed
    ? measurementProposalsFrequentlyUsed.map((x) => createOptions(x))
    : [];

  const menu: MeasurementProposalsMenu = [];
  if (freqUsedOptions.length) {
    menu.push({ header: favoritesHeader }, ...freqUsedOptions);
  }

  if (freqUsedOptions.length && standardOption.length) {
    menu.push({ divider: true });
  }

  if (standardOption.length) {
    menu.push({ header: standardHeader }, ...standardOption);
  }

  return menu;
}
