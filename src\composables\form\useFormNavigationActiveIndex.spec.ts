import { ref } from 'vue';
import useFormNavigationActiveIndex from '@/composables/form/useFormNavigationActiveIndex';

const mockItems = [
  ref(document.createElement('div')),
  ref(document.createElement('div')),
  ref(document.createElement('div')),
];

const mockItemsSpies = mockItems.map(() => vi.fn());

const mockElementBounding = {
  bottom: 100,
  height: 100,
  left: 0,
  right: 300,
  top: 0,
  width: 300,
  x: 0,
  y: 0,
  toJSON: () => '',
};

mockItems.forEach((item, i) => {
  item.value.getBoundingClientRect = mockItemsSpies[i];
});

const setMockBottomBoundings = (...values: number[]) => {
  mockItemsSpies.forEach((spy, i) => {
    spy.mockImplementationOnce(() => ({
      ...mockElementBounding,
      bottom: values[i],
    }));
  });
};

describe('useFormNavigationActiveIndex composable', () => {
  beforeAll(() => {
    setMockBottomBoundings(50, 150, 250);
  });

  it('returns 0 when no items are passed', () => {
    expect(useFormNavigationActiveIndex()).toEqual(0);
  });

  it('returns 0 when option "top" is true', () => {
    expect(useFormNavigationActiveIndex(mockItems, { top: true, bottom: false })).toEqual(0);
  });

  it('returns 2 when option "bottom" is true', () => {
    expect(useFormNavigationActiveIndex(mockItems, { top: false, bottom: true })).toEqual(2);
  });

  it('returns indices depending on scrolled element position', () => {
    expect(useFormNavigationActiveIndex(mockItems, { top: false, bottom: false })).toEqual(0);

    setMockBottomBoundings(-50, 50, 150);
    expect(useFormNavigationActiveIndex(mockItems, { top: false, bottom: false })).toEqual(1);

    setMockBottomBoundings(-150, -50, 50);
    expect(useFormNavigationActiveIndex(mockItems, { top: false, bottom: false })).toEqual(2);
  });
});
