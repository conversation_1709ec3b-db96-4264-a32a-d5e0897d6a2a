import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { ContactData, Presets } from '@dfe/dfe-address-api';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { TextTypes } from '@/enums';

export function useApplyOrderAddressPresets(
  presets: Presets,
  defaultContact: ContactData | undefined,
): boolean {
  const { isRoadOrder } = storeToRefs(useCreateOrderFormStore());
  const { deliveryOption, tailLiftDelivery, contactDataDelivery } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );
  let atLeastOnePresetApplied = false;

  if (!isRoadOrder.value) return false;

  if (presets.tailLiftDelivery) {
    tailLiftDelivery.value = presets.tailLiftDelivery;
    atLeastOnePresetApplied = true;
  }

  if (presets.deliveryOption) {
    deliveryOption.value = presets.deliveryOption;
    if (defaultContact !== undefined) {
      contactDataDelivery.value = defaultContact;
    }
    atLeastOnePresetApplied = true;
  }

  if (presets.deliveryInstructions) {
    atLeastOnePresetApplied = applyTextPreset(
      presets.deliveryInstructions,
      TextTypes.DeliveryInstructions,
    );
  }

  if (presets.goodsDescription) {
    atLeastOnePresetApplied = applyTextPreset(presets.goodsDescription, TextTypes.GoodsDescription);
  }

  if (presets.invoiceText) {
    atLeastOnePresetApplied = applyTextPreset(presets.invoiceText, TextTypes.InvoiceText);
  }

  return atLeastOnePresetApplied;
}

function applyTextPreset(value: string, textType: TextTypes): boolean {
  const { texts: orderTexts } = storeToRefs(useCreateOrderTextsStore());
  const textItem = orderTexts.value.find((text) => text.textType === textType);

  if (textItem) {
    textItem.active = true;
    textItem.value = value;
    return true;
  }

  return false;
}
