import { createUuid } from '@/utils/createUuid';
import { Api as BookApi } from '@dfe/dfe-book-api';
import { Api as AddressApi } from '@dfe/dfe-address-api';
import { Api as ConfigApi } from '@dfe/dfe-configserver-api-module';
import { Api as DynamicLabelApi } from '@dfe/dfe-dynamiclabel-api';

export type ApiSecurityDataFn = () => Promise<string | undefined>;

interface ApiConfig {
  baseUrl?: string;
  getToken: ApiSecurityDataFn;
  getLanguage: () => string;
  getRequestOrigin?: () => string;
}

const Api = {
  Book: BookApi,
  Address: AddressApi,
  Config: ConfigApi,
  DynamicLabel: DynamicLabelApi,
};

export const requestOriginHeader = 'dfe-book-frontend';

export function createApi(
  Api: 'Book',
  { baseUrl, getToken, getLanguage, getRequestOrigin }: ApiConfig,
): BookApi<ApiSecurityDataFn>;

export function createApi(
  Api: 'Address',
  { baseUrl, getToken, getLanguage, getRequestOrigin }: ApiConfig,
): AddressApi<ApiSecurityDataFn>;

export function createApi(
  Api: 'Config',
  { baseUrl, getToken, getLanguage, getRequestOrigin }: ApiConfig,
): ConfigApi<ApiSecurityDataFn>;

export function createApi(
  Api: 'DynamicLabel',
  { baseUrl, getToken, getLanguage }: ApiConfig,
): DynamicLabelApi<ApiSecurityDataFn>;

export function createApi(
  Key: keyof typeof Api,
  { baseUrl, getToken, getLanguage, getRequestOrigin }: ApiConfig,
) {
  const api = new Api[Key]({
    baseUrl,
    securityWorker: async (securityData: ApiSecurityDataFn | null) => {
      return securityData ? { headers: { Authorization: `Bearer ${await securityData()}` } } : {};
    },
  });

  const request = api.request;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  api.request = async (params: any) => {
    const headers = {
      'Accept-Language': getLanguage(),
      ...params.headers,
    };

    // DFE-3641 - Do not add request_origin header for DynamicLabel API
    if (Key !== 'DynamicLabel' && getRequestOrigin) {
      headers['request_origin'] = getRequestOrigin();
    }

    return request({
      ...params,
      headers,
    });
  };

  api.setSecurityData(getToken);

  return api;
}

export const createCancelToken = () => {
  return Symbol(createUuid());
};
