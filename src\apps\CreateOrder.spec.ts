import CreateOrder from '@/apps/CreateOrder.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { mockServer } from '@/mocks/server';
import { airExportOrder, roadForwardingOrder } from '@/mocks/fixtures/order';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';
import { customers } from '@/mocks/fixtures/customers';
import RoadMain from '@/views/createOrder/road/RoadMain.vue';
import SeaMain from '@/views/createOrder/sea/SeaMain.vue';
import AirMain from '@/views/createOrder/air/AirMain.vue';
import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import flushPromises from 'flush-promises';
import useValidateOrder from '@/composables/form/useValidateOrder';
import { createClientMock } from '@test/util/mock-client';
import { ClientKey } from '@/types/client';
import * as i18n from '@/plugins/i18n';
import { useCreateOrderDataStore } from '@/store/createOrder/data';

describe('CreateOrder app', () => {
  let wrapper: VueWrapper;
  const client = createClientMock();

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        roadForwardingOrder,
        airExportOrder,
        customers,
      },
    });
  });

  beforeEach(() => {
    vi.spyOn(i18n, 'useLocale').mockResolvedValueOnce();
    wrapper = shallowMount(CreateOrder, {
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
    wrapper.unmount();
  });

  it('should emit currentOrderStatus event', async () => {
    const formStore = useCreateOrderFormStore();
    const customerNumber = 1;
    const orderId = 123;

    const event = vi.fn();
    client.events.on('currentOrderStatus', event);

    client.events.emit('editOrder', { customerNumber, orderId });
    await wrapper.vm.$nextTick();

    expect(formStore.getOrder).toBeCalledWith(orderId);

    await vi.waitFor(() => {
      expect(event).toHaveBeenCalledTimes(1);
      expect(event).toHaveBeenCalledWith({
        status: 'labels.draft.text',
      });
    });
  });

  it('shows CreateOrder RoadMain view and hides AirMain and SeaMain for road orders', async () => {
    client.events.emit('createOrder', { transportType: Segment.ROAD });
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadMain).exists()).toBe(true);

    expect(wrapper.findComponent(SeaMain).exists()).toBe(false);

    expect(wrapper.findComponent(AirMain).exists()).toBe(false);
  });

  it('shows CreateOrder AirMain view for hides RoadMain and SeaMain air orders', async () => {
    client.events.emit('createOrder', { transportType: Segment.AIR });
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.AIR);
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadMain).exists()).toBe(false);

    expect(wrapper.findComponent(SeaMain).exists()).toBe(false);

    expect(wrapper.findComponent(AirMain).exists()).toBe(true);
  });

  it('shows CreateOrder SeaMain view for hides RoadMain and AirMain air orders', async () => {
    client.events.emit('createOrder', { transportType: Segment.SEA });
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.SEA);
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadMain).exists()).toBe(false);

    expect(wrapper.findComponent(AirMain).exists()).toBe(false);

    expect(wrapper.findComponent(SeaMain).exists()).toBe(true);
  });

  it('shows edit layer with correct data', async () => {
    const formStore = useCreateOrderFormStore();
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);
    const customerNumber = 1;
    const orderId = 123;

    client.events.emit('editOrder', { customerNumber, orderId });

    await wrapper.vm.$nextTick();

    expect(formStore.getOrder).toBeCalledWith(orderId);
  });

  it('should show loader when saveOrder loading is true', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { isSaveLoading } = storeToRefs(createOrderFormStore);

    isSaveLoading.value = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(LoaderOverlay).props()).toMatchObject({
      modelValue: true,
      showContentAtTop: false,
    });
  });

  it('should not show loader when saveOrder loading is false', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { isSaveLoading } = storeToRefs(createOrderFormStore);

    isSaveLoading.value = false;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(LoaderOverlay).props()).toMatchObject({
      modelValue: false,
      showContentAtTop: false,
    });
  });

  it('should show loader when submitOrder loading is true', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { isSubmitLoading } = storeToRefs(createOrderFormStore);

    isSubmitLoading.value = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(LoaderOverlay).props()).toMatchObject({
      modelValue: true,
      showContentAtTop: false,
    });
  });

  it('should not show loader when submitOrder loading is false', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { isSubmitLoading } = storeToRefs(createOrderFormStore);

    isSubmitLoading.value = false;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(LoaderOverlay).props()).toMatchObject({
      modelValue: false,
      showContentAtTop: false,
    });
  });

  it('should show loader when fetchLabels loading is true', async () => {
    const labelStore = useLabelStore();
    const { isFetchLabelsLoading } = storeToRefs(labelStore);

    isFetchLabelsLoading.value = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(LoaderOverlay).props()).toMatchObject({
      modelValue: true,
      showContentAtTop: false,
    });
  });

  it('should not show loader when fetchLabels loading is false', async () => {
    const labelStore = useLabelStore();
    const { isFetchLabelsLoading } = storeToRefs(labelStore);

    isFetchLabelsLoading.value = false;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(LoaderOverlay).props()).toMatchObject({
      modelValue: false,
      showContentAtTop: false,
    });
  });

  it('should reset orderType to RoadForwardingOrder, when transporType is ROAD', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);
    client.events.emit('createOrder', { transportType: Segment.ROAD });

    await wrapper.vm.$nextTick();

    expect(orderType.value).toBe(OrderTypes.RoadForwardingOrder);
  });

  it('should reset orderType to AirExportOrder, when transporType is AIR', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.AIR);
    client.events.emit('createOrder', { transportType: Segment.AIR });

    await wrapper.vm.$nextTick();

    expect(orderType.value).toBe(OrderTypes.AirExportOrder);
  });

  it('should reset orderType to SeaExportOrder, when transporType is SEA', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());

    client.events.emit('createOrder', { transportType: Segment.SEA });

    await wrapper.vm.$nextTick();

    expect(orderType.value).toBe(OrderTypes.SeaExportOrder);
  });

  it('should call scrollToDocumentsSection', async () => {
    const documentsSection = document.getElementById('documentsSection');
    const customerNumber = 1;
    const orderId = 2;
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.AIR);

    if (!documentsSection) {
      expect(documentsSection).toBeDefined();
      return;
    }

    const documentSectionScrollIntoViewSpy = vi.spyOn(documentsSection, 'scrollIntoView');

    client.events.emit('editOrder', { customerNumber, orderId, scrollToDocuments: true });

    expect(documentSectionScrollIntoViewSpy).toHaveBeenCalledTimes(1);
  });

  // FIXME
  it.skip('should set initialOrder on createOrder', async () => {
    vi.useFakeTimers();

    const { order } = useValidateOrder();
    const { initialOrder } = storeToRefs(useCreateOrderFormStore());

    client.events.emit('createOrder', { transportType: Segment.AIR });

    await flushPromises();

    vi.runAllTimers();

    expect(initialOrder.value).toMatchObject(order.value);
  });

  // FIXME: Fix this test in vue3
  it.skip('should set initialOrder on editOrder', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { order } = useValidateOrder();
    const { initialOrder } = storeToRefs(createOrderFormStore);
    const { orderData } = storeToRefs(createOrderFormStore);

    orderData.value = {
      ...order.value,
    };

    createOrderFormStore.getOrder = vi.fn();

    client.events.emit('editOrder', { customerNumber: 1, orderId: 123 });

    await flushPromises();

    expect(order.value).toStrictEqual(initialOrder.value);
  });
});
