import type { IncoTerms } from '@/mocks/fixtures/incoTerms';
import type { OrderContentResponse } from '@/types/createOrder';
import type { ContactData } from '@dfe/dfe-address-api';
import {
  Address,
  AirExportOrder,
  AirImportOrder,
  BooleanResult,
  CollectionTimeSlots,
  ContainerTypesWithLastUsed,
  Countries,
  CreationPreferences,
  Currencies,
  Customers,
  CustomerSettings,
  DangerousGoodDataItem,
  DeliveryProducts,
  DeliveryProductsWithFavorites,
  Document,
  DocumentResponse,
  DocumentTypes,
  Extensions,
  FavoriteCountries,
  FreightTerm,
  FurtherAddressTypes,
  GoodsGroupResponse,
  MeasurementProposals,
  Option,
  OrderAddress,
  OrderLabelResult,
  OrderProcessResult,
  PackagingOptionsWithFavorites,
  Ports,
  RoadCollectionOrder,
  RoadForwardingOrder,
  SeaExportOrder,
  SeaImportOrder,
  ValidationResult,
} from '@dfe/dfe-book-api';
import { IrelandPostalCode, type PostcodeValidation } from '@dfe/dfe-book-api';
import type { AirProductRoutingConfig, IncotermConfig } from '@dfe/dfe-configserver-api-module';
import type FakeXMLHttpRequest from 'fake-xml-http-request';
import type { Server } from 'miragejs';
import { createServer, Model, Serializer } from 'miragejs';
import { useRoutes as useAddressRoutes } from './routes/address';
import { useRoutes as useBookRoutes } from './routes/book';
import { useRoutes as useConfigRoutes } from './routes/config';
import { useRoutes as useDynamicLabelRoutes } from './routes/dynamicLabel';

export interface Models {
  creationPreferences?: CreationPreferences;
  customers?: Customers;
  addresses?: OrderAddress[];
  countriesRoad?: Countries;
  countriesAir?: Countries;
  countryFavorites?: FavoriteCountries;
  furtherAddressTypes?: FurtherAddressTypes[]; // Array of address types
  contactData?: ContactData;
  contactDataUpdated?: ContactData;
  contacts?: ContactData[];
  containerTypesWithLastUsed?: ContainerTypesWithLastUsed;
  packagingOptionsWithFavorites?: PackagingOptionsWithFavorites;
  measurementProposals?: MeasurementProposals;
  goodsGroupResponse?: GoodsGroupResponse;
  currencies?: Currencies;
  isThirdCountryConstellation?: BooleanResult;
  orderContent?: OrderContentResponse;
  deliveryProducts?: DeliveryProductsWithFavorites;
  deliveryOptions?: Option[];
  collectionOptions?: Option[];
  freightTerms?: FreightTerm[];
  collectionTimeSlots?: CollectionTimeSlots;
  collectionInterpreterOptions?: Option[];
  hsCodeOptions?: Option[];
  transports?: Option[];
  orderGroups?: Option[];
  address?: Address;
  documents?: Document[];
  documentsResponse?: DocumentResponse[];
  documentTypes?: DocumentTypes;
  extensions?: Extensions;
  roadForwardingOrder?: RoadForwardingOrder;
  roadCollectionOrder?: RoadCollectionOrder;
  airExportOrder?: AirExportOrder;
  airImportOrder?: AirImportOrder;
  seaExportOrder?: SeaExportOrder;
  seaImportOrder?: SeaImportOrder;
  validationSuccess?: ValidationResult;
  validationError?: ValidationResult;
  submitOrderResult?: OrderProcessResult;
  ports?: Ports;
  labels?: OrderLabelResult[];
  updateDocument?: BooleanResult;
  customerSettings?: CustomerSettings;
  incoTerms?: IncoTerms[];
  incotermConfig?: IncotermConfig;
  airProductRoutingConfig?: AirProductRoutingConfig;
  loadingPoints?: Address[];
  validateAddressData?: ValidationResult;
  airDeliveryProducts?: DeliveryProducts;
  townCounty?: IrelandPostalCode[];
  validatePostcodeValid?: PostcodeValidation;
  validatePostcodeInvalid?: PostcodeValidation;
  embargoCountries?: string[];
  natureOfGoods?: string[];
  unNumberSearchResults?: DangerousGoodDataItem[];
}

const models = {
  creationPreferences: Model.extend<Partial<CreationPreferences>>({}),
  customers: Model.extend<Partial<Customers>>([]),
  addresses: Model.extend<Partial<OrderAddress[]>>([]),
  countriesRoad: Model.extend<Partial<Countries>>([]),
  countriesAir: Model.extend<Partial<Countries>>([]),
  countryFavorites: Model.extend<Partial<FavoriteCountries>>({}),
  furtherAddressTypes: Model.extend<Partial<FurtherAddressTypes[]>>([]),
  contactData: Model.extend<Partial<ContactData>>({}),
  contactDataUpdated: Model.extend<Partial<ContactData>>({}),
  contacts: Model.extend<Partial<ContactData[]>>([]),
  packagingOptionsWithFavorites: Model.extend<Partial<PackagingOptionsWithFavorites>>({}),
  measurementProposals: Model.extend<Partial<MeasurementProposals>>({}),
  goodsGroupResponse: Model.extend<Partial<GoodsGroupResponse>>([]),
  currencies: Model.extend<Partial<Currencies>>([]),
  isThirdCountryConstellation: Model.extend<Partial<BooleanResult>>({}),
  orderContent: Model.extend<Partial<OrderContentResponse>>([]),
  deliveryOptions: Model.extend<Partial<Option[]>>([]),
  collectionOptions: Model.extend<Partial<Option[]>>([]),
  deliveryProducts: Model.extend<Partial<DeliveryProductsWithFavorites>>({}),
  freightTerms: Model.extend<Partial<FreightTerm[]>>([]),
  collectionTimeSlots: Model.extend<Partial<CollectionTimeSlots>>([]),
  collectionInterpreterOptions: Model.extend<Partial<Option[]>>([]),
  hsCodeOptions: Model.extend<Partial<Option[]>>([]),
  transports: Model.extend<Partial<Option[]>>([]),
  orderGroups: Model.extend<Partial<Option[]>>([]),
  address: Model.extend<Partial<Address>>({}),
  documents: Model.extend<Partial<Document[]>>([]),
  documentsResponse: Model.extend<Partial<DocumentResponse[]>>([]),
  documentTypes: Model.extend<Partial<DocumentTypes>>([]),
  extensions: Model.extend<Partial<Extensions>>([]),
  roadForwardingOrder: Model.extend<Partial<RoadForwardingOrder>>({}),
  roadCollectionOrder: Model.extend<Partial<RoadCollectionOrder>>({}),
  airExportOrder: Model.extend<Partial<AirExportOrder>>({}),
  validationSuccess: Model.extend<Partial<ValidationResult>>({}),
  validationError: Model.extend<Partial<ValidationResult>>({}),
  submitOrderResult: Model.extend<Partial<OrderProcessResult>>({}),
  ports: Model.extend<Partial<Ports>>([]),
  labels: Model.extend<Partial<OrderLabelResult[]>>([]),
  updateDocument: Model.extend<Partial<BooleanResult>>({}),
  customerSettings: Model.extend<Partial<CustomerSettings>>({}),
  incoTerms: Model.extend<Partial<IncoTerms>>({}),
  incotermConfig: Model.extend<Partial<IncotermConfig>>({}),
  airProductRoutingConfig: Model.extend<Partial<AirProductRoutingConfig>>({}),
  loadingPoints: Model.extend<Partial<Address[]>>([]),
  validateAddressData: Model.extend<Partial<ValidationResult>>({}),
  airDeliveryProducts: Model.extend<Partial<DeliveryProducts>>([]),
  townCounty: Model.extend<Partial<IrelandPostalCode[]>>([]),
  validatePostcodeValid: Model.extend<Partial<PostcodeValidation>>({}),
  validatePostcodeInvalid: Model.extend<Partial<PostcodeValidation>>({}),
  embargoCountries: Model.extend<Partial<string[]>>([]),
  natureOfGoods: Model.extend<Partial<string[]>>([]),
  unNumberSearchResults: Model.extend<Partial<DangerousGoodDataItem[]>>([]),
};

const factories = {};

export type Fixtures<T> = {
  [K in keyof T]: T[K];
};

interface MockServerParams {
  fixtures?: Fixtures<Models>;
  environment?: string;
  trackRequests?: boolean;
}

interface Request extends FakeXMLHttpRequest {
  passthrough?: () => void;
}

interface PretenderHandledRequests {
  pretender: { handledRequests?: Request[] };
}

export function mockServer({
  fixtures,
  environment = 'development',
  trackRequests = false,
}: MockServerParams): Server & PretenderHandledRequests {
  return createServer<typeof models, typeof factories>({
    environment,
    models,
    factories,
    fixtures,
    serializers: {
      application: Serializer.extend({
        embed: true,
        root: false,
      }),
    },
    trackRequests,
    seeds(server) {
      server.loadFixtures();
    },
    routes() {
      useBookRoutes(this, fixtures, environment);
      useAddressRoutes(this, fixtures, environment);
      useConfigRoutes(this, fixtures, environment);
      useDynamicLabelRoutes(this, fixtures, environment);

      // Passthrough all requests where no route is defined
      this.pretender.unhandledRequest = (verb: string, path: string, request: Request) => {
        if (environment === 'test') {
          console.warn(`No route defined for ${verb} ${path}`);
          return;
        }
        console.log('Mirage passthrough', verb, path);
        if (request.passthrough) {
          request.passthrough();
        }
      };
    },
  });
}
