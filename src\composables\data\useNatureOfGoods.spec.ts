import { beforeAll, describe, expect, it } from 'vitest';
import { nextTick } from 'vue';
import { mockServer } from '@/mocks/server';
import { withSetup } from '@test/util/with-setup';
import { natureOfGoods, natureOfGoodsMapped } from '@/mocks/fixtures/natureOfGoods';
import { useNatureOfGoods } from '@/composables/data/useNatureOfGoods';

describe('useNatureOfGoods', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        natureOfGoods,
      },
    });
  });

  it('returns formatted nature of goods', async () => {
    const { data: natureOfGoods } = withSetup(() => useNatureOfGoods())[0];
    await nextTick();
    await vi.waitFor(() => {
      expect(natureOfGoods.value).toEqual(natureOfGoodsMapped);
    });
  });
});
