import SeaMain from '@/views/createOrder/sea/SeaMain.vue';
import { customers } from '@/mocks/fixtures/customers';
import { mockServer } from '@/mocks/server';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import FormNavigation from '@/components/form/FormNavigation.vue';
import SeaOrder from '@/views/createOrder/sea/SeaOrder.vue';

describe('SeaMain view', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customers,
      },
    });
  });

  beforeEach(() => {
    wrapper = shallowMount(SeaMain);
  });

  it('shows form navigation and sea-order-view', () => {
    expect(wrapper.findComponent(FormNavigation).exists()).toBe(true);
    expect(wrapper.findComponent(SeaOrder).exists()).toBe(true);
  });
});
