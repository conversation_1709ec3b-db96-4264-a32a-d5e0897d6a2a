import { airProductRoutingConfig, incotermConfig } from '@/mocks/fixtures/configs';
import { mockServer } from '@/mocks/server';
import { useConfigStore } from '@/store/config';
import { initPinia } from '../../test/util/init-pinia';
import { incoTerms } from '@/mocks/fixtures/incoTerms';
import { useCreateOrderDataStore } from '@/store/createOrder/data';

describe('createConfig store', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {
        incotermConfig,
        incoTerms,
        airProductRoutingConfig,
      },
    });
  });

  it('fetches incoterm config', async () => {
    const store = useConfigStore();
    await store.fetchIncotermsConfig();

    expect(store.incoterms).toEqual(incotermConfig);
  });

  it('fetches incoterms', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchIncoTerms();

    expect(store.incoTerms).toEqual(incoTerms);
  });

  it('fetches airProductRouting config', async () => {
    const store = useConfigStore();
    await store.fetchAirProductRoutingConfig();

    expect(store.airProductRouting).toEqual(airProductRoutingConfig);
  });
});
