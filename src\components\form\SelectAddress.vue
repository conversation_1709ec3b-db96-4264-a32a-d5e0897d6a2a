<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>

    <VSelect
      :id="id"
      v-model="selection"
      :label="noDataText"
      :items="items"
      item-title="label"
      :item-value="itemValue ? itemValue : 'customerNumber'"
      :menu-props="{
        scrollStrategy: 'close',
        attach: $appRoot,
      }"
      :single-line="true"
      variant="outlined"
      density="compact"
      hide-details="auto"
      item-props.color="grey darken-4"
      :return-object="returnObject"
      :disabled="!!disabled"
    >
      <template v-if="loadingPointAddress" #item="{ item, props: slotProps }">
        <VListItem v-bind="slotProps">
          <template #title>
            <div class="text-label-2">
              {{ item.raw.address?.name }}
              <template v-if="item.raw.address?.name2">, {{ item.raw.address?.name2 }}</template>
              <template v-if="item.raw.address?.name3">, {{ item.raw.address?.name3 }}</template>
            </div>

            <div class="text-body-3 mt-1 text-grey-darken-2">
              <template v-if="item.raw.address?.street"> {{ item.raw.address?.street }}, </template>
              <template v-if="item.raw.address?.countryCode">
                {{ item.raw.address?.countryCode }}&nbsp;
              </template>
              <template v-if="item.raw.address?.postcode">
                {{ item.raw.address?.postcode }}&nbsp;
              </template>
              <template v-if="item.raw.address?.city">
                {{ item.raw.address?.city }}
              </template>
              <template v-if="isIrelandCountryCode(item.raw.address?.countryCode ?? '')"
                >, {{ item.raw.address?.supplement }}</template
              >
            </div>
          </template>
        </VListItem>
      </template>
      <template v-else #item="{ item, props: slotProps }">
        <VListItem v-bind="slotProps">
          <template #title>
            <div class="text-label-2">
              <span>{{ item.raw.address?.name }}</span>
              <span v-if="item.raw.address?.name1">, {{ item.raw.address?.name1 }} </span>
              <span v-if="item.raw.address?.name2">, {{ item.raw.address?.name2 }} </span>
            </div>
            <div class="text-body-3 mt-1 text-grey-darken-2">
              {{ item.raw.address?.street }}, {{ item.raw.address?.countryCode }}
              {{ item.raw.address?.postcode }}
              {{ item.raw.address?.city }}
              <template v-if="isIrelandCountryCode(item.raw.address?.countryCode ?? '')"
                >, {{ item.raw.address?.supplement }}</template
              >
            </div>
          </template>
        </VListItem>
      </template>
    </VSelect>
  </div>
</template>

<script setup lang="ts">
import type { CustomerWithAddress } from '@/store/createOrder/data';
import { createUuid } from '@/utils/createUuid';
import { BasicAddress } from '@dfe/dfe-book-api';
import { isIrelandCountryCode } from '@dfe/dfe-frontend-composables';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  items: CustomerWithAddress[];
  itemValue?: string;
  label?: TranslateResult;
  required?: boolean;
  returnObject?: boolean;
  disabled?: boolean;
  noDataText?: TranslateResult;
  loadingPointAddress?: boolean;
}

defineProps<Props>();
const selection = defineModel<string | BasicAddress>();

const id = `select-address-${createUuid()}`;
</script>
