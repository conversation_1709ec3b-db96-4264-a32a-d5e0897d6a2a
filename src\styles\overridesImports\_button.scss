[dfe-book-frontend] {
:deep() {
  .v-btn {
    padding: 0 12px;

    &.v-btn--disabled {
      cursor: not-allowed;
      pointer-events: auto;

      &.text-primary {
        &:hover {
          background-color: transparent !important;
        }
      }
    }

    &.bg-primary {
      &:hover {
        background-color: var(--color-base-blue-700) !important;
      }

      &:focus-visible {
        box-shadow: inset 0 0 0 2px var(--color-base-blue-700);
      }

      &.v-btn--disabled {
        background-color: rgb(var(--v-theme-grey-400)) !important;
      }
    }

    &.text-primary {
      &:hover {
        background-color: var(--color-base-blue-100) !important;
      }

      &:focus-visible {
        box-shadow: inset 0 0 0 1px var(--color-base-blue-700) !important;
        color: var(--color-base-blue-700) !important;
      }

      &.v-btn--disabled {
        color: var(--color-base-grey-500) !important;
        opacity: 1;
      }

      &[aria-expanded='true'] {
        background-color: var(--color-base-blue-100) !important;
        box-shadow: none !important;
        transition: none;
      }
    }

    .material-symbol {
      .left {
        margin-left: -4px;
        margin-right: 8px;
      }

      .right {
        margin-left: 8px;
        margin-right: -4px;
      }
    }
  }
}
}