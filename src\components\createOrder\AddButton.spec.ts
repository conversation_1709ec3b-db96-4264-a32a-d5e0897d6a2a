import AddButton from '@/components/createOrder/AddButton.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';

describe('Order references - AddButton component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(AddButton);
  });

  it("emits 'add-new-item' event on button click", async () => {
    await wrapper.findComponent({ name: 'v-btn' }).trigger('click');
    const addNewItemEvent = wrapper.emitted('add-new-item');

    expect(addNewItemEvent).toHaveLength(1);
  });
});
