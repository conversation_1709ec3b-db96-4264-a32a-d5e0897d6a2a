import SearchAddressField from '@/components/createOrder/formSectionAddresses/SearchAddressField.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { mockServer } from '@/mocks/server';
import { addresses } from '@/mocks/fixtures/addresses';
import AutocompleteAddressField from '@/components/form/AutocompleteAddressField.vue';
import { i18n } from '@/plugins/i18n';
import { Address as AddressBookAddress } from '@dfe/dfe-address-api';

describe('SearchAddressField component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        addresses,
      },
    });
  });

  it('mounts', () => {
    wrapper = mount(SearchAddressField);
    expect(wrapper.exists()).toBe(true);
  });

  it('should show customers addresse as smart proposals only for current customer, if search field is empty', async () => {
    const { t } = i18n.global;
    const { customers } = storeToRefs(useCreateOrderDataStore());
    const { addresses: storeAddresses } = storeToRefs(useCreateAddressDataStore());

    await wrapper.setProps({
      header: t('labels.shipper_dfe.text'),
      isCustomer: true,
    });

    storeAddresses.value.search = [];

    if (customers) {
      customers.value = [addresses[0]];
    }

    await wrapper.vm.$nextTick();

    const autoComleteField = wrapper.findComponent(AutocompleteAddressField);

    await autoComleteField.vm.$emit('focus');

    expect(autoComleteField.props().items).toHaveLength(1);

    await wrapper.setProps({
      header: t('labels.consignee_dfe.text'),
      isCustomer: false,
    });

    await wrapper.vm.$nextTick();

    expect(autoComleteField.props().items).toHaveLength(0);
  });

  it('should show search results, if search field is not empty', async () => {
    const { addresses: storeAddresses } = storeToRefs(useCreateAddressDataStore());
    const { customers } = storeToRefs(useCreateOrderDataStore());

    storeAddresses.value.search = [...(addresses as AddressBookAddress[])];

    if (customers) {
      customers.value = [addresses[0]];
    }

    const autoCompleteField = wrapper.findComponent(AutocompleteAddressField);

    await wrapper.vm.$nextTick();

    expect(autoCompleteField.props().items).toHaveLength(6);
  });

  it('should show the asterisk if its mandatory', async () => {
    expect(wrapper.find('span.label-indicator').exists()).toBe(false);

    await wrapper.setProps({
      isRequired: true,
    });

    expect(wrapper.find('span.label-indicator').exists()).toBe(true);
  });
});
