import { OrderResponseBody, SeaOrder } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';

export function useSetFullContainerLoadData(editOrderData: OrderResponseBody) {
  const orderLineStore = useCreateOrderOrderLineFormStore();
  const { fullContainerLoads, orderLines } = storeToRefs(orderLineStore);
  const fullContainerLoadListResponse = (editOrderData as SeaOrder).fullContainerLoads;
  const fullContainerLoadResponse = (editOrderData as SeaOrder).fullContainerLoad;

  if (fullContainerLoadResponse) {
    orderLineStore.isFullContainerLoad = fullContainerLoadResponse;
  }

  if (!fullContainerLoadListResponse) {
    return;
  }

  while (fullContainerLoadListResponse.length > fullContainerLoads.value.length) {
    orderLineStore.addFullContainerLoad(true);
  }

  fullContainerLoads.value.forEach((fullContainerLoad, i) => {
    Object.assign(fullContainerLoad, {
      ...fullContainerLoadListResponse[i],
      lines: undefined,
    });

    const orderLinesForFullContainerLoads = orderLines.value.filter(
      (orderLine) => orderLine.fullContainerLoadId === fullContainerLoad.id,
    );

    orderLinesForFullContainerLoads.forEach((orderLine) => {
      orderLine.fullContainerLoadId = fullContainerLoad.localId;
    });
  });
}
