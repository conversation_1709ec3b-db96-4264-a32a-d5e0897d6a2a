import { defineStore } from 'pinia';
import type { FreightTerm } from '@dfe/dfe-book-api';
import { CustomsType, FreightPayerType } from '@dfe/dfe-book-api';
import { i18n } from '@/plugins/i18n';

export interface CreateOrderFormState {
  customsDeclarationRequired: boolean;
  customsDeclarationExecutor: CustomsType;
  frostProtectionRequired: boolean;
  selectedFreightTerm: FreightTerm | null;
  freightPayer: FreightPayerType;
  transport: string;
  palletLocationsNumber: number | undefined;
  orderGroup: string | undefined;
}

export const useCreateOrderFormAccountingAdditionalServices = defineStore(
  'createOrderFormAccountingAdditionalServices',
  {
    state: (): CreateOrderFormState => ({
      customsDeclarationRequired: false,
      customsDeclarationExecutor: CustomsType.CUSTOMER,
      frostProtectionRequired: false,
      selectedFreightTerm: null,
      freightPayer: FreightPayerType.Principal,
      transport: '',
      palletLocationsNumber: undefined,
      orderGroup: undefined,
    }),
    getters: {
      incoTermText(state) {
        if (!state.selectedFreightTerm) {
          return i18n.global.t('messages.id6349.text');
        }

        if (!state.selectedFreightTerm.incoTermKey) {
          return i18n.global.t('messages.id6350.text');
        }

        return state.selectedFreightTerm.incoTermKey;
      },
    },
  },
);
