import { useRequireOneOf } from '@/composables/createOrder/useRequireOneOf';
import type { ComputedRef } from 'vue';
import { computed, ref } from 'vue';

describe('useRequireOneOf', () => {
  type Contact = {
    name: string;
    email?: string;
    mobile?: string;
    telephone?: string;
  };

  it.each<[ComputedRef<Contact>, ComputedRef<(keyof Contact)[]>, (keyof Contact)[]]>([
    [
      computed(() => ({ name: '<PERSON>', email: '<EMAIL>' })),
      computed(() => ['mobile', 'telephone']),
      ['mobile', 'telephone'],
    ],
    [
      computed(() => ({ name: '<PERSON>', email: '<EMAIL>' })),
      computed(() => ['email', 'telephone']),
      ['email'],
    ],
    [
      computed(() => ({
        name: '<PERSON>',
        email: '<EMAIL>',
        telephone: '01234567',
      })),
      computed(() => ['telephone', 'email']),
      ['telephone'],
    ],
  ])('should return the correct required fields', (contact, fieldCollection, expected) => {
    const { requiredFields } = useRequireOneOf(ref(contact), fieldCollection);
    expect(requiredFields.value).toEqual(expected);
  });

  it('should keep its last required field although another field has been added', async () => {
    const contact = ref<Contact>({
      name: 'John Doe',
    });
    const { requiredFields } = useRequireOneOf(
      contact,
      computed(() => ['email', 'telephone']),
    );
    contact.value.telephone = '01234567';
    expect(requiredFields.value).toEqual(['telephone']);

    contact.value.email = '<EMAIL>';
    expect(requiredFields.value).toEqual(['telephone']);
  });

  it('should switch to another onOf field if the original field has been emptied', async () => {
    const contact = ref<Contact>({
      name: 'John Doe',
      email: '<EMAIL>',
      telephone: '01234567',
    });
    const { requiredFields } = useRequireOneOf(
      contact,
      computed(() => ['email', 'telephone']),
    );

    expect(requiredFields.value).toEqual(['email']);
    contact.value.email = '';
    expect(requiredFields.value).toEqual(['telephone']);
  });

  it('should return all fields if every field has been emptied', async () => {
    const contact = ref<Contact>({
      name: 'John Doe',
      email: '<EMAIL>',
      telephone: '01234567',
    });

    const { requiredFields } = useRequireOneOf(
      contact,
      computed(() => ['email', 'telephone']),
    );

    expect(requiredFields.value).toEqual(['email']);

    contact.value.email = '';
    contact.value.telephone = '';

    expect(requiredFields.value).toEqual(['email', 'telephone']);
  });
});
