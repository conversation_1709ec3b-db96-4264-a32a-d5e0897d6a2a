<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VCombobox
      :id="id"
      v-model="computedValue"
      :items="menuItems"
      :placeholder="placeholder"
      :item-title="itemTitle ?? 'text'"
      :item-value="itemValue ?? 'value'"
      single-line
      variant="outlined"
      density="compact"
      item-props.color="grey darken-4"
      :menu-props="{ scrollStrategy: 'close', attach: $appRoot }"
      bg-color="white"
      :no-filter="staticMenu || showLoader"
      hide-details="auto"
      validate-on="blur"
      return-object
      :rules="validationRules"
      @update:search="onSearchInput"
      @focus="$event.target.click()"
    >
      <template
        v-if="(addressInput || contactsOutput || hasItemSlot) && !showLoader"
        #item="{ item, props: itemProps }"
      >
        <VListItem v-if="item.raw" v-bind="itemProps">
          <template #title>
            <div class="text-label-2">
              <span>{{ item.raw.name }}</span>
              <span v-if="item.raw.name2 || item.raw.name3">, </span>
              <span v-if="item.raw.name2">{{ item.raw.name2 }}</span>
              <span v-if="item.raw.name2 && item.raw.name3">, </span>
              <span v-if="item.raw.name3"> {{ item.raw.name3 }} </span>
            </div>
            <div class="text-body-3 text-grey-darken-2">
              <div v-if="addressInput">
                <span v-if="item.raw.street">{{ item.raw.street }},</span>
                <span v-if="item.raw.postcode"> {{ item.raw.postcode }},</span>
                <span v-if="item.raw.city"> {{ item.raw.city }},</span>
                <span v-if="item.raw.countryCode"> {{ item.raw.countryCode }}</span>
              </div>
              <div v-if="contactsOutput">
                <span v-if="item.raw.email"> {{ item.raw.email }} <br /></span>
                <span v-if="item.raw.telephone"> {{ item.raw.telephone }} <br /></span>
                <span v-if="item.raw.mobile"> {{ item.raw.mobile }} </span>
                <span v-if="item.raw.fax"> {{ item.raw.fax }} </span>
              </div>
            </div>
          </template>
        </VListItem>
      </template>

      <template v-else-if="showLoader" #item>
        <VListItem :disabled="true">
          <ProgressCircular color="primary" size="24" indeterminate />
        </VListItem>
      </template>

      <template v-if="append || $slots.icon || contactsOutput || addressInput" #append-inner>
        <span class="v-text-field__suffix text-body-2">{{ append }}</span>
        <span class="text-grey-darken-2 mt-1">
          <slot name="icon">
            <PersonIcon v-if="contactsOutput" />
            <SearchIcon v-if="addressInput" />
          </slot>
        </span>
      </template>
    </VCombobox>
  </div>
</template>

<script setup lang="ts">
import ProgressCircular from '@/components/ProgressCircular.vue';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { useDebouncedLoader } from '@/composables/useDebouncedLoader';
import { createUuid } from '@/utils/createUuid';
import PersonIcon from '@dfe/dfe-frontend-styles/assets/icons/person-16px.svg';
import SearchIcon from '@dfe/dfe-frontend-styles/assets/icons/search-16px.svg';
import { computed, toRefs, useSlots } from 'vue';
import type { TranslateResult } from 'vue-i18n';
interface Props {
  items?: unknown[];
  itemTitle?: string;
  itemValue?: string;
  maxLength?: number;
  label?: TranslateResult;
  append?: string;
  required?: boolean;
  staticMenu?: boolean;
  loading?: boolean;
  showLoaderAfterMs?: number;
  returnObject?: boolean;
  addressInput?: boolean;
  contactsOutput?: boolean;
  placeholder?: TranslateResult;
  rules?: ValidationRule[];
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  itemTitle: undefined,
  itemValue: undefined,
  maxLength: Infinity,
  label: '',
  append: undefined,
  showLoaderAfterMs: 500,
  placeholder: '',
  rules: undefined,
});
const computedValue = defineModel<string>({ default: '' });

const emit = defineEmits(['input-multiple', 'search-input']);
const slots = useSlots();

const id = `combobox-${createUuid()}`;

const { loading, showLoaderAfterMs } = toRefs(props);
const showLoader = useDebouncedLoader(loading, showLoaderAfterMs);

const onSearchInput = (value: string | null) => {
  emit('search-input', value);
};

const menuItems = computed(() => (showLoader.value ? [''] : props.items));

const hasItemSlot = computed(() => !!slots.item);

const validationRules = computed(() => [
  ...(props.maxLength ? [useValidationRules.maxChars(props.maxLength)] : []),
  ...(props.rules ?? []),
]);
</script>
