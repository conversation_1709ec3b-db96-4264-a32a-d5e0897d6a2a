<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VTextField
      :id="id"
      ref="textField"
      v-data-test="'number-field'"
      :model-value="model"
      single-line
      variant="outlined"
      density="compact"
      :hint="hint"
      :persistent-hint="!!hint"
      hide-details="auto"
      :placeholder="placeholder"
      step="any"
      type="number"
      :min="min ? min : null"
      :max="max ? max : null"
      hide-spin-buttons
      bg-color="white"
      :rules="validationRules"
      :disabled="disabled"
      :validate-on="shouldValidateOnBlur"
      :error-messages="errorMessages"
      @update:model-value="onInputChange"
      @update:focused="onFocus"
    >
      <template v-if="append" #append-inner>
        <!-- eslint-disable vue/no-v-html -->
        <span class="v-text-field__suffix text-body-2" v-html="sanitizeHtml(append)"></span>
        <!-- eslint-enable -->
      </template>
    </VTextField>
  </div>
</template>

<script setup lang="ts">
import { createUuid } from '@/utils/createUuid';
import { computed, ref } from 'vue';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { getMessages, useValidationRules } from '@/composables/form/useValidationRules';
import sanitizeNumbers from '@/utils/sanitizeInput';
import sanitizeHtml from 'sanitize-html';
import type { TranslateResult } from 'vue-i18n';
import { VTextField } from 'vuetify/components';

interface Props {
  label?: TranslateResult;
  hint?: string;
  placeholder?: TranslateResult;
  append?: string;
  required?: boolean;
  min?: number;
  max?: number;
  rules?: ValidationRule[];
  disabled?: boolean;
  allowedDecimals?: number;
  validateOnBlur?: boolean;
}

const props = defineProps<Props>();

const model = defineModel<number | null>();

const id = `text-field-${createUuid()}`;
const errors = ref<string[]>([]);
const showErrors = ref(true);
const textField = ref<typeof VTextField | null>();

const errorMessages = computed(() => {
  return showErrors.value === true ? errors.value : [];
});

const validationRules = computed(() => [
  ...(props.required ? [useValidationRules.required] : []),
  useValidationRules.numbers,
  useValidationRules.min(props.min),
  useValidationRules.max(props.max),
  ...(props.allowedDecimals
    ? [useValidationRules.decimalWithMaxPlaces(props.allowedDecimals)]
    : []),
  ...(props.rules ?? []),
]);

const shouldValidateOnBlur = computed(() => (props.validateOnBlur ? 'blur' : undefined));

const onInputChange = (value: HTMLInputElement['value']) => {
  if (value === '' || isNaN(Number(value))) {
    model.value = null;
  } else {
    model.value = sanitizeNumbers(value, Boolean(props.allowedDecimals));
  }
};

// fixes numeric-only input validation in safari
const onFocus = (value: boolean) => {
  if (value) {
    textField.value?.resetValidation();
    showErrors.value = false;
  } else {
    showErrors.value = true;
    if (textField.value && textField.value.$el) {
      const { valid } = textField.value.validity;
      if (valid) {
        errors.value = [];
      } else {
        errors.value = getMessages(validationRules.value, model.value);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}
</style>
