import type { Server } from 'miragejs';
import type { Fixtures, Models } from '../server';

const url = import.meta.env.VITE_APP_DFE_DYNAMICLABEL_API_URL ?? '';
function withURL(path: string) {
  return `${url}${path}`;
}

export function useRoutes(
  server: Server,
  fixtures?: Fixtures<Models>,
  environment = 'development',
) {
  // Mock routes in test environment only
  if (environment === 'test') {
    server.get(withURL('/v1/getTranslations'), () => {
      return {
        labels: { foo: { text: 'bar' } },
        messages: { id1: { text: 'text' } },
      };
    });
  }
}
