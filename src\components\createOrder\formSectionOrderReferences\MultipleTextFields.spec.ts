import MultipleTextFields from '@/components/createOrder/formSectionOrderReferences/MultipleTextFields.vue';
import type { CreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import TextField from '@/components/form/TextField.vue';
import { nextTick } from 'vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';

const props: {
  items: CreateOrderOrderReferencesFormStore['purchaseOrderNumbers'];
  label: string;
} = {
  items: [
    {
      id: '0',
      value: '0',
    },
    {
      id: '1',
      value: '1',
    },
  ],
  label: 'label',
};

describe('Order references - MultipleTextFields component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(MultipleTextFields, {
      props,
    });

    expect(wrapper.exists()).toBe(true);
  });

  it('shows label for first text field', async () => {
    const textFields = wrapper.findAllComponents(TextField);
    expect(textFields).toHaveLength(2);
    expect(textFields.at(0)?.props('label')).toBe('label');
    expect(textFields.at(1)?.props('label')).toBe('');
  });

  it('does not set duplicate error for unique values', async () => {
    const inputs = wrapper.findAll('input');
    expect(inputs[0].attributes('error')).toBeUndefined();
    expect(inputs[1].attributes('error')).toBeUndefined();
  });

  it('sets duplicate error when two fields have the same value', async () => {
    const inputs = wrapper.findAllComponents(TextField);

    const input1 = inputs.at(0);
    const input2 = inputs.at(1);

    input1?.setValue('H54YR28');
    input1?.vm.$emit('input');

    input2?.setValue('H54YR28');
    input2?.vm.$emit('input');

    await nextTick();

    expect(input1?.vm.error).toBe(true);
    expect(input2?.vm.error).toBe(true);
  });

  it('skips validation for empty or null values', async () => {
    const inputs = wrapper.findAllComponents(TextField);

    const input1 = inputs.at(0);
    const input2 = inputs.at(1);

    input1?.setValue('');
    input1?.vm.$emit('input');

    input2?.setValue('');
    input2?.vm.$emit('input');

    await nextTick();

    expect(input1?.vm.error).toBe(false);
    expect(input2?.vm.error).toBe(false);
  });

  it('displays the DfeBanner when duplicate entries are detected and hides when resolved', async () => {
    const inputs = wrapper.findAllComponents(TextField);

    const input1 = inputs.at(0);
    const input2 = inputs.at(1);

    input1?.setValue('H54YR28');
    input1?.vm.$emit('input');

    input2?.setValue('H54YR28');
    input2?.vm.$emit('input');

    await nextTick();

    const banner = wrapper.findComponent(DfeBanner);
    expect(banner.exists()).toBe(true);

    input1?.setValue('222');
    input1?.vm.$emit('input');
    await nextTick();

    const updatedBanner = wrapper.findComponent(DfeBanner);
    expect(updatedBanner.exists()).toBe(false);
  });
});
