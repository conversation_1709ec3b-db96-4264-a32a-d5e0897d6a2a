import ErrorPrompt from '@/components/base/modal/ErrorPrompt.vue';
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { VBtn } from 'vuetify/components';

const props = {
  modelValue: true,
  headline: 'Headline',
};
const headline = 'Headline';
const confirmText = 'Confirm Text';
const cancelText = 'Cancel Text';

describe('ConfirmPrompt component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(ErrorPrompt, {
      props,
    });
  });

  const modalHeader = () => wrapper.getComponent('.modal-header');
  const modalFooter = () => wrapper.getComponent('.modal-footer');

  it('displays headline depending on prop', async () => {
    await wrapper.setProps({ headline });

    const headlineEl = modalHeader().find('h3');
    expect(headlineEl.exists()).toBe(true);
    expect(headlineEl.text()).toEqual(headline);
  });

  it('displays confirm text depending on prop', async () => {
    await wrapper.setProps({ confirmText });

    const confirmTextEl = modalFooter().find('[data-test="book-confirm-btn"]');
    expect(confirmTextEl.exists()).toBe(true);
    expect(confirmTextEl.text()).toEqual(confirmText);
  });

  it('emits confirm event on click', async () => {
    await modalFooter().find('[data-test="book-confirm-btn"]').trigger('click');

    const event = wrapper.emitted('close');

    expect(event).toHaveLength(1);
  });

  it('displays cancel text depending on prop', async () => {
    await wrapper.setProps({ cancelText });

    const cancelTextEl = modalFooter().find('[data-test="book-cancel-btn"]');
    expect(cancelTextEl.exists()).toBe(true);
    expect(cancelTextEl.text()).toEqual(cancelText);
  });

  it('emits cancel event on cancel-btn click', async () => {
    await modalFooter().find('[data-test="book-cancel-btn"]').trigger('click');

    const event = wrapper.emitted('closeOrderForm');

    expect(event).toHaveLength(1);
  });

  it('emits close event on cancel-btn click', async () => {
    await modalHeader().find('.modal-close-button').findComponent(VBtn).trigger('click');

    const modalWrapper = wrapper.getComponent(ModalWrapper);

    expect(modalWrapper.vm.$props.modelValue).toBeFalsy();
  });
});
