import { useTransportDirection } from '@/composables/form/useTransportDirection';
import { ConsigneeAddressType, FurtherAddressTypesList } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { customers } from '@/mocks/fixtures/customers';
import { mockServer } from '@/mocks/server';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import type { HandOverSelectionValue } from '@/types/hand-over';
import { HandOverSelection } from '@/types/hand-over';
import { OrderType, PortType, Segment } from '@dfe/dfe-book-api';
import type { Server } from 'miragejs';
import { storeToRefs } from 'pinia';
import { describe } from 'vitest';
import type { ComputedRef } from 'vue';
import { nextTick } from 'vue';
import { initPinia } from '../../../test/util/init-pinia';
const mockAddress = {
  id: undefined,
  name: '',
  name2: '',
  name3: '',
  countryCode: undefined,
  street: '',
  street2: '',
  postcode: '',
  city: '',
  supplement: '',
  gln: undefined,
};
describe('Composable - useTransportDirection', () => {
  let server: Server;

  beforeEach(() => {
    initPinia();
  });

  afterEach(() => {
    if (server) {
      server.shutdown();
    }
  });

  describe.each([
    ['Air', OrderType.AirImportOrder, OrderType.AirExportOrder],
    ['Sea', OrderType.SeaImportOrder, OrderType.SeaExportOrder],
  ])(`Toggle %s Order`, (_, importOrderType, exportOrderType) => {
    it.each([
      ['from import to export', importOrderType, exportOrderType],
      ['from export to import', exportOrderType, importOrderType],
    ])('should toggle correctly %s', (_, initialOrderType, expectedOrderType) => {
      const direction = useTransportDirection();
      const addressStore = useCreateOrderAddressesStore();
      const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
      const { orderType } = storeToRefs(useCreateOrderFormStore());
      orderType.value = initialOrderType;

      shipperHandOverSelection.value = {
        selection: HandOverSelection.port,
      } as HandOverSelectionValue;

      consigneeHandOverSelection.value = {
        selection: HandOverSelection.alternateAddress,
      } as HandOverSelectionValue;

      direction.toggle();

      expect(orderType.value).toBe(expectedOrderType);
      expect(shipperHandOverSelection.value.selection).toBe(HandOverSelection.port);
      expect(consigneeHandOverSelection.value.selection).toBe(HandOverSelection.alternateAddress);
    });

    it('is import order', () => {
      const direction = useTransportDirection();
      const { orderType } = storeToRefs(useCreateOrderFormStore());
      orderType.value = importOrderType;

      expect(direction.isImportOrder.value).toBe(true);
      expect(direction.isExportOrder.value).toBe(false);
    });

    it('is export order', () => {
      const direction = useTransportDirection();
      const { orderType } = storeToRefs(useCreateOrderFormStore());
      orderType.value = exportOrderType;

      expect(direction.isImportOrder.value).toBe(false);
      expect(direction.isExportOrder.value).toBe(true);
    });
  });

  describe('Watch order type', () => {
    async function toggleAndAssert(direction: {
      isExportOrder: ComputedRef<boolean>;
      toggle: () => void;
      isImportOrder: ComputedRef<boolean>;
    }) {
      const {
        shipperAddress,
        consigneeAddress,
        shipperHandOverSelection,
        consigneeHandOverSelection,
        contactDataShipper,
        contactDataConsignee,
        differentConsigneeAddress,
        consigneeAddressType,
      } = storeToRefs(useCreateOrderAddressesStore());

      consigneeAddress.value.address = addresses[1];
      shipperHandOverSelection.value = {
        selection: 'portSelection',
        port: {
          type: PortType.AIRPORT,
          code: 'Airport',
        },
      };
      consigneeHandOverSelection.value = {
        selection: 'portSelection',
        port: {
          type: PortType.AIRPORT,
          code: 'Airport',
        },
      };

      contactDataShipper.value = contactData();
      contactDataConsignee.value = { ...contactData() };

      differentConsigneeAddress.value.address = orderAddress();
      consigneeAddressType.value = ConsigneeAddressType.PRINCIPALS_ADDRESS;

      direction.toggle();

      await nextTick();

      expect({
        shipperAddress: shipperAddress.value,
        consigneeAddress: consigneeAddress.value,
        shipperHandOverSelection: shipperHandOverSelection.value,
        consigneeHandOverSelection: consigneeHandOverSelection.value,
        contactDataShipper: contactDataShipper.value,
        contactDataConsignee: contactDataConsignee.value,
        differentConsigneeAddress: differentConsigneeAddress.value,
        consigneeAddressType: consigneeAddressType.value,
      }).toMatchSnapshot();
    }

    it('should reset addresses when order type changes to import', async () => {
      server = mockServer({
        environment: 'test',
        fixtures: {
          customers,
          addresses,
        },
      });
      await useCreateOrderDataStore().fetchCustomers(Segment.AIR);
      const direction = useTransportDirection();

      const { orderType, customerNumber } = storeToRefs(useCreateOrderFormStore());

      if (customers[0].customerNumber) {
        customerNumber.value = customers[0].customerNumber;
      }

      await nextTick();

      orderType.value = OrderType.AirExportOrder;
      await nextTick();

      await toggleAndAssert(direction);
    });

    it('should reset addresses when order type changes to export', async () => {
      server = mockServer({
        environment: 'test',
        fixtures: {
          customers,
          addresses,
        },
      });
      await useCreateOrderDataStore().fetchCustomers(Segment.AIR);
      const direction = useTransportDirection();

      const { orderType, customerNumber } = storeToRefs(useCreateOrderFormStore());

      if (customers[0].customerNumber !== undefined) {
        customerNumber.value = customers[0].customerNumber;
      }

      await nextTick();

      orderType.value = OrderType.AirImportOrder;
      await nextTick();

      await toggleAndAssert(direction);
    });
  });

  describe('Toggle Road Order', async () => {
    it.each([
      [
        'from collection to forwarding',
        OrderType.RoadCollectionOrder,
        OrderType.RoadForwardingOrder,
      ],
      [
        'from forwarding to collection',
        OrderType.RoadForwardingOrder,
        OrderType.RoadCollectionOrder,
      ],
    ])('should toggle correctly %s', async (_, initialOrderType, expectedOrderType) => {
      server = mockServer({
        environment: 'test',
        fixtures: {
          customers,
          addresses,
        },
      });
      const direction = useTransportDirection();
      const addressStore = useCreateOrderAddressesStore();
      const { differentConsigneeAddress, consigneeAddressType } = storeToRefs(addressStore);
      const { orderType, customerNumber } = storeToRefs(useCreateOrderFormStore());
      if (customers[0].customerNumber !== undefined) {
        customerNumber.value = customers[0].customerNumber;
      }
      await nextTick();
      orderType.value = initialOrderType;
      await nextTick();

      direction.toggle();

      expect(orderType.value).toBe(expectedOrderType);
      expect(differentConsigneeAddress.value.address).toStrictEqual(orderAddress());
      expect(consigneeAddressType.value).toBe(ConsigneeAddressType.PRINCIPALS_ADDRESS);
    });

    it('is import order', () => {
      const direction = useTransportDirection();
      const { orderType } = storeToRefs(useCreateOrderFormStore());
      orderType.value = OrderType.RoadCollectionOrder;

      expect(direction.isImportOrder.value).toBe(true);
      expect(direction.isExportOrder.value).toBe(false);
    });

    it('is export order', () => {
      const direction = useTransportDirection();
      const { orderType } = storeToRefs(useCreateOrderFormStore());
      orderType.value = OrderType.RoadForwardingOrder;

      expect(direction.isImportOrder.value).toBe(false);
      expect(direction.isExportOrder.value).toBe(true);
    });

    it('reset the cover address when orderType change', () => {
      const direction = useTransportDirection();
      const { orderType } = storeToRefs(useCreateOrderFormStore());
      orderType.value = OrderType.RoadForwardingOrder;
      expect(direction.isExportOrder.value).toBe(true);
      const store = useCreateOrderAddressesStore();

      store.addNewFurtherAddress(FurtherAddressTypesList.coverAddressConsignor);

      expect(
        store.getFurtherAddress(FurtherAddressTypesList.coverAddressConsignor)?.address,
      ).toEqual(mockAddress);

      direction.toggle();
      expect(direction.isImportOrder.value).toBe(true);
      store.removeFurtherAddress(FurtherAddressTypesList.coverAddressConsignor);
    });
  });
});
