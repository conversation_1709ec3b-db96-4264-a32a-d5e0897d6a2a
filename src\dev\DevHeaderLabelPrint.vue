<template>
  <VLayout>
    <header class="py-2 mb-4">
      <VBtn
        class="mr-2"
        size="small"
        variant="outlined"
        :disabled="!printBtnDisabled"
        @click="emitEvent"
        >Render label preview</VBtn
      >
      <VBtn size="small" variant="outlined" :disabled="printBtnDisabled" @click="startPrinting"
        >Do Print Labels</VBtn
      >
    </header>
  </VLayout>
</template>

<script setup lang="ts">
import { ClientKey } from '@/types/client';
import { inject, onBeforeUnmount } from 'vue';
import { labels } from '@/mocks/fixtures/labels';
import { ref } from 'vue';

const client = inject(ClientKey);

const printBtnDisabled = ref(true);

const emitEvent = () => {
  client?.events.emit('renderLabelPreview', {
    file: labels[1].orderLabel as string,
    fileName: 'filename.pdf',
    modalHeadline: 'ModalHeadline',
    orderId: 4010183600,
  });
};

const handleRenderLabelPreview = () => {
  printBtnDisabled.value = false;
};

client?.events.on('renderLabelPreview', handleRenderLabelPreview);

onBeforeUnmount(() => {
  client?.events.off('renderLabelPreview', handleRenderLabelPreview);
});

const startPrinting = () => {
  client?.events.emit('doPrintLabels');
};
</script>

<style lang="scss">
@import '@dfe/dfe-frontend-styles/build/scss/variables';

@font-face {
  font-family: $font-family-base;
  font-weight: $font-weight-base;
  font-style: normal;
  src: url('@dfe/dfe-frontend-styles/' + $asset-font-base-ttf-regular) format('truetype');
}

@font-face {
  font-family: $font-family-base;
  font-weight: $font-weight-heading;
  font-style: normal;
  src: url('@dfe/dfe-frontend-styles/' + $asset-font-base-ttf-bold) format('truetype');
}

@font-face {
  font-family: $font-family-base;
  font-weight: $font-weight-label;
  font-style: normal;
  src: url('@dfe/dfe-frontend-styles/' + $asset-font-base-ttf-medium) format('truetype');
}

:root {
  --color-base-white: #{$color-base-white};

  --color-base-grey-50: #{$color-base-grey-50};
  --color-base-grey-100: #{$color-base-grey-100};
  --color-base-grey-200: #{$color-base-grey-200};
  --color-base-grey-300: #{$color-base-grey-300};
  --color-base-grey-400: #{$color-base-grey-400};
  --color-base-grey-500: #{$color-base-grey-500};
  --color-base-grey-600: #{$color-base-grey-600};
  --color-base-grey-700: #{$color-base-grey-700};
  --color-base-grey-800: #{$color-base-grey-800};
  --color-base-grey-900: #{$color-base-grey-900};

  --color-base-blue-50: #{$color-base-blue-50};
  --color-base-blue-100: #{$color-base-blue-100};
  --color-base-blue-200: #{$color-base-blue-200};
  --color-base-blue-300: #{$color-base-blue-300};
  --color-base-blue-400: #{$color-base-blue-400};
  --color-base-blue-500: #{$color-base-blue-500};
  --color-base-blue-600: #{$color-base-blue-600};
  --color-base-blue-700: #{$color-base-blue-700};

  --color-base-green-100: #{$color-base-green-100};
  --color-base-green-500: #{$color-base-green-500};
  --color-base-green-800: #{$color-base-green-800};

  --color-base-orange-100: #{$color-base-orange-100};
  --color-base-orange-500: #{$color-base-orange-500};
  --color-base-orange-800: #{$color-base-orange-800};

  --color-base-red-100: #{$color-base-red-100};
  --color-base-red-500: #{$color-base-red-500};
  --color-base-red-800: #{$color-base-red-800};

  --color-base-yellow-500: #{$color-base-yellow-500};

  --color-text-base: #{$color-text-base};
  --color-text-primary: #{$color-text-primary};
  --color-text-grey: #{$color-text-grey};
}

body {
  padding: 0 32px;
}
</style>
