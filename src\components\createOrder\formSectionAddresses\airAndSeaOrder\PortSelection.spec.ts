import PortSelection from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortSelection.vue';
import { mount, VueWrapper } from '@vue/test-utils';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import SearchIcon from '@dfe/dfe-frontend-styles/assets/icons/search-16px.svg';

describe('Port component', () => {
  const wrapper: VueWrapper = mount(PortSelection);

  it('mounts', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should show the search icon in the search field, when it is empty', () => {
    const searchField = wrapper.findComponent(AutocompleteField);

    expect(searchField.props().menuIcon).toBe(SearchIcon);
  });

  it('should show NOT show the search icon in the search field, when it is NOT empty', async () => {
    const searchField = wrapper.findComponent(AutocompleteField);

    searchField.vm.$emit('update:modelValue', 'MUC');

    await wrapper.vm.$nextTick();

    expect(searchField.props().menuIcon).toBe('');
  });

  it('should NOT show a tooltip by default', async () => {
    await wrapper.setProps({ tooltip: 'test-tooltip', disabled: false });

    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('test-tooltip');
    expect(tooltipHTML).toContain('display: none;');
  });

  it('should show a tooltip if disabled', async () => {
    await wrapper.setProps({ tooltip: 'test-tooltip', disabled: true });

    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('test-tooltip');
  });
});
