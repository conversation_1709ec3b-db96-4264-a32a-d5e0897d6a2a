import { type ApiSecurityDataFn, createApi, requestOriginHeader } from '@/services/api';
import type { Api as BookApi } from '@dfe/dfe-book-api';
import type { Api as AddressApi } from '@dfe/dfe-address-api';
import type { Api as ConfigApi } from '@dfe/dfe-configserver-api-module';
import { i18n } from '@/plugins/i18n';
import type { Api as DynamicLabelApi } from '@dfe/dfe-dynamiclabel-api';

export type Api = {
  book: BookApi<ApiSecurityDataFn>;
  address: AddressApi<ApiSecurityDataFn>;
  config: ConfigApi<ApiSecurityDataFn>;
  dynamicLabel: DynamicLabelApi<ApiSecurityDataFn>;
};

export const useApi = (): { api: Api } => {
  const api = {
    book: createApi('Book', {
      getToken: () => new Promise((resolve) => resolve('token')),
      getLanguage: () => i18n.global.locale.value,
      getRequestOrigin: () => requestOriginHeader,
    }),
    address: createApi('Address', {
      getToken: () => new Promise((resolve) => resolve('token')),
      getLanguage: () => i18n.global.locale.value,
      getRequestOrigin: () => requestOriginHeader,
    }),
    config: createApi('Config', {
      getToken: () => new Promise((resolve) => resolve('token')),
      getLanguage: () => i18n.global.locale.value,
      getRequestOrigin: () => requestOriginHeader,
    }),
    dynamicLabel: createApi('DynamicLabel', {
      getToken: () => new Promise((resolve) => resolve('token')),
      getLanguage: () => i18n.global.locale.value,
    }),
  };
  return { api };
};
