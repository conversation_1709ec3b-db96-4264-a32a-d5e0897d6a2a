import { displayValueWithFallback } from '@/utils/form/valueWithFallback';

describe('displayValueWithFallback util', () => {
  it.each([
    ['test1', 'test1'],
    [11, '11'],
    [0, '0'],
    ['', ''],
    [undefined, '-'],
    [null, '-'],
  ])('displays value %s correctly', (value, expected) => {
    const formattedValue = displayValueWithFallback(value);
    expect(formattedValue).toBe(expected);
  });
});
