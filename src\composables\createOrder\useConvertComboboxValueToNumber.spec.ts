import useConvertComboboxValueToNumber from '@/composables/createOrder/useConvertComboboxValueToNumber';

describe('useCalcOrderLineVolume composable', () => {
  it('execute convert combobox value to number', () => {
    const inputExamples = ['42', { value: '42' }, null];
    const expectedResults = [42, 42, null];
    expect(useConvertComboboxValueToNumber(inputExamples[0])).toBe(expectedResults[0]);
    expect(useConvertComboboxValueToNumber(inputExamples[1])).toBe(expectedResults[1]);
    expect(useConvertComboboxValueToNumber(inputExamples[2])).toBe(expectedResults[2]);
  });
});
