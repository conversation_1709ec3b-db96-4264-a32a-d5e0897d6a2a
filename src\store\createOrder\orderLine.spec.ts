import { OrderLine4Store, useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { initPinia } from '../../../test/util/init-pinia';
import * as uuid from '@/utils/createUuid';
import { hsCodeOptions } from '@/mocks/fixtures/hsCodeOptions';
import { mockServer } from '@/mocks/server';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes } from '@/enums';
import { expect } from 'vitest';

const uuidSpy = vi.spyOn(uuid, 'createUuid');

describe('createOrderOrderLine store', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        hsCodeOptions,
      },
    });
  });

  beforeEach(() => {
    initPinia();
  });

  afterEach(() => {
    uuidSpy.mockClear();
  });

  it('adds a new order line', () => {
    const store = useCreateOrderOrderLineFormStore();

    uuidSpy.mockReturnValueOnce(1);
    store.addOrderLine();

    expect(store.orderLines).toHaveLength(2);
  });

  it('adds a new packing position and adds first empty order line to it', () => {
    const store = useCreateOrderOrderLineFormStore();

    uuidSpy.mockReturnValueOnce(1);
    store.addPackingPosition();

    expect(store.packingPositions).toHaveLength(1);
    expect(store.orderLines).toHaveLength(1);
    expect(store.orderLines[0].packingPositionId).toBe(1);
  });

  it('adds a new packing position with an empty order line', () => {
    const store = useCreateOrderOrderLineFormStore();
    store.orderLines[0].quantity = 1;

    uuidSpy.mockReturnValueOnce(1);
    store.addPackingPosition();

    expect(store.packingPositions).toHaveLength(1);
    expect(store.orderLines).toHaveLength(2);
    expect(store.orderLines[0].packingPositionId).not.toBe(1);
    expect(store.orderLines[1].packingPositionId).toBe(1);
  });

  it('adds a new packing position without order line if flag is set', () => {
    const store = useCreateOrderOrderLineFormStore();

    uuidSpy.mockReturnValueOnce(1);
    store.addPackingPosition(true);

    expect(store.packingPositions).toHaveLength(1);
    expect(store.orderLines).toHaveLength(1);
    expect(store.orderLines[0].packingPositionId).not.toBe(1);
  });

  it('skips adding changed order line to first packing position', () => {
    const store = useCreateOrderOrderLineFormStore();
    store.orderLines[0].length = 1;
    uuidSpy.mockReturnValueOnce(1);
    store.addPackingPosition(true);

    expect(store.packingPositions).toHaveLength(1);
    expect(store.orderLines).toHaveLength(1);
    expect(store.orderLines[0].packingPositionId).toBe(undefined);
  });

  it('removes an order line, keeping at least one empty item in store', () => {
    const store = useCreateOrderOrderLineFormStore();

    uuidSpy.mockReturnValueOnce(1);
    store.addOrderLine();

    expect(store.orderLines).toHaveLength(2);
    store.removeOrderLine(1);
    expect(store.orderLines).toHaveLength(1);

    store.removeOrderLine(0);
    expect(store.orderLines).toHaveLength(1);
  });

  it('removes a packing position', () => {
    const store = useCreateOrderOrderLineFormStore();

    uuidSpy.mockReturnValueOnce(1);
    store.addPackingPosition();

    uuidSpy.mockReturnValueOnce(3);
    store.addOrderLineToPackingPosition(1);

    expect(store.packingPositions).toHaveLength(1);
    expect(store.orderLines).toHaveLength(2);

    store.removePackingPosition(1);
    expect(store.packingPositions).toHaveLength(0);
    expect(store.orderLines).toHaveLength(1);
  });

  it('returns total value', () => {
    const store = useCreateOrderOrderLineFormStore();
    expect(store.totalValue).toBe(undefined);
  });

  it('returns initial total weight', () => {
    const store = useCreateOrderOrderLineFormStore();
    expect(store.totalWeight).toBe(0);
  });

  it('returns initial total volume', () => {
    const store = useCreateOrderOrderLineFormStore();
    expect(store.totalVolume).toBe(0);
  });

  it('returns total weight for all lines', () => {
    const store = useCreateOrderOrderLineFormStore();
    uuidSpy.mockReturnValueOnce(1);
    store.addOrderLine();

    store.orderLines[0].weight = 1;
    store.orderLines[1].weight = 2;

    expect(store.totalWeight).toBe(3);
  });

  it('returns initial total number of labels', () => {
    const store = useCreateOrderOrderLineFormStore();
    expect(store.numberOfLabels).toBe(0);
  });

  it('returns total number of all labels', () => {
    const store = useCreateOrderOrderLineFormStore();

    store.addOrderLine();

    store.orderLines[0].quantity = 1;
    store.orderLines[1].quantity = 2;

    expect(store.numberOfLabels).toBe(3);
  });

  it('returns total number of all lables for packing posiitons', () => {
    const store = useCreateOrderOrderLineFormStore();
    const orderStore = useCreateOrderFormStore();
    store.packagingAidPosition = true;
    orderStore.orderType = OrderTypes.RoadForwardingOrder;

    store.addOrderLineToPackingPosition();

    store.orderLines[0].quantity = 1;
    store.orderLines[1].quantity = 2;

    uuidSpy.mockReturnValueOnce(99);
    store.addPackingPosition();
    store.addOrderLineToPackingPosition(99);

    store.orderLines[2].quantity = 5;
    store.packingPositions[0].quantity = 3;

    expect(store.numberOfLabels).toBe(6);
  });

  it('returns total volume for all lines', () => {
    const store = useCreateOrderOrderLineFormStore();

    store.addOrderLine();

    store.orderLines[0].volume = 1;
    store.orderLines[1].volume = 2.345;

    expect(store.totalVolume).toBe(3.345);
  });

  it('returns an order line by local id', () => {
    const store = useCreateOrderOrderLineFormStore();

    store.addOrderLine();

    store.orderLines[0].content = 'First order line';
    store.orderLines[1].content = 'Second order line';

    expect(store.orderLines[0]?.content).toEqual('First order line');
    expect(store.orderLines[1].content).toEqual('Second order line');
  });

  it('returns unassigned and sorted order lines', () => {
    const store = useCreateOrderOrderLineFormStore();

    store.addOrderLine();
    store.addOrderLine();
    uuidSpy.mockReturnValueOnce(5);
    store.addPackingPosition();
    store.addOrderLineToPackingPosition(5);

    store.orderLines[1].number = 3;
    store.orderLines[2].number = 2;

    expect(store.getUnassignedOrderLines.length).toBe(3);
    expect(store.getUnassignedOrderLines[0].number).toBe(0);
    expect(store.getUnassignedOrderLines[1].number).toBe(2);
    expect(store.getUnassignedOrderLines[2].number).toBe(3);
  });

  it('update an order line', () => {
    const store = useCreateOrderOrderLineFormStore();
    uuidSpy.mockReturnValueOnce(1);
    store.addOrderLine();
    const updatedOrderLine = { localId: 1, quantity: 1 };

    store.updateOrderLine(updatedOrderLine as OrderLine4Store);

    expect(store.orderLines[1].quantity).toEqual(1);
  });
  it('adds a goods classifications item to a specified order line', () => {
    const store = useCreateOrderOrderLineFormStore();

    store.addOrderLine();

    store.addGoodsClassificationItem(uuidSpy.mock.results[0].value);

    expect(store.orderLines[0]?.goodsClassifications).toHaveLength(2);
    expect(store.orderLines[1]?.goodsClassifications).toHaveLength(1);
    store.addGoodsClassificationItem(uuidSpy.mock.results[1].value);
    expect(store.getOrderLine(uuidSpy.mock.results[1].value)?.goodsClassifications).toHaveLength(2);
  });

  it('removes a goods classifications item from a specified order line, keeping at least one empty item in store', () => {
    const store = useCreateOrderOrderLineFormStore();

    store.addOrderLine();

    store.addGoodsClassificationItem(uuidSpy.mock.results[0].value);
    store.addGoodsClassificationItem(uuidSpy.mock.results[1].value);

    expect(store.orderLines[0]?.goodsClassifications).toHaveLength(2);
    expect(store.orderLines[1]?.goodsClassifications).toHaveLength(2);

    store.removeGoodsClassificationItem(uuidSpy.mock.results[0].value, 1);
    expect(store.orderLines[0]?.goodsClassifications).toHaveLength(1);
    store.removeGoodsClassificationItem(uuidSpy.mock.results[0].value, 1);
    expect(store.orderLines[0]?.goodsClassifications).toHaveLength(1);

    expect(store.orderLines[1]?.goodsClassifications).toHaveLength(2);
  });

  it('returns a goods classifications item by order line local id and index', () => {
    const store = useCreateOrderOrderLineFormStore();
    const mockItem = { goods: '', loading: false, search: [] };

    store.addOrderLine();

    store.addGoodsClassificationItem(uuidSpy.mock.results[0].value);
    store.addGoodsClassificationItem(uuidSpy.mock.results[1].value);

    expect(store.orderLines[0].goodsClassifications?.[0]).toBeDefined();
    expect(store.orderLines[1].goodsClassifications?.[0]).toBeDefined();

    if (
      store.orderLines[0].goodsClassifications?.[0] &&
      store.orderLines[1].goodsClassifications?.[0]
    ) {
      store.orderLines[0].goodsClassifications[0].hsCode = { code: 'code100' };
      store.orderLines[1].goodsClassifications[0].hsCode = { code: 'code200' };
      store.orderLines[1].goodsClassifications[1].hsCode = { code: 'code300' };

      expect(store.getGoodsClassificationItem(uuidSpy.mock.results[0].value, 0)).toEqual({
        ...mockItem,
        hsCode: { code: 'code100' },
      });

      expect(store.getGoodsClassificationItem(uuidSpy.mock.results[1].value, 0)).toEqual({
        ...mockItem,
        hsCode: { code: 'code200' },
      });

      expect(store.getGoodsClassificationItem(uuidSpy.mock.results[1].value, 1)).toEqual({
        ...mockItem,
        hsCode: { code: 'code300' },
      });
    }
  });

  it('searches hs code options', async () => {
    const store = useCreateOrderOrderLineFormStore();
    const item = store.getGoodsClassificationItem(uuidSpy.mock.results[0].value, 0);
    expect(item?.loading).toBe(false);

    const response = store.searchHsCodeOptions(uuidSpy.mock.results[0].value, 0, '070190');
    expect(item?.loading).toBe(true);

    await response;

    expect(item?.search).toEqual(hsCodeOptions);
    expect(item?.loading).toBe(false);
  });

  it('should get orderLine with HsCode', () => {
    const store = useCreateOrderOrderLineFormStore();
    const formStore = useCreateOrderFormStore();

    formStore.orderType = OrderTypes.AirExportOrder;

    uuidSpy.mockReturnValueOnce(1);
    store.addOrderLine();

    store.orderLines[0].hsCodes = [{ hsCode: 'code100', goods: 'goods100' }];

    expect(store.orderLines[0]?.hsCodes).toEqual([
      {
        hsCode: 'code100',
        goods: 'goods100',
      },
    ]);
  });

  it.each([
    {
      key: 'initial values',
      manualNumberOfLabels: undefined,
      manualNumberOfLabelsOnLoad: 0,
      generatedSSccs: [],
      expectedDifference: 0,
      expectedDecreaseOnUpdate: false,
      expectedDecrease: false,
    },
    {
      key: 'empty value',
      manualNumberOfLabels: 0,
      manualNumberOfLabelsOnLoad: 0,
      generatedSSccs: [],
      expectedDifference: 0,
      expectedDecreaseOnUpdate: false,
      expectedDecrease: false,
    },
    {
      key: 'equal value',
      manualNumberOfLabels: 1,
      manualNumberOfLabelsOnLoad: 0,
      generatedSSccs: [''],
      expectedDifference: 0,
      expectedDecreaseOnUpdate: false,
      expectedDecrease: false,
    },
    {
      key: 'more ssccs',
      manualNumberOfLabels: 1,
      manualNumberOfLabelsOnLoad: 0,
      generatedSSccs: ['', ''],
      expectedDifference: 1,
      expectedDecreaseOnUpdate: true,
      expectedDecrease: true,
    },
    {
      key: 'more manualNumber',
      manualNumberOfLabels: 3,
      manualNumberOfLabelsOnLoad: 0,
      generatedSSccs: [''],
      expectedDifference: 2,
      expectedDecreaseOnUpdate: false,
      expectedDecrease: false,
    },
    {
      key: 'more manualNumberOnLoad',
      manualNumberOfLabels: 3,
      manualNumberOfLabelsOnLoad: 4,
      generatedSSccs: [''],
      expectedDifference: 2,
      expectedDecreaseOnUpdate: true,
      expectedDecrease: false,
    },
    {
      key: 'more manualNumberOnLoad and ssccs',
      manualNumberOfLabels: 3,
      manualNumberOfLabelsOnLoad: 4,
      generatedSSccs: ['', '', '', ''],
      expectedDifference: 1,
      expectedDecreaseOnUpdate: true,
      expectedDecrease: true,
    },
  ])(
    'computes getters for manualNumberOfLabels with $key correctly',
    ({
      manualNumberOfLabels,
      manualNumberOfLabelsOnLoad,
      generatedSSccs,
      expectedDifference,
      expectedDecreaseOnUpdate,
      expectedDecrease,
    }) => {
      const store = useCreateOrderOrderLineFormStore();
      store.manualNumberOfLabels = manualNumberOfLabels;
      store.manualNumberOfLabelsOnLoad = manualNumberOfLabelsOnLoad;
      store.generatedSSccs = generatedSSccs;

      expect(store.differenceInManualNumberOfLabels).toBe(expectedDifference);
      expect(store.hasManualNumberOfLabelsDecreasesOnUpdate).toBe(expectedDecrease);
      expect(store.hasManualNumberOfLabelsDecreased).toBe(expectedDecreaseOnUpdate);
    },
  );
});
