<template>
  <div>
    <div>
      <div class="d-inline-flex">
        <CheckboxField
          v-model="customsDeclarationRequired"
          v-data-test="'customs-declaration-field'"
          :label="t('labels.customs_declaration.text')"
          class="mt-1 custom-spacing"
          :required="customTypeValidationError || isCustomDeclarationRequired"
          :error-messages="
            customTypeValidationError && !customsDeclarationRequired
              ? t('labels.validation_input_required.text')
              : ''
          "
        />
      </div>

      <div class="indented-radioboxes mb-4">
        <VRadioGroup v-model="customsDeclarationExecutor" hide-details="auto" density="compact">
          <RadioField
            v-model="CustomsType.CUSTOMER"
            :disabled="!customsDeclarationRequired"
            :label="t('labels.by_customer.text')"
            :tooltip="customsDeclarationRadioGroupTooltip"
            class="mb-2"
          />
          <RadioField
            v-model="CustomsType.DACHSER"
            :disabled="!customsDeclarationRequired"
            :label="t('labels.by_dachser.text')"
            :tooltip="customsDeclarationRadioGroupTooltip"
          />
        </VRadioGroup>
      </div>
    </div>
    <div v-if="showFrostProtection">
      <CheckboxField
        v-model="frostProtectionRequired"
        v-data-test="'frost-protection'"
        class="d-inline-flex"
        :label="t('labels.frost_protection_required.text')"
        :disabled="isDisabledForPriceRelevantChanges"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import CheckboxField from '@/components/form/CheckboxField.vue';
import RadioField from '@/components/form/RadioField.vue';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { CustomsType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { DocumentTypes } from '@/enums';

const { t } = useI18n();

const createOrderFormAdditionalServicesStore = useCreateOrderFormAccountingAdditionalServices();
const { frostProtectionRequired, customsDeclarationRequired, customsDeclarationExecutor } =
  storeToRefs(createOrderFormAdditionalServicesStore);

const createOrderFormStore = useCreateOrderFormStore();
const { customTypeValidationError, isRoadForwardingOrder } = storeToRefs(createOrderFormStore);
const { isDisabledForPriceRelevantChanges } = storeToRefs(createOrderFormStore);

const { data: customerSettings } = useCustomerSettings();

const createOrderDocuments = useCreateOrderDocumentsStore();
const { documents } = storeToRefs(createOrderDocuments);

const showFrostProtection = computed(
  () => customerSettings.value?.frostProtection && isRoadForwardingOrder.value,
);

const isCustomDeclarationRequired = computed(() => {
  return documents.value.some(
    (document) =>
      document.documentType !== undefined && document.documentType !== DocumentTypes.EDN,
  );
});

const customsDeclarationRadioGroupTooltip = computed(() => {
  if (customsDeclarationRequired.value) {
    return;
  }
  return t('labels.available_if.text', {
    required: t('labels.customs_declaration.text'),
  });
});
</script>

<style lang="scss" scoped>
.indented-radioboxes {
  padding-left: 18px;
  margin-top: -2px;

  .v-input--selection-controls {
    margin-top: 0;
  }
}
</style>
<style lang="scss">
.custom-spacing {
  .v-messages {
    padding-left: 24px;
    padding-bottom: 10px;
  }
}
</style>
