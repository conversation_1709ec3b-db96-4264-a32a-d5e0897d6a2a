import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import type { Ref } from 'vue';
import { computed } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import isValidDate from '@/utils/isValidDate';
import { formatISO } from 'date-fns';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';

export const useCollectionTimeSlots = (collectionDate: Ref<Date | string>) => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { customerNumber, transportType, orderType } = storeToRefs(formStore);

  return useCustomerQuery(
    'collectionTimeSlots',
    () => {
      useCreateOrderFormCollectionAndDeliveryStore().collectionTimeSlot = null;
      return api.book.customers.getCustomerCollectionTimeSlots({
        // casting "as Date" is valid here because "isValidDate" in the enabled section makes sure it is a Date-Object
        collectionDate: formatISO(collectionDate.value as Date, {
          representation: 'date',
        }),
        customerNumber: customerNumber.value,
        customerSegment: transportType.value,
        orderType: orderType.value,
      });
    },
    {
      queryKey: [orderType, collectionDate],
      enabled: computed(
        () => !!(customerNumber.value && transportType.value && isValidDate(collectionDate.value)),
      ),
      placeholderData: [],
    },
  );
};
