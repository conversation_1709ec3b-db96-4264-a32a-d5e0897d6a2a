import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { FurtherAddressTypesList } from '@/enums';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import AddButton from '@/components/createOrder/AddButton.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import AdditionalAddresses from '@/components/createOrder/formSectionAddresses/forwardingOrder/AdditionalAddresses.vue';
import { afterEach } from 'vitest';

describe('Addresses ForwardingOrder AdditionalAddresses component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(AdditionalAddresses);

    const store = useCreateOrderDataStore();
    store.furtherAddressTypes = [
      {
        code: FurtherAddressTypesList.customsAgent,
        description: 'cb',
      },
      {
        code: FurtherAddressTypesList.deviatingFreightPayer,
        description: 're',
      },
      {
        code: FurtherAddressTypesList.DC,
        description: 'dc',
      },
      {
        code: FurtherAddressTypesList.AE,
        description: 'ae',
      },
      {
        code: FurtherAddressTypesList.importer,
        description: 'im',
      },
    ];
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('shows add buttons for all further address types', async () => {
    expect(wrapper.findAllComponents(AddButton).length).toBe(3);
  });

  it('shows AddressCard component after add buttons are clicked', async () => {
    const buttons = wrapper.findAllComponents(AddButton);
    for (const button of buttons) {
      await button.trigger('click');
    }
    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(AddressCard).length).toBe(3);
  });

  it('should show customs additional addresses', async () => {
    const addressStore = useCreateOrderAddressesStore();
    addressStore.isThirdCountryAddress = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(AddButton).length).toBe(5);
  });
});
