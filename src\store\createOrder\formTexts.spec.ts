import { initPinia } from '@test/util/init-pinia';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { TextTypes } from '@/enums';

describe('createOrderTexts store', () => {
  beforeAll(() => {
    initPinia();
  });

  it('returns texts without active', () => {
    const store = useCreateOrderTextsStore();
    expect(store.getTexts).toEqual([
      {
        id: undefined,
        textType: TextTypes.CollectionInstructions,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.DeliveryInstructions,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.GoodsDescription,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.InvoiceText,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.OtherInformation,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.SpecialRegulation,
        value: undefined,
        active: false,
      },
    ]);
  });
});
