import type { TermType } from '@/types/createOrder';
import type { IncoTerm } from '@dfe/dfe-book-api';
import { OrderType, Segment } from '@dfe/dfe-book-api';
import { defineStore, storeToRefs } from 'pinia';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderAddressesStore } from './formAddresses';
import { useConfigStore } from '@/store/config';
import { HandOverSelection } from '@/types/hand-over';
import { computed } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';

export interface FormTermsState {
  termType: TermType | null;
  selectedIncoTerm: IncoTerm | null;
}

const formTerms: FormTermsState = {
  termType: null,
  selectedIncoTerm: null,
};

export const useTermStore = defineStore('formTerms', {
  state: (): FormTermsState => ({ ...formTerms }),
  getters: {
    incoTermsOptions(): IncoTerm[] {
      const dataStore = useCreateOrderDataStore();
      const { incoTerms } = storeToRefs(dataStore);

      const validIncoTermCodes = this.validIncoTermCodes ?? [];
      const filteredIncoTerms = incoTerms.value.filter(
        (item: IncoTerm) =>
          (item.code && validIncoTermCodes.includes(item.code)) ||
          (item.dachserCode && validIncoTermCodes.includes(item.dachserCode)),
      );

      return filteredIncoTerms.map((term: IncoTerm) => ({
        id: term.id,
        code: term.code,
        dachserCode: term.dachserCode,
        label: `${term.label}`,
        description: term.description,
      }));
    },
    fromAddress() {
      const { shipperHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());
      const type = computed(() => shipperHandOverSelection.value.selection);
      return {
        type: type.value,
        isSet:
          type.value === HandOverSelection.default ||
          type.value === HandOverSelection.alternateAddress,
      };
    },
    toAddress() {
      const { consigneeHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());
      const type = computed(() => consigneeHandOverSelection.value.selection);
      return {
        type: type.value,
        isSet:
          type.value === HandOverSelection.default ||
          type.value === HandOverSelection.alternateAddress,
      };
    },
    validAirIncoTermCodes() {
      const { incoterms } = useConfigStore();
      const { orderType } = storeToRefs(useCreateOrderFormStore());

      if (this.fromAddress.isSet && this.toAddress.isSet) {
        // From address to address
        return orderType.value !== OrderType.AirImportOrder
          ? incoterms?.air?.export?.from_address?.to_address
          : incoterms?.air?.import?.from_address?.to_address;
      } else if (this.fromAddress.type === HandOverSelection.port && this.toAddress.isSet) {
        // From airport to address
        return orderType.value !== OrderType.AirImportOrder
          ? incoterms?.air?.export?.from_port?.to_address
          : incoterms?.air?.import?.from_port?.to_address;
      } else if (this.fromAddress.isSet && this.toAddress.type === HandOverSelection.port) {
        // From address to airport on export
        return orderType.value !== OrderType.AirImportOrder
          ? incoterms?.air?.export?.from_address?.to_port
          : [];
      }
    },
    validSeaIncoTermCodes() {
      const { incoterms } = useConfigStore();
      const { orderType } = storeToRefs(useCreateOrderFormStore());

      if (this.fromAddress.isSet && this.toAddress.isSet) {
        // From address to address
        return orderType.value !== OrderType.SeaImportOrder
          ? incoterms?.sea?.export?.from_address?.to_address
          : incoterms?.sea?.import?.from_address?.to_address;
      } else if (this.fromAddress.type === HandOverSelection.port && this.toAddress.isSet) {
        // From seaport to address
        return orderType.value !== OrderType.SeaImportOrder
          ? incoterms?.sea?.export?.from_port?.to_address
          : incoterms?.sea?.import?.from_port?.to_address;
      } else if (this.fromAddress.isSet && this.toAddress.type === HandOverSelection.port) {
        // From address to seaport on export
        return orderType.value !== OrderType.SeaImportOrder
          ? incoterms?.sea?.export?.from_address?.to_port
          : incoterms?.sea?.import?.from_address?.to_port;
      }
    },
    validIncoTermCodes(): string[] | undefined {
      const incoTermsConfigStore = useConfigStore();
      const { incoterms } = incoTermsConfigStore;
      const formStore = useCreateOrderFormStore();
      const { transportType } = storeToRefs(formStore);

      if (
        !this.fromAddress.type ||
        !this.toAddress.type ||
        !incoterms ||
        !Object.keys(incoterms ?? {}).length
      )
        return [];

      if (transportType.value === Segment.AIR) {
        return this.validAirIncoTermCodes;
      } else if (transportType.value === Segment.SEA) {
        return this.validSeaIncoTermCodes;
      }
    },
  },
});
