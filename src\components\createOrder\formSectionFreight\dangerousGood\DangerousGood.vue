<template>
  <VExpansionPanels
    class="mt-5 position__header section-freight-sizes"
    :static="true"
    :model-value="activePanels"
  >
    <VExpansionPanel :eager="true">
      <VExpansionPanelTitle
        class="border-b-sm border-opacity pa-4"
        :class="hasError ? 'panel-errors' : 'pannel-info'"
      >
        <template #default>
          <div class="d-flex justify-space-between align-center w-100">
            <div class="d-flex align-center">
              <span class="text-h4 order-0">{{ getTitle() }}</span>
              <div v-if="hasError" class="order-1 ml-2">
                <DfeIconButton
                  :size="ComponentSize.SMALL"
                  :color="ColorVariants.NEUTRAL"
                  :tooltip="t('labels.invalid_input.text')"
                  :filled="false"
                >
                  <MaterialSymbol size="16" color="red" class="mr-1">
                    <ErrorIcon />
                  </MaterialSymbol>
                </DfeIconButton>
              </div>
            </div>
            <div class="pr-4 order-2">
              <DfeIconButton
                :size="ComponentSize.DEFAULT"
                :color="ColorVariants.NEUTRAL"
                :tooltip="t('labels.delete_label.text')"
                :filled="false"
                @click="deleteDangerousGoods"
              >
                <MaterialSymbol size="24" :class="{ 'panel-errors': hasError }">
                  <DeleteIcon />
                </MaterialSymbol>
              </DfeIconButton>
              <ConfirmPrompt
                v-model="showConfirmDeleteDG"
                :headline="t('messages.id7252.text')"
                :confirm-text="t('labels.delete_label.text')"
                :cancel-text="t('labels.cancel_label.text')"
                :size="'sm'"
                @confirm="deleteDangerousGoods"
                @cancel="setShowConfirmDeleteDG(false)"
                @close="setShowConfirmDeleteDG(false)"
              >
                <h5 class="text-body-2 text-grey-darken-4">
                  {{ t('messages.id7253.text') }}
                </h5>
              </ConfirmPrompt>
            </div>
          </div>
        </template>
        <template #actions="{ expanded }">
          <IconButton v-if="!expanded">
            <ChevronDownIcon />
          </IconButton>
          <IconButton v-else>
            <ChevronUpIcon />
          </IconButton>
        </template>
      </VExpansionPanelTitle>
      <VExpansionPanelText>
        <div
          v-for="(dangerousGood, i) in dangerousGoods"
          :key="dangerousGood.id"
          class="border-b-sm border-opacity"
          :class="{ 'mt-4': i === 0, 'mb-4': i !== dangerousGoods.length - 1 }"
        >
          <div class="mb-4">
            <component
              :is="currentComponent(dangerousGood.dangerousGoodType)"
              :ref="(el: unknown) => setChildRef(el, i)"
              :model-value="dangerousGood"
              :computed-line-count="computedLineCount + '.' + (i + 1)"
              @delete-dangerous-good="deleteDangerousGood(dangerousGood.localId)"
              @update:model-value="updateDangerousGood($event, i)"
            />
          </div>
        </div>
        <div>
          <UnNumberDialog
            button-class="mr-3 mt-4"
            @select-item="addDangerousGood(DangerousGoodType.UnNumber, $event)"
          />
          <AddButton
            v-data-test="'add-limited-quantities'"
            class="mr-3 mt-4"
            :label="t('labels.limited_quantities.text')"
            variant="text"
            @add-new-item="addDangerousGood(DangerousGoodType.LQ)"
          />
          <AddButton
            v-data-test="'add-excepted-quantitites'"
            class="mr-3 mt-4"
            :label="t('labels.excepted_quantities.text')"
            variant="text"
            @add-new-item="addDangerousGood(DangerousGoodType.EQ)"
          />
        </div>
      </VExpansionPanelText>
    </VExpansionPanel>
  </VExpansionPanels>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import AddButton from '../../AddButton.vue';
import EQ from './EQDangerousGood.vue';
import { ref, watchEffect } from 'vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import {
  ADRDangerousGood4Store,
  EQDangerousGood4Store,
  LQDangerousGood4Store,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import { DangerousGoodType } from '@/enums';
import LQ from './LQDangerousGood.vue';
import UnNumber from './UnNumberDangerousGood.vue';
import { useValidationDataStore } from '@/store/validation';
import { storeToRefs } from 'pinia';
import ErrorIcon from '@dfe/dfe-frontend-styles/assets/icons/error-16px.svg';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import UnNumberDialog from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberDialog.vue';
import { DangerousGoodDataItem } from '@dfe/dfe-book-api';
import ChevronDownIcon from '@dfe/dfe-frontend-styles/assets/icons/chevron_down-24px.svg';
import ChevronUpIcon from '@dfe/dfe-frontend-styles/assets/icons/chevron_up-24px.svg';
import IconButton from '@/components/base/buttons/IconButton.vue';

const { t } = useI18n();
const activePanels = ref([0]);
const emit = defineEmits(['deleteDangerousGoods']);
const { formValidationSectionsRoad: valid } = storeToRefs(useValidationDataStore());
const hasError = ref(false);
const childRefs = ref<(typeof EQ | typeof LQ | typeof UnNumber | null)[]>([]);
const showConfirmDeleteDG = ref(false);

interface Props {
  dangerousGoods: (LQDangerousGood4Store | EQDangerousGood4Store | ADRDangerousGood4Store)[];
  computedLineCount: string;
  localId: number;
}
const props = defineProps<Props>();
const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();

function getTitle() {
  return `${t('labels.dangerous_goods.text')} ( ${t('labels.order_line_header.text')} ${props.computedLineCount} )`;
}

function currentComponent(type: 'ADRDangerousGood' | 'LQDangerousGood' | 'EQDangerousGood') {
  switch (type) {
    case 'EQDangerousGood':
      return EQ;
    case 'LQDangerousGood':
      return LQ;
    case 'ADRDangerousGood':
      return UnNumber;
    default:
      return '';
  }
}

const setShowConfirmDeleteDG = (value: boolean) => {
  showConfirmDeleteDG.value = value;
};

function addDangerousGood(type: DangerousGoodType, dangerousGoodDataItem?: DangerousGoodDataItem) {
  createOrderOrderLineFormStore.addDangerousGood(type, props.localId, dangerousGoodDataItem);
}

const deleteDangerousGoods = () => {
  if (!showConfirmDeleteDG.value && props.dangerousGoods.length > 0) {
    showConfirmDeleteDG.value = true;
    return;
  }
  showConfirmDeleteDG.value = false;
  emit('deleteDangerousGoods');
};

function deleteDangerousGood(localId: number) {
  createOrderOrderLineFormStore.deleteDangerousGood(props.localId, localId);
}

function updateDangerousGood(
  dangerousGood: LQDangerousGood4Store | EQDangerousGood4Store | ADRDangerousGood4Store,
  index: number,
) {
  createOrderOrderLineFormStore.updateDangerousGood(props.localId, index, dangerousGood);
}

const setChildRef = (el: unknown, index: number) => {
  childRefs.value[index] = el as typeof EQ | typeof LQ | typeof UnNumber | null;
};

const validateAll = async () => {
  const results = await Promise.all(
    childRefs.value.map((child, i: number) => {
      if (child?.triggerValidation) {
        return child.triggerValidation();
      } else {
        console.warn(
          `The child component at index ${i} does not expose a triggerValidation method.`,
        );
        return true;
      }
    }),
  );
  hasError.value = results.some((result: boolean) => result === false);
};

watchEffect(() => {
  if (!valid.value.roadOrderFreight) {
    validateAll();
  }
});
</script>

<style lang="scss">
@use '@/styles/components/freightLineSizes';
</style>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfe-variables;
@use '@/styles/settings';
@use '@/styles/base' as base;
@use 'sass:map';

.position__header {
  background-color: dfe-variables.$color-base-grey-50;
  border: 1px solid dfe-variables.$color-base-grey-400;
  border-radius: base.space(1);
  border-top-right-radius: base.space(1);
}

.position__body {
  border: 1px solid dfe-variables.$color-base-grey-400;
  border-top: none;
  border-bottom-left-radius: base.space(1);
  border-bottom-right-radius: base.space(1);
}
</style>

<style lang="scss">
@use '@/styles/base' as base;

.v-expansion-panel-text__wrapper {
  padding: 0 base.space(4) base.space(4) !important;
}
.v-expansion-panel__shadow {
  box-shadow: none !important;
}
.panel-errors {
  background-color: var(--color-base-red-100);
  .v-btn.icon-only:not(.v-btn--disabled):hover .v-btn__overlay {
    opacity: 0;
  }
}
.pannel-info {
  background-color: var(--color-base-grey-50);
}
</style>
