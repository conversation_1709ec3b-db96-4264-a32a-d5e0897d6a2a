import { sortContacts } from './sortContactData';
import { ContactData } from '@dfe/dfe-address-api';

describe('sortContacts function', () => {
  it('sorts contacts by isMainContact and then by name alphabetically', () => {
    const contacts: ContactData[] = [
      { name: '<PERSON>', isMainContact: false },
      { name: '<PERSON>', isMainContact: true },
      { name: '<PERSON>', isMainContact: false },
      { name: '<PERSON>', isMainContact: true },
    ];
    const sortedContacts = sortContacts(contacts);
    expect(sortedContacts.map((contact) => contact.name)).toEqual([
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
    ]);
  });

  it('handles an empty array without errors', () => {
    const contacts: ContactData[] = [];
    const sortedContacts = sortContacts(contacts);
    expect(sortedContacts).toEqual([]);
  });

  it('sorts correctly when all contacts are main contacts', () => {
    const contacts: ContactData[] = [
      { name: '<PERSON>', isMainContact: true },
      { name: '<PERSON>', isMainContact: true },
      { name: '<PERSON>', isMainContact: true },
    ];
    const sortedContacts = sortContacts(contacts);
    expect(sortedContacts.map((contact) => contact.name)).toEqual(['Alice', 'Bob', 'Charlie']);
  });

  it('sorts correctly when no contacts are main contacts', () => {
    const contacts: ContactData[] = [
      { name: 'Charlie', isMainContact: false },
      { name: 'Alice', isMainContact: false },
      { name: 'Bob', isMainContact: false },
    ];
    const sortedContacts = sortContacts(contacts);
    expect(sortedContacts.map((contact) => contact.name)).toEqual(['Alice', 'Bob', 'Charlie']);
  });

  it('maintains the order of contacts with the same name and isMainContact status', () => {
    const contacts: ContactData[] = [
      { name: 'Alice', isMainContact: true },
      { name: 'Alice', isMainContact: true },
      { name: 'Charlie', isMainContact: true },
      { name: 'Charlie', isMainContact: true },
    ];
    const sortedContacts = sortContacts(contacts);
    expect(sortedContacts).toEqual(contacts);
  });
});
