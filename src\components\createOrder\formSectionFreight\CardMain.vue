<template>
  <SectionCard v-data-test="'section-freight'">
    <template #headline>{{ t('labels.order_line_title.text') }}</template>
    <FullContainerLoadSwitcher
      v-if="getFullContainerLoadsAllowed"
      :model-value="isFullContainerLoad != undefined ? isFullContainerLoad : false"
    />
    <div
      v-if="isAirOrder || isSeaLclOrder"
      v-data-test="'stackable-and-shock-sensitive'"
      class="d-flex flex-wrap align-start mb-6"
    >
      <CheckboxField
        v-model="stackable"
        v-data-test="'freight-stackable'"
        :label="t('labels.stackable_label.text')"
        class="mr-4"
        :data-test-details="'bo-freight-stackable-' + stackable"
      />
      <CheckboxField
        v-model="shockSensitive"
        v-data-test="'freight-shock-sensitive'"
        :label="t('labels.shock_sensitive.text')"
        :data-test-details="'bo-freight-shock-sensitive-' + shockSensitive"
      />
    </div>

    <div v-if="!!orderLinesToShow.length" class="mt-4">
      <OrderLine
        v-for="({ localId }, i) in orderLinesToShow"
        :key="`orderLine${localId}`"
        :model-value="orderLinesToShow[i]"
        :local-id="localId"
        :optional-mode="false"
        :is-weight-required="i === 0"
        :line-counter="i + 1"
        @update:model-value="updateOrderLine($event)"
      />
    </div>

    <AddLineButtons
      v-if="!!orderLinesToShow.length"
      v-data-test="'freight-add-order-line-buttons'"
      :disable-buttons="isDisabledForPriceRelevantChanges"
      :show-order-line="true"
      :show-packing-position="!packingPositions.length && getPackingPositionsAllowed"
      :show-full-container-load="
        !fullContainerLoads.length && getFullContainerLoadsAllowed && isFullContainerLoad
      "
      @add-order-line="addNewOrderLine"
      @add-packing-position="addNewPackingPosition"
      @add-full-container-load="addNewFullContainerLoad"
    />

    <div v-if="!!packingPositions.length && getPackingPositionsAllowed">
      <PackingPosition
        v-for="({ localId }, i) in packingPositions"
        :key="`packingPosition${localId}`"
        v-model="packingPositions[i]"
        :local-id="localId"
        :line-counter="i + 1"
      />
    </div>

    <div v-if="!!fullContainerLoads.length && getFullContainerLoadsAllowed">
      <FullContainerLoad
        v-for="({ localId }, i) in fullContainerLoads"
        :key="`fullContainerLoad${localId}`"
        v-model="fullContainerLoads[i]"
        :local-id="localId"
        :line-counter="i + 1"
      />
    </div>

    <AddLineButtons
      v-if="!!packingPositions.length && getPackingPositionsAllowed"
      v-data-test="'freight-add-packing-position-buttons'"
      :disable-buttons="isDisabledForPriceRelevantChanges"
      :show-order-line="!orderLinesToShow.length"
      :show-packing-position="true"
      :show-full-container-load="false"
      @add-order-line="addNewOrderLine"
      @add-packing-position="addNewPackingPosition"
    />

    <AddLineButtons
      v-if="!!fullContainerLoads.length && getFullContainerLoadsAllowed"
      v-data-test="'freight-add-full-container-load-buttons'"
      :disable-buttons="isDisabledForPriceRelevantChanges"
      :show-divider="false"
      :show-order-line="false"
      :show-packing-position="false"
      :show-full-container-load="true"
      @add-full-container-load="addNewFullContainerLoad"
    />

    <OrderSummary />
  </SectionCard>
</template>

<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import AddLineButtons from '@/components/createOrder/formSectionFreight/AddLineButtons.vue';
import OrderLine from '@/components/createOrder/formSectionFreight/OrderLine.vue';
import OrderSummary from '@/components/createOrder/formSectionFreight/OrderSummary.vue';
import PackingPosition from '@/components/createOrder/formSectionFreight/packingPosition/PackingPosition.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import FullContainerLoad from '@/components/createOrder/formSectionFreight/fullContainerLoad/FullContainerLoad.vue';
import FullContainerLoadSwitcher from '@/components/createOrder/formSectionFreight/fullContainerLoad/FullContainerLoadSwitcher.vue';

const { t } = useI18n();

const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
const {
  orderLines,
  getUnassignedOrderLines,
  packingPositions,
  fullContainerLoads,
  isFullContainerLoad,
  shockSensitive,
  stackable,
  getPackingPositionsAllowed,
  getFullContainerLoadsAllowed,
  isSeaLclOrder,
} = storeToRefs(createOrderOrderLineFormStore);
const { updateOrderLine } = createOrderOrderLineFormStore;
const createOrderFormStore = useCreateOrderFormStore();
const { isAirOrder, isDisabledForPriceRelevantChanges } = storeToRefs(createOrderFormStore);

const orderLinesToShow = computed(() => {
  if (getPackingPositionsAllowed.value || getFullContainerLoadsAllowed.value) {
    return getUnassignedOrderLines.value;
  }

  return orderLines.value;
});
const addNewOrderLine = () => {
  createOrderOrderLineFormStore.addOrderLine();
};

const addNewPackingPosition = () => {
  createOrderOrderLineFormStore.addPackingPosition();
};

const addNewFullContainerLoad = () => {
  createOrderOrderLineFormStore.addFullContainerLoad();
};
</script>
