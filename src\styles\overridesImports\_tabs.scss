@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
[dfe-book-frontend] {
:deep() {
  .tab-item-transition :deep(&-enter-active) {
    transition: vars.$transition-tab-item;
  }

  .tab-item-transition :deep(&-enter-to) {
    opacity: 1;
  }

  .tab-item-transition :deep(&-leave-active) {
    position: absolute;
    width: 100%;
    opacity: 1;
  }

  .tab-item-transition :deep(&-leave-to) {
    opacity: 0;
  }
}
}