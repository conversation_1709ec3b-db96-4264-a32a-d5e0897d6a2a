import { fileURLToPath, URL } from 'node:url';

import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify';
import svgLoader from 'vite-svg-loader';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import autoprefixer from 'autoprefixer';
import prefixer from 'postcss-prefix-selector';
import path from 'path';
import _ from 'lodash';
import federation from '@originjs/vite-plugin-federation';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import vueDevTools from 'vite-plugin-vue-devtools';

const { name, dependencies } = require('./package.json');

loadEnv(process.env.NODE_ENV ?? '', process.cwd());

const shared = Object.entries(dependencies).reduce((shared, [name, version]) => {
  shared[name] = { requiredVersion: version };
  return shared;
}, {});
delete shared.pinia;
delete shared.vue;
delete shared.vuetify;
delete shared['vue-i18n'];
delete shared['@vueuse/core'];
delete shared['sanitize-html'];
delete shared['@tanstack/vue-query'];
delete shared['@dfe/dfe-frontend-composables'];
delete shared['@dfe/dfe-frontend-shared-components'];

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const buildProd = command === 'build' && mode === 'production';
  const isDevelopment = mode === 'development';
  const buildDev = command === 'build' && isDevelopment;
  const devRoot = path.join(__dirname, 'src/dev/');

  return {
    root: isDevelopment ? devRoot : undefined,
    build: {
      target: 'es2022',
      emptyOutDir: true,
      outDir: path.join(__dirname, 'dist'),
      assetsDir: '',
      minify: true,
      rollupOptions: {
        ...(buildDev
          ? {
              input: {
                main: path.resolve(devRoot, 'index.html'),
                labelprint: path.resolve(devRoot, 'labelprint/index.html'),
              },
            }
          : {}),
      },
    },
    preview: {
      port: 3001,
    },
    plugins: [
      vue({
        template: { transformAssetUrls },
        script: {
          defineModel: true,
        },
      }),
      vueDevTools(),
      // https://github.com/vuetifyjs/vuetify-loader/tree/master/packages/vite-plugin
      vuetify({
        autoImport: true,
        styles: {
          configFile: path.resolve(__dirname, 'src/styles/settings.scss'),
        },
      }),
      // https://vue-i18n.intlify.dev/guide/advanced/optimization.html
      VueI18nPlugin({ runtimeOnly: false }),
      // https://github.com/jpkleemans/vite-svg-loader
      svgLoader({
        defaultImport: 'component',
        svgo: true,
        svgoConfig: {
          plugins: [
            {
              name: 'addClassesToSVGElement',
              params: {
                classNames: ['icon'],
              },
            },
          ],
        },
      }),
      ...(buildProd
        ? [
            cssInjectedByJsPlugin({
              jsAssetsFilterFunction: (outputChunk) => {
                return outputChunk.fileName === 'remoteEntry.js';
              },
            }),
            federation({
              name: _.camelCase(name),
              filename: 'remoteEntry.js',
              exposes: {
                './create-order': './src/bootstrap-create-order.ts',
                './label-print': './src/bootstrap-label-print.ts',
              },
              shared,
            }),
          ]
        : []),
    ],
    define: { 'process.env': {} },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@test': fileURLToPath(new URL('./test', import.meta.url)),
      },
      extensions: ['.js', '.json', '.jsx', '.mjs', '.ts', '.tsx', '.vue'],
    },
    server: {
      port: 8080,
    },
    css: {
      postcss: {
        plugins: [
          ...(buildProd
            ? [
                prefixer({
                  prefix: `[${name}]`,
                  transform(prefix, selector, prefixedSelector) {
                    let result = prefixedSelector;

                    if (selector.startsWith('html') || selector.startsWith('body')) {
                      result = prefix + selector.substring(4);
                    } else if (selector.startsWith(prefix) || selector.startsWith(':deep')) {
                      result = selector;
                    }
                    return result;
                  },
                }),
              ]
            : []),
          autoprefixer({}),
        ],
      },
    },
  };
});
