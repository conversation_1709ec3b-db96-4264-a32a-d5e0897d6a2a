<template>
  <SectionCard v-if="airDeliveryProducts.length">
    <template #headline>{{ t('labels.products_label.text') }}</template>
    <SelectionMandatoryLabel />
    <VRow v-if="!formValidationSectionsAir.airOrderProducts">
      <VCol cols="12" xl="8">
        <DfeBanner type="error" class="my-4">
          <span class="text-h5">{{ t('labels.select_option.text') }}</span>
        </DfeBanner>
      </VCol>
    </VRow>
    <ProductSelection
      v-model="selectedAirDeliveryProduct"
      :products="airDeliveryProducts"
      :is-disabled="isAirOrderFromQuote"
      required
    />
  </SectionCard>
</template>
<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import SelectionMandatoryLabel from '@/components/base/SelectionMandatoryLabel.vue';
import ProductSelection from '@/components/createOrder/formSectionProducts/ProductSelection.vue';
import { useAirDeliveryProducts } from '@/composables/data/useAirDeliveryProducts';
import { storeToRefs } from 'pinia';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useValidationDataStore } from '@/store/validation';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useI18n } from 'vue-i18n';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';

const { t } = useI18n();
const { airDeliveryProducts } = useAirDeliveryProducts();
const { selectedAirDeliveryProduct } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());
const { formValidationSectionsAir } = storeToRefs(useValidationDataStore());
const { isAirOrderFromQuote } = storeToRefs(useCreateOrderFormStore());
</script>
