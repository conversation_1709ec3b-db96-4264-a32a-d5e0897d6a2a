<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VMenu
      v-model="menu"
      class="date-picker"
      :close-on-content-click="false"
      transition="scale-transition"
      location="top"
      :attach="$appRoot"
      scroll-strategy="close"
      min-width="auto"
      :target="textField"
      @update:model-value="onMenuChange"
    >
      <template #activator="{ props: menuProps }">
        <VTextField
          :id="id"
          ref="textField"
          :model-value="textValue"
          single-line
          hide-details="auto"
          :placeholder="textFieldPlaceholder"
          :min="min"
          :max="max"
          :rules="validationRules"
          validate-on="lazy blur"
          :disabled="disabled"
          @update:model-value="onTextValueInput"
          @update:focused="onTextValueBlur"
          @keydown.enter="textField?.blur()"
        >
          <template #append-inner>
            <VIconBtn size="small" class="mr-n4" v-bind="menuProps" @click="textField?.blur()">
              <CalendarMonthIcon />
            </VIconBtn>
          </template>
        </VTextField>
      </template>
      <VLocaleProvider :locale="locale">
        <VDatePicker
          v-if="menu"
          :model-value="datePickerValue"
          :min="min"
          :max="max"
          :disabled="disabled"
          no-title
          hide-header
          mode-icon="$dropdown"
          input-mode="calendar"
          color="primary"
          elevation="24"
          :cancel-text="$t('labels.cancel_label.text')"
          @update:model-value="onDatePickerChange"
          @click:cancel="closeDatePicker"
          @keydown.enter.stop
        />
      </VLocaleProvider>
    </VMenu>
  </div>
</template>

<script setup lang="ts">
import useParsedDate from '@/composables/dateTimeUtilities/useParsedDate';
import { useFormattedDate } from '@/composables/dateTimeUtilities/useTimeFromDate';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { createUuid } from '@/utils/createUuid';
import isValidDate from '@/utils/isValidDate';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import CalendarMonthIcon from '@dfe/dfe-frontend-styles/assets/icons/calendar_month-16px.svg';
import { endOfDay } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { VTextField } from 'vuetify/components';

interface Props {
  label?: string;
  append?: string;
  required?: boolean;
  min?: string;
  max?: string;
  disabled?: boolean;
}

const props = defineProps<Props>();
const dateModel = defineModel<Date | string>();

const { t } = useI18n();
const id = `date-picker-${createUuid()}`;
const { dateFormat, dateFormatPlaceholder, locale } = usePreferences();
const menu = ref(false);
const textField = ref<VTextField>();

const textFieldPlaceholder = computed(() => {
  const translation = t(
    `labels["dateformat_${dateFormatPlaceholder.value.replaceAll('/', '_')}"].text`,
  );

  // We want to ensure that the translation was successful, otherwise the user would see the i18n key as a placeholder.
  if (!translation.startsWith('labels')) {
    return translation;
  }
  return dateFormatPlaceholder.value;
});

const validationRules = computed(() => [
  ...(props.required && !menu.value ? [useValidationRules.required] : []),
  useValidationRules.date(dateFormat.value),
  ...(props.min && props.max
    ? [useValidationRules.dateRange(dateModel.value ?? '', dateFormat.value, props.min, props.max)]
    : []),
]);

const textInput = ref<Date | string>('');

const textValue = computed(() => {
  return dateModel.value && isValidDate(new Date(dateModel.value))
    ? useFormattedDate(dateModel.value, dateFormat.value)
    : dateModel.value;
});

const onTextValueInput = (value: string) => {
  if (!value) {
    dateModel.value = '';
  }
  textInput.value = useParsedDate(value, dateFormat.value);
};

const onTextValueBlur = (focus: boolean) => {
  if (!focus && textInput.value) {
    dateModel.value = textInput.value;
  }
};

const datePickerValue = computed(() => {
  return dateModel.value && isValidDate(dateModel.value) ? new Date(dateModel.value) : new Date();
});

const closeDatePicker = () => {
  menu.value = false;
};

const onDatePickerChange = (value: Date) => {
  textInput.value = '';
  dateModel.value = toZonedTime(endOfDay(value), 'UTC');
  closeDatePicker();
  textField.value?.validate();
  textField.value?.focus();
  textField.value?.blur();
};

const onMenuChange = (value: boolean) => {
  if (value) {
    textField.value?.resetValidation();
  }
};
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables';

:deep(.disable-year) .v-date-picker-header__value .accent--text > button {
  pointer-events: none;
}

.v-btn--variant-plain {
  opacity: 1;
}
</style>
