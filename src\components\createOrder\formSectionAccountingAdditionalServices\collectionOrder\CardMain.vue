<template>
  <SectionCard>
    <template #headline>
      {{ t('labels.accountingAndAdditionalServices.text') }}
    </template>

    <VRow>
      <!-- Freight Payer SelectField -->
      <VCol cols="12" md="4" xl="5">
        <TooltipWithSlot
          :show-tooltip="!isDifferentConsigneeAddress"
          :tooltip-props="{
            location: 'right',
            maxWidth: 240,
            text: t('messages.id7158.text'),
          }"
        >
          <template #content>
            <SelectField
              ref="freightPayersSelect"
              v-model="freightPayer"
              :items="freightPayers"
              item-text="description"
              item-value="code"
              :label="t('labels.freight_payer.text')"
              :placeholder="t('labels.select_option.text')"
              :required="true"
              :disabled="!isDifferentConsigneeAddress"
            />
          </template>
        </TooltipWithSlot>
      </VCol>

      <!-- Additional Services -->
      <v-col cols="12" md="6" xl="5">
        <div class="text-label-3 mb-4">
          {{ t('labels.additionalServices.text') }}
        </div>
        <AdditionalServices />
      </v-col>
    </VRow>
  </SectionCard>
</template>

<script setup lang="ts">
import SelectField from '@/components/form/SelectField.vue';
import { computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import SectionCard from '@/components/base/SectionCard.vue';
import { useI18n } from 'vue-i18n';
import AdditionalServices from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AdditionalServices.vue';
import { FreightPayerType } from '@dfe/dfe-book-api';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import TooltipWithSlot from '@/components/base/tooltips/TooltipWithSlot.vue';

const addressStore = useCreateOrderAddressesStore();

const { isDifferentConsigneeAddress } = storeToRefs(addressStore);

const createOrderFormAccountingAdditionalServices =
  useCreateOrderFormAccountingAdditionalServices();
const { freightPayer } = storeToRefs(createOrderFormAccountingAdditionalServices);

const { t } = useI18n();

const freightPayers = computed(() => {
  return [
    {
      code: FreightPayerType.Principal,
      description: `${t('labels.principal_title.text')}`,
    },
    {
      code: FreightPayerType.Consignee,
      description: `${t('labels.consignee_title.text')}`,
    },
  ];
});

watch(isDifferentConsigneeAddress, (newValue) => {
  if (!newValue) {
    freightPayer.value = FreightPayerType.Principal;
  }
});
</script>
