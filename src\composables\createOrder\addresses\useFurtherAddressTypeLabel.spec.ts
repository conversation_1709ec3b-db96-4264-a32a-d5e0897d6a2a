import useFurtherAddressTypeLabel from '@/composables/createOrder/addresses/useFurtherAddressTypeLabel';
import { i18n } from '@/plugins/i18n';

describe('useFurtherAddressTypeLabel composable', () => {
  const { t } = i18n.global;
  it.each([
    ['CB', t('labels.address_type_cb.text')],
    ['RE', t('labels.address_type_re.text')],
    ['IM', t('labels.address_type_importer.text')],
  ])('should return the correct label for the given address type', (value, expected) => {
    expect(useFurtherAddressTypeLabel(value)).toBe(expected);
  });
});
