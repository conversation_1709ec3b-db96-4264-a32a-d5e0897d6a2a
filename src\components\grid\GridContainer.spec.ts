import GridContainer from '@/components/grid/GridContainer.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';

describe('GridContainer component', () => {
  let wrapper: VueWrapper;

  const slotContent = '<h1>GridContainer</h1>';
  beforeEach(() => {
    wrapper = mount(GridContainer, {
      slots: {
        default: slotContent,
      },
    });
  });

  it('should render correctly', () => {
    expect(wrapper.html()).toMatchSnapshot();
  });
});
