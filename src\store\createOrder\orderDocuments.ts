import { defineStore, storeToRefs } from 'pinia';
import type { BooleanResult, Document, DocumentResponse } from '@dfe/dfe-book-api';
import { UploadStatus } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useOnline } from '@vueuse/core';
import { watch } from 'vue';
import { isDocumentResponse, isErrorHttpResponse } from '@/services/type-guards';
import { createUuid } from '@/utils/createUuid';
import { cloneDeep } from 'lodash';

export type StoreDocument = Document &
  DocumentResponse & {
    progress?: number;
    size?: number;
    documentType?: string;
  };

export interface CreateOrderDocumentsState {
  documents: StoreDocument[];
  updateDocumentData: BooleanResult;
  uploadTimeout: number;
  documentUploadFailed: boolean;
  initialDocuments: StoreDocument[];
}

export const useCreateOrderDocumentsStore = defineStore('createOrderDocuments', {
  state: (): CreateOrderDocumentsState => ({
    documents: [],
    updateDocumentData: { result: false },
    uploadTimeout: 180 * 1000,
    documentUploadFailed: false,
    initialDocuments: [],
  }),
  actions: {
    async getOrderDocuments(orderId: number) {
      try {
        const { data } = await this.api.book.v1.getDocuments({ orderId });
        data.forEach((document: StoreDocument) => {
          document.progress = 100;
          document.size = (document.size ?? 0) * 1024;
          document.uploadStatus = UploadStatus.Success;
        });
        this.documents = data;
        this.initialDocuments = cloneDeep(data);
      } catch (error) {
        this.client?.log.error('Failed to get order documents', 'dfe-book-frontend', error);
      }
    },
    async addDocument(file: Document) {
      const documentIndex = this.documents.push({ ...file, progress: 0 }) - 1;
      return this.uploadDocument(this.documents[documentIndex]);
    },
    removeDocument(id?: number) {
      const documentName = this.documents.find((document: Document & DocumentResponse) => {
        return document.documentId === id;
      })?.documentName;

      this.documents = this.documents.filter((document: Document & DocumentResponse) => {
        return document.documentId !== id;
      });

      this.documentUploadFailed = false;
      this.documents.forEach((document) => {
        this.documentUploadFailed = document.uploadStatus === UploadStatus.UnspecificError;
      });

      return { id, documentName };
    },
    async uploadDocument(document: StoreDocument) {
      const createOrderFormStore = useCreateOrderFormStore();
      const isOnline = useOnline();

      document.documentId = createUuid();
      document.uploadStatus = undefined;
      document.progress = 0;

      try {
        const cancelToken = Symbol(0);

        setTimeout(() => this.api.book.abortRequest(cancelToken), this.uploadTimeout);

        watch(
          () => isOnline.value,
          (isStillOnline) => {
            if (!isStillOnline) {
              this.api.book.abortRequest(cancelToken);
            }
          },
        );

        const { orderId } = storeToRefs(createOrderFormStore);
        const documentToSend = { ...document, ...(orderId.value && { orderId: orderId.value }) };

        delete documentToSend.documentId;
        delete documentToSend.uploadStatus;

        const { data } = await this.api.book.documents.uploadSingleDocument(
          createOrderFormStore.orderInformation,
          documentToSend,
          { cancelToken },
        );
        document.progress = 100;
        document.documentId = data.documentId;
        document.uploadStatus = data.uploadStatus;
        this.documentUploadFailed = false;
        return {
          id: document.documentId,
          documentName: document.documentName,
          uploadStatus: document.uploadStatus,
        };
      } catch (error) {
        document.progress = 100;
        this.documentUploadFailed = true;
        if (
          isErrorHttpResponse(error, isDocumentResponse) &&
          error.error.uploadStatus !== UploadStatus.UnspecificError
        ) {
          document.uploadStatus = error.error.uploadStatus;
          return;
        }

        document.uploadStatus = UploadStatus.UnspecificError;
        throw error;
      }
    },
    async updateDocument(customerNumber: number, documentId: number, document: StoreDocument) {
      try {
        const createOrderFormStore = useCreateOrderFormStore();
        const { data } = await this.api.book.documents.updateSingleDocument(
          documentId,
          createOrderFormStore.orderInformation,
          document,
        );
        this.updateDocumentData = data;
      } catch (error) {
        this.client?.log.error('Failed to update document', 'dfe-book-frontend', error);
      }
    },
    async linkAllDocumentsToOrder(customerNumber: number, orderId: number | undefined) {
      try {
        await Promise.all(
          this.documents.map(async (document: Document & DocumentResponse) => {
            if (document.documentId && !document.orderId) {
              document.orderId = orderId;
              await this.updateDocument(customerNumber, document.documentId, document);
            }
          }),
        );
      } catch (error) {
        this.client?.log.error('Failed to link documents to order', 'dfe-book-frontend', error);
      }
    },
  },
});
