<template>
  <div v-data-test="'counter-field'">
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VTextField
      :id="id"
      v-data-test="'counter-field-input'"
      :model-value="modelValue"
      :single-line="true"
      variant="outlined"
      density="compact"
      :min="min"
      :max="max"
      hide-details="auto"
      type="number"
      :rules="rules"
      :disabled="disabled"
      @update:model-value="onInputNumberChange"
      @focus="modelValue == 0 ? $event.target.select() : null"
    >
      <template #prepend-inner>
        <MaterialSymbol
          v-data-test="'counter-field-decrement'"
          size="16"
          class="cursor-pointer mt-1"
          color="grey-darken-2"
          :disabled="modelValue <= (min ? min : 0) ? 'disabled' : null"
          @click="stepDown"
        >
          <MinusIcon />
        </MaterialSymbol>
      </template>
      <template #append-inner>
        <MaterialSymbol
          v-data-test="'counter-field-increment'"
          size="16"
          class="cursor-pointer mt-1"
          color="grey-darken-2"
          :disabled="max ? modelValue >= max : null"
          @click="stepUp"
        >
          <PlusIcon />
        </MaterialSymbol>
      </template>
    </VTextField>
  </div>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { createUuid } from '@/utils/createUuid';
import PlusIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
import MinusIcon from '@dfe/dfe-frontend-styles/assets/icons/horizontal_rule-16px.svg';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  label?: TranslateResult;
  required?: boolean;
  min?: number;
  max?: number;
  rules?: ValidationRule[];
  disabled: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  label: '',
  disabled: false,
  min: undefined,
  max: undefined,
  rules: undefined,
});

const modelValue = defineModel<number>({
  default: 0,
});
const id = `counter-${createUuid()}`;

const onInputNumberChange = (value: string) => {
  if (!value || isNaN(Number(value))) {
    return;
  }

  modelValue.value = Number(value);
};

const stepDown = () => {
  modelValue.value = Math.max(props.min ?? modelValue.value - 1, modelValue.value - 1);
};

const stepUp = () => {
  modelValue.value = Math.min(props.max ?? modelValue.value + 1, modelValue.value + 1);
};
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

:deep(input) {
  text-align: center;
}

:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

.cursor-pointer {
  cursor: pointer;
}

:deep(.v-field__prepend-inner),
:deep(.v-field__append-inner) {
  .material-symbol {
    &:hover {
      svg {
        color: vars.$color-base-grey-900;
      }
    }
  }
}
</style>
