<template>
  <VRow>
    <VCol class="d-flex justify-space-between align-center">
      <span class="order-line--title | text-body-3 text-grey-darken-2">
        {{ getTitle() }}
      </span>

      <DfeIconButton
        :size="ComponentSize.DEFAULT"
        :color="ColorVariants.NEUTRAL"
        :tooltip="t('labels.delete_label.text')"
        :filled="false"
        @click="deleteEQ"
      >
        <MaterialSymbol size="24" color="grey-darken-2">
          <DeleteIcon />
        </MaterialSymbol>
      </DfeIconButton>
    </VCol>
  </VRow>

  <div class="d-flex flex-wrap">
    <div class="d-flex flex-grow-1 flex-wrap flex-md-nowrap align-start">
      <SpanField
        class="size-xs mr-3 mt-4"
        :label="t('labels.type_label.text')"
        :value="t('labels.excepted_quantities_short.text')"
      />
      <CounterField
        class="size-sm mr-3 mt-4"
        :required="true"
        :label="t('labels.number_of_packages.text')"
        type="number"
        :max="999"
        :min="0"
        :rules="[
          useValidationRules.integer,
          useValidationRules.min(0),
          useValidationRules.max(999),
          useValidationRules.positiveNumber,
        ]"
        :model-value="dangerousGood.noOfPackages"
        append-inner-icon="mdi-plus"
        prepend-inner-icon="mdi-minus"
        @update:model-value="updatePackages"
      />
      <AutocompleteField
        class="mr-3 mt-4 size-lg"
        :model-value="dangerousGood.packaging"
        :items="dangerousGoodsPackagingOptionsList"
        item-title="translationKey"
        item-value="code"
        :label="t('labels.dangerous_good_packaging.text')"
        :placeholder="t('labels.select_option.text')"
        :required="true"
        :multiline-menu="false"
        return-object
        :message="t('labels.validation_select_input_required.text')"
        :menu-icon="ArrowDropDownIcon"
        @update:model-value="updateValue('packaging', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import CounterField from '@/components/form/CounterField.vue';
import { useI18n } from 'vue-i18n';
import { EQDangerousGood4Store, getEmptyEQDangerousGood } from '@/store/createOrder/orderLine';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import { update } from 'lodash';
import { SelectItemKey } from '@/types/vuetify';
import { useDangerousGoodsPackagingOptions } from '@/composables/data/useDangerousGoodPackagingOptions';
import { computed } from 'vue';
import SpanField from '@/components/form/SpanField.vue';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { useDangerousGoodPackagingOptionsList } from '@/composables/createOrder/useDangerousGoodPackagingOptionsList';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import { useValidationRules } from '@/composables/form/useValidationRules';

interface Props {
  computedLineCount: string;
}
const props = defineProps<Props>();
const dangerousGood = defineModel<EQDangerousGood4Store>({ default: getEmptyEQDangerousGood(1) });
const emit = defineEmits(['deleteDangerousGood']);
const { t } = useI18n();

const { data: dangerousGoodsPackagingOptions } = useDangerousGoodsPackagingOptions();

const dangerousGoodsPackagingOptionsList = computed(() => {
  if (!dangerousGoodsPackagingOptions.value) {
    return [];
  }
  return useDangerousGoodPackagingOptionsList(dangerousGoodsPackagingOptions, t);
});

function getTitle() {
  return `${t('labels.dangerous_good_position.text')} ( ${t('labels.order_line_header.text')} ${props.computedLineCount} )`;
}

function deleteEQ() {
  emit('deleteDangerousGood');
}

const updatePackages = async (quantity: number) => {
  updateValue('noOfPackages', quantity);
};

const triggerValidation = async () => {
  return (
    dangerousGood.value.noOfPackages !== undefined && dangerousGood.value.packaging !== undefined
  );
};

defineExpose({ triggerValidation });

const updateValue = (key: string, value?: string | number | null | SelectItemKey) => {
  dangerousGood.value = update({ ...dangerousGood.value }, key, () => value);
};
</script>
