import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { computed, watch } from 'vue';
import { useConfigStore } from '@/store/config';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import type { DeliveryProducts } from '@dfe/dfe-book-api';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';

export const useAirDeliveryProducts = () => {
  const { api } = useInit();

  const { data: customerSettings } = useCustomerSettings();

  const { data, isLoading } = useCustomerQuery(
    'airDeliveryProducts',
    api.book.v1.getAirDeliveryProducts,
    {
      enabled: computed(() => !!customerSettings.value?.airProducts),
    },
  );

  const configStore = useConfigStore();
  const addressStore = useCreateOrderAddressesStore();
  const { selectedAirDeliveryProduct } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );
  const { fromIATA, toIATA } = storeToRefs(addressStore);

  const isRoutingValidForProducts = () => {
    const fromAirportConfig = configStore.airProductRouting?.product_related_airports?.find(
      (airportConfig) => airportConfig.country === fromIATA.value?.countryCode,
    );
    const toAirportConfig = configStore.airProductRouting?.product_related_airports?.find(
      (airportConfig) => airportConfig.country === toIATA.value?.countryCode,
    );

    if (fromAirportConfig && toAirportConfig) {
      return (
        fromAirportConfig.airports?.find((airport) => airport === fromIATA.value?.code) &&
        toAirportConfig.airports?.find((airport) => airport === toIATA.value?.code)
      );
    }

    return false;
  };

  const resetSelectedAirDeliveryProduct = () => {
    if (!isRoutingValidForProducts()) {
      selectedAirDeliveryProduct.value = undefined;
    }
  };

  watch([fromIATA, toIATA], () => {
    resetSelectedAirDeliveryProduct();
  });

  const airDeliveryProducts = computed<DeliveryProducts>(() =>
    isRoutingValidForProducts() && data.value ? data.value : [],
  );

  return {
    airDeliveryProducts,
    isLoading,
  };
};
