import { useInit } from '@/composables/useInit';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { Segment } from '@dfe/dfe-book-api';
import { computed, Ref } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import { useI18n } from 'vue-i18n';

export const useNatureOfGoods = () => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { transportType } = storeToRefs(formStore);
  const { t } = useI18n();

  return useQuery({
    queryKey: ['natureOfGoods', transportType] as [string, Ref<Segment>],
    enabled: computed(() => Boolean(transportType.value)),
    placeholderData: [],
    queryFn: async () => {
      const response = await api.book.natureOfGoods.getLastUsedNatureOfGoods({
        customerSegment: transportType.value,
      });

      return [
        { header: t('labels.last_used.text') },
        ...response.data.map((item) => ({ code: item })),
      ];
    },
  });
};
