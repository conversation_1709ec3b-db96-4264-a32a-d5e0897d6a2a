import type { OrderValidationResult } from '@dfe/dfe-book-api';
import { OrderStatus } from '@dfe/dfe-book-api';

export const validationError: OrderValidationResult = {
  valid: false,
  newOrderStatus: OrderStatus.DRAFT,
  results: [
    {
      field: 'orderLines[0].weight',
      errorType: 'REQUIRED_FIELD_MISSING',
      description: 'must not be empty',
    },
    {
      field: 'shipperAddress.name',
      errorType: 'REQUIRED_FIELD_MISSING',
      description: 'must not be empty',
    },
    {
      field: 'orderReferences.referenceType.delivery_note_number',
      errorType: 'REQUIRED_FIELD_MISSING',
      description: 'must not be empty',
    },
  ],
};

export const validationSuccess: OrderValidationResult = {
  valid: true,
  newOrderStatus: OrderStatus.LABEL_PENDING,
  results: [],
};
