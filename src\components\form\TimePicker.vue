<template>
  <ComboboxField
    :model-value="modelValue"
    :items="items"
    :label="label"
    :static-menu="true"
    :required="required"
    :rules="validationRules"
    :placeholder="placeholder"
    @update:model-value="onInput"
  />
</template>

<script setup lang="ts">
import ComboboxField from '@/components/form/ComboboxField.vue';
import useParsedDate from '@/composables/dateTimeUtilities/useParsedDate';
import { useFormattedDate } from '@/composables/dateTimeUtilities/useTimeFromDate';
import { useValidationRules, withMessage } from '@/composables/form/useValidationRules';
import isValidDate from '@/utils/isValidDate';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import { computed, onMounted, watch } from 'vue';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  items: string[];
  label?: TranslateResult;
  required?: boolean;
  defaultValue?: string;
  placeholder?: TranslateResult;
  message?: TranslateResult;
}

const props = withDefaults(defineProps<Props>(), {
  label: undefined,
  defaultValue: undefined,
  placeholder: undefined,
  message: undefined,
});

const { timeFormat } = usePreferences();

const modelValue = defineModel<string>({ default: '' });

const validationRules = computed(() => [
  ...(props.required
    ? [
        props.message
          ? withMessage(useValidationRules.required, props.message)
          : useValidationRules.required,
      ]
    : []),
  useValidationRules.time(timeFormat.value),
]);

const emitUpdate = (date: string | Date, value: string) => {
  if (isValidDate(date)) {
    modelValue.value = useFormattedDate(date, timeFormat.value);
  } else {
    modelValue.value = value;
  }
};

const onInput = (value: string) => {
  const date = useParsedDate(value, timeFormat.value);
  emitUpdate(date, value);
};

onMounted(() => {
  if (!modelValue.value && props.defaultValue) {
    modelValue.value = props.defaultValue;
  }
});

watch(timeFormat, (_, oldTimeFormat) => {
  // If the time format changes, we need to re-parse the date
  const date = useParsedDate(modelValue.value, oldTimeFormat);
  emitUpdate(date, modelValue.value);
});
</script>
