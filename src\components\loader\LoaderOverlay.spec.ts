import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { beforeAll, beforeEach } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import ProgressCircular from '@/components/ProgressCircular.vue';

const props = {
  modelValue: false,
};

describe('LoaderOverlay component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(LoaderOverlay, { props, attachTo: document.body });
  });

  it('shows spinner while loading', async () => {
    expect(wrapper.findComponent(ProgressCircular).exists()).toBe(false);

    await wrapper.setProps({ modelValue: true });

    expect(wrapper.findComponent(ProgressCircular).exists()).toBe(true);
  });
});
