import { defineStore, storeToRefs } from 'pinia';
import type { ValidationResult, ValidationResultEntry } from '@dfe/dfe-book-api';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useValidationDataStore } from '@/store/validation';
import { i18n } from '@/plugins/i18n';

export interface ValidationErrorBannerState {
  validationMessage: string;
  validationErrors: ValidationResultEntry[];
  hasFrontendErrors: boolean;
}
export const useErrorBanner = defineStore('errorBanner', {
  state: (): ValidationErrorBannerState => ({
    validationMessage: i18n.global.t('labels.invalid_input.text'),
    validationErrors: [],
    hasFrontendErrors: false,
  }),
  getters: {
    isVisible: (state) => state.validationErrors.length > 0 || state.hasFrontendErrors,
    hasCommercialInvoiceError: (state) => {
      return state.validationErrors.some((error) => error.field === 'orderDocuments');
    },
    deduplicatedErrorDescriptions: (state) => {
      return state.validationErrors
        .map((error) => error.description)
        .reduce((deduplicatedErrors, currentError) => {
          if (currentError && !deduplicatedErrors.includes(currentError)) {
            deduplicatedErrors.push(currentError);
          }
          return deduplicatedErrors;
        }, [] as string[]);
    },
  },
  actions: {
    updateErrors(errors: ValidationResultEntry[]) {
      this.clearErrors();
      this.validationErrors = errors;
    },
    addFrontendErrors() {
      this.clearErrors();
      this.hasFrontendErrors = true;
      useValidationDataStore().scrollToFirstInvalidSection();
    },
    clearErrors() {
      this.validationErrors = [];
      this.hasFrontendErrors = false;
    },
    importFromValidationResult(result: ValidationResult) {
      this.clearErrors();
      this.validationErrors = [...new Set(result.results)];
    },
    pushErrors(results?: ValidationResultEntry[]) {
      const createOrderFormStore = useCreateOrderFormStore();
      const { customTypeValidationError } = storeToRefs(createOrderFormStore);
      if (results && results.length > 0) {
        this.validationErrors = results.map((result) => {
          if (result.field === 'customsType') {
            customTypeValidationError.value = true;
          }
          if (result.description) {
            return result;
          } else {
            return {};
          }
        });
      }
    },
  },
});
