import { storeToRefs } from 'pinia';
import { useCreateOrderValidation } from './useCreateOrderValidation';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useValidationDataStore } from '@/store/validation';
import { initPinia } from '@test/util/init-pinia';

describe('useCreateOrderValidation', () => {
  beforeAll(() => {
    initPinia();
  });

  it('emits "createOrderValidate" event and passes validate function', () => {
    const emit = vi.fn();

    const { createOrderValidate } = useCreateOrderValidation(emit);

    createOrderValidate();

    expect(emit).toHaveBeenCalledTimes(1);
    expect(emit).toHaveBeenCalledWith('createOrderValidate', expect.any(Function));
  });

  it.each([true, false])('awaits for validation result and returns it -> %s', async (result) => {
    let validateMock = (isValid: boolean) => isValid;
    const emit = vi.fn((_, validate) => {
      validateMock = validate;
    });

    const { createOrderValidate } = useCreateOrderValidation(emit);

    const isValid = createOrderValidate();

    expect(isValid).toBeInstanceOf(Promise);
    validateMock(result);
    expect(await isValid).toBe(result);
  });

  it.each([true, false])('calls callback with validation result -> %s', async (result) => {
    let validateMock = (isValid: boolean) => isValid;
    const emit = vi.fn((_, validate) => {
      validateMock = validate;
    });

    const { createOrderValidate } = useCreateOrderValidation(emit);

    const callback = vi.fn();
    const isValid = createOrderValidate(callback);
    validateMock(result);

    expect(await isValid).toBe(result);
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback).toHaveBeenCalledWith(result);
  });

  it.each([
    [true, true],
    [true, false],
    [false, true],
  ])('validates contact form -> %s %s', async (isShipperContactSet, isConsigneeContactSet) => {
    const { createOrderValidate } = useCreateOrderValidation(vi.fn());
    const { formValidationSectionsAir } = storeToRefs(useValidationDataStore());
    const addressStore = useCreateOrderAddressesStore();

    vi.spyOn(addressStore, 'isShipperContactDataSet', 'get').mockReturnValueOnce(
      isShipperContactSet,
    );
    vi.spyOn(addressStore, 'isConsigneeContactDataSet', 'get').mockReturnValueOnce(
      isConsigneeContactSet,
    );
    createOrderValidate();

    expect(formValidationSectionsAir.value.airOrderAddresses).toBe(
      isShipperContactSet && isConsigneeContactSet,
    );
  });
});
