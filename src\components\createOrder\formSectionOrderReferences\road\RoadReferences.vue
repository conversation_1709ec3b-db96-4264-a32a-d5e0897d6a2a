<template>
  <div>
    <div class="grid-container-form | align-center | mb-4">
      <TextField
        v-model="orderNumber"
        v-tooltip="{
          text: t('labels.status_complete_tooltip.text'),
          location: 'right',
          disabled: !isOrderNumberDisabled,
          attach: $appRoot,
        }"
        :label="t('labels.order_number.text')"
        :required="isOrderNumberMandatory"
        :max-length="MaxLength.Default"
        :disabled="isOrderNumberDisabled"
      />
    </div>

    <EkaerNumber v-if="showEkaerNumberField" />

    <MultipleTextFields
      v-for="compData in optionalReferencesObjects"
      :key="compData.name"
      :type="compData.name"
      :items="compData.items"
      :label="compData.label"
      :max-length="compData.maxLength"
      :prefix="compData.prefix"
      :rules="compData.rules ? compData.rules : []"
    />

    <div class="d-flex align-center mt-6">
      <AddButton
        :label="t('labels.purchase_order_numbers.text')"
        :disabled="disablePurchaseOrderNumberButton"
        variant="text"
        class="mr-1"
        @add-new-item="useAddReference(OrderReferenceType.PURCHASE_ORDER_NUMBER)"
      />
      <AddButton
        :label="t('labels.delivery_note_numbers.text')"
        :disabled="disableDeliveryNoteNumberButton"
        variant="text"
        @add-new-item="useAddReference(OrderReferenceType.DELIVERY_NOTE_NUMBER)"
      />
      <AddButton
        data-test-id="booking_reference"
        :label="t('labels.booking_reference_label.text')"
        :disabled="disableBookingReferenceButton"
        variant="text"
        @add-new-item="useAddReference(OrderReferenceType.BOOKING_REFERENCE)"
      />
      <AddButton
        v-if="showIdentificationCodeTransport"
        data-test-id="identification-code-transport"
        :label="t('labels.identification_code_transport.text')"
        :disabled="disableIdentificationCodeTransportButton"
        variant="text"
        @add-new-item="useAddReference(OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import AddButton from '@/components/createOrder/AddButton.vue';
import EkaerNumber from '@/components/createOrder/formSectionOrderReferences/EkaerNumber.vue';
import MultipleTextFields from '@/components/createOrder/formSectionOrderReferences/MultipleTextFields.vue';
import TextField from '@/components/form/TextField.vue';
import useAddReference from '@/composables/createOrder/useAddReference';
import useCleanReference from '@/composables/createOrder/useCleanReference';
import useShowEkaerNumberField from '@/composables/createOrder/useShowEkaerNumberField';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { CountryCodes, FurtherAddressTypesList, MaxLength } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const addressStore = useCreateOrderAddressesStore();
const { getFurtherAddress } = addressStore;
const { differentConsigneeAddress } = storeToRefs(addressStore);
const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
const createOrderFormStore = useCreateOrderFormStore();
const {
  storeInfosforEkaer,
  disablePurchaseOrderNumberButton,
  disableDeliveryNoteNumberButton,
  disableBookingReferenceButton,
  optionalReferencesObjects,
  disableIdentificationCodeTransportButton,
} = storeToRefs(createOrderOrderReferencesFormStore);
const { orderNumber, isCompleteRoadOrder, initialOrderNumber, transportCountry } =
  storeToRefs(createOrderFormStore);
const { data: customerSettings } = useCustomerSettings();

const isOrderNumberDisabled = computed(
  () => isCompleteRoadOrder.value && initialOrderNumber.value != undefined,
);

const isOrderNumberMandatory = computed(() => customerSettings?.value?.orderNumberMandatory);
const showEkaerNumberField = computed(() => useShowEkaerNumberField(storeInfosforEkaer));

const showIdentificationCodeTransport = computed(() => {
  const listOfAvailableCountry = [String(CountryCodes.RO)];
  return (
    listOfAvailableCountry.includes(String(transportCountry.value.fromCountry)) ||
    listOfAvailableCountry.includes(String(transportCountry.value.toCountry)) ||
    listOfAvailableCountry.includes(String(differentConsigneeAddress.value.address.countryCode)) ||
    listOfAvailableCountry.includes(
      String(getFurtherAddress(FurtherAddressTypesList.finalDeliveryAddress).address.countryCode),
    )
  );
});

watch(showIdentificationCodeTransport, () => {
  if (!showIdentificationCodeTransport.value) {
    useCleanReference(OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT);
  }
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use 'sass:map';
:deep(.grid-container-form) {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: auto;
  grid-gap: 0.75em;

  @media #{map.get(settings.$display-breakpoints, 'lg')} {
    grid-template-columns: repeat(3, 1fr);
  }
  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
