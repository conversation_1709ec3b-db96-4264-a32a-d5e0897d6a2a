import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import EditModal from '@/components/base/modal/EditModal.vue';
import AddButton from '@/components/createOrder/AddButton.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import FormAddress from '@/components/createOrder/formSectionAddresses/FormAddress.vue';
import SearchAddressField from '@/components/createOrder/formSectionAddresses/SearchAddressField.vue';
import OutputAddress from '@/components/createOrder/OutputAddress.vue';
import FormContactPerson from '@/components/form/FormContactPerson.vue';
import TextField from '@/components/form/TextField.vue';
import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import { useValidatePostcode } from '@/composables/createOrder/useValidatePostcode';
import { AddressCardType, OrderTypes } from '@/enums';
import { addressBookAddresses, addresses } from '@/mocks/fixtures/addresses';
import { contactDataMock } from '@/mocks/fixtures/contactData';
import { validateAddressData } from '@/mocks/fixtures/validateAddressData';
import { validatePostcodeInvalid, validatePostcodeValid } from '@/mocks/fixtures/validatePostcode';
import { mockServer } from '@/mocks/server';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import * as uuid from '@/utils/createUuid';
import { DuplicateEntryProblem, Severity } from '@dfe/dfe-address-api';
import type { TestUtils } from '@test/test-utils';
import { withSetup } from '@test/util/with-setup';
import { mount } from '@vue/test-utils';
import type { VueInstance } from '@vueuse/core';
import flushPromises from 'flush-promises';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { Response, Server } from 'miragejs';
import { storeToRefs } from 'pinia';
import { afterEach, expect } from 'vitest';
import { nextTick } from 'vue';

const uuidSpy = vi.spyOn(uuid, 'createUuid');

const props = {
  modelValue: { ...orderAddress() },
  applyPresets: true,
};

describe('AddressCard component', () => {
  type VFormComponent = VueInstance & { validate: () => { valid: boolean }; value: boolean };
  let wrapper: TestUtils.VueWrapper<typeof AddressCard>;
  let server: Server;
  beforeAll(() => {
    mockResizeObserver();
    server = mockServer({
      environment: 'test',
      fixtures: {
        addresses,
        validateAddressData,
        contactData: contactDataMock,
        validatePostcodeValid,
        validatePostcodeInvalid,
      },
    });
  });
  afterAll(() => {
    server.shutdown();
  });
  beforeEach(() => {
    wrapper = mount(AddressCard, {
      props,
    });
  });

  afterEach(() => {
    uuidSpy.mockClear();
    wrapper.unmount();
  });

  it('testing delete - cancel button', async () => {
    await wrapper.setProps({ showDelete: true });
    await nextTick();

    expect(wrapper.find('.delete').exists()).toBe(false);
  });

  it('testing header prop', async () => {
    await wrapper.setProps({ header: 'PickUp address' });

    await nextTick();

    const searchAddressField = wrapper.findComponent(SearchAddressField);
    expect(searchAddressField.exists()).toBe(true);
    expect(searchAddressField.find('h5').exists()).toBe(true);
  });

  it('testing hideSearchFieldLabel prop', async () => {
    await wrapper.setProps({ hideSearchFieldLabel: true });
    await nextTick();

    const searchAddressField = wrapper.findComponent(SearchAddressField);
    expect(searchAddressField.exists()).toBe(true);
    expect(searchAddressField.find('h5').exists()).toBe(false);
  });

  it('shows OutputAddress component and passes props isEditable and isDeletable', async () => {
    await wrapper.setProps({
      modelValue: { ...orderAddress(), postcode: '12345', countryCode: 'DE', name: 'Muenchen' },
    });

    const outputAddress = wrapper.findComponent(OutputAddress);

    expect(outputAddress.props('isEditable')).toBe(true);
    expect(outputAddress.props('isDeletable')).toBe(true);

    await wrapper.setProps({ isEditable: false, isDeletable: false });
    expect(outputAddress.props('isEditable')).toBe(false);
    expect(outputAddress.props('isDeletable')).toBe(false);
  });

  it('sets AddressCard props isShipperAddress', async () => {
    const addressCard = wrapper.findComponent(AddressCard);

    expect(addressCard.props('isShipperAddress')).toBe(false);
    expect(addressCard.props('smartProposalsEnabled')).toBe(false);

    await wrapper.setProps({
      isShipperAddress: true,
      smartProposalsEnabled: true,
    });

    expect(addressCard.props('isShipperAddress')).toBe(true);
    expect(addressCard.props('smartProposalsEnabled')).toBe(true);
  });

  it('should update contactDataDelivery when address is selected and main contact is available', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const deliveryStore = useCreateOrderFormCollectionAndDeliveryStore();
    const { contactDataDelivery } = storeToRefs(deliveryStore);
    const { orderType } = storeToRefs(orderFormStore);

    orderType.value = OrderTypes.RoadForwardingOrder;
    await wrapper.setProps({
      isConsigneeAddress: true,
    });

    const searchAddressField = wrapper.findComponent(SearchAddressField);
    searchAddressField.vm.$emit('update:model-value', addresses[0]);
    await nextTick();

    await vi.waitFor(() => {
      expect(contactDataDelivery.value).toStrictEqual(contactDataMock);
    });
  });

  it('should set townCounty when address from Ireland is selected', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const { townCounty } = storeToRefs(useCreateOrderAddressesStore());
    const { orderType } = storeToRefs(orderFormStore);

    orderType.value = OrderTypes.RoadForwardingOrder;
    await wrapper.setProps({
      isConsigneeAddress: true,
      applyPresets: true,
    });

    const searchAddressField = wrapper.findComponent(SearchAddressField);
    searchAddressField.vm.$emit('update:model-value', addressBookAddresses[2]);
    await nextTick();

    expect(townCounty.value).toStrictEqual({
      label: 'Rathcoole/Dublin',
      data: {
        town: 'Rathcoole',
        county: 'Dublin',
        dachserPlz: '',
      },
    });
  });

  it('should update contact data', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const { orderType } = storeToRefs(orderFormStore);

    orderType.value = OrderTypes.AirExportOrder;

    await wrapper.setProps({
      modelValue: {
        id: 1,
        name: 'Flink CPS',
        name2: 'Nationwide CO. LTD',
        name3: '',
        countryCode: 'DE',
        street: 'Hauptstraße 12',
        city: 'Rottenburg am Neckar',
        postcode: '72108',
        supplement: '',
        gln: '12345',
        contact: {
          name: 'name',
        },
      },
      contactData: contactData(),
    });

    wrapper.vm.$emit('update-contact', {
      name: 'name changed',
      email: '<EMAIL>',
    });

    await nextTick();

    const addressCard = wrapper.findComponent(AddressCard);
    const emit = addressCard.emitted('update-contact');
    expect(emit).toBeDefined();

    if (emit) {
      expect(emit[0][0]).toStrictEqual({ name: 'name changed', email: '<EMAIL>' });
    }
  });

  it('name and email should be required for form-contact-person component, when orderType is AirOrder and RoadCollectionOrder (Consignee = Principal)', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const { orderType } = storeToRefs(orderFormStore);

    const addNewItemAndExpectRequiredFields = async () => {
      const addButton = wrapper.findComponent(AddButton);
      addButton.vm.$emit('add-new-item');

      await flushPromises();

      const formContactPerson = wrapper.findComponent(FormContactPerson);
      expect(formContactPerson.props('requiredFields')).toStrictEqual(['name', 'email']);
    };

    orderType.value = OrderTypes.AirExportOrder;

    await addNewItemAndExpectRequiredFields();

    orderType.value = OrderTypes.RoadCollectionOrder;
    await wrapper.setProps({
      addressCardType: AddressCardType.CONSIGNEE,
    });

    await addNewItemAndExpectRequiredFields();
  });

  it('should not show formContactperson if orderType is RoadCollecting order', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const { orderType } = storeToRefs(orderFormStore);

    orderType.value = OrderTypes.RoadCollectionOrder;

    const addButton = wrapper.findComponent(AddButton);
    addButton.vm.$emit('add-new-item');

    await flushPromises();

    const formContactPerson = wrapper.findComponent(FormContactPerson);

    expect(formContactPerson.exists()).toBe(true);
  });

  it('should not show formContactperson if orderType is RoadForwarding order', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const { orderType } = storeToRefs(orderFormStore);

    orderType.value = OrderTypes.RoadForwardingOrder;

    const addButton = wrapper.findComponent(AddButton);
    addButton.vm.$emit('add-new-item');

    await flushPromises();

    const formContactPerson = wrapper.findComponent(FormContactPerson);

    expect(formContactPerson.exists()).toBe(false);
  });
  it('should set isShipperAddressDisabled to false, when addNewAddress is called', async () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { isShipperAddressDisabled, townCounty } = storeToRefs(createOrderAddressesStore);

    isShipperAddressDisabled.value = true;

    const addButton = wrapper.findComponent(AddButton);

    addButton.vm.$emit('add-new-item');

    await flushPromises();

    expect(isShipperAddressDisabled.value).toBe(false);
    expect(townCounty.value).toBe(undefined);
  });

  it('should set isShipperAddressDisabled to true, when editAddress is called and shipperAddress is equal to actual address', async () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { isShipperAddressDisabled } = storeToRefs(createOrderAddressesStore);

    isShipperAddressDisabled.value = false;

    const { selectedCustomer } = storeToRefs(useCreateOrderFormStore());

    selectedCustomer.value.address = addresses[0];

    await wrapper.setProps({
      modelValue: addresses[0],
    });

    wrapper.findComponent(OutputAddress).vm.$emit('edit-address');

    await flushPromises();

    expect(isShipperAddressDisabled.value).toBe(true);
  });

  it('should set isShipperAddressDisabled to false, when editAddress is called and shipperAddress is not equal to actual address', async () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { isShipperAddressDisabled } = storeToRefs(createOrderAddressesStore);

    const { selectedCustomer } = storeToRefs(useCreateOrderFormStore());

    selectedCustomer.value.address = addresses[0];

    await wrapper.setProps({
      modelValue: addresses[2],
    });

    wrapper.findComponent(OutputAddress).vm.$emit('edit-address');

    await flushPromises();

    expect(isShipperAddressDisabled.value).toBe(false);
  });

  it('should emit empty address and contact data, when delete button is clicked in OutputAddress', async () => {
    await wrapper.setProps({ modelValue: addresses[0] });

    const outputAddress = wrapper.findComponent(OutputAddress);

    outputAddress.vm.$emit('delete-address');

    const emitAddress = wrapper.emitted('update:modelValue');
    const emitContact = wrapper.emitted('update-contact');

    expect(emitAddress).toBeDefined();
    expect(emitAddress?.[0][0]).toEqual({ ...orderAddress() });
    expect(emitContact).toBeDefined();
    expect(emitContact?.[0][0]).toEqual({ ...contactData() });
  });

  const openEditModal = async () => {
    wrapper.findComponent(OutputAddress).vm.$emit('edit-address');

    await flushPromises();
  };

  it('should show discard changes dialog if address have been changed', async () => {
    await wrapper.setProps({
      modelValue: addresses[0],
    });

    await openEditModal();

    const editModal = wrapper.findComponent(EditModal);
    const formAddress = editModal.findComponent(FormAddress);
    const discardChangesDialog = formAddress.findComponent(ConfirmPrompt);
    const nameField = formAddress.findAllComponents(TextField).at(0);

    await nameField?.setValue('new name');
    await nextTick();

    editModal.vm.$emit('close');

    await nextTick();

    expect(discardChangesDialog.exists()).toBe(true);
  });

  it('should NOT show the discard changes, if address have not be changed', async () => {
    await wrapper.setProps({
      modelValue: addresses[0],
    });

    await openEditModal();

    const editModal = wrapper.findComponent(EditModal);
    const formAddress = editModal.findComponent(FormAddress);
    const discardChangesDialog = formAddress.findComponent(ConfirmPrompt);

    await nextTick();

    editModal.vm.$emit('close');

    await nextTick();

    expect(discardChangesDialog.exists()).toBe(false);
  });

  it('should validate addressForm and contactForm when calling editAddress', async () => {
    const orderFormStore = useCreateOrderFormStore();
    const { orderType } = storeToRefs(orderFormStore);

    orderType.value = OrderTypes.AirExportOrder;

    await wrapper.setProps({
      modelValue: {
        id: 1,
        name: 'Flink CPS',
        name2: 'Nationwide CO. LTD',
        name3: '',
        countryCode: 'DE',
        street: 'Hauptstraße 12',
        city: 'Rottenburg am Neckar',
        postcode: '72108',
        supplement: '',
        gln: '12345',
        contact: {
          name: 'name',
        },
      },
      contactData: contactData(),
    });

    await nextTick();

    const outputAddress = wrapper.findComponent(OutputAddress);

    await outputAddress.find('button').trigger('click');
    await nextTick();

    const addressForm = wrapper.findComponent({ ref: 'addressForm' });
    const contactForm = wrapper.findComponent({ ref: 'contactForm' });

    const addressFormSpy = vi.fn(() => ({ valid: true }));
    const contactFormSpy = vi.fn(() => ({ valid: true }));

    (<VFormComponent>addressForm.vm).validate = addressFormSpy;
    (<VFormComponent>contactForm.vm).validate = contactFormSpy;

    outputAddress.vm.$emit('edit-address');

    await flushPromises();

    expect(addressFormSpy).toHaveBeenCalled();
    expect(contactFormSpy).toHaveBeenCalled();
  });

  it('should discard address changes if user confirms discard dialog', async () => {
    const { hasUnsavedAddressChanges, isUnsavedAddressChangesDialogOpen } = storeToRefs(
      useCreateOrderAddressesStore(),
    );

    await wrapper.setProps({
      modelValue: addresses[0],
    });

    await openEditModal();

    let editModal = wrapper.findComponent(EditModal);
    let formAddress = editModal.findComponent(FormAddress);
    let nameField = formAddress.findAllComponents(TextField).at(0);

    nameField?.vm.$emit('update:modelValue', 'new name');

    await nextTick();

    editModal.vm.$emit('close');

    await nextTick();

    expect(hasUnsavedAddressChanges.value).toBe(true);
    expect(isUnsavedAddressChangesDialogOpen.value).toBe(true);

    const discardChangesDialog = formAddress.findComponent(ConfirmPrompt);
    discardChangesDialog.vm.$emit('confirm');

    await nextTick();
    await openEditModal();

    editModal = wrapper.findComponent(EditModal);
    formAddress = editModal.findComponent(FormAddress);
    nameField = formAddress.findAllComponents(TextField).at(0);

    expect(hasUnsavedAddressChanges.value).toBe(false);
    expect(isUnsavedAddressChangesDialogOpen.value).toBe(false);
    expect(nameField?.props('modelValue')).toBe(addresses[0].name);
  });

  it('should show the loader overlay, if the postcode validation is fetching', async () => {
    await wrapper.setProps({
      modelValue: addresses[0],
    });

    await openEditModal();

    await nextTick();

    const editModal = wrapper.findComponent(EditModal);
    editModal.vm.$emit('confirm');

    await flushPromises();

    const loaderOverlay = wrapper.findComponent(LoaderOverlay);

    expect(loaderOverlay.exists()).toBe(true);
  });

  it('should NOT show the loader overlay, if the postcode validation is done', async () => {
    const { refetch } = withSetup(() => useValidatePostcode())[0];
    await wrapper.setProps({
      modelValue: addresses[0],
    });

    await openEditModal();

    await nextTick();

    const editModal = wrapper.findComponent(EditModal);
    editModal.vm.$emit('confirm');

    await refetch();

    const loaderOverlay = wrapper.findComponent(LoaderOverlay);

    expect(loaderOverlay.exists()).toBe(false);
  });
  it('shows banner when the response has DuplicateEntryProblem', async () => {
    expect(wrapper.findComponent({ name: 'DfeBanner' }).exists()).toBe(false);

    server.post(
      '/v1/addresses',
      () =>
        new Response(400, undefined, {
          oasDiscriminator: 'DuplicateEntryProblem',
          type: 'address:address:err:bad-request',
          errorId: 'errAL-18',
          title: 'Address contact validation error',
          status: 400,
          detail: 'Address already exists',
          severity: Severity.Moderate,
          timestamp: '2024-11-22T13:16:57.522458551Z',
          existingDuplicateId: 15232,
        } satisfies DuplicateEntryProblem),
    );

    const addButton = wrapper.find("[data-test='book-add-address-button']");
    expect(addButton.exists()).toBe(true);
    await addButton.trigger('click');

    await flushPromises();

    const editModal = wrapper.findComponent(EditModal);
    expect(editModal.exists()).toBe(true);

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'DfeBanner' }).exists()).toBe(true);
  });
});
