<template>
  <div v-data-test="'subsection-delivery'">
    <div>
      <div>
        <DfeBanner :value="!hasAddresses" type="info" class="mt-0 mb-4">
          <span class="text-h5">{{ $t('messages.id6994.text') }}</span>
        </DfeBanner>
        <SelectField
          ref="deliveryProductField"
          v-model="deliveryProduct"
          v-data-test="'delivery-product-field'"
          :items="deliveryProductsList"
          :label="t('labels.product_label.text')"
          :required="true"
          :placeholder="t('labels.select_product.text')"
          item-value="code"
          item-text="description"
          :hint="selectedDeliveryProduct?.hint"
          :persistent-hint="true"
          :readonly="!isDeliveryProductSelectable"
          :rules="[useValidationRules.required]"
          :disabled="!hasAddresses || isDisabledForPriceRelevantChanges"
          :with-smart-proposals="true"
        />
      </div>
      <div
        v-if="
          (isRoadForwardingOrder || isRoadCollectionOrder) &&
          selectedDeliveryProduct?.fixedDeliveryDate
        "
        cols="12"
        class="mt-4"
      >
        <DatePicker
          v-model="dateDelivery"
          :label="t('labels.date_label.text')"
          :required="true"
          :min="minAllowedDateDelivery"
          :max="maxAllowedDateDelivery"
          data-testid="date-delivery-picker"
        />
      </div>
    </div>
    <div class="mt-4 align-center">
      <div class="mb-4 pb-0">
        <SelectField
          ref="deliveryOptionField"
          v-model="deliveryOption"
          :items="selectableDeliveryOptionsFiltered"
          :label="t('labels.delivery_option.text')"
          :placeholder="t('labels.select_option.text')"
          item-value="code"
          item-text="description"
          :required="isMandatoryDeliveryOption"
          :disabled="isDisabledForPriceRelevantChanges || deliveryProduct === null"
        />
      </div>
      <DfeBanner
        v-if="previousDeliveryOptionSelectionNotAvailable"
        v-data-test="'banner-warning'"
        type="warning"
        class="custom-banner mb-4"
      >
        <span class="text-h5">{{ $t('messages.id7207.text') }}</span>
      </DfeBanner>
      <div v-if="hideContactForDeliveryOptionNone && deliveryOption" class="mb-4">
        <FormContactPerson
          v-model="contactDataDelivery"
          :address-id="addressId"
          :contacts="contactsDelivery"
          :default-country="consigneeAddress.address.countryCode"
          :required-fields="requiredDeliveryContactFields"
          :hint="isAutomaticDelivery ? t('messages.id6357.text') : t('messages.id6358.text')"
          class="px-3 pt-1 pb-3 rounded-lg bg-grey-lighten-5"
        />
      </div>
    </div>
    <div class="d-inline-flex py-0">
      <CheckboxField
        v-model="tailLiftDelivery"
        :label="t('labels.taillift_delivery.text')"
        class-styles="pt-0"
        :disabled="isDisabledForPriceRelevantChanges"
      />
    </div>
    <div class="mt-4">
      <TooltipWithSlot
        :show-tooltip="isOrderFromQuote"
        :tooltip-props="{
          location: 'right',
          maxWidth: 240,
          text: t('labels.cannot_change_saved_order.text'),
        }"
      >
        <template #content>
          <CheckboxField
            v-if="showCOD"
            v-model="cashOnDelivery"
            v-data-test="'cash-on-delivery-field'"
            :label="t('labels.cash_on_delivery.text')"
            class-styles="pt-0"
            :disabled="isOrderFromQuote"
          />
          <NumberField
            v-if="cashOnDelivery"
            v-model="cashOnDeliveryAmount"
            v-data-test="'cash-on-delivery-amount-field'"
            :label="t('labels.amount_label.text')"
            :required="true"
            class="size-xs mt-4 mr-3"
            append="EUR"
            :max="99999.99"
            :min="0"
            :allowed-decimals="2"
            :disabled="isOrderFromQuote"
          />
        </template>
      </TooltipWithSlot>
    </div>
    <div v-if="showSelfCollection" class="pt-0">
      <SwitchField
        :disabled="isDisabledForPriceRelevantChanges"
        :value="selfCollection"
        class="mt-4"
        :label="t('labels.self_collection.text')"
        @update:model-value="selfCollection = $event"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SelectField from '@/components/form/SelectField.vue';
import SwitchField from '@/components/form/SwitchField.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import FormContactPerson from '@/components/form/FormContactPerson.vue';
import DatePicker from '@/components/form/DatePicker.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import { ContactsType, DeliveryOptions, Product } from '@/enums';
import useAllowedDate from '@/composables/dateTimeUtilities/useAllowedDate';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useRequireOneOf } from '@/composables/createOrder/useRequireOneOf';
import { useDeliveryOptions } from '@/composables/data/useDeliveryOptions';
import { useRoadDeliveryProductsList } from '@/composables/data/useRoadDeliveryProductsList';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { contactData } from '@/store/sharedInitialStates';
import type { ContactData } from '@dfe/dfe-address-api';
import { DeliveryProducts, Option } from '@dfe/dfe-book-api';
import { computed, onMounted, ref, watch, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';
import NumberField from '@/components/form/NumberField.vue';
import { useClient } from '@/composables/useClient';
import TooltipWithSlot from '@/components/base/tooltips/TooltipWithSlot.vue';

const { t } = useI18n();
const { client } = useClient();

const { selectableDeliveryOptions } = useDeliveryOptions();
const selectableDeliveryOptionsFiltered = ref(selectableDeliveryOptions.value);
const isMandatoryDeliveryOption = ref(false);
const previousDeliveryOptionSelectionNotAvailable = ref(false);

const { data: customerSettings } = useCustomerSettings();

const createOrderFormStore = useCreateOrderFormStore();
const {
  transportCountry,
  isRoadForwardingOrder,
  isRoadCollectionOrder,
  isFoodLogistics,
  isEuropeanLogistics,
  isDisabledForPriceRelevantChanges,
  isCashOnDelivery,
  isOrderFromQuote,
} = storeToRefs(createOrderFormStore);

const createOrderFormCollectionAndDeliveryStore = useCreateOrderFormCollectionAndDeliveryStore();
const {
  dateDelivery,
  deliveryProduct,
  deliveryOption,
  selfCollection,
  tailLiftDelivery,
  cashOnDelivery,
  cashOnDeliveryAmount,
  contactDataDelivery,
} = storeToRefs(createOrderFormCollectionAndDeliveryStore);

const addressStore = useCreateOrderAddressesStore();
const { consigneeAddress } = storeToRefs(addressStore);

const addressId = computed(() => consigneeAddress.value.address?.id);
const originAddressId = computed(() => consigneeAddress.value.address?.originAddressId);

const createAddressDataStore = useCreateAddressDataStore();
const { contactsDelivery } = storeToRefs(createAddressDataStore);

const getAddressContacts = () => {
  if (originAddressId?.value) {
    createAddressDataStore.getAddressContacts(originAddressId.value, ContactsType.Delivery);
  } else {
    contactsDelivery.value = [];
  }
};

const { minAllowedDateDelivery, maxAllowedDateDelivery } = useAllowedDate();
const isAutomaticDelivery = computed(() => {
  return (
    deliveryOption.value === DeliveryOptions.AutomaticDeliveryBooking ||
    deliveryOption.value === DeliveryOptions.AutomaticDeliveryNotification
  );
});

const isDeliveryProductSelectable = computed(() => {
  return deliveryProductsList.value && deliveryProductsList.value.length > 1;
});

const showSelfCollection = computed(() => {
  return isRoadForwardingOrder.value && customerSettings.value?.selfCollection;
});

const showCOD = computed(() => {
  return (
    isCashOnDelivery.value &&
    (isFoodLogistics.value || isEuropeanLogistics.value) &&
    isRoadForwardingOrder.value
  );
});

const hideContactForDeliveryOptionNone = computed(() => {
  return deliveryOption.value !== DeliveryOptions.None;
});

const { requiredFields } = useRequireOneOf(
  contactDataDelivery,
  computed(() => (isAutomaticDelivery.value ? ['mobile', 'email'] : ['telephone', 'mobile'])),
);

const requiredDeliveryContactFields = computed<(keyof ContactData)[]>(
  () => ['name', ...requiredFields.value] as (keyof ContactData)[],
);

const hasAddresses = computed(
  () => transportCountry.value.fromCountry && transportCountry.value.toCountry,
);

const { data: deliveryProductsList, selectedDeliveryProduct } = useRoadDeliveryProductsList(
  t('labels.frequently_used.text'),
  t('labels.more_options.text'),
);

onMounted(async () => {
  getAddressContacts();
});

watch([originAddressId], () => {
  getAddressContacts();
});

watch(deliveryProductsList, (value) => {
  const filtered = value.filter((product) => 'code' in product) as DeliveryProducts;

  if (filtered && filtered.length === 1) {
    createOrderFormCollectionAndDeliveryStore.deliveryProduct = filtered[0].code;
  }
});

watch(deliveryOption, (value) => {
  if (!value) {
    createOrderFormCollectionAndDeliveryStore.contactDataDelivery = contactData();
  }
});

watch(cashOnDelivery, (value) => {
  if (!value) {
    cashOnDeliveryAmount.value = null;
  }
});

watch(selectedDeliveryProduct, (value) => {
  if (value && !value.fixedDeliveryDate) {
    dateDelivery.value = '';
  }
});

interface FilteredOptions {
  [key: string]: string[];
}
const DeliveryOptionsFiltered: FilteredOptions = {
  [Product.TargoOnSite]: [
    DeliveryOptions.AutomaticDeliveryBooking,
    DeliveryOptions.PhoneDeliveryBooking,
  ],
  [Product.TargoOnSiteFix]: [DeliveryOptions.AutomaticDeliveryBooking],
  [Product.TargoOnSitePlus]: [
    DeliveryOptions.PhoneDeliveryBooking,
    DeliveryOptions.AutomaticDeliveryBooking,
  ],
  [Product.TargoOnSitePrenium]: [
    DeliveryOptions.PhoneDeliveryBooking,
    DeliveryOptions.AutomaticDeliveryBooking,
  ],
};

watch(selectableDeliveryOptions, (value) => {
  selectableDeliveryOptionsFiltered.value = value;
});

watch(deliveryProduct, (value) => {
  switch (value) {
    case Product.TargoOnSite:
      setDeliveryOptionValues(getDeliveryOptionsFiltered(Product.TargoOnSite), true);
      break;
    case Product.TargoOnSiteFix:
      setDeliveryOptionValues(getDeliveryOptionsFiltered(Product.TargoOnSiteFix), true);
      break;
    case Product.TargoOnSitePlus:
      setDeliveryOptionValues(getDeliveryOptionsFiltered(Product.TargoOnSitePlus), true);
      break;
    case Product.TargoOnSitePrenium:
      setDeliveryOptionValues(getDeliveryOptionsFiltered(Product.TargoOnSitePrenium), true);
      break;
    default:
      setDeliveryOptionValues(selectableDeliveryOptions.value, false);
  }
  if (
    selectableDeliveryOptionsFiltered.value.length > 0 &&
    !selectableDeliveryOptionsFiltered.value.some((option) => option.code === deliveryOption.value)
  ) {
    const isTargoOnSite =
      Product.TargoOnSite === value ||
      Product.TargoOnSitePlus === value ||
      Product.TargoOnSitePrenium === value;
    const optionsHavePhoneDelivery = selectableDeliveryOptionsFiltered.value.some(
      (option) => option.code === DeliveryOptions.PhoneDeliveryBooking,
    );

    if (DeliveryOptions.None !== deliveryOption.value) {
      previousDeliveryOptionSelectionNotAvailable.value = true;
    }

    if (
      DeliveryOptions.PhoneDeliveryNotification === deliveryOption.value &&
      isTargoOnSite &&
      optionsHavePhoneDelivery
    ) {
      deliveryOption.value = DeliveryOptions.PhoneDeliveryBooking;
    } else {
      deliveryOption.value = String(selectableDeliveryOptionsFiltered.value[0].code);
    }
  } else {
    previousDeliveryOptionSelectionNotAvailable.value = false;
  }
});

function getDeliveryOptionsFiltered(product: string): Array<Option> {
  return selectableDeliveryOptions.value.filter((option) =>
    DeliveryOptionsFiltered[product].includes(String(option.code)),
  );
}

function setDeliveryOptionValues(deliveryOptionsFiltered: Array<Option>, isMandatory: boolean) {
  selectableDeliveryOptionsFiltered.value = deliveryOptionsFiltered;
  isMandatoryDeliveryOption.value = isMandatory;
}

watchEffect(() => {
  if (!showCOD.value) {
    if (cashOnDelivery.value) {
      displayCODToast();
    }
    cashOnDelivery.value = false;
  }
});

function displayCODToast() {
  client?.toast.info(t('messages.id7217.text').toString());
}
</script>
<style lang="scss" scoped>
@use '@/styles/variables' as projectVars;

:deep(.v-input),
:deep(.v-banner),
:deep(.v-card) {
  max-width: projectVars.$form-cols-width-xl;
}

.custom-banner {
  margin-top: 18px !important;
}
</style>
