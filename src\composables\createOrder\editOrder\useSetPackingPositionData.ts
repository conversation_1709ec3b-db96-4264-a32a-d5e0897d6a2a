import { OrderResponseBody, RoadOrder } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';

export function useSetPackingPositionData(editOrderData: OrderResponseBody) {
  const orderLineStore = useCreateOrderOrderLineFormStore();
  const { packingPositions, orderLines } = storeToRefs(orderLineStore);
  const packingPositionsResponse = (editOrderData as RoadOrder).packingPositions;

  if (!packingPositionsResponse) {
    return;
  }

  while (packingPositionsResponse.length > packingPositions.value.length) {
    orderLineStore.addPackingPosition(true);
  }

  packingPositions.value.forEach((packingPosition, i) => {
    Object.assign(packingPosition, {
      ...packingPositionsResponse[i],
      lines: undefined,
    });

    const orderLinesForPackingPosition = orderLines.value.filter(
      (orderLine) => orderLine.packingPositionId === packingPosition.id,
    );

    orderLinesForPackingPosition.forEach((orderLine) => {
      orderLine.packingPositionId = packingPosition.localId;
    });
  });
}
