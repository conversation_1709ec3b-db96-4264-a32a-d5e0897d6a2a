<template>
  <VRow v-if="selectedCustomer.address">
    <VCol cols="12" sm="6" md="6" lg="5" xl="4">
      <div class="d-flex justify-space-between align-center mb-5">
        <h4 class="text-h4">
          {{ t('labels.address_type_shipper.text') }}
        </h4>
        <SwitchField
          :key="orderType"
          v-tooltip="{
            text: t('labels.cannot_change_saved_order.text'),
            location: 'right',
            disabled: !typeSwitchDisabled,
            attach: $appRoot,
          }"
          v-data-test="isOrderSaved ? 'principal-switch-saved' : 'principal-switch-default'"
          label-left
          :value="isExportOrder"
          :disabled="typeSwitchDisabled"
          @input="toggle"
        >
          <b>{{ t('labels.is_principal.text') }}</b>
        </SwitchField>
      </div>
      <AddressCard
        v-if="shipperAddress.address"
        :model-value="shipperAddress.address"
        :customer-number="shipperAddress.customerNumber"
        :is-customer="isExportOrder"
        class="mt-5"
        :header="
          isRoadCollectionOrder
            ? t('labels.collection_address_label.text')
            : t('labels.shipper_dfe.text')
        "
        :contact-data="contactDataShipper"
        :smart-proposals-enabled="true"
        :is-shipper-address="true"
        :is-deletable="
          isRoadOrder
            ? isRoadCollectionOrder && !isDisabledForPriceRelevantChanges
            : quoteAirAddressesOrigin.shipper !== HandOverSelection.default && !isCompleteRoadOrder
        "
        :is-editable="isRoadOrder ? isRoadCollectionOrder : !isCompleteRoadOrder"
        :is-currently-final-address="
          embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.SHIPPER)
        "
        :is-contact-required="!isRoadForwardingOrder"
        :address-card-type="AddressCardType.SHIPPER"
        required
        @update:model-value="updateShipperAddress($event)"
        @update-contact="updateShipperContact($event)"
        @delete-address="deleteShipperAddress()"
      >
        <template
          v-if="isRoadCollectionOrder && addressStore.isDifferentConsigneeAddress"
          #neutralize
        >
          <InfoTooltipWithSlot
            :label="t('labels.neutralize_collection_address_info.text')"
            class-style="mt-1 ms-1"
          >
            <CheckboxField
              v-model="shipperAddress.address.neutralizeAddress"
              :label="t('labels.neutralize_address.text')"
              class="mt-1"
            />
          </InfoTooltipWithSlot>
        </template>
      </AddressCard>

      <AddButton
        v-if="isRoadCollectionOrder && !showCollectionOption"
        :label="t('labels.collection_option.text')"
        variant="text"
        class="mt-6"
        :disabled="!shipperAddress.address.name || isDisabledForPriceRelevantChanges"
        @add-new-item="showCollectionOptionRef = true"
      />

      <div v-if="showCollectionOption" class="mt-6 d-flex justify-space-between">
        <SelectField
          ref="collectionOptionField"
          v-model="collectionOption"
          :items="collectionOptions"
          :label="t('labels.collection_option.text')"
          :placeholder="t('labels.select_option.text')"
          :disabled="isDisabledForPriceRelevantChanges"
          item-value="code"
          item-text="description"
          class="flex-grow-1"
        />
        <DfeIconButton
          class="icon-btn ml-2 mt-6 align-self-auto"
          :size="ComponentSize.DEFAULT"
          :color="ColorVariants.NEUTRAL"
          :tooltip="t('labels.remove_label.text')"
          :filled="false"
          :disabled="isDisabledForPriceRelevantChanges"
          @click="removeCollectionOption()"
        >
          <MaterialSymbol size="24">
            <DeleteIcon />
          </MaterialSymbol>
        </DfeIconButton>
      </div>

      <template
        v-if="isRoadCollectionOrder && addressStore.isDifferentConsigneeAddress"
        #neutralize
      >
        <InfoTooltipWithSlot
          class-style="mt-1 ms-1"
          :label="t('labels.neutralize_collection_address_info.text')"
        >
          <CheckboxField
            v-model="shipperAddress.address.neutralizeAddress"
            :label="t('labels.neutralize_address.text')"
            class="mt-1"
          />
        </InfoTooltipWithSlot>
      </template>

      <SelectAddress
        v-if="loadingPointsWithLabel.length && isRoadForwardingOrder"
        :model-value="loadingPointRef"
        class="mt-6"
        :items="[noneAddress, ...loadingPointsWithLabel]"
        :label="t('labels.differing_loading_point.text')"
        item-value="address"
        :no-data-text="t('labels.dfe_none.text')"
        :loading-point-address="true"
        @update:model-value="updateLoadingPoint($event)"
      />
      <div
        v-if="isFurtherAddressTypeAvailable(FurtherAddressTypesList.coverAddressConsignor)"
        class="mt-6"
      >
        <AddressCard
          v-if="showAddress[FurtherAddressTypesList.coverAddressConsignor]"
          v-model="getFurtherAddress(FurtherAddressTypesList.coverAddressConsignor).address"
          :is-customer="false"
          :header="useFurtherAddressTypeLabel(FurtherAddressTypesList.coverAddressConsignor)"
          :smart-proposals-enabled="true"
          :is-shipper-address="isExportOrder"
          :contact-data="contactDataFurtherAddresses[FurtherAddressTypesList.coverAddressConsignor]"
          :show-delete="true"
          class="mt-5"
          :is-editable="!isCompleteRoadOrder"
          :is-deletable="!isCompleteRoadOrder"
          :is-contact-required="false"
          :tooltip="isCompleteRoadOrder ? t('labels.status_complete_tooltip.text') : ''"
          @delete-address="showAddress[FurtherAddressTypesList.coverAddressConsignor] = false"
        />
        <AddButton
          v-else-if="orderType === OrderType.RoadForwardingOrder"
          v-tooltip="{
            text: t('labels.status_complete_tooltip.text'),
            location: 'right',
            maxWidth: 240,
            disabled: !isCompleteRoadOrder,
            attach: $appRoot,
          }"
          :disabled="isCompleteRoadOrder"
          variant="text"
          :label="t('labels.address_type_da.text')"
          @add-new-item="addNewFurtherAddress(FurtherAddressTypesList.coverAddressConsignor)"
        />
      </div>
      <slot name="shipperAddress" />
    </VCol>

    <VCol cols="12" sm="6" md="6" lg="5" xl="4" offset-lg="1">
      <div class="d-flex justify-space-between align-center mb-5">
        <h4 class="text-h4">
          {{ t('labels.address_type_consignee.text') }}
        </h4>
        <SwitchField
          :key="orderType"
          v-tooltip="{
            text: t('labels.cannot_change_saved_order.text'),
            location: 'right',
            disabled: !typeSwitchDisabled,
            attach: $appRoot,
          }"
          :disabled="typeSwitchDisabled"
          label-left
          :value="isImportOrder"
          @input="toggle"
        >
          <b>{{ t('labels.is_principal.text') }}</b>
        </SwitchField>
      </div>
      <AddressCard
        v-data-test="'consignee-address'"
        :model-value="consigneeAddress.address"
        :is-customer="isImportOrder"
        :customer-number="consigneeAddress.customerNumber"
        :header="
          isRoadCollectionOrder
            ? t('labels.principal_address.text')
            : t('labels.consignee_title.text')
        "
        :contact-data="contactDataConsignee"
        :smart-proposals-enabled="true"
        :is-shipper-address="isImportOrder"
        :is-consignee-address="true"
        :is-editable="!isCompleteRoadOrder"
        :is-deletable="
          !isRoadForwardingOrderFromQuote &&
          quoteAirAddressesOrigin.consignee !== HandOverSelection.default &&
          !isCompleteRoadOrder &&
          !isRoadCollectionOrder
        "
        :is-contact-required="!isRoadForwardingOrder"
        :address-card-type="AddressCardType.CONSIGNEE"
        required
        class="mt-5"
        :tooltip="isCompleteRoadOrder ? t('labels.status_complete_tooltip.text') : ''"
        :is-currently-final-address="
          embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE)
        "
        :apply-presets="true"
        @update:model-value="updateConsigneeAddress($event)"
        @update-contact="updateConsigneeContact($event)"
        @delete-address="deleteConsigneeAddress()"
      />

      <!-- Final delivery address -->
      <div
        v-if="isFurtherAddressTypeAvailable(FurtherAddressTypesList.finalDeliveryAddress)"
        class="mt-6"
      >
        <AddressCard
          v-if="showAddress[FurtherAddressTypesList.finalDeliveryAddress]"
          :model-value="getFurtherAddress(FurtherAddressTypesList.finalDeliveryAddress).address"
          :is-customer="false"
          :header="useFurtherAddressTypeLabel(FurtherAddressTypesList.finalDeliveryAddress)"
          :smart-proposals-enabled="true"
          :contact-data="contactDataFurtherAddresses[FurtherAddressTypesList.finalDeliveryAddress]"
          :show-delete="true"
          :is-currently-final-address="
            embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.FINAL_DELIVERY_ADDRESS)
          "
          :is-contact-required="false"
          @update:model-value="updateFinalDeliveryAddress($event)"
          @delete-address="showAddress[FurtherAddressTypesList.finalDeliveryAddress] = false"
        />
        <AddButton
          v-else-if="orderType === OrderType.RoadForwardingOrder"
          :label="t('labels.address_type_dp.text')"
          variant="text"
          @add-new-item="addNewFurtherAddress(FurtherAddressTypesList.finalDeliveryAddress)"
        />
      </div>
      <div v-if="isRoadOrder">
        <div v-if="isRoadCollectionOrder" class="mt-3">
          <VRadioGroup v-model="consigneeAddressType" hide-details="auto" density="compact">
            <RadioField
              v-model="ConsigneeAddressType.PRINCIPALS_ADDRESS"
              :label="t('labels.delivery_principal_address.text')"
              class="mb-2"
            />
            <RadioField
              v-model="ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS"
              :label="t('labels.different_consignee_address.text')"
            />
          </VRadioGroup>
        </div>
        <AddressCard
          v-if="addressStore.isDifferentConsigneeAddress"
          ref="differentConsigneeAddressRef"
          :model-value="differentConsigneeAddress.address"
          class="mt-5"
          :hide-search-field-label="true"
          :is-editable="true"
          :is-deletable="true"
          :smart-proposals-enabled="true"
          :required="false"
          :is-contact-required="false"
          :is-currently-final-address="
            embargoStore.checkIfCurrentAddressIsFinalAddress(
              AddressCardType.DIFFERENT_CONSIGNEE_ADDRESS,
            )
          "
          :apply-presets="true"
          @update:model-value="updateDifferentConsigneeAddress($event)"
        >
          <template v-if="isRoadCollectionOrder" #neutralize>
            <InfoTooltipWithSlot
              :label="t('labels.neutralize_diff_consignee_info.text')"
              class-style="ms-1"
            >
              <CheckboxField
                v-model="differentConsigneeAddress.address.neutralizeAddress"
                :label="t('labels.neutralize_address.text')"
              />
            </InfoTooltipWithSlot>
          </template>
        </AddressCard>

        <div v-if="isNotFoodLogistics">
          <AddButton
            v-if="!showDropOfLocation"
            v-data-test="'drop-of-location-btn'"
            :label="t('labels.drop_of_location_heading.text')"
            variant="text"
            class="mt-6"
            @add-new-item="showDropOfLocationRef = true"
          />

          <div v-if="showDropOfLocation" class="mt-6 d-flex justify-space-between">
            <TextField
              ref="dropOfLocationRef"
              v-model="consigneeAddress.address.dropOfLocation"
              :label="t('labels.drop_of_location_heading.text')"
              :placeholder="t('labels.drop_of_location_placeholder.text')"
              :hint="t('labels.drop_of_location_info.text')"
              class="flex-grow-1"
              :rules="[useValidationRules.maxChars(MaxLength.DropOfLocation)]"
            />
            <DfeIconButton
              v-data-test="'drop-of-location-remove-btn'"
              class="icon-btn ml-2 mt-6 align-self-auto"
              :size="ComponentSize.DEFAULT"
              :color="ColorVariants.NEUTRAL"
              :tooltip="t('labels.remove_label.text')"
              :filled="false"
              @click="removeDropOfLocation()"
            >
              <MaterialSymbol size="24">
                <DeleteIcon />
              </MaterialSymbol>
            </DfeIconButton>
          </div>
        </div>
      </div>

      <slot name="consigneeAddress" />
    </VCol>
  </VRow>
</template>

<script setup lang="ts">
import AddButton from '@/components/createOrder/AddButton.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import InfoTooltipWithSlot from '@/components/form/InfoTooltipWithSlot.vue';
import RadioField from '@/components/form/RadioField.vue';
import SelectAddress from '@/components/form/SelectAddress.vue';
import SelectField from '@/components/form/SelectField.vue';
import SwitchField from '@/components/form/SwitchField.vue';
import TextField from '@/components/form/TextField.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { useCheckFurtherAddresses } from '@/composables/createOrder/addresses/useCheckAdditionalAddresses';
import useFurtherAddressTypeLabel from '@/composables/createOrder/addresses/useFurtherAddressTypeLabel';
import useLoadingPointsWithLabel from '@/composables/createOrder/useLoadingPointsWithLabel';
import { useLoadingPoints } from '@/composables/data/useLoadingPoints';
import { useTransportDirection } from '@/composables/form/useTransportDirection';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { useClient } from '@/composables/useClient';
import {
  AddressCardType,
  ConsigneeAddressType,
  ContactsType,
  DeliveryOptions,
  FurtherAddressTypesList,
  MaxLength,
} from '@/enums';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import type { FurtherAddressTypes } from '@/types/createOrder';
import { HandOverSelection } from '@/types/hand-over';
import type { ContactData } from '@dfe/dfe-address-api';
import { BasicAddress, Division, OrderAddress, OrderType } from '@dfe/dfe-book-api';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import { storeToRefs } from 'pinia';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const { client } = useClient();
const { mobileNumber, phoneNumber } = usePreferences();

const { isExportOrder, isImportOrder, toggle } = useTransportDirection();

const shipperAddressWasUpdated = ref(false);
const showAddress = ref({
  [FurtherAddressTypesList.finalDeliveryAddress]: false,
  [FurtherAddressTypesList.coverAddressConsignor]: false,
});
const showDropOfLocationRef = ref(false);
const showDropOfLocation = computed(() => {
  return (
    showDropOfLocationRef.value ||
    (consigneeAddress.value.address.dropOfLocation != null &&
      consigneeAddress.value.address.dropOfLocation !== '')
  );
});

const showCollectionOptionRef = ref(false);
const showCollectionOption = computed(() => {
  return showCollectionOptionRef.value && isRoadCollectionOrder.value;
});
const isNotFoodLogistics = computed(
  () => selectedCustomer.value.division !== Division.FOOD_LOGISTICS,
);

const embargoStore = useEmbargoStore();
const createOrderDataStore = useCreateOrderDataStore();
const { isFurtherAddressTypeAvailable, collectionOptions } = storeToRefs(createOrderDataStore);
const createOrderFormStore = useCreateOrderFormStore();
const {
  selectedCustomer,
  orderType,
  isAirExportOrderFromQuote,
  isAirImportOrderFromQuote,
  isAirImportOrder,
  isSeaImportOrder,
  isRoadForwardingOrderFromQuote,
  isOrderLoaded,
  isRoadOrder,
  isOrderFromQuote,
  isCompleteRoadOrder,
  isOrderSaved,
  isDraftOrder,
  isRoadCollectionOrder,
  isRoadForwardingOrder,
  isAirAndSeaOrder,
  isDisabledForPriceRelevantChanges,
} = storeToRefs(createOrderFormStore);
const addressStore = useCreateOrderAddressesStore();
const { getFurtherAddress } = addressStore;
const {
  consigneeAddress,
  shipperAddress,
  differentConsigneeAddress,
  loadingPoint,
  quoteAirAddressesOrigin,
  shipperHandOverSelection,
  consigneeHandOverSelection,
  contactDataShipper,
  contactDataConsignee,
  contactDataFurtherAddresses,
  collectionOption,
  consigneeAddressType,
} = storeToRefs(addressStore);
const createAddressDataStore = useCreateAddressDataStore();
const accountingAndAdditionalServicesStore = useCreateOrderFormAccountingAdditionalServices();
const { selectedFreightTerm } = storeToRefs(accountingAndAdditionalServicesStore);
const loadingPointRef = ref<string | BasicAddress | undefined>(undefined);

const { data: loadingPoints } = useLoadingPoints();
const { loadingPointsWithLabel } = useLoadingPointsWithLabel(loadingPoints);

const typeSwitchDisabled = computed(() => {
  if (client?.env.isProduction) {
    return isOrderFromQuote.value || isOrderSaved.value;
  }
  return isOrderFromQuote.value || !isDraftOrder.value;
});

const addNewFurtherAddress = (addressType: FurtherAddressTypes) => {
  addressStore.addNewFurtherAddress(addressType);
  showAddress.value[addressType as keyof typeof showAddress.value] = true;
};

const noneAddress = {
  label: t('labels.dfe_none.text'),
  address: {
    supplement: 'labels.dfe_none.text',
    name: t('labels.dfe_none.text'),
  },
};

const updateLoadingPoint = (loadingPointParam?: OrderAddress | string) => {
  loadingPointRef.value = loadingPointParam;

  if (loadingPointParam === noneAddress.address) {
    addressStore.setDefaultLoadingPoint();
  } else {
    loadingPoint.value = loadingPointParam as OrderAddress;
  }
};

const removeDropOfLocation = () => {
  consigneeAddress.value.address.dropOfLocation = '';
  showDropOfLocationRef.value = false;
};

const removeCollectionOption = () => {
  collectionOption.value = undefined;
  showCollectionOptionRef.value = false;
};

const updateShipperContact = (contact: ContactData) => {
  if (orderType.value === OrderType.RoadForwardingOrder) {
    return;
  }
  contactDataShipper.value = contact || { ...contactData() };
};
const updateConsigneeContact = (contact: ContactData) => {
  if (orderType.value === OrderType.RoadForwardingOrder) {
    return;
  }
  contactDataConsignee.value = contact || { ...contactData() };
};

const updateShipperAddress = async (address: OrderAddress) => {
  shipperAddress.value.address = address;
  shipperAddressWasUpdated.value = true;
  if (isAirAndSeaOrder) {
    await addressStore.updateRoutingForSelection(
      shipperHandOverSelection.value,
      'from',
      HandOverSelection.default,
    );
  }
  if (isExportOrder.value) {
    const userInformation = client?.auth.getUserInformation();

    if (userInformation && !isRoadForwardingOrder && client) {
      addressStore.setDefaultContactDataShipper(userInformation, {
        mobileNumber: mobileNumber.value,
        phoneNumber: phoneNumber.value,
      });
    }
  }
};

const deleteShipperAddress = () => {
  addressStore.resetPortRoutingForSelection(
    shipperHandOverSelection.value,
    'from',
    HandOverSelection.default,
  );

  if (isRoadCollectionOrder.value) {
    removeCollectionOption();
  }
  resetDeliveryAndFreightTerm();
};

const updateDifferentConsigneeAddress = async (address: OrderAddress) => {
  differentConsigneeAddress.value.address = address;
  await embargoStore.fetchHasEmbargo(
    shipperAddress.value.address.countryCode,
    address.name == '' ? consigneeAddress.value.address.countryCode : address.countryCode,
  );
};
const updateFinalDeliveryAddress = async (address: OrderAddress) => {
  getFurtherAddress(FurtherAddressTypesList.finalDeliveryAddress).address = address;
  await embargoStore.fetchHasEmbargo(
    shipperAddress.value.address.countryCode,
    address.name == '' ? consigneeAddress.value.address.countryCode : address.countryCode,
  );
};

const updateConsigneeAddress = async (address: OrderAddress) => {
  const dropOfLocation = consigneeAddress.value.address.dropOfLocation;
  consigneeAddress.value.address = address;
  consigneeAddress.value.address.dropOfLocation = dropOfLocation;

  await addressStore.updateRoutingForSelection(
    consigneeHandOverSelection.value,
    'to',
    HandOverSelection.default,
  );
};

const { deliveryProduct, deliveryOption } = storeToRefs(
  useCreateOrderFormCollectionAndDeliveryStore(),
);

const deleteConsigneeAddress = () => {
  addressStore.resetPortRoutingForSelection(
    consigneeHandOverSelection.value,
    'to',
    HandOverSelection.default,
  );
  resetDeliveryAndFreightTerm();
};

function resetDeliveryAndFreightTerm() {
  deliveryProduct.value = null;
  deliveryOption.value = DeliveryOptions.None;
  selectedFreightTerm.value = null;
}

const handleClearCreateOrderFormData = () => {
  useCheckFurtherAddresses(showAddress.value);
  shipperAddressWasUpdated.value = false;
};
const handleCreateOrder = () => {
  showDropOfLocationRef.value = false;
};

client?.events.on('clearCreateOrderFormData', handleClearCreateOrderFormData);
client?.events.on('createOrder', handleCreateOrder);

onBeforeUnmount(() => {
  client?.events.off('clearCreateOrderFormData', handleClearCreateOrderFormData);
  client?.events.off('createOrder', handleCreateOrder);
});

watch(
  selectedCustomer,
  async () => {
    if (
      (isAirExportOrderFromQuote.value &&
        quoteAirAddressesOrigin.value.shipper === HandOverSelection.default) ||
      (isAirImportOrderFromQuote.value &&
        quoteAirAddressesOrigin.value.consignee === HandOverSelection.default)
    ) {
      return;
    }

    addressStore.setCustomerAddressToPrincipal();
    if (loadingPoint.value.id) {
      loadingPointRef.value = loadingPoint.value;
    } else {
      loadingPointRef.value = undefined;
    }

    const userInformation = client?.auth.getUserInformation();
    if (userInformation && client && !isOrderFromQuote.value) {
      addressStore.setDefaultContactDataShipper(userInformation, {
        mobileNumber: mobileNumber.value,
        phoneNumber: phoneNumber.value,
      });
    }
    if (userInformation && client && isOrderFromQuote.value) {
      addressStore.setDefaultContactDataConsignee(userInformation, {
        mobileNumber: mobileNumber.value,
        phoneNumber: phoneNumber.value,
      });
    }

    if (
      isOrderFromQuote.value &&
      (isRoadCollectionOrder.value || isAirImportOrder.value || isSeaImportOrder.value) &&
      shipperAddress.value.address.originAddressId
    ) {
      const fetchedContactsForAddress = await createAddressDataStore.getAddressContacts(
        shipperAddress.value.address.originAddressId,
        ContactsType.Collection,
      );

      if (fetchedContactsForAddress) {
        const mainContact = fetchedContactsForAddress.find(
          (value: ContactData) => value?.isMainContact,
        );
        if (mainContact) {
          contactDataShipper.value = mainContact;
        }
      }
    }
  },
  { immediate: true },
);

onMounted(() => {
  showDropOfLocationRef.value = false;
  showCollectionOptionRef.value = !!collectionOption.value;
  createOrderDataStore.fetchCollectionOptions();
});

watch(
  () => consigneeAddressType.value,
  () => {
    if (consigneeAddressType.value === ConsigneeAddressType.PRINCIPALS_ADDRESS) {
      differentConsigneeAddress.value = {
        address: { ...orderAddress() },
        addressType: '',
      };
      if (consigneeAddress.value.address.countryCode) {
        embargoStore.fetchHasEmbargo(
          shipperAddress.value.address.countryCode,
          consigneeAddress.value.address.countryCode,
        );
      }
    }
  },
);

watch(
  () => isOrderLoaded.value,
  () => {
    useCheckFurtherAddresses(showAddress.value);
  },
);
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables';
</style>
