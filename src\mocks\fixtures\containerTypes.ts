import type { ContainerTypesWithLastUsed } from '@dfe/dfe-book-api';
import { ContainerTypesAsOptions } from '@/composables/data/useContainerTypes';

export const containerTypesWithLastUsed: ContainerTypesWithLastUsed = {
  containerTypes: [
    {
      key: 'OT-40',
      size: 40,
      type: 'OT',
      description: 'Open Top',
    },
  ],
  lastUsed: [
    {
      key: 'GP-20',
      size: 20,
      type: 'GP',
      description: 'Standard',
    },
  ],
};

export const containerTypesAsOptions: ContainerTypesAsOptions = {
  containerTypes: [
    {
      code: 'OT-40',
      description: '40 Open Top',
    },
  ],
  lastUsed: [
    {
      code: 'GP-20',
      description: '20 Standard',
    },
  ],
};
