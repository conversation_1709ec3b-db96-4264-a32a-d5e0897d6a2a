import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { mockServer } from '@/mocks/server';
import { airExportOrder } from '@/mocks/fixtures/order';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { initPinia } from '../../test/util/init-pinia';
import linkAllDocumentsToOrder from '@/utils/linkAllDocumentsToOrder';

describe('useLinkAllDocumentsToOrder composable', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {
        airExportOrder,
      },
    });
  });

  it('should call linkAllDocumentsToOrder method from orderDocuments store', async () => {
    const { customerNumber, saveOrderData } = storeToRefs(useCreateOrderFormStore());

    const linkAllDocumentsToOrderSpy = vi.spyOn(
      useCreateOrderDocumentsStore(),
      'linkAllDocumentsToOrder',
    );

    customerNumber.value = '123';
    saveOrderData.value = { ...airExportOrder, orderId: 123 };

    await linkAllDocumentsToOrder();

    expect(linkAllDocumentsToOrderSpy).toHaveBeenCalledWith(
      +customerNumber.value,
      saveOrderData.value?.orderId,
    );
  });
});
