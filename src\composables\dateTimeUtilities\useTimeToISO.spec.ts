import convertTimeToISO from '@/composables/dateTimeUtilities/useTimeToISO';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { initPinia } from '../../../test/util/init-pinia';

describe('useTimeToISO composable', () => {
  beforeAll(() => {
    initPinia();
  });

  it('should convert time in 12h or 24h format to ISO', () => {
    const { customCollectionTimeSlot } = storeToRefs(
      useCreateOrderFormCollectionAndDeliveryStore(),
    );

    customCollectionTimeSlot.value.collectionDate = '2023-02-14T12:00:00.000Z';

    expect(convertTimeToISO('20:00')).toEqual('2023-02-14T20:00:00.000Z');
    expect(convertTimeToISO('2:30 pm')).toEqual('2023-02-14T14:30:00.000Z');
    expect(convertTimeToISO('12:00 am')).toEqual('2023-02-14T00:00:00.000Z');

    expect(convertTimeToISO('14:30')).toEqual(convertTimeToISO('2:30 pm'));
  });

  it('should convert time in 12h or 24h format to ISO from today', () => {
    expect(convertTimeToISO('20:00')).toMatch('T20:00:00.000Z');
    expect(convertTimeToISO('2:30 pm')).toMatch('T14:30:00.000Z');
    expect(convertTimeToISO('12:00 am')).toMatch('T00:00:00.000Z');

    expect(convertTimeToISO('14:30')).toMatch(convertTimeToISO('2:30 pm'));
  });
});
