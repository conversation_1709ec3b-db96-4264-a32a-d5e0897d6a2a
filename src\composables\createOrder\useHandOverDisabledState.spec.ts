import { useHandOverDisabledState } from '@/composables/createOrder/useHandOverDisabledState';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { HandOverSelection } from '@/types/hand-over';
import { AirExportQuoteInformation, OrderType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { initPinia } from '@test/util/init-pinia';

const defaultState = {
  default: false,
  alternateAddress: false,
  port: false,
};

function withState(values: Partial<typeof defaultState> = {}) {
  return {
    ...defaultState,
    ...values,
  };
}

describe('useHandOverDisabledState', () => {
  beforeAll(() => {
    initPinia();
  });

  it('should return default state', () => {
    const { shipper, consignee } = useHandOverDisabledState();

    expect(shipper.value).toEqual(withState());
    expect(consignee.value).toEqual(withState());
  });

  it('should disable shipper or consignee airport', () => {
    const { shipper, consignee } = useHandOverDisabledState();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(
      useCreateOrderAddressesStore(),
    );

    expect(shipper.value).toEqual(withState());
    expect(consignee.value).toEqual(withState());

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    expect(shipper.value).toEqual(withState());
    expect(consignee.value).toEqual(withState({ port: true }));

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.port;
    expect(shipper.value).toEqual(withState({ port: true }));
    expect(consignee.value).toEqual(withState());
  });

  it('should set state for shipper with "isAirExportOrderFromQuote"', () => {
    const { shipper } = useHandOverDisabledState();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { quoteInformation, orderType, isAirExportOrderFromQuote } =
      storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderType.AirExportOrder;
    quoteInformation.value = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    expect(isAirExportOrderFromQuote.value).toBe(true);

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    expect(shipper.value).toEqual(withState({ alternateAddress: true, port: true }));

    shipperHandOverSelection.value.selection = HandOverSelection.alternateAddress;
    expect(shipper.value).toEqual(
      withState({ default: false, alternateAddress: true, port: true }),
    );

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    expect(shipper.value).toEqual(withState({ default: false, alternateAddress: true }));
  });

  it('should set state for consignee with "isAirExportOrderFromQuote"', () => {
    const { consignee } = useHandOverDisabledState();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { quoteInformation, orderType, isAirExportOrderFromQuote } =
      storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderType.AirExportOrder;
    quoteInformation.value = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    expect(isAirExportOrderFromQuote.value).toBe(true);

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    expect(consignee.value).toEqual(
      withState({ default: false, alternateAddress: false, port: true }),
    );
    expect(consignee.value).toEqual(withState({ alternateAddress: false, port: true }));

    consigneeHandOverSelection.value.selection = HandOverSelection.alternateAddress;
    expect(consignee.value).toEqual(withState({ default: false, port: true }));

    consigneeHandOverSelection.value.selection = HandOverSelection.port;
    expect(consignee.value).toEqual(withState({ default: false, alternateAddress: false }));
  });

  it('should set state for shipper with "isAirImportOrderFromQuote"', () => {
    const { shipper } = useHandOverDisabledState();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { quoteInformation, orderType, isAirImportOrderFromQuote } =
      storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderType.AirImportOrder;
    quoteInformation.value = {
      orderType: OrderTypes.AirImportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    expect(isAirImportOrderFromQuote.value).toBe(true);

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    expect(shipper.value).toEqual(withState({ alternateAddress: false, port: true }));

    shipperHandOverSelection.value.selection = HandOverSelection.alternateAddress;
    expect(shipper.value).toEqual(withState({ default: false, port: true }));

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    expect(shipper.value).toEqual(withState({ default: true, alternateAddress: true }));
  });

  it('should set state for consignee with "isAirImportOrderFromQuote"', () => {
    const { consignee } = useHandOverDisabledState();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { quoteInformation, orderType, isAirImportOrderFromQuote } =
      storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderType.AirImportOrder;
    quoteInformation.value = {
      orderType: OrderTypes.AirImportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    expect(isAirImportOrderFromQuote.value).toBe(true);

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );
    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );

    consigneeHandOverSelection.value.selection = HandOverSelection.alternateAddress;
    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );

    consigneeHandOverSelection.value.selection = HandOverSelection.port;
    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );
  });

  it('should set state for consignee with "isAirImport"', () => {
    const { consignee } = useHandOverDisabledState();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { orderType, isAirImportOrder } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderType.AirImportOrder;
    expect(isAirImportOrder.value).toBe(true);

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );
    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );

    consigneeHandOverSelection.value.selection = HandOverSelection.alternateAddress;
    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );

    consigneeHandOverSelection.value.selection = HandOverSelection.port;
    expect(consignee.value).toEqual(
      withState({ default: true, alternateAddress: true, port: true }),
    );
  });
});
