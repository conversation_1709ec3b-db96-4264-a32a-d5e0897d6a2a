<template>
  <div v-data-test="'full-container-load'" :class="[isExpanded && 'mb-6']">
    <div
      v-data-test="'full-container-load__header'"
      :class="[!isExpanded && 'collapsed']"
      class="pa-4"
    >
      <div
        class="d-flex flex-row justify-space-between align-center"
        :class="[isExpanded && 'mb-4']"
      >
        <div class="d-flex align-center flex-grow-1" @click="setIsExpanded(true)">
          <div class="text-h3">
            {{ t('labels.full_container_load_header.text') }} {{ lineCounter }}
          </div>
          <p
            v-if="!isExpanded"
            v-data-test="'full-container-load__title'"
            class="text-h5 ml-10 collapsed-header-information"
          >
            <span v-if="fullContainerLoad.containerNumber"
              >{{ fullContainerLoad.containerNumber }} -
            </span>
            {{
              t('labels.freight_lines_pluralization.text', {
                n: orderLinesForFullContainerLoad.length,
              })
            }}
          </p>
        </div>
        <div class="d-flex">
          <div class="delete-button ml-auto">
            <IconButton
              v-data-test="'full-container-load-delete-button'"
              :tooltip="t('labels.delete_label.text')"
              class="ml-md-0 align-self-end mr-4"
              :disabled="isDisabledForPriceRelevantChanges"
              @click="removeFullContainerLoad"
            >
              <DeleteIcon />
            </IconButton>
          </div>
          <IconButton v-if="!isExpanded">
            <ChevronDownIcon
              v-data-test="'full-container-load-expand-button'"
              @click="setIsExpanded(true)"
            />
          </IconButton>
          <IconButton v-else>
            <ChevronUpIcon
              v-data-test="'full-container-load-collapse-button'"
              @click="setIsExpanded(false)"
            />
          </IconButton>
        </div>
      </div>

      <div
        class="d-flex flex-grow-1 flex-wrap flex-sm-grow-0 align-start"
        :class="[!isExpanded && 'not-visible']"
      >
        <AutocompleteField
          v-model="fullContainerLoad.containerType"
          v-data-test="'full-container-load-container-types'"
          :items="containerTypesList"
          item-title="description"
          item-value="code"
          :label="t('labels.container-type-label.text')"
          :placeholder="t('labels.select_option.text')"
          :required="true"
          :multiline-menu="false"
          return-object
          :message="t('labels.validation_select_input_required.text')"
          :disabled="isDisabledForPriceRelevantChanges"
          class="size-lg full-width-xs mr-0 mr-sm-3 mb-3 mb-sm-0"
          :menu-icon="ArrowDropDownIcon"
          @update:model-value="updateValue('containerType', $event)"
        />

        <TextField
          v-data-test="'full-container-load-container-number'"
          :model-value="fullContainerLoad.containerNumber"
          :label="t('labels.container_number.text')"
          :placeholder="t('labels.eg-generic.text', [containerNumberPlaceholder])"
          class="size-md full-width-xs mr-0 mr-sm-3 mb-3 mb-sm-0"
          :rules="[
            useValidationRules.regex(
              new RegExp('^[A-Z]{3}[UJZ][0-9]{6}[0-9]$'),
              'labels.invalid_postcode_structure.text',
              containerNumberPlaceholder,
            ),
          ]"
          @update:model-value="updateValue('containerNumber', $event)"
        />

        <ComboboxMeasurementFields
          v-data-test="'full-container-load-container-vgm'"
          :model-value="fullContainerLoad.verifiedGrossMass ?? null"
          :label="t('labels.verified-gross-mass-short.text')"
          :label-tooltip="t('labels.verified-gross-mass.text')"
          class="size-sm full-width-xs mr-0 mr-sm-3"
          append="kg"
          :max="99999.999"
          :allowed-decimals="3"
          @update:model-value="updateValue('verifiedGrossMass', $event)"
        />
      </div>
    </div>

    <div
      v-data-test="'full-container-load__body'"
      class="pa-4"
      :class="[!isExpanded && 'not-visible pb-2']"
    >
      <div v-if="!!orderLinesForFullContainerLoad.length">
        <OrderLine
          v-for="({ localId: lineLocalId }, i) in orderLinesForFullContainerLoad"
          :key="`orderLine${lineLocalId}`"
          :model-value="orderLinesForFullContainerLoad[i]"
          :local-id="lineLocalId"
          :is-weight-required="false"
          :optional-mode="true"
          :line-counter="i + 1"
          :full-container-load-counter="lineCounter"
          @update:model-value="updateOrderLine($event)"
        />
      </div>

      <VDivider class="my-4" />
      <VBtn
        v-data-test="'add-order-line-full-container-load-button'"
        :disabled="isDisabledForPriceRelevantChanges"
        variant="text"
        size="small"
        color="primary"
        @click="addNewOrderLineToFullContainerLoad"
      >
        <MaterialSymbol class="mr-2" left size="16">
          <AddIcon />
        </MaterialSymbol>
        {{ t('labels.add_line_to_full_container_load.text') }}
      </VBtn>
    </div>

    <ConfirmPrompt
      v-model="showConfirmDeleteModal"
      :headline="t('labels.remove_full_container_load.text')"
      :confirm-text="t('labels.delete_label.text')"
      :cancel-text="t('labels.cancel_label.text')"
      @confirm="removeFullContainerLoad"
      @cancel="setShowConfirmDeleteModal(false)"
      @close="setShowConfirmDeleteModal(false)"
    >
      <h5 class="text-body-2 text-grey-darken-4">
        {{ t('labels.remove_full_container_load_info.text') }}
      </h5>
    </ConfirmPrompt>
  </div>
</template>

<script setup lang="ts">
import IconButton from '@/components/base/buttons/IconButton.vue';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import OrderLine from '@/components/createOrder/formSectionFreight/OrderLine.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import TextField from '@/components/form/TextField.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { useContainerTypesList } from '@/composables/createOrder/useContainerTypesList';
import { useContainerTypes } from '@/composables/data/useContainerTypes';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import {
  FullContainerLoad4Store,
  getEmptyFullContainerLoad,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import { useValidationDataStore } from '@/store/validation';
import AddIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import ChevronDownIcon from '@dfe/dfe-frontend-styles/assets/icons/chevron_down-24px.svg';
import ChevronUpIcon from '@dfe/dfe-frontend-styles/assets/icons/chevron_up-24px.svg';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import { update } from 'lodash';
import { storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  localId: number;
  lineCounter: number;
}

const fullContainerLoad = defineModel<FullContainerLoad4Store>({
  default: getEmptyFullContainerLoad(),
});

const props = defineProps<Props>();

const { t } = useI18n();
const createOrderFormStore = useCreateOrderFormStore();
const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
const { isDisabledForPriceRelevantChanges } = storeToRefs(createOrderFormStore);
const { orderLines, fullContainerLoads } = storeToRefs(createOrderOrderLineFormStore);
const { updateOrderLine } = createOrderOrderLineFormStore;
const containerNumberPlaceholder = 'AAAU1111111';

const { containerTypesAsOptions } = useContainerTypes();

const containerTypesList = computed(() => {
  if (!containerTypesAsOptions.value) {
    return [];
  }
  return useContainerTypesList(
    t('labels.last_used.text'),
    t('labels.more_types.text'),
    containerTypesAsOptions,
  );
});

const showConfirmDeleteModal = ref<boolean>(false);

const orderLinesForFullContainerLoad = computed(() => {
  return orderLines.value
    .filter((orderLine) => orderLine.fullContainerLoadId === props.localId)
    .sort((a, b) => a.number - b.number);
});

const updateValue = (key: string, value?: string | number | null) => {
  fullContainerLoad.value = update({ ...fullContainerLoad.value }, key, () => value);
};

const removeFullContainerLoad = () => {
  if (fullContainerLoads.value.length <= 1) {
    createOrderOrderLineFormStore.removeFullContainerLoad(props.localId);
    createOrderOrderLineFormStore.addFullContainerLoad();
    return;
  }

  if (!showConfirmDeleteModal.value && !!orderLinesForFullContainerLoad.value.length) {
    setShowConfirmDeleteModal(true);
    return;
  }

  setShowConfirmDeleteModal(false);

  createOrderOrderLineFormStore.removeFullContainerLoad(props.localId);
  if (fullContainerLoads.value.length === 0) {
    createOrderOrderLineFormStore.addFullContainerLoad();
  }
};

const addNewOrderLineToFullContainerLoad = () => {
  createOrderOrderLineFormStore.addOrderLineToFullContainerLoad(props.localId);
};

const setShowConfirmDeleteModal = (value: boolean) => {
  showConfirmDeleteModal.value = value;
};

const isExpanded = ref(true);

const setIsExpanded = (newValue: boolean) => {
  isExpanded.value = newValue;
};

const validationStore = useValidationDataStore();

validationStore.$subscribe((mutation, state) => {
  if (!state.formValidationSectionsSea.seaOrderFreight) {
    setIsExpanded(true);
  }
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfe-variables;
@use '@/styles/settings';
@use '@/styles/base' as base;
@use 'sass:map';

[data-test='book-full-container-load__header'] {
  background-color: dfe-variables.$color-base-grey-50;
  border: 1px solid dfe-variables.$color-base-grey-400;
  border-top-left-radius: base.space(1);
  border-top-right-radius: base.space(1);

  &.collapsed {
    cursor: pointer;
    max-height: 56px;
    border-bottom-left-radius: base.space(1);
    border-bottom-right-radius: base.space(1);
  }
}

[data-test='book-full-container-load__body'] {
  border: 1px solid dfe-variables.$color-base-grey-400;
  border-top: none;
  border-bottom-left-radius: base.space(1);
  border-bottom-right-radius: base.space(1);
}

.collapsed-header-information {
  color: dfe-variables.$color-base-grey-700;
}

.not-visible {
  visibility: hidden;
  height: 0;
}

.size {
  &-md {
    width: 145px;
  }
}

.full-width-xs {
  @media #{map.get(settings.$display-breakpoints, 'xs')} {
    width: 100%;
  }
}

.v-btn.v-btn--density-default {
  --v-btn-height: 24px;
}
</style>
