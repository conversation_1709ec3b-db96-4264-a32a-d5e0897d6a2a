import type { StoreDocument } from '@/store/createOrder/orderDocuments';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { mockServer } from '@/mocks/server';
import { initPinia } from '../../../test/util/init-pinia';

import { documents, documentsResponse, updateDocument } from '@/mocks/fixtures/documents';
import { UploadStatus } from '@/enums';
import { storeToRefs } from 'pinia';

describe('createOrderDocuments store', () => {
  let document: StoreDocument;
  let documentStore: ReturnType<typeof useCreateOrderDocumentsStore>;
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        documents,
        documentsResponse,
        updateDocument,
      },
    });
  });

  beforeEach(() => {
    initPinia();
    documentStore = useCreateOrderDocumentsStore();
    document = {
      documentId: 111,
      orderId: undefined,
      documentTypeId: 123,
      documentName: 'invoice.xlsx',
      extension: 'pdf',
      startProcessing: true,
      file: new File([''], 'test.pdf', { type: 'application/pdf' }),
    };
  });

  it('removes document', async () => {
    documentStore.documents = documentsResponse;

    expect(documentStore.documents).toHaveLength(4);
    expect(documentStore.documents).toContain(
      documentStore.documents.find((doc) => doc.documentId === 111),
    );

    documentStore.removeDocument(111);

    expect(documentStore.documents).toHaveLength(3);
    expect(documentStore.documents).not.toContain(documentsResponse[0]);
  });

  it('get order documents', async () => {
    await documentStore.getOrderDocuments(1);

    expect(documentStore.documents).toHaveLength(4);
    const newDocuments = documents.map((doc) => ({
      ...doc,
      progress: 100,
      size: 0,
      uploadStatus: UploadStatus.Success,
    }));
    expect(documentStore.documents).toStrictEqual(newDocuments);
  });

  /**
   * Skipping this test for now as assertError is not working as expected in jest29
   * FIXME: Fix this test
   */
  it.skip('deleteDocument should log an error if it fails', async () => {
    // const { api } = useInit();
    // await assertError(
    //   () => documentStore.deleteDocument(1, 111),
    //   api.book.documents,
    //   'deleteSingleDocument',
    // );
  });

  it('update order id of document', async () => {
    documentStore.documents = documentsResponse;
    const orderIdValue = 1231;
    const document = {
      documentId: 111,
      shipmentNumber: 2222,
      orderId: undefined,
      documentTypeId: 5,
      documentName: 'invoice.xlsx',
      extension: 'pdf',
      startProcessing: true,
      file: new File([''], 'test.pdf', { type: 'application/pdf' }),
    };
    await documentStore.addDocument(document);
    await documentStore.linkAllDocumentsToOrder(1, 1231);

    expect(documentStore.documents[0].orderId).toBe(orderIdValue);
  });

  it('should update the document state when document has been uploaded', async () => {
    await documentStore.uploadDocument(document);
    expect(document).toHaveProperty('progress', 100);
    expect(document).toHaveProperty('uploadStatus', UploadStatus.Success);
  });

  it("should have temporary documentId when document hasn't been uploaded yet, and should be replaced by documentId from server after upload", async () => {
    document.documentId = 1234;

    await documentStore.uploadDocument(document);

    expect(document).toHaveProperty('documentId', 111);
  });

  it('should set upload status correctly once the server returns ErrorInvalidExtension as response', async () => {
    document.documentName = 'virus.exe';

    await documentStore.uploadDocument(document);

    expect(document.uploadStatus).toBe(UploadStatus.ErrorInvalidExtension);
  });

  it('should update the document', async () => {
    const { updateDocumentData } = storeToRefs(documentStore);

    document.orderId = 123;
    await documentStore.updateDocument(1, 111, document);

    expect(updateDocumentData.value).toEqual(updateDocument);
  });

  /**
   * Skipping this test for now as assertError is not working as expected in jest29
   * FIXME: Fix this test
   */
  it.skip('updateDocument should log an error if it fails', async () => {
    // const { api } = useInit();
    // await assertError(
    //   () => documentStore.updateDocument(1, 111, document),
    //   api.book.documents,
    //   'updateSingleDocument',
    // );
  });

  /**
   * Skipping flaky test for now as it has the potential to break the pipeline.
   * (Runs perfectly on local env though)
   */
  it.skip('should set state to uploadError when timeout is reached', async () => {
    documentStore.uploadTimeout = 1;
    await documentStore.uploadDocument(document);
    expect(document).toHaveProperty('uploadStatus', UploadStatus.UnspecificError);
  });
});
