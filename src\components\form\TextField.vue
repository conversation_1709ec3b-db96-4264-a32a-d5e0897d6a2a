<template>
  <div>
    <FormLabel v-if="label" :for="textFieldId" :required="required" :tooltip="tooltip">{{
      label
    }}</FormLabel>
    <VTextField
      :id="textFieldId"
      v-model="value"
      bg-color="white"
      :placeholder="placeholder"
      single-line
      variant="outlined"
      density="compact"
      hide-details="auto"
      :disabled="disabled"
      :rules="validationRules"
      validate-on="blur"
      persistent-hint
      :error-messages="errorMessage"
      :hint="hint"
      :error="error"
      :prefix="prefix"
      :readonly="readonly"
      @paste="onPaste"
    >
      <template #prepend-inner>
        <span class="v-text-field__prefix text-body-2">{{ prepend }}</span>
      </template>
      <template #append-inner>
        <span class="v-text-field__suffix text-body-2">{{ append }}</span>
      </template>
      <template v-if="showDeleteIcon" #append>
        <div class="grid-item delete-button d-flex align-end">
          <IconButton class="ml-md-0 align-self-end" @click="remove()">
            <DeleteIcon />
          </IconButton>
        </div>
      </template>
    </VTextField>
  </div>
</template>

<script setup lang="ts">
import IconButton from '@/components/base/buttons/IconButton.vue';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import FormLabel from '@/components/form/FormLabel.vue';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { createUuid } from '@/utils/createUuid';
import { computed } from 'vue';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  id?: string;
  label?: TranslateResult;
  placeholder?: string;
  prepend?: string;
  append?: string;
  required?: boolean;
  maxLength?: number;
  disabled?: boolean;
  errorMessage?: TranslateResult;
  rules?: ValidationRule[];
  tooltip?: TranslateResult;
  hint?: TranslateResult;
  error?: boolean;
  prefix?: string;
  showDeleteIcon?: boolean;
  readonly?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['paste', 'delete']);

const value = defineModel<string>();

const textFieldId = props.id ?? `text-field-${createUuid()}`;

const onPaste = (event: ClipboardEvent) => {
  emit('paste', event);
};

function remove() {
  emit('delete', props.id);
}

const validationRules = computed(() => {
  return [
    ...(props.rules ? props.rules : []),
    props.required ? useValidationRules.required : undefined,
    props.maxLength ? useValidationRules.maxChars(props.maxLength) : undefined,
  ].filter(Boolean);
});
</script>

<style lang="scss">
.v-input--horizontal .v-input__append {
  margin-inline-start: 0px !important;
}
</style>

<style lang="scss" scoped>
.v-btn.v-btn--size-small {
  padding: 0 0 0 12px !important;
}
</style>
