# [2.19.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.18.0...2.19.0) (2025-06-27)


### Bug Fixes

* DFE-4551 Introduce text wrap and remove size lg from autocomplete ([9de6075](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9de607531bfe664e0e6e8aa15aed05eb8199cc3c))
* DFE-4626 fix css of Accounting & additional services when is small view ([99b98aa](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/99b98aa5721c44ad2b2ab3166d7dc07ecec029ae))
* fix build DFE-1182 ([2a26c12](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2a26c12e2b7ee11d3c0803eb236d34a0221dcc71))
* fix package.json DFE-1182 ([142d754](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/142d7540db14f859db03fbd179993a46befa3b7a))
* fix rebase DFE-2246 ([e8cbe45](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e8cbe4586c5fbdd9f58fbfee47d27f96adec0780))
* fix test DFE-1182 ([9049472](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/90494727e93e7fecd5e2bcdc71bd79830c603e35))
* fix test DFE-4577 ([005dd58](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/005dd58fac7756f231d3e4e9aafb849fc177b59a))
* fix title on modal confirm & use dfeIcon for icons DFE-4465 ([c63e3c9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c63e3c9e8b87be34b865d6a43d09b840786b4148))
* fix XD DFE-4548 ([5262d0a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/5262d0aaec15255af52a7615767331a1b1a17f86))
* rebuild package-lock DFE-4608 ([d7cb184](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d7cb18496dccb40be537a234db73a3e7ccfb2ead))
* review XD to DG DFE-4548 ([e81603b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e81603b2b450c623ba98a840e92934ff965c600a))
* use personal icon for expansionPanel DFE-1182 ([6f850cd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6f850cd81382a5fad50329f10e551f42872aa980))


### Features

* add dangerousTest and fix test DFE-1182 ([a1a547f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a1a547fe37d8e09087ff3f08077b565de2a31afb))
* add modal to confirm delete all dangerous good DFE-4630 ([264afb9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/264afb9075522bf3a6157694ea3706ad6422b1f9))
* add test DFE-1182 ([fb3df39](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fb3df39236b5a84ac18e6b568d4c9082860334d6))
* add test DFE-4584 ([84273b4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/84273b4143f7cc2e86cea2b50725d0d71bf55e67))
* add translation on title of detail chip DFE-4548 ([3db1157](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3db11578528f39081313420f7fdb93d26eb89ca1))
* Add trigger validation DFE-4584 ([6fce397](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6fce3977695e9d4c33cc6989fbf890233edddd4d))
* create new structure for DangerousGood DFE-1182 ([e3e29a4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e3e29a4ea0a42ea34f342d4a3de0e039bfe7dda5))
* create spanField to display type DFE-4577 ([c874bbc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c874bbcdceb6e26a5255eac06192acf9e52e67a8))
* DFE-3980 scroll to the top of the form ([7d335aa](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7d335aa549305fd1a84c531b4e783b64d269d6ea))
* DFE-4442 data table poc ([d3cdf36](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d3cdf36012c5434564dea91fd0e52df6f3de4d08))
* DFE-4501 Add text type GS to road order ([5c4efa6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/5c4efa6070fcd88c05c39427e2cd911cd8d5ca53))
* DFE-4550 add dangerous goods translations for dangerous goods ([91bc47b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/91bc47b0bec7475d35e21363de3bdbbc7ee93600))
* DFE-4550 add dangerous goods translations for dangerous goods ([5554d44](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/5554d44279ba2b84861c000b8567942c0afd885b))
* DFE-4550 add dangerous goods translations for Un Number ([822bc74](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/822bc74c3752ef1bf9d73fc392cf704c9cadcd1b))
* DFE-4550 add more tests ([ccef361](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ccef3616fc03c8a879f9d327a0a12a61abf80089))
* DFE-4550 add more tests ([153aa1c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/153aa1cf03ea6da4424290aa368eb90dbf57bf6b))
* DFE-4550 add remaining fields for ADRDangerousGood ([438ce14](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/438ce14e5518f801ff5b69094f28283cf4c90fa4))
* DFE-4550 add table placeholder and loading state ([908834d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/908834ddb6a1f88f9a497c24a6cd7b3b94763ad2))
* DFE-4550 add tests ([a720acd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a720acd51e837a5d8a2f4ebff786fa483f3061e6))
* DFE-4550 add tests for table ([b56174a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b56174a470ab7ac57d706f6106c2524a05071dc7))
* DFE-4550 add tests for table composable ([a9902b3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a9902b386e2be9d58e34f1b9f2644e20dba02085))
* DFE-4550 add translations, update types, increase test coverage ([e86b80e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e86b80e71c4bc8f54da41ac521e2c9e7e308a704))
* DFE-4550 add un number detail layer ([15df489](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/15df48963a8aed969c98c7ed47938453783444c2))
* DFE-4550 comment dummy entries out ([fc43b6c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fc43b6c2e845ccd3c9aa7bf6e47e78bcc137163a))
* DFE-4550 debounce search results loading indicator ([9c2db0d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9c2db0dc6aac38a673acf8f5718502be64909563))
* DFE-4550 display selected UN Number first draft ([6c68444](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6c6844436b7c8315e416aecc321f7e1346ef5d9c))
* DFE-4550 enable single selection, disable footer ([3d7e152](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3d7e1525a20a605543308ec83dcdf453a0113531))
* DFE-4550 fix build ([b62f586](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b62f586f99f3f93f7cd197bca91cf5ad14659926))
* DFE-4550 fix build ([ad478c4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ad478c4ed903fa6d17ad417f7419d0020cd1cb64))
* DFE-4550 fix build errors ([6b6d7f2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6b6d7f25e6b210d22c509e56a4cea13b971897ba))
* DFE-4550 fix dialog tests ([eed8212](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/eed821214ac22e7c2ac9ec46e41dcfa2b8ac976b))
* DFE-4550 fix merge conflicts ([32e8bcc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/32e8bcccf6b7a1e9a9a325bdab6d6dcc4816705c))
* DFE-4550 fix style warning ([58ae986](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/58ae98622f936d99d0536faa096355e7528909bd))
* DFE-4550 fix test in pipeline ([474b1b6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/474b1b66c7a92cef8c9f07988868714bc783ddb6))
* DFE-4550 fix tests ([00edfce](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/00edfceaacfce6e4b9cf4a58d28ea759e68be661))
* DFE-4550 handle search with dummy data ([8e99ba3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8e99ba354b3e648b8612dfba45c40c56400034e3))
* DFE-4550 handle selected table element in parent component ([1babfb0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1babfb00a0d31c43b5167aeda4d6a76af7fe08cc))
* DFE-4550 refactor file structure ([0dfb7f2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0dfb7f2eccd0c0eb1bb533182dabc5bcc7f4f902))
* DFE-4550 remove console log ([420c9d5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/420c9d55fee922b1b904c83fcd3e2299db4927a5))
* DFE-4550 reset search text and table selection ([048c00a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/048c00ac18e7d541941710fdae26b63a0865ada0))
* DFE-4550 sonarqube aaaah ([15580aa](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/15580aa1dbff370644b71f1d8ac6768d81ef05d6))
* DFE-4550 update book-api version, update tests, prepare for actual endpoint ([2e36ead](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2e36ead2a8ba2f8596fff0c93ee520b5d96de501))
* DFE-4550 update emit ([68f0003](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/68f00038b50e4cf2956504f7c6566568402f547b))
* DFE-4550 update un number field sizes ([b3d450e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b3d450e0344954fb09fc6c64cd82d9f11985260a))
* DFE-4550 use book-api types for dangerous goods ([650be53](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/650be53ea8b9ebff2c338901d68980d5129138d2))
* DFE-4550 use unNumbers endpoint, make queries not go stale, fix flaky tests maybe ([a76c35c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a76c35c74f69b32a7f2dc6090cb759aff07c231d))
* DFE-4608 add DG counter field validation ([53ad2b3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/53ad2b38fae8aa8390731cef773862b1e2947283))
* DFE-4608 add environmentally hazardous ([2951748](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/295174808cd36e9517572393ad57ebf4d5423197))
* DFE-4608 add packaging options from merged changes ([9c0e5bc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9c0e5bc0e5ebee288343a8de0d7e896d1a34d5f3))
* DFE-4608 allow DG input field label to grow in width ([c78b6a1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c78b6a144122665823ca3b571954b2430b1675d0))
* DFE-4608 attempt detail layer fix ([f0b79de](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f0b79dee2f324658be65fe8baa72122c0098126d))
* DFE-4608 better handling for table placeholder and loading state ([7fe3295](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7fe32954fffb4c9d04f404cc3500d8287d95f960))
* DFE-4608 fix checkbox spacing ([626a3db](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/626a3db6c4d4d687198b02758ea50668893614d4))
* DFE-4608 fix test ([277cd12](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/277cd120bf2f90e7e1f598cfb38d025605c6aa20))
* DFE-4608 fix test ([982dfb3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/982dfb30f7b3fe169c0d480106ff71b239cdac20))
* DFE-4608 fix test ([46862e3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/46862e3950a7f245bc367258e12ef2cfd4277019))
* DFE-4608 improve code coverage ([f21a6da](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f21a6da31dd2f949c3cd51fedac128206b7f984f))
* DFE-4608 load existing order with dangerous goods ([0362aab](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0362aabba8308738337033e9132deab6d2e8eb63))
* DFE-4608 make table properly scrollable ([129a828](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/129a82892b334a9dadfd22ca3f6b63711dafba81))
* DFE-4608 open un number dialog before adding line ([b4f0f23](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b4f0f23edb7157517cd9e1ff24b72d1d2f8cfb96))
* DFE-4608 remove vuetify version restriction ([2eec226](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2eec226dac801c110ce6929c2b623a666449dc96))
* DFE-4608 unify sizes and spacing, update delete buttons ([6333105](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/63331057b0ebe211a507218d00cf3b9bf429181d))
* DFE-4608 update book-api version to release ([76ffa47](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/76ffa47394000bfaead3c01f748b74ece100bb5c))
* DFE-4608 update DG line spacing ([f7bdfc3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f7bdfc38b3d40127305a7948d7569cab554f5191))
* DFE-4608 update packaging options, add detail layer data ([8e0601e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8e0601ec8ce0f34372e07db2f795dc7627c3b63f))
* DFE-4608 update packaging options, add detail layer data ([2e74425](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2e74425de5f41f693b854892baefb0fd9b451526))
* DFE-4608 update un number display ([c5fa363](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c5fa363d9cb0c6ce7632e1c766cf36e301403c79))
* DFE-4608 use correct property name for dangerous goods ([cfd7e50](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/cfd7e506a432ea4b225a0f31885ede7e0571e74d))
* DFE-4608 ux updates ([314b069](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/314b069195b7dc58ecaba203dfa65ecd39fb649d))
* display error if there is some invalide input DFE-4584 ([52649bb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/52649bbea06481f35f936898a55105bdcb0f3cba))
* filtered list of category DFE-4633 ([885df82](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/885df82aff61de0662e30a081582221a86aab4c5))
* fix background color icons DFE-4584 ([02442d6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/02442d6b8e5ce97e8cf9236e35ceacedd86f507c))
* fix equals DFE-4630 ([7811ff1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7811ff1ff02aaa10befaf286dc51b8842f488acb))
* fix lint and XD DFE-4584 ([0d12237](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0d1223736a1eefda1c391a9bc02f12afc647ca93))
* fix XD review DFE-4589 ([6ce55ca](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6ce55ca32b319db2d39ef6852cf7f9c76365547f))
* refactoring css DFE-1182 ([601cfae](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/601cfaedd8fc62c7263af817138c817bd03c02f2))
* refactoring DFE-1182 ([77aa3a3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/77aa3a3a4212474dfdd1145a751d8fd70e18de9e))
* refactoring DFE-1182 ([55e3865](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/55e3865125c85f14533b22fd126dd0a69982ad64))
* refactoring DFE-1182 ([0229b07](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0229b072ee23c5d2ec2f301390bf9fda2f77808f))
* refactoring DFE-4577 ([662b1c0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/662b1c0509b5e08c303f1f51c0d27c1206a7aff9))
* refactoring DFE-4584 ([4ecbc03](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4ecbc0367b4e4d771b5d1890c9dcf2ac814ef255))
* sort the list of DangerousPackaging and fix test DFE-4549 ([22f5d50](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/22f5d506d059f9bf39a264ba4a18ba557c6a1fc6))
* translate code from dangerous packaging option DFE-4549 ([d9d739f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d9d739f8b7334f581d0c020aedcd5286b01b25a1))
* Unsubscribe event listeners DFE-4369 ([836c31f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/836c31f2f59ebb3bab1c8f09ae3ffffd57a337a6))
* update package DFE-1182 ([a6d112b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a6d112b4e2ff8d1c19ec943ef3ac7a379063af31))
* update to translationKey instead of description DFE-4549 ([bc4bc1b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bc4bc1b69be9730a0368b6b98f92cdf0593514a7))
* use PAM setting to display dangerousGood DFE-4577 ([c7f4d41](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c7f4d41527391cdd986f41d2a3a2231662ecac85))

# [2.18.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.17.0...2.18.0) (2025-06-05)


### Bug Fixes

* add attribute on DfeIconButton DFE-4383 ([0e91fbb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0e91fbb791d8ba24dee675ec9ed6b3a530ea593f))
* change watch to watchEffect DFE-4493 ([2ad50d3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2ad50d3b146c58d897cba20f684e359ee08b6bdf))
* DFE-3838 correct the style for principal for lg device ([bb475e5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bb475e545c8ee68643d312a3e678e1584eb88324))
* DFE-3838 Principal label update after XD review ([9322c7b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9322c7b96d6d8dab3837399334a3261f1cac73dc))
* display toast to prevent COD will delete DFE-4493 ([62a93f6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/62a93f6ffe3a25eb98c4073356a418ad3143433f))
* drop comment DFE-4383 ([54bda1b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/54bda1b737e01de71818f369416c02c3dd0bcc28))
* fix icon button with style from shared-component DFE-4383 ([965c5ee](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/965c5ee6c5638bc26ecee5fae14bed9032dce351))
* fix test DFE-4493 ([ef4a6d3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ef4a6d3d280768f2b71cf45da8b36bea447e019a))
* fix the status of complete DFE-4133 ([ed46430](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ed464309666e3a8f1f0bd6cbf1b47038409792d8))
* update 3 more icons DFE-4383 ([b278892](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b278892abf785485aa59fec3c963b0cdba5f1de0))


### Features

* add tooltip DFE-4494 ([b2f3b3f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b2f3b3f2e7c002ce29b4f3c83a776b3e320430bb))
* DFE-3980 Fix no space between principal card and header. ([dcf52af](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/dcf52af781492071f9bf2408a7c532d4cad529f3))
* DFE-4550 add vuetify group for renovate and allow patch updates from v2 ([cd0c5fd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/cd0c5fdf209e6a70622086cf85335679f0aa6ee9))
* disabled COD if from quote DFE-4494 ([d3ba109](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d3ba1096208ec03634e38af993fb5a8a0f39a256))
* display cash on delivery DFE-4043 ([115892a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/115892a065eb9d8c1f3dfb131c737e50e354186e))
* display tooltip for all area DFE-4494 ([0b74299](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0b7429977391c02aebe40c56f1f9eb41395c061e))
* fix edit to set null if we don't have value on cashOnDeliveryAmount DFE-4043 ([27fbadc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/27fbadc5856734cdffcde4805e161dab892285ac))
* fix return MR DFE-4043 ([91ad04a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/91ad04a77183194ce376fe13ab36a1d6b89628b3))
* init cashOnDeliveryAmount to null and drop the disabled function  DFE-4043 ([4f99b93](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4f99b939e47787b2fb4535051f9ad716cbf5a82c))

# [2.17.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.16.1...2.17.0) (2025-05-20)


### Bug Fixes

* add test DFE-4133 ([6112ebb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6112ebb89e31d3de771118fce0d6e309dedc4270))
* change color and hover button on address card DFE-4383 ([c0f4611](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c0f4611bb3b450e104a522c5258fe9a3f163c836))
* clear useless overloading DFE-4383 ([ff9f7c1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ff9f7c1c31714960be9c8512b6dd8db772322c8d))
* DFE-3838 correct the width of the principal field for large device ([4c9f051](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4c9f0512d8be3e1a927560e1e5a1330f702fed36))
* DFE-4416 clear final address and cover address while switching the order ([f46ccde](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f46ccde7474327338ed852c2b7ebd879ce321930))
* DFE-4475 adress contact for collection and q2b order ([a0dd762](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a0dd762ca99836897ea8254d45a7f8d19971e89e))
* DFE-4475 contact data for consignee ([80b10e1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/80b10e1972e44f19ef98a2047504a457802f407c))
* DFE-4475 improve the test coveragw ([25c31da](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/25c31da72d7e0202992e1a6bd6e5604304b277d5))
* DFE-4475 PR review changes ([703fc26](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/703fc26e5ca6aef22c86a4930cc53573955335da))
* DFE-4475 remove the test not needed ([91eb277](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/91eb27755ecd91792ca549e5e5393dfc6d7de1ee))
* DFE-4525 reset  delivery option and frieght term when consignee address deleted ([8d8255a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8d8255a35ba599cc189f07798e3482ec407d620b))
* DFE-5410 don't send gln if value is empty string ([64c9127](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/64c9127f3c723b9c2c96a5c06334b14e217dc611))
* fix label create Order DFE-4133 ([2959594](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2959594e4c871a3d74ecebe8b6772b5eed8c46cd))
* fix reset freight & delivery after drop address DFE-4525 ([29bbd5c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/29bbd5ca697f4a0edb52b3531c0b9d38c5f5ba52))
* fix the update of loadingPointRef when loadingPoint change  DFE-4522 ([05079e8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/05079e83ce90dc6d1c4cccb6b5c3289c6eedc1cf))


### Features

* add a mandatory attribute if we have one or more custom documents DFE-4472 ([e824b66](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e824b66edb877a9ef79e4e701a32ec68ee5edd94))
* add exception to change the delivery option DFE-4389 ([c57fdfc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c57fdfc352620d9484f10a9668747e312e1d2478))
* DFE-1202 always disable goods value fields, update tests ([96f9ba4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/96f9ba4ec4abcf2eaf047e230bce74b924118211))
* DFE-1202 apply q2b disabling to road collection orders ([f2a34c3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f2a34c36a9e13dbc1955dc2b4642a35ef7c06d2c))
* DFE-1202 fix order type in test ([92ac70a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/92ac70aafd1af380c932f5ee4dee9904bb96b4cf))
* DFE-1202 handle disabling of order summary goods value and currency ([17b74dd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/17b74dd791cbc340d6f46d9c8e172dfa7b62b37b))
* DFE-1202 use correct disabled check ([80957a7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/80957a706dbe3abfafc66c84b468f107df56c876))
* DFE-4246 Fix styling issues for collapsed FCL state. ([98c32d2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/98c32d2638db9ac3af6b7fa8e4a3bc8ef3d6e716))
* DFE-4409 cleanup old files ([fd44b34](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fd44b34db2dea979c9e4d156edca70fbdf3a0c30))
* DFE-4409 commit package-lock.json ([a2a7cf0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a2a7cf07788cf1b980bc3508c1f03bf35f27508b))
* DFE-4409 merge develop, update dependency ([1c82996](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1c82996b6555f621956781272a9cf010e1efd0b4))
* DFE-4409 merge develop, update dependency ([6fda5dc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6fda5dcd43d5cee4c2a97a91f12028664f4d2885))
* DFE-4409 pin dependency versions in package.json ([a332074](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a332074067cc91f161b82eae4faef7f19da405d4))
* DFE-4409 unpin dfe dependencies ([0fed586](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0fed586c15ff8300ef2206e15ea2bc64f4a29e08))
* DFE-4409 update deprecated config option ([d60f020](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d60f0201c571fdc2df1a2e4ca6e08211bad8d4e5))
* DFE-4409 update eslint related packages ([21c785a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/21c785ac8ad65a14ff5b06ee43000b74299d6e1b))
* DFE-4409 use eslint flat config, fix eslint errors ([1667795](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1667795baf12cdbd28251f50d1c3f36fbcf34ca0))
* DFE-4442 fix dependency warning ([1c9d24e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1c9d24eef1e28c844442516a4abf0da510416e20))
* DFE-4442 fix switch style override, fix additional services label spacing ([85e24da](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/85e24dae58280754a62045503eda690a557dd563))
* DFE-4442 fix usage of space function ([ddcb0be](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ddcb0bedffaff91d5ae4afbd92418a6f3a576030))
* DFE-4442 import overrides styles ([e6c3482](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e6c34829d075d663c259c7a3101dd8a61041f3d4))
* DFE-4442 sass update first version ([f177bd9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f177bd9ca33b303d88215c830a0ee5ce77e27efa))
* DFE-4442 update package-lock.json ([f4aff4a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f4aff4a72759ae519d81d7bdfe59056b1a3542b1))
* DFE-4442 use correct sass variable ([3787113](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3787113bdcd97464a060008d8d8635cc561e9c3f))
* DFE-4442 UX fix ([be5f25d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/be5f25d07478883ab910db28cd0a9996ab3d1ce5))
* DFE-4506 - Hide "+ Add container" button for LCL ([bbc2222](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bbc22222c663c9d26aa936dfe8073ac2ce1001e9))
* DFE-4506 Remove console.log ([f599d44](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f599d44d661a76ad9782cf8f121537083c4d0e72))
* DFE-4521 reactivate renovate bot, update config, update packages ([346c0be](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/346c0be666b8de2f23ddbaf9de35deff2aaab956))
* DFE-4521 update package-lock.json ([0a5ecf2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0a5ecf29da5820c076dbb1a2d11bbbe879053df6))
* DFE-4535 Implement Ireland logic for fetching delivery products ROAD ([129ba2b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/129ba2b3942b8fe0a54ad3fe0c0e3759ff257534))
* don't display the banner if the previous value is none DFE-1726 ([ba752a5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ba752a560bc0fb8a897ba1d676210a56b02468ba))
* fix test DFE-4368 ([1dcea73](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1dcea738ea63a75db801667896414b5cd760e801))
* move boolean to be more readable DFE-4389 ([9ef0f25](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9ef0f25eca1f7407afd66c896865bed120c8fc25))
* reactivate Identification Code Transport DFE-4368 ([f3d2f9f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f3d2f9f1ea1934478d6201a72b9c31fed860008d))
* remove customTypeValidationError if we delete a document DFE-4472 ([eac84c9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/eac84c97715e7fa578a06c15195e2f44a9c3835d))
* show the banner delivery options have changed DFE-4389 ([42f98a1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/42f98a1d12e859d4f7ebf18a3df00e8800eeaa04))
* use phoneInput from shared-component DFE-3234 ([3a71bcc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3a71bcc9331d72a025e4ebc19a85951046395be7))

## [2.16.1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.16.0...2.16.1) (2025-04-28)


### Bug Fixes

* DFE-5410 HOTFIX don't send gln if value is empty string, update dfe-book-api ([13765f9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/13765f92dd0f4c31f6c39da1984ede0386e08620))

# [2.16.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.15.0...2.16.0) (2025-04-22)


### Bug Fixes

* DFE-4285 Code review fix - skip the validation for eircode if the principal address is from Ireland. ([cd9cf5d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/cd9cf5da6950ef8f5d43575931d492c63a000674))
* DFE-4285 Fixed blocking the order submit for Irish principal. ([f278110](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f27811079fcb56d8ad5a875cd4f71438aae26420))
* DFE-4381 Delete orderline inside full container load when only one is left in second container ([d417e53](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d417e535481a936ea57df5e3f1410b4ae0f3d4e3))
* DFE-4393 Fix tail-lift delivery on order load ([76680e5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/76680e5330db5bcce4824ad6c4be7a51de288d42))
* DFE-4393 update snapshot ([30d614f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/30d614f5cf30adf0e2f8d571d5ff50215c74fc19))
* DFE-4399 Dont show custom time slots ([49ce35e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/49ce35e33c7218aa4510edd9c7e2fc6e4c81f286))
* DFE-4408 update dfe-frontend-client and packages ([3a614fe](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3a614fe9855752563b2d94648fbd77083dd3ed47))
* DFE-4410 Adjust logic to make Switch from FCL to LCL work ([f77fe08](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f77fe083583b6de53eacbd747f94cb43216d08ea))
* fix css about container display DFE-4459 ([85712c1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/85712c1d8e280298c4eac41f33eb327ea167976a))
* fix css between from and to in collection DFE-4414 ([bba21d5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bba21d5e575ecc60be7c854707a518e31fe2dc34))
* up the timeout to fix the flaky test on datepicker field DFE-4457 ([6c28887](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6c2888712e20547fe1c2c867c74dd8c9336d8f11))
* use margin instead of flex DFE-4459 ([e3d9dc9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e3d9dc920dfd4727c6d7e53a3f01cc7a58b8b162))


### Features

* add shared component dependencies DFE-3234 ([54aaca2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/54aaca200b13a05e6cdfa1f4aaf2e3d3f5a29673))
* DFE-4078 Introduce mimetype instead of extension to make it consitent ([36ddc60](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/36ddc602368be3c15f5ceb835fe4837a7a8aa11f))
* DFE-4173 Use only lastName as contact name if firstName and lastName is longer than 30 characters. ([70b994d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/70b994dad661d10f0f10de679df91cf2780626f0))
* DFE-4246 XD review styling adjustments for FCL collapsible state. ([8767b28](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8767b283f5c915f551c7bea0631c330ba1bf3f62))
* DFE-4381 Only sent shockSensitive and stackable when it is not a FullContainerLoad order. ([15b4742](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/15b474244a52918596c544972353f92a557791a9))
* DFE-4381 Simplify code ([ff45192](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ff451923affe5f5211b08f18f18672a3f865a9a1))
* DFE-4403 fix remaining tests, move incoterm logic out of getter ([8594859](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/859485987ea9d7a02d73c882352f5c90e030444a))
* DFE-4403 fix useTransportDirection.spec.ts ([94e93b6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/94e93b6fa9159009b5602239fc3fd7fa6fb44f1e))
* DFE-4405 Reset product selection after consignee address deletion. ([62f2b88](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/62f2b880e1a311da78526cc13ea30664e7f05eaf))
* DFE-44078 Raise API ([cbc6299](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/cbc6299b840094a92db813fb69e6a4bf5aadc6e4))
* DFE-4409 update core-js, sanitize-html, vue-pdf-embed, vue-upload-document, types/lodash, types/sanitize-html, rollup-linux-x64-gnu, rollup-win32-x64-msvc ([bebfd39](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bebfd39bcee493d2153a692bf0a13b39e9bc6a41))
* DFE-4409 update del-cli, is-ci, jsdom, jsdom-testing-mocks, postcss-prefix-selector ([45fa018](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/45fa01872e14bae054d8f18f9c54a8a10f3f13bf))
* DFE-4409 update Dockerfile and README.md ([811d873](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/811d873ed84e18a35e6ac895cd9021270ce10784))
* DFE-4409 update node, @intlify/unplugin-vue-i18n ([ec190d5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ec190d5db54054d8ec440b0b2bd2f4c14d74d0c6))
* DFE-4409 update prettier, commitlint, concurrently, husky, lint-staged ([6bb3c4d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6bb3c4df26c699e693ddcb76c235a07e61a81cac))
* DFE-4409 update uuid, vitest, tsconfig, eslint-plugin-node ([3d00056](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3d000560f423d1e4213ad1e495cf12194fbe0390))
* DFE-4409 update vite-plugin-federation, vitejs/plugin-vue, vite, vite-svg-loader, vite-plugin- stuff ([8cca650](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8cca650ee35d344ea100d90e4874bc4c6e486e8e))
* DFE-4409 update vue, vue-i18n, vue-tsc ([a87a409](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a87a409c56ce800417a8f2a328c39e6e5d0d31a3))
* DFE-4409 update vuetify ([df951b8](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/df951b8c52854c243d63286ff8f1954de2073e7f))
* fix margin bottom DFE-4389 ([fd90698](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fd90698bbffe215b31b6f5332c9a6aa9278889ed))
* fix test DFE-3234 ([2077307](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/207730783d7217c51cd9062498a62f23903acbc8))
* get the country code from language DFE-3234 ([54f041d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/54f041d78738319b7fa1a6dc96d2602c4b670500))
* upgrade shared-component DFE-3234 ([4b4aac5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4b4aac5b042d8bc30565ee03901e294ec135e9c6))
* use DfePhoneInput DFE-3234 ([bd2739e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bd2739ed46fb30cb476066a041d7c73a9bcf50ff))

# [2.15.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.14.2...2.15.0) (2025-04-01)


### Bug Fixes

* add a props to be required and display the asterisk DFE-3951 ([8e75ed4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8e75ed46142332d98169ea37ea33ec484ca5eefe))
* DFE-4026 delete extra margin ([fd32377](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fd32377219f4de790c9a32507c2f026b04748754))
* DFE-4163 Adjust remove logic for full container loads ([5685307](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/568530710fc5f8a4218fe10092673354cf3f5139))
* DFE-4163 allow 0 as quantity for fcl order lines ([dfcb868](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/dfcb8689de606a53bcab1054585eaaf2ed2cc9ae))
* DFE-4163 fix order line delete button spacing ([8b87beb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8b87beb693e32b0ec71856892859ee21da0f874a))
* DFE-4163 Make freight type change more readable and fix minor bugs ([66aa471](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/66aa4715bd202251602adf31296a74dc0f6c9af0))
* DFE-4163 Remove positive validation for orderLines inside container ([7ef3678](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7ef3678c59ff1ddc2ce428b3545873e2a053d996))
* DFE-4163 Revert falsy made changes ([115344f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/115344f153f4993d34e796fafe46c8a55336050e))
* DFE-4163 Simplify orderLineCounter to be more readable ([5f943c7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/5f943c7d071c3c08d99b5d6500ec045baae90d63))
* DFE-4163 Some code simplifications ([9abdbec](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9abdbec34b4ba63c98550b0abae378d7df4e5bf7))
* DFE-4163 update isPositiveNumber validation rule ([d300b3c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d300b3c9effe587699c63226b3dba260e2fa935a))
* DFE-4163 Use right radius for button group item ([51e62d3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/51e62d30abe2f4532e9e198f1c8239aff54ad5f4))
* DFE-4263 Determine sortingPosition of fullContainer when saving ([9125b29](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9125b29dadb405767f1cb7659a31d2eb5484a1b0))
* DFE-4362 correct the margin for adding more reference ([95e2230](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/95e22302e6bcf8e64c1d5f0acd438da87ee2c2e3))
* DFE-4377 Introduce right handling for order texts and do some proper testing ([4a33197](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4a331979fee6402d4c73c80307c7980380e747b5))
* DFE-4382 fix counter field width for asl ([2eef376](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/2eef3766d76ef414c019b25fbcc41bcc24dba3d8))
* DFE-4382 update the freight section style ([c2afe2c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c2afe2c748d202b41d3111006120256e66d09f68))
* DFE-4394 Don´t show LCL/FCL toggle on production ([631e1ee](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/631e1ee67bc7b6ff9b533b5283a983b736e820fe))
* DFE-4394 Don´t show LCL/FCL toggle on production ([74e3439](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/74e34394671be49453d33935e8757d6855cf849f))
* feed the isRequired field DFE-3951 ([95dcd88](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/95dcd88d6e6fca57ea2b7bc2225a4d36dbe0c0c2))


### Features

* add css or radiobutton DFE-4332 ([d53d971](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d53d9715824736d057cfeff2357e355ddfb58a35))
* add disabled on deliveryOption if there is no product selected DFE-1726 ([f18a691](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f18a691b886b16faad16a0e0ca6c791664f1e9db))
* add reviewer to MR from renovate DFE-4262 ([746b9bc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/746b9bc3024deac42df0201b8a7a1e746105b0ce))
* add TargoOnSitePrenium DFE-1726 ([48509c6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/48509c6387a5f104abed187a38860e68671ecf08))
* add test on watcher who change deliveryOption value if needed DFE-1726 ([b008377](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b008377c81172e5761743e91eb6c38528d1475a2))
* DFE-4026 Change teh column layout for colllection ([abd74cc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/abd74cc13a024d9be5a8dad7b6df093839cf9546))
* DFE-4026 delete console logs ([afb6c32](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/afb6c320d6d78ce933c7894b33e0ff6e4a9ab721))
* DFE-4026 fix margin for collection order ([28230c5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/28230c591e9218f3a99d3d784793c7a44b22080b))
* DFE-4026 PR changes and test fix ([83e37f5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/83e37f58dee5764b272737d55c8be863a8c08775))
* DFE-4026 revert the changes for time slot ([c475952](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c4759520483402adaa052d3f2ff7f1e172685ce6))
* DFE-4026 test fix to find collection option component ([8eb0daa](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8eb0daaea43796469353677743d5b2ef2a0a2ddb))
* DFE-4163 Adapt deletion logic for full container load ([7a6b6a2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7a6b6a2d72e0f27d98600fa774fcd9473d2b682a))
* DFE-4163 Add right translation ([71c3030](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/71c30302f8f7a304d85195adef43e001c2b897f0))
* DFE-4163 Change edit behaviour to be working. ([9301ac3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9301ac3f6360cc8af34943cc6adf492d18af0d8d))
* DFE-4163 Implement own component for FCL switcher ([85ab336](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/85ab336e8ff4b6319b6407b8efebc4a63bc3c281))
* DFE-4163 Introduce basic container handling ([1888c6d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1888c6d11f647f35e61495ce1d40d96441cc910d))
* DFE-4163 Introduce basic structure in the logic to handle full container loads ([93cc2e6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/93cc2e62cd029b9e0542d3ca134ea1b65bea7338))
* DFE-4163 Introduce local storage handling for full container load ([39753c4](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/39753c4134c68a0dac23ce9374f5d47e4b249a49))
* DFE-4163 Make FullContainerLoad saveable ([a77eacc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a77eaccb066279c0d75e15c9469bd256ebe38921))
* DFE-4163 Minor fixed und write tests ([a2f0239](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a2f0239c94551c71145e2c8c1b1dda721c52a846))
* DFE-4163 Update cancel behavior for freigh type change ([c0e27c5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c0e27c5e4449c009f385754e2b63bd9fa8995af4))
* DFE-4163 Update cancel behavior for freigh type change ([37de8a9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/37de8a91ec403c88d5b78dd0e30e4860a603526e))
* DFE-4163 Update cancel behavior for freight type change ([49cf906](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/49cf906607f1ce461541d12532f89e530b74f93e))
* DFE-4163 When fullContainerLoad is lastUsed from local storage then build up structure for it ([4f4868a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4f4868a69b6bf9610171180f4d5a68208bad400b))
* DFE-4239 remove commercial document as required ([75cc006](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/75cc0061a79c6cd1c60b9038bf07091f2f8a8d89))
* DFE-4246 Code review changes. ([29d2046](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/29d204646d106764e8930a90cfec7b96fa80ccab))
* DFE-4246 Implemented collapse function for FCL containers and show container details in collapsed state. ([dbb7300](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/dbb7300f8a5258ed76adc50b397cc58b7922e7c8))
* DFE-4247 Adjust order summary for sea fcl orders ([20e2dfc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/20e2dfc6d242e38483dc2234c524b9dd8cfeceb1))
* DFE-4271 Introduce last used nature of goods for ROAD / AIR / SEA ([1b1daad](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1b1daad7271d7fff02a9f72b032b8b7e570ddbaf))
* DFE-4302 adding test for reseting document array when container updated ([d36eceb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d36eceb4b69fa1ace1e2ad12e605bc742cb29722))
* DFE-4302 ducument reset when container change ([e36e751](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e36e751e81c6e683d1d824625601c00b26e4e30e))
* disable selection option if no product DFE-1726 ([51c9c4d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/51c9c4d4a7b2e6fbc84b6bb4d7e6f6acaacc1e05))
* reactivate booking reference DFE-4366 ([4754743](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/47547437d48f24c6bb2f512ff3045e80c90a2282))
* refactoring the update of deliveryOption DFE-1726 ([538a176](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/538a1764e2fa99f69b2bfafcc44ad42969afe05a))
* refactoring the watcher DFE-1726 ([170e439](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/170e439ae0cc4cc46838122b275648999f15ca47))
* update banner test DFE-1726 ([74093e9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/74093e988623f909806759152fc33dd52fe1e5e5))
* update renovate to ignore our package DFE-4262 ([1f058c0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1f058c0380c1a3ee60efcc070dba912b1364a679))

## [2.14.2](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.14.1...2.14.2) (2025-03-20)


### Bug Fixes

* DFE-4375 Disable collection order for production ([7a7aaa7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7a7aaa7cdf38b297991d8cef46001fb3e9634baf))

## [2.14.1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.14.0...2.14.1) (2025-03-17)


### Bug Fixes

* DFE-4375 Disable collection order for production ([0c307a6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0c307a67380f1dae1729bc5cdc81f1b4fb54b536))

# [2.14.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.13.0...2.14.0) (2025-03-14)


### Bug Fixes

* DFE-4275 disable handover for ASLimport order shipper with port ([4598936](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4598936027519390454026d2b881f93253f19cba))
* DFE-4354 Enable collection order for production ([b37bbfd](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b37bbfdb81c7db595dd9757c04a7198c82585285))
* fix coverage DFE-4275 ([177b473](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/177b473e0488dc08d16e56563fbc52a436edd363))
* fix reception of validationResult on checkAndSaveOrder DFE-4322-bis ([7618bb7](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7618bb7576a7c0cc1d683bf189131228bde0c608))
* reactivate error message from backend side DFE-4322 ([4fadfcc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/4fadfcccd72f05ecf4ace44760f7bc5baa0324ca))


### Features

*  update book-api DFE-4276 ([f272245](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f2722459c0eef1df805f4c7b73868597429cb81b))
* add booking reference in OrderReference DFE-4275 ([a62eb98](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a62eb980f5e8ee9cd68e626e22757c2c90b8cf75))
* add margin on neutralize icon DFE-3998 ([d66f390](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d66f390e10a9cd31dbfc5f4e36f75e0113748f8c))
* add new ordre reference Identification Code Transport DFE-4276 ([df3aa8c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/df3aa8ccd5d2724ef888cb2038b3d1eb65e194c6))
* add required rule for UIT DFE-4276-bis ([6a84750](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/6a84750afcf068cb07bd4b848d104e33da5920e7))
* change DP value to finalDeliveryAddress DFE-4275 ([d59cc95](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d59cc9570ca8f5169975be8cb21b6ccd51f246cd))
* clean Ekaer when its hidden DFE-4276 ([c5903cb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c5903cbb76ab66508b7282a6c92cf8bf2e53c0f8))
* deactivate test DFE-4275-bis ([50fb5f3](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/50fb5f362e73707b8c771e1e985b05c3aa6c9b6c))
* hidden booking_reference DFE-4275-bis ([32c9b5d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/32c9b5d459e1888f307971182705a6426925397a))
* hidden invoice text if we are on collection order DFE-4364 ([7015f72](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7015f72b35a200b98c45ec13f80c6c4a036e3a82))
* past rules optional DFE-4276 ([f6eae3e](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f6eae3ef377e4f31e69c0f4aeecd3e12a0793b72))
* remove bullshit DFE-4276 ([3d04549](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3d04549919c7c683d6586efae0b435877fef324a))
* reset value if we hidden invoice text DFE-4364 ([89fc4f6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/89fc4f6743ec71a8cbfc325ea93844714c3c2624))
* test if there is a value before to add some rules DFE-4276 ([bae4217](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/bae4217502241f900a64526e2ad259c6af7fa851))
* truncated UIT from backend value DFE-4276 ([7aecdce](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7aecdcee1fb95e7ac503dd1b06c1c0e358d2e5d4))
* update label error DFE-4275 ([f80d59b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f80d59bd7a74de6ca45da117dd4291e411166078))
* update label of custom declaration DFE-4206 ([11b2097](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/11b20976a4d341d0f311429c9b67c941ebdad533))
* update label UIT error and add a delete icon on OrderReference DFE-4276 ([8286c91](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8286c91ae7c430881b6a75576d124b62aa5b31e1))
* update snapshot DFE-4275 ([446dccb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/446dccb3bb6f5bfbde72599c3021e2e00bac0c7f))
* update some XD review for the new button delete DFE-4276-bis ([02d3b4b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/02d3b4b6217ffadc8176764954d83de40f4bad83))
* update test DFE-4276-bis ([7bfb04c](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7bfb04cb5e730fa6720bb5d71c28607c7c58937e))
* use skip for test DFE-4275-bis ([fea0492](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fea0492e5c10005f1594a095e069689651707197))

# [2.13.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.12.0...2.13.0) (2025-02-27)


### Bug Fixes

* DFE-4325 revert book actions to V1 ([a0c0364](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a0c03646c2cb3eb2df7eff2336e5e471a7855641))


### Features

* add color and hover and active Chip DFE-3888 DFE-3892 ([77d3f9b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/77d3f9b3d8a0b3d0ddd179c8b488b97b42505264))
* add hover on checkbox DFE-3888 DFE-3891 ([46b6bdb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/46b6bdb205f1d93fb700431e43fde74e73abd0f4))
* add test with the new propertie hasBorder DFE-3888 DFE-4064 ([723a8bc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/723a8bc192afa5501c240abf1f2c01ceff7d7c9a))
* DFE-4189 Book API Update ([128e298](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/128e29800c78b9b066468c3ae9641966f1d66f6b))
* DFE-4189 don't switch address ID when switching addresses ([467cc61](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/467cc611810c240111db7dcff48b910b9c6efa5f))
* DFE-4189 don't switch address ID when switching addresses ([0e33ce6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/0e33ce6cab261ee3b06e3394292dc039748270be))
* DFE-4189 enable order type switch for draft order, correct address id switch ([7e3acec](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7e3acec101a40ecac6fa173955e6341f94e76ab4))
* DFE-4189 handle order refetch and detail page routing on save ([9b926fa](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/9b926fa1dc240dab4808dedae6e2eb872f8cb2ca))
* DFE-4189 revert change for address IDs ([1641470](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1641470ae04e4e51fe2dc5cbf976248effed0475))
* drop comment DFE-3888 ([202ea65](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/202ea6540769454f8226b8edc40f4b30614ae4cd))
* drop opacity on icon datepicker DFE-3889 ([eda1f2a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/eda1f2a077a12f5bea8fc8a22473e89f1344ec9d))
* drop the wrap to fix the position of markAndNumbers DFE-4269 ([af1a98f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/af1a98f482eb7ff657b40b8c688da75d9bf2f2ef))
* fix cloned banner DFE-3888 DFE-3920 ([d7a143f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d7a143f3ef1031e872f865b125204e06b9961e48))
* fix close icon size color and position DFE-3888 DFE-3893 ([b71015a](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b71015af43df2740ca7973eecd068ea562ad0612))
* fix Datepicker in color and placement DFE-3888 DFE-3889 ([f2090a6](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f2090a6d3862e2b483dbb4210dddd95e0d2ba118))
* fix position of headline in contact chip overlay DFE-3888 DFE-3894 ([5846e9f](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/5846e9f4ce571cd82b3402a4ecf66d14bd3ee63c))
* fix UI DFE-4064 ([8dadb01](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/8dadb01bb466ff3a0bc6b3c880742a4fe14dde1d))
* fix UI of close order form DFE-3888 DFE-4064 ([f0cb0d1](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f0cb0d1bcf0b4034e1d6ea75db767a88b296cfc1))
* fix UI to be granted with figma DFE-3893 ([b972cbb](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/b972cbbfde2675b98a85cf5af71be09b9a3d76ce))
* implement renovate bot DFE-4262 ([ec8d5dc](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ec8d5dc1d0ecf98d317298f832d165a7858a87a5))
* update card with DFE-3888 ([a8514be](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/a8514be4635665e798349073672d348aa04038c0))
* update snapshot DFE-3888 ([f87e8bf](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f87e8bfe1057e2307adf6f4622d5a31c7185f06c))

# [2.12.0](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/compare/2.11.0...2.12.0) (2025-02-12)


### Bug Fixes

* add rules of weight only for the first line DFE-4226 ([27d8c8b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/27d8c8b96a1a9d45607d7b8e327de562754ccb8e))
* DFE-4077 Simplify logic ([1c1472d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/1c1472d9fd18eb5614201af8f832a27db7574dd2))
* DFE-4168 add more button correction after XD review ([fed155b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/fed155b88d3f4dd4f9b9d0df759d6e44c811e802))
* DFE-4168 style and position corection for HScode ([ecbf9e9](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ecbf9e9843dc914349b56db1ea9832c861b4b8b0))
* DFE-4181 loading points ref problem ([f27449d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f27449df7d857acec3626a290eb28979a737e33d))
* DFE-4183 add disabled tooltip for port selection component ([ba24175](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/ba24175d058c4db7a46ddd68a3d2c234407b3972))
* DFE-4183 add test for disabled airport input ([f33e278](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f33e2780881b4dbbdc8b20a9c221e31bc7a5993e))
* DFE-4183 add test for disabled airport input ([3afe295](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/3afe2954a25d66ed334fbe9c6c3b9d3c58c2b240))
* DFE-4183 disable airport input when relevant handover option is disabled ([e518013](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/e518013f90e02263c9b2579a2af56ff88937f769))
* event issued when the form is ready  DFE-4180 ([7b7f706](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/7b7f706485a3300610aec8a9143744397989a5d1))


### Features

* DFE-4171 remove the max length for vueity element ([c102ea5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/c102ea596a533b27de2d58fc68c1ee9a70c7a2c7))
* DFE-4171 remove the max length for vueity element ([68dd03b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/68dd03be536f87473d72086325777634885f589d))
* DFE-4175 reenable packing positions ([dda7ab5](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/dda7ab5d7b507f96a63b5ed66aab45c28c8e5ff4))
* DFE-4215 PR review changes ([287d45b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/287d45b642428a101e3a4a76163e52797c3c8a16))
* DFE-4215 reset port for collecting order and display message ([598e85d](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/598e85d25dbe6af0d08b4805e16f86c8114b0cc5))
* DFE-4215 snapshot fix for test ([d103eac](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d103eac847e660c432bacbebc4c7e00b6cfaf782))
* DFE-4237 Code review adjustments. ([d256114](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/d2561141b39da1b3fda04ee6514f5eacd519d212))
* DFE-4237 Code review adjustments. ([18657ba](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/18657ba3e60e46936b7d5526387f3232a6406028))
* DFE-4237 Principal contact for Road Collection Order always required. Fixed saveable invalid contact data. ([f15a79b](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/f15a79b967d2c734c822be4dbf581985e0e649a9))
* DFE-4237 Show the edit icon for the principal for Road Collection Order. ([36bbd37](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend/commit/36bbd3729eb9ef5671c4435e46e2591e8c18d53c))

# [2.11.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.10.0...2.11.0) (2025-01-16)


### Bug Fixes

* DFE-3835 Dont show frostProtection for collection orders ([511d007](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/511d00765877ff7fc1ab868f6a733bc71990da4c))
* DFE-4081 airorder value update ([f815ed5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f815ed587aa21ee0c16dc17fa15c356069d5988d))
* DFE-4081 port routing for  Q2b ([4b7fa32](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4b7fa32e9304d2127ee0f67db5b2c205b275c26c))
* DFE-4081 PR review with a test ([f3f8a61](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f3f8a613096edf90a1309c19d0e7b1f1daf578ac))
* DFE-4135 update weight limits for air and sea order ([d54ba4f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d54ba4ff49a22cbae0e891f5702fbd92fc4ce27f))
* DFE-4137 replace translations from other apps ([06246eb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/06246eb9b3094e093d623b7769bd647edf6df88b))
* DFE-4142 add test ([f235081](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f23508149390e4a9eecf239efc1f59ce3ddba7ce))
* DFE-4142 fix contact name validation ([20aff57](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/20aff57fa10fcc38bcc4d27a17366a2041d96b29))
* DFE-4142 fix directive warning in local development ([16d603c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/16d603ca542deec1a51c659baa93acb84123dfd6))
* DFE-4142 fix edit address closing with invalid contact data ([b957d8c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b957d8c41178af35dfb79b276da40208f3c08c0f))
* DFE-4142 remove max-length prop ([2d369ee](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d369ee55401dd04583f0d64e6aa59ca4419aa27))
* DFE-4143 show the datepicker completely whiole scrolling ([7be9abc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7be9abc103337ceb52fc912d0c47c846911fc7ba))


### Features

* DFE-3835 Add customsGoods when saving order ([8b7a7df](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8b7a7df3d4db663edf97434d5b8b10bb51e6d256))
* DFE-3835 Introduce freightPayer and additional services for collection orders ([cea616a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cea616affa4422a28d3354ba8625b2f14664b3ea))
* DFE-4033 Introduce validation for duplicate entries in order references Road/Air/Sea ([938fc6e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/938fc6e6a402cb6e97fba842bfa6896d63711121))
* DFE-4033 Show DfeError banner when dupicate entry of order references is detected. ([b3d7c35](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b3d7c350e4773983c367118b04553c8055332f57))
* DFE-4033 Use composable for duplicate entry logic ([2d851dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d851dda2e4d26784ad36f17b6b5bd826ed18dc6))
* DFE-4074 add data-test attribute for road goods ([4bb69f2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4bb69f21bc8107abf9a3f7eb6b2326714926a611))
* DFE-4074 add data-test attributes for road happy path ([8c8e321](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8c8e321b7713d3c10a7848d617363ac7b6f2e0bb))
* DFE-4074 add data-test for customs declaration ([171c706](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/171c7061c442fb6e4e40d109824b1f782639cea6))
* DFE-4079 allow packing positions by default ([7217876](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7217876fea0b12431c5d9163853b70582dabd597))
* DFE-4079 allow packing positions by default ([1525cba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1525cba491a28e83d6febb224696cb7a72707fbe))
* DFE-4079 always add empty order line to new packing position ([f511e43](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f511e430923ccebddf8382b254246ac21bb03c0d))
* DFE-4079 disable packing positions feature ([e1d3767](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e1d37672d7ed4c4baaf9be8ed3a7a1c26aed178c))
* DFE-4079 use release version of book-api ([efd3cf4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/efd3cf4da340cfa7db09886b055e3a2365a88203))
* DFE-4108 add tests for new code and update existing ones ([2601ea3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2601ea320b195e1763526b19c3875c53c6f88f0e))
* DFE-4108 handle load and save of packing positions ([5acd6c7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5acd6c74dd2a293e12fee9957fb654feb35ba141))
* DFE-4108 init packing position ([f84c3ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f84c3bac39968dd63dccd848126f08796a42cdc3))
* DFE-4108 update order line data-test attributes to fit naming convention ([f337573](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f3375737faa7e8d802afe00334da91fe75436503))
* DFE-4108 use v-if instead of prop to hide buttons ([588a6ac](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/588a6acfaa1ae38e16a783f38d01775aedac953d))
* DFE-4109 add explicit type for modelValue handler ([632e146](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/632e14602bc4c3fae3640bfef9a8e0871353b2fb))
* DFE-4109 add SelectPackingPosition tests ([57f97cf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/57f97cf936094f2ceadaaf9213b7c8f08a2648f8))
* DFE-4109 add store tests ([72d02b7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/72d02b768c69680accca0303f382d1e837b47be0))
* DFE-4109 enable moving of order lines and removing of packing positions ([97a7ab0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/97a7ab04a79440d2bd39783cc66e09431481e6ce))
* DFE-4112 add orderLine test ([495a766](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/495a766113f1b16ad49971bc80819c5572c4e620))
* DFE-4112 add test, fix code smell ([67ec531](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/67ec531e3f1f57132ad185dfe642107c97e8367f))
* DFE-4112 add test, fix code smell ([ec30fc4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ec30fc4b6b712aef3e19d5d12d9cfab0d287ff60))
* DFE-4112 fix linter errors, better updatePackingPosition ([36520da](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/36520da2843d6c750252a5b5dc0d8eb1660f8047))
* DFE-4112 fix tests ([1c22309](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1c2230930b7d48f3a4dd0b8ad2b638f7f386e7e7))
* DFE-4112 handle order of order lines ([ed609bc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ed609bcf2c45520ba513190b4b81dedd11b156a4))
* DFE-4112 handle packing position right ([74619c7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/74619c789d0aaae260dd9b783e844ea68b2c4898))
* DFE-4112 merge main feature branch ([130440a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/130440ac31f0ca896b725c198d161c36d7198d6b))
* DFE-4115 Hide packing position button for AirOrder ([23804e3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/23804e37c89dda292b25bb7873d38ce1c1515d04))
* DFE-4125 replace waitForExpect with vi.waitFor ([2df2728](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2df2728a9ff55665b8c13da80639dcbd0dcbb5e5))
* DFE-4126 fix packing position dropdown spacing ([bb70abb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bb70abb7d35e830e43c84f3e16a8cddcee637648))
* DFE-4126 remove line ([f357244](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f357244b26c9fb82c8a4b1d0a25c62df8bdd8814))
* DFE-4126 update customer settings, add empty order line to first packing position ([e089329](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e089329d96537141ebf4e9990ee9b03ed4e4b4ff))
* DFE-4126 update snapshot ([1e9a99e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1e9a99e9f369773fa875a17f7e155cc80664384e))
* DFE-4135 fix sonarqube issues ([30aaebd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/30aaebd86f446f178e6dac7d96665a7340e576a9))
* DFE-4135 fix tests ([82738f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/82738f83256f13a9f280d3fe0230ad1d5b6e9a1c))
* DFE-4135 move data-test directive for NumberField.vue to component ([7c3d2e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7c3d2e6d25453d10f046ef4a4a2e9bb4abea6166))
* DFE-4135 revert changes, use combobox for number input ([b0a9775](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b0a9775fbf402ac80dbe4091150fbd3f642a6fd9))

# [2.10.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.9.1...2.10.0) (2024-12-11)


### Bug Fixes

* DFE-3630 Remove unnecessary snackbar ([7725fd3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7725fd3a690d5f43693c8d8632cf32145cc25d00))
* DFE-3769 missing contact in address card ([27297fa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/27297faaa9baf8ebee279560790579e2a914f1f9))
* DFE-3769 test fix for contact data ([dfe266d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dfe266dbc5733bc8d828637eb0dddb5a795c127d))
* DFE-3926 Fixed select date on enter in DatePicker component. ([9c3d59d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9c3d59de7ef71329484b8af38e0787aa0c3889fa))
* DFE-4007 add more snapshots ([ee11c10](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ee11c10dfc36b63559bed3b39e63a08073cb928e))
* DFE-4007 add tests for AddressesTransport.vue ([1a1aa51](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1a1aa51783df93a9e4ab7bd7663484b1a4cc3014))
* DFE-4029 Adapt validation for ekaerNumber ([79ad09f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/79ad09fa424af607dfd5aa3bdc78f92c5f16a5c8))
* DFE-4029 Don't useTimeFormat from frontend composable - instead use own implementation ([540ede1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/540ede1ac2d8ca7953d3e33eab42dae8aa5edf99))
* DFE-4036 update port routing for no postalcode ([6bbb0fd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6bbb0fd6363e9615f7d7a48668585640afcead21))
* DFE-4063 Fixed switching from principal with order groups to one without blocks the order from being submitted. ([c83b061](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c83b061e448570a436bea2c1eb8d7b9faba628dc))
* DFE-4068 fix test ([68a06c8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/68a06c82e137a83533356d7e344634b36a80570b))
* DFE-4073 fix valid document check for uploaded files ([ec8f903](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ec8f90351a6e1107e12e3ff22033d28454991810))
* DFE-4073 remove TODO ([12c9c1e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/12c9c1e052775aa02ce0bdba34a057d6f20afb57))
* DFE-4081 port routing for china from quotetobook ([905f6a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/905f6a239702b09dbc21e486e0f52c1cbe930d2f))
* DFE-4081 Revert the changes for portrouting ([ddc0969](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ddc0969caa09c3970378b6b028bd95311232c8af))
* DFE-4083 adding address for different consignee correction ([1dcc60a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1dcc60aef35f4047caadba0e6770f2a44d4d1ecc))
* DFE-4083 removing console log ([4ce7cf5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4ce7cf51e23a24af0ccdfc566985ad187e552ee4))
* DFE-4086 Fixed address search error requests sent on clearing search field. ([5219609](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5219609e6336591d616796a10ac973657d252766))
* DFE-4118 Q2B different dliver address problem ([8ef298c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8ef298cea3696f8798092c1f97e4b3e6f4dff00d))
* DFE-4118 Revert the changes to disable the different deliver address ([da908dc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/da908dcf90119d1f98bb04f547af26f91c5ef247))
* DFE-4118 Test fix for useHandover for alternate address ([21525c6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/21525c63eeca39566c715135f3419e02bde4bee4))
* DFE-4118 Text fix for handover ([00bf23f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/00bf23f61a939a91ccf561a5d4089765012d0d6b))
* DFE-4134 disable drawer close on print labels, add tests ([d3ac09c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d3ac09cd6f7702b5bde272e65436ec2dfb5eb2c5))
* DFE-4134 fix triplicate orders created with print labels action ([4d82edc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4d82edccc852cb866552e62879bf02a1645f6427))


### Features

* DFE-2802 Implement error banner for invalid port routing ([44c9547](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/44c9547a054683593bc74b0dbf10d5bc9b08cbf5))
* DFE-2823 add banner for address duplicate entry ([dc4b293](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dc4b293d0d614cb3ee39979539dff04f1f1c1508))
* DFE-2915 Add new document on the bottom of the list. ([53b62ab](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/53b62ab25139d733c019ca07b34f3d59ecdfa5f3))
* DFE-3215 Changed airport / port icon to search icon in airport / port search field. ([6533778](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/65337783163b9176af4404bfd42dfcfcd8c3dde8))
* DFE-3237 Add error modal for port/airport routing not possible ([10eec41](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/10eec4106cf340e566ad1ccd3cc35336de277ae4))
* DFE-3926 fix click in date picker ([8463e2f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8463e2f48631f9cacbfe69d60564d17c0030e534))
* DFE-3959 add disabled pointer ([0ff0a98](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0ff0a985c70f13defd04eadfe8625e7d3a070d98))
* DFE-3959 add disabled pointer for checkbox and text field ([1b5f5d8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1b5f5d8a50a5c51adc3e6309db6352ee4a039175))
* DFE-3959 add missing tooltips ([6040654](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6040654e0cedda2d01244c2a0c754b0d4e190e20))
* DFE-3959 add tests for new component ([3cd27db](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3cd27db7eba427b0224961d32f443e37de739475))
* DFE-3959 checkbox fixes ([def4d97](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/def4d9742082b7a6a34af29512c536a782be2f9f))
* DFE-3959 fix hover of disabled fields ([68cc818](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/68cc8182faafa89457718d319f6b528e6e12d5cf))
* DFE-3959 fix text input and dropdowns disabled colors ([9653448](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/965344821b51a3fc560d8e8a971dc634a30cadc2))
* DFE-3959 make wrapper tag dynamic ([130fc2e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/130fc2e35179c3dd1d1669f5ab58b23a1b1a2424))
* DFE-3971 Preset from address only for consignee address and different consignee address. ([1d232e8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1d232e8128d1d86838f93964abbc29e700807964))
* DFE-3971 Reduce function complexity for updateAddress in AddressCard.vue for Sonar. ([f4c4dca](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f4c4dca6b29a4d015274623926f03a6e8d4aab7f))
* DFE-4007 add test for principal switch ([01cc81d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/01cc81dd25f359c0e97e77171d8365601970e315))
* DFE-4007 fix coverage for SelectAddress ([f26252c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f26252cd6797e75536cada82ee133abc6796fb7b))
* DFE-4007 groovy seveties ([2519427](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/251942724d27ead867f3aa32c412ed06fe9300e7))
* DFE-4007 replace commented code with skip ([37e18c1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/37e18c160893290f29bc92581992f8b317ab4703))
* DFE-4060 filter document categories that don't supoprt file type ([edbe3f0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/edbe3f0667e3cb77c6f3c3879fe2b31a5698dff0))
* DFE-4060 fix tests ([db12da7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/db12da7d173975f6a6939d9cd2656b1017036740))
* DFE-4060 show categories for files without extension ([229f7e5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/229f7e5b3ef0ff2e44ef3364febb9a3b96efc66b))
* DFE-4060 use some instead of map and includes ([dda0b6b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dda0b6bc6234ec1e4b20fbba3ef895444e07b968))
* DFE-4063 Code review adjustments. ([0520e61](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0520e61f0c8812ee39923400428c628a776ca9ab))
* DFE-4080 fix tests ([69b29b0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/69b29b04f9b75d6b9352291a212c37ea4cc5d83f))
* DFE-4080 fix tests and AddButton max width ([07bd4f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/07bd4f8521ccdf34db4bd42e840b019dd0d5cec2))
* DFE-4080 make tooltip component more flexible ([3159eb5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3159eb585e450206a872c59b5745ea46185e7a94))
* DFE-4080 remove old code ([e4c174d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e4c174d7c4a5d18df50735d56c7c3b281dc6d190))
* DFE-4080 use new tooltip component ([4e1b6d0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4e1b6d08c4770e92fa29cdba606ed5092d892fc4))
* DFE-4080 use text prop for tooltip so tests still work ([ea767a1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ea767a17abf6235e9e851446f27e7b7701f8e6c6))
* DFE-4080 use v-tooltip directive where possible ([fcc26d3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fcc26d3dcfe51575fb7a2cda6a7a63929c53ceae))

## [2.9.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.9.0...2.9.1) (2024-11-18)


### Bug Fixes

* DFE-4029 Don't useTimeFormat from frontend composable - instead use own implementation ([26cb6e5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/26cb6e5ee5798bb2706940b6085718ff34527d8e))

# [2.9.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.8.0...2.9.0) (2024-11-13)


### Bug Fixes

* DFE-3861 Correct default value for errorMessages - AutocompleteField.vue ([c5760d1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c5760d120a696b4b731365a2c2bfe05ca6bde435))
* DFE-3861 Remove not necessary error attribute for AutocompleteField.vue ([ade4263](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ade4263fc0797685f2a4ae9de5f5f46ea6f33a2c))
* DFE-3881 Only set contactDataDelivery for consigneeAddress ([f09a3fa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f09a3fa83b50559e3f439250fa9101dbda29b017))
* DFE-3907 change checkbox field for backend validation ([c97b60a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c97b60ae0ac3748e13f5d70ffbaeb05d5e89ee9b))
* DFE-3907 PR review ([527e84b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/527e84bfdf6dda6bd597d3a804149b1fa586883d))
* DFE-3907 PR review attribute fix ([627e26c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/627e26c7e2ff744e83769b2cb3dd467c2897f90c))
* DFE-3952 Use timeformat for from and to ([79a4957](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/79a49579bbf0a51388418c12a8c1ffc08d3e4a32))
* DFE-3952 Use timeFromDate from frontend composables ([ec090bd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ec090bd53650291187017001131443a2680d1258))
* DFE-3990 show savetoaddress checkbox only for Newaddress ([5cf825d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5cf825d4ff77b3d135281e9ebbddc48ad83fad08))
* DFE-4002 Use customerNumber and transportType for customerQuery ([d0e48fa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d0e48fa34d7a466fb76ac9f495343ac8a98d22f1))
* DFE-4015 add tests ([4846d1f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4846d1fa824a9d0894d2b6ad8f30ee9ef7926575))
* DFE-4015 change type of orderId from event ([ffa5d8f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ffa5d8fd73070324f590794a4306abcd8ef0be67))
* DFE-4015 handle bulk printLabels ([9c113dc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9c113dc231ee56328d6ea93a1a6399941589fd31))
* DFE-4050 fix label store test ([d38168c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d38168c733612e3926e3d5a47fb9a0cb73c290f9))
* DFE-4050 fix response format for bulk print ([4991fe8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4991fe856a09954dc30a4c25faa50f25cf2dbbc3))
* Do not validate postalcode if not required. ([c7c6cd2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c7c6cd2446193ec07d917599f6654f3b83544967))
* Dont use coalescing ([9bf57a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9bf57a78e3e3cc69d4d4e3df73e178748e72b27a))


### Features

* 3989 Order Groups items update ([2a91180](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2a9118018266ef3d469260c138de4629f57a302b))
* Code refactoring. ([08813e5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/08813e554fae1a5fcc0773acb8e6de82366d6587))
* DFE-3836 Implement order text CollectionInstructions for collection order ([f1d3fd8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f1d3fd86b98df9e3d7958d6308091ed057af4279))
* DFE-3861 Implement embargo check ([e7b5c34](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e7b5c344fdac3a29bb351af5fc5918e3923ef611))
* DFE-3861 Show validation in navigation list if embargo check has failed ([75d6624](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/75d662412f7917cc4a7b6ebe8dc55e6eed93f76f))
* DFE-3885 Add error handling for technical errors ([dc83de5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dc83de5f7ede2d880d0ee12a3b4af92e0fcbef40))
* DFE-3885 Simplify code and adjust tests ([b926525](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b926525cb7af81fce55dbd19c02f284913d03fdb))
* DFE-3929 add husky pre-commit hooks ([73e72cb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/73e72cb8828cd51c4d542a2ccf14852a72d43f9b))
* DFE-3929 fail on lint warnings ([42acddb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/42acddbfe6f291e6eee54569143b2254d1524666))
* DFE-3929 update husky setup ([6b04105](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6b041054e3ddecebfeabd0f3672d1677a6cb9890))
* DFE-3931 Introduce PascalCase for components ([ef53c65](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ef53c65c84611ceb7c3e8ebb41d566261faf10e5))
* DFE-3989 Order groups changes after po review ([05d9db2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/05d9db2d73e5df5715b13eb35feaea0a57dcde6c))
* DFE-3989 PR review ([6ae3f6e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6ae3f6ed712b2a4d9da3b00e3282c19dba2b7c61))
* DFE-4001 change the label name in loading points ([dad270f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dad270f3e57839c1e1d6ac24dbfd56f4868acb9f))
* DFE-4001 correct teh loading points in card ([45a942c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/45a942c86c5580f6ed1b9c7ca67594c84ae6827b))
* DFE-4001 correct the test and change the label name ([943b466](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/943b4669423d5ef125485a6fcb35962707b97e94))
* DFE-4001 loading name update ([0a3844b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0a3844b6546830d795e0f3fe6423e6b36e08eeaf))
* DFE-4001 PR review fix ([e6018fe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e6018fe19bae6cb2013b8901e195eab7e922195a))
* DFE-4040 total volume for Road order ([ff822a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ff822a28ad52ee8e4ecad734bc64b17d1b51206e))
* DFE-4045 update dependencies ([49df864](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/49df8646a2c8086c16d3940822d73a26b50e9136))
* Order groups Api angepasst ([1b53d14](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1b53d14a0c01ea943f862d4a92390750fdb93a2e))

# [2.8.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.7.0...2.8.0) (2024-10-31)


### Bug Fixes

* DFE-3881 Only set contactDataDelivery for consigneeAddress ([bcd968b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bcd968b55f901101225c83b723596634adc428f4))
* DFE-3990 show savetoaddress checkbox only for Newaddress ([02199d7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/02199d7133fe7be484ded4775cd1a74217633cfc))
* DFE-4002 Use customerNumber and transportType for customerQuery ([9079313](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/907931372736a4ee52b4f5ab9f915955dd00c9ee))
* DFE-4015 add tests ([69e6de7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/69e6de799ffa304946280047705e73b79ba06bfd))
* DFE-4015 change type of orderId from event ([f9b78fa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f9b78fab373d10f4f14eb756e12568adbb40910f))
* DFE-4015 handle bulk printLabels ([1dfadff](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1dfadffa124ef643c9217eaa6f8c66b0b314b37b))
* Do not validate postalcode if not required. ([6795e66](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6795e66459f5b097bdb7a30285b3b7571f059c64))
* Dont use coalescing ([ba1197c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ba1197c265579c9f38e7c06ca3822e608a477946))


### Features

* 3989 Order Groups items update ([051e86f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/051e86f4ef0d574dcaf7c6e74f36b3ebd728b5f0))
* Code refactoring. ([0f63cc4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0f63cc4a252e5cfc034fe230da4213bb69e2a7ca))
* DFE-3885 Add error handling for technical errors ([1de89e2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1de89e2930134ce3907bc66df2aa96d65ea9c500))
* DFE-3885 Simplify code and adjust tests ([6b7b61e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6b7b61e478980c1b2b301dda6d428bc1fc4fdcef))
* DFE-3929 add husky pre-commit hooks ([d0ee8f3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d0ee8f375d30dcf03d149b7e56c0aecdb5e1fde6))
* DFE-3929 fail on lint warnings ([b6a80e7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b6a80e7b51d57ecb5b58ba446fdfd0c714069770))
* DFE-3929 update husky setup ([1135ba2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1135ba26fbfc668afbc7152d2bc3bf2df412a683))
* DFE-3989 Order groups changes after po review ([f99c673](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f99c6735a75fd3f5ebe200a3ba3fdc04fe2fff9a))
* DFE-3989 PR review ([9d082a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9d082a2323b1130119633a64e8d98537eac7b51b))
* Order groups Api angepasst ([4c12919](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4c129195fe4fbac4659f43dfce71fe14d191d0bd))

# [2.7.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.6.1...2.7.0) (2024-10-22)


### Bug Fixes

* DFE-3832 Use right section for customers ([8ccd65f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8ccd65f012d5d08d398bf3cd2b843ddc665cdca2))
* DFE-3834 Do adjustment for collection option and interpreter to match given API ([492d929](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/492d929ecbbdbd2aa589454fd0ff0ee3cc133e83))
* DFE-3834 Re add falsy deleted colelction option ([eba79a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/eba79a713e10195f989e7036b7027bd2b1fda2a0))
* DFE-3857 Remove hint from select address ([7c526b2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7c526b224c9334e2745aea63f104d5979da99d16))
* DFE-3857 Set collectionOption to undefined when removed ([01192e5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/01192e5d6879d55c6cb48d93fc2986eb18bb1bff))
* DFE-3857 Use right variable for if condition ([725b5a5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/725b5a5f76bfd1b6d63c4cd5b036163e129d4d5c))
* DFE-3858 Set differentConsigneeAddress to undefined if it is not set ([9c2d6b0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9c2d6b051619df9b10576199282d02dd000549bb))
* DFE-3858 Show collection order function also for not food logistics customer ([fc8b567](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fc8b567fb2b04993e37052a4cb456cd906025ca7))
* DFE-3890 Updating button color in Address card ([6027c9a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6027c9aa84727b8413190109f1dea07b8d366ea9))
* DFE-3911 add test ([9d06ccf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9d06ccf24f443f22afcdfa3dd78470739fd72004))
* DFE4-3911 order text bug ([5d2a7a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5d2a7a7a461a99e7ce29871b2755aa67215cf977))
* DFE4-3911 PR review Test fix ([981ef83](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/981ef839069a78c1926fc77464cfb1c7af523366))
* DFE4-3911 Test coverage ([6ac4aca](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6ac4acae0989fd9b904ab09bd7eec2bc5d8fd2b8))
* Fix test. ([1421869](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1421869a45e0161fbd527606be2f3ae77ba8604d))
* Fix validation for too long postcode. ([8bf8c23](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8bf8c239c4ea1022609af631895667300b9be155))
* Fix validation for too long postcode. ([c2f4166](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c2f41669614896af256ae46c9d1061e218c1d23c))
* Fixed low resolution of labels when printed from dialog. ([bcd1465](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bcd1465717b71d4ad8c599d3bb43bfdcb33eef16))
* Postcode check for too long postcodes. ([4877a49](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4877a49767410f77d1b2d4c6bfcc6d05640a07dc))
* Send search string in lower case to address book to fix inconsistent search behavior. ([70b2a3d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/70b2a3de327e8142f8ecc946e8a5dfe0e04db367))


### Features

* Added checkboxes to set neutralize address true or [secure] for shipper and/or different consignee address. ([a382dcb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a382dcbf1021535717744fd7fb8e808480f857b3))
* Adjust logic for showing tooltip text. ([215420b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/215420bc68a863af34781ab51c951509af00105b))
* Clean-up AddressCard. ([f45970b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f45970b2a8abd1f9fbe566ed9588493ad94a524e))
* DFE-2618 add data-test attributes for upload list ([2a5078b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2a5078b7fe7161c965d06f24f91c1d9e4990386a))
* DFE-2618 add data-test to toast ([a00c086](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a00c086917adc7ebb3447090f1a6b586dc939cb7))
* DFE-2618 re-add changes for toast update ([99a3114](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/99a3114fed7265f80650fff2863ba696d5f2bc88))
* DFE-2618 revert frontend-client and frontend-composables version ([ba09c40](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ba09c40b918137cc8edc99ecd313a311c81f5807))
* DFE-2618 revert frontend-client version ([59b2f5d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/59b2f5de23200b89387fb42cd2f95eaa91a49f22))
* DFE-2618 use release version of updated packages ([f41bb1f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f41bb1f93f029acbf4cceb0cd895a14e87e0352e))
* DFE-3576 add tests ([4dea986](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4dea9861f20a9ba5120287448b23aa579ae66ba2))
* DFE-3576 book orderform focus ([b94007b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b94007bff1a6a008807d6ff93d2022446b3358cd))
* DFE-3576 fix focusing and update tests ([60c4efe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/60c4efec17e468c1b3894df69a43e0b5d3b3408e))
* DFE-3576 focus road order on process start ([dd9ff97](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dd9ff9728b1a5fcf7c1450e01e90f008de24b5ee))
* DFE-3641 - Add request_origin header to requests ([6223292](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6223292f439f68191ef156aa607accc8eb91be0e))
* DFE-3641 - Add request_origin header to requests ([3b2259f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3b2259f2a88d38e17c5d510013bbe44d8d48c639))
* DFE-3832 Adapt collection order for principal switch ([b2e2d8d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b2e2d8d60e70bda78ee5d00d19886bbf347af94e))
* DFE-3832 Adapt collection order for principal switch ([714802d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/714802de805a380d11254255009490bda8ee38f3))
* DFE-3832 Change translation when order type is road collection order ([600d204](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/600d204b2661f07e0e6b4c0013d90bb05e154096))
* DFE-3832 Change translation when order type is road collection order ([100a4ed](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/100a4eda4fadc6e321f218afa668fadd3eaaf3f7))
* DFE-3832 Implement feature flag for principal switch ([2d2ef17](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d2ef179a2e214a362ee2b335c74c03530670deb))
* DFE-3832 Implement feature flag for principal switch ([4b9d3da](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4b9d3da95a59bbe3f49929ca8825695c0c2fa328))
* DFE-3832 Move ref to store ([d8c413d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d8c413de655a5b83cabdd3d50c40c6c307a326ea))
* DFE-3832 Save collection order with differentconsignee address ([56d0a4a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/56d0a4acdb1e3f6e279c5d92e915b0e841315218))
* DFE-3832 Save collection order with differentconsignee address ([9644943](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9644943655a43f7b75b9299e06646e0750a251ce))
* DFE-3832 Should reset different consignee address on radio button click ([1612c46](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1612c46117bc1ea5e84ed560f1017a63d02b6e1c))
* DFE-3832 Use book api released version 6.5.0 ([4393191](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4393191eac8788d4bd254130ac520729fbf98835))
* DFE-3832 When roadcollecting order then shipper address can be edited/deleted and consignee not ([5e73def](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5e73def13026b4a761b2ff5ee67e6157fc2b46e9))
* DFE-3833 Show total quantity also for collecting orders ([9cbf18c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9cbf18c202bdf100ec32e156863844d2f2ec1e73))
* DFE-3834 Adjust collection form to match AC´s ([5b409f3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5b409f37b1236856ea5370933a50e7873ff30e1e))
* DFE-3834 Map code from interpreter to upper case. ([bbbb144](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bbbb14413c676ca17506e5db64e853c59b0ab9b9))
* DFE-3834 Save and load tailLiftCollection for collecting orders ([1580211](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/158021186bcd32f75c5c9c9407cff0500bdf22b7))
* DFE-3857 Add collection option for collecting order ([b56a7ea](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b56a7ea09519db3abeb2b6948522d650f33d3b99))
* DFE-3857 Add validatio for contact when collection option is set ([e12cf0b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e12cf0bebe816a73276d0429f812bb5a47a04918))
* DFE-3857 Adjust logic for required fields in contact form ([ff007f7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ff007f7cf2776daec90914955b3f821af5007c4b))
* DFE-3857 Do some simplifications ([f88a325](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f88a325ccd692f15c9e76ff6c48a41c1f53deb8a))
* DFE-3857 Show collection option for collecting orders and set default contact when adding shipper ([d657f86](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d657f861943f554ff11aa3af340fec26a3abc3ed))
* DFE-3870 add Contact for collection order ([0991854](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/099185471c579071386968e63892e3a7b420c030))
* DFE-3870 Code review ([bac4145](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bac41451fd9a75630da9e93033231d1c1128913d))
* DFE-3870 Contact for Road ([76b7c2d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/76b7c2deeee2d87b4204f1ba0a2819a907331bcf))
* DFE-3870 merge conflict fixes ([732cc5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/732cc5a52f74cd272578399a0d767c4462bd5bd6))
* DFE-3870 merge fixes ([f4f0a58](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f4f0a581cc23b653d0ed05779cae3bd0b3f4996c))
* DFE-3870 PR review changes ([4c4907e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4c4907e6683b36a80fa10cec17f49e50a9c67f1e))
* DFE-3870 test fix ([245e5d7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/245e5d768c87a098aab193c02515e8394447bb48))
* DFE-3870 Test fix and review ([25a2a09](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/25a2a091610cd4ad64c8727ca451c1cf83d1c8b9))
* DFE-3870 Test update and Code review ([8cbd93f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8cbd93fec82a0fde9151228133db3fae29907a9d))
* DFE-3870 Testand update ([f7773e3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f7773e3d87dae0ce4829c0384a3375743ca58584))
* DFE-3911 fix setting order text if first entry is disabled ([ca7e818](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ca7e8181b477b27a763d12698c06d1c9904a7082))
* Disable principal selection and toggle after orders have been saved. ([e325f68](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e325f685ca8d96a96a0facfed2a66175f25a9151))
* Display Ireland town/county selection correctly. ([04cc469](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/04cc4694210385ec5a5e732fd7a2d45146a4f68d))
* Implement validation for postcode. ([46215eb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/46215eb90dd8bef214e6804c28beb51580e15bad))
* Remove unnecessary code. ([7656153](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/765615335cdfe91176519b8ed3758781df326640))
* Use useUpperFirst composable from dfe-frontend-composables. ([50375d5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/50375d5d9a90f45e518129d5dac8ed4a607ace32))

## [2.6.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.6.0...2.6.1) (2024-10-14)


### Bug Fixes

* (Hotfix) DFE-3983 Add missing import ([cf3a812](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cf3a8125067adc07f7585d41fd09f66c1077f5e1))

# [2.6.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.5.0...2.6.0) (2024-10-08)


### Bug Fixes

* DFE-3832 Use right section for customers ([9ed705b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9ed705bf15f0a40acb60f80ab15eed7aa12c51fd))
* DFE-3858 Show collection order function also for not food logistics customer ([9230639](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/923063924f2019c267ef414b9a3687c539334d49))
* DFE-3911 add test ([e9f53fc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e9f53fcd219c8b21f50722557e19e2029f8f49bf))
* DFE4-3911 order text bug ([894e894](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/894e894b59dae846a529ee2e7202c2646a2030b2))
* DFE4-3911 PR review Test fix ([094341e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/094341e773542aaebc1ec400f4a4eaee6fa828ee))
* DFE4-3911 Test coverage ([df34fa0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/df34fa0d6cc8bb1020182ff8f2f9d6fda175842f))
* Fix test. ([02db187](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/02db187619d4403cc0c15fb582b0e84c383db19a))
* Fix validation for too long postcode. ([8ebf13c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8ebf13ceb9af09a106b22d761dee44f609f6a25a))
* Fix validation for too long postcode. ([7c8c758](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7c8c758f7810fab86a37e55fef1b4ce4eaf003b9))
* Postcode check for too long postcodes. ([180db5c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/180db5ca0b2e620077439c82514034d8130f42d7))


### Features

* Adjust logic for showing tooltip text. ([c1e3a72](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c1e3a7281fb8c804a7d82e536f72909c86b0ef33))
* DFE-2618 add data-test attributes for upload list ([b259848](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b259848e26e035e640fb35eb9448b0fa50b0f872))
* DFE-3576 add tests ([7dc28f4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7dc28f474184cb73f278541261135747967b06b3))
* DFE-3576 book orderform focus ([f91c821](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f91c8217c8b44aa39404e9199925d14cdfa274a3))
* DFE-3576 fix focusing and update tests ([45e0d74](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/45e0d7409aa230f823ee04f6922f8b8ada4ae8af))
* DFE-3576 focus road order on process start ([db0b4fc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/db0b4fc075fb888938a175f761851a58d1579179))
* DFE-3641 - Add request_origin header to requests ([69b9791](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/69b97915b340db8755d00baaa3d3403de9932ca6))
* DFE-3832 Adapt collection order for principal switch ([c9e8574](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c9e8574c65ecc8d6db45e0f11911b705ff330105))
* DFE-3832 Adapt collection order for principal switch ([e77d6ae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e77d6ae62f8eec6b355c4f0cac78588cc8b8d56a))
* DFE-3832 Change translation when order type is road collection order ([5ac78e0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5ac78e0512e8975f9180cc2f5f6926945beaed10))
* DFE-3832 Change translation when order type is road collection order ([9924cc1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9924cc1ce076797e30c756d9153a6aa4f3b3c3b9))
* DFE-3832 Implement feature flag for principal switch ([b678aea](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b678aea650a220bbcf647b7577635a2bbdfdb4e9))
* DFE-3832 Implement feature flag for principal switch ([acfc171](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/acfc171f6edf8164b61b9ca4d304a6bac17ec3d1))
* DFE-3832 Move ref to store ([da7c743](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/da7c743b99bf2ad72f9d0a059865dd96de8480c6))
* DFE-3832 Save collection order with differentconsignee address ([cc1d359](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cc1d359beebf9dd3eafb7b8582e330abc3ecc2aa))
* DFE-3832 Save collection order with differentconsignee address ([ffa26cb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ffa26cbf5403036d2f54eb54c084cc6e75e4b747))
* DFE-3832 Should reset different consignee address on radio button click ([202c9ea](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/202c9ead12f0e7fbe22e8e04fa56557dde699624))
* DFE-3832 Use book api released version 6.5.0 ([fafe259](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fafe259aef5206bc502b63637598a9846c48c62c))
* DFE-3832 When roadcollecting order then shipper address can be edited/deleted and consignee not ([62ace07](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/62ace07dacf92c20cc395d5fc30803a0de1a8e2e))
* DFE-3911 fix setting order text if first entry is disabled ([b05c7a5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b05c7a5ed5ce1de66f6e5ff8d14e7f977dafb5f1))
* Disable principal selection and toggle after orders have been saved. ([7f437da](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7f437dafec8d145e4eba6b5ff73dd17a7236af74))
* Implement validation for postcode. ([963dd9c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/963dd9c30e5a8c5831b51a782c13e35c500c0f12))

# [2.5.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.4.1...2.5.0) (2024-09-18)


### Bug Fixes

* DFE-3844 add wrapping for FormCollection time inputs ([84f5a92](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/84f5a92fd2817559549e0409c144a770ca2170e6))
* DFE-3844 fix linter warning ([131a1e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/131a1e6251943aad6f057c1b9e6c72fcd0d2ec96))
* DFE-3886 Use from and to from available data ([956ae71](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/956ae717520763547c8ae2c89d01125ce1b9c32d))
* Remove unnecessary fallback ([42317dc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/42317dc9e28a769d813ff4e9fdd7f113657c9f9c))
* Show starting print options only for dinA4_printer. ([0b9c9a3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0b9c9a3e1b827a52ed2b7fab22a98865c3739bc8))


### Features

* DFE-2730 make datetime input work with enter, add data-test attributes for documents section ([c730897](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c73089726642e24c531c6ba5280423719d8fee71))
* DFE-3435 PR review2 ([2f316c5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2f316c5451af2398936cbc3ded3d60260f20c929))
* DFE-3580 Focus on document upload ([3e443a3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3e443a3e7c755ae72c181829dee87ab3ede5c2d9))
* DFE-3786 Remove fallback for generated translations ([8a63797](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8a63797ea6ef76fa20f83e025f2b4fbbce0d515e))
* DFE4-3435 PR review ([98f73c8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/98f73c894ee5555673e53adbb333ac07cb51a82d))
* DFE4-3435 principal flyout names ([e548473](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e548473aa2f0f233c177a1ffa0bb78019c69166f))

## [2.4.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.4.0...2.4.1) (2024-09-06)


### Bug Fixes

* DFE-3844 fix input fields spacing ([def1c8c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/def1c8c1fb17bb61fe11b86f7c023ce58eda5063))
* DFE-3844 fix linter warning ([25eb4f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/25eb4f8430a21f91930faddea32ba357a2bef1b5))

# [2.4.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.3.1...2.4.0) (2024-09-05)


### Bug Fixes

* Add comma beteween city and supplement field. ([ae5cef9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ae5cef97aa0048dc9b1beedbf22b2d3f6d9d9b26))
* BO newly created orders cannot be saved. ([0d902f5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0d902f5ab8be2b862d4104e71e376ab91dc99446))
* comments ([6265794](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/62657945e4b1d6ac39fdc6f60de7aeae219c10c0))
* DFE-2703 add en locales as fallback ([c1fb06c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c1fb06c2ce62c6eeecdbdcbf83a9bbb06c6a47ca))
* DFE-2703 conflicts after rebase ([4da5a3c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4da5a3c6367868a36efe1b6fffda564dfb7490ce))
* DFE-3465 Wrap checkbox into selection-control-group ([977146e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/977146ecd1cb4d85fecda8ed8f1ac216d5f34d2a))
* DFE-3583 await form section validation before sending data to backend ([439cf26](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/439cf261fda777771bdce4747c8cd6b090a4c5d6))
* DFE-3583 deduplicate error descriptions for error banner ([0356ed2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0356ed285dea1458f19a568580e1ec07714e059b))
* DFE-3583 fix sonar warning ([24e9c49](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/24e9c49667f4f70219f644d444973ff63a738c12))
* DFE-3704 Migrate user profile to user information based on migration guide ([d49b7c5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d49b7c59d9d6e4015eb52dc47324267f8542858d))
* DFE-3704 Rebase fixes ([1be575f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1be575f6ea00c326c90dd836f665c87dc30fb8e2))
* DFE-3704 Simplify code ([71b8083](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/71b808396950648e24f7e31a29870884bc63bd8a))
* DFE-3712 Dont use return as spread ([2dff635](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2dff6353b7734b2b45e0926f91567b12721ce267))
* DFE-3712 Write tests and update condition for filteredTexts ([89fe166](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/89fe166f151f76dc00c4b562672dedc61c8efb39))
* DFE-3743 allowed dates for collection and delivery dates ([54acb0b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/54acb0bd9f3183377e15e5e2033a41d6a52481d0))
* DFE-3793 reset postcode when changing to country without required postcode ([a3bc115](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a3bc115182279f29011de97c2d56ec0580c99235))
* DFE-3797 use code as value in air product selection ([31613bf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/31613bfc50e31a801f89faad74c2df80de0c533f))
* DFE-3810 - fixes all order address ids ([c761e71](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c761e71f63f015b2f1c507fdb2bc626e21f3c933))
* DFE-3828 Correct condition for showing error and write test ([82aad0f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/82aad0f6b789f231cea8df6ee97f59860a6213ce))
* Fixed no search suggestions for input field contact name when disregarding upper and lower cases. ([d69d584](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d69d5848ccf5fe9ccdf455dec1392f6b2cd39d47))
* lint errors and warnings DFE-3782 ([9e17852](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9e178523c803010be136433f0f5e5d880f59396c))
* Only show technical error when ireland is selected ([2044aab](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2044aab5d91443b64d2cf82683982f0c08fdde15))
* Only show technical error when ireland is selected ([7c39e36](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7c39e36fda4bf4c89975d48a3d3a0964796c234c))
* Pick-up address search field is not shown again after deleting address. ([baba5ff](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/baba5ff0437e73829937e9db6a9e72b30e068323))
* Show discard changes dialog also for documents changes. ([8fd20e0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8fd20e0e394896d64292e082d41795cb76dd6cf1))


### Features

* Adjustments for Ireland. ([3edcbd8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3edcbd8e6b501cedcf30ae1c806fc8c19348b484))
* Adjustments for Ireland. ([58d5610](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/58d5610f5b85a5c7f7168e15dae280d812c1c577))
* Change display data pattern for principal select and address book search fields. ([e7d8842](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e7d884208f2e1e64bc3d395dcb439db73b10a6f3))
* Change display data pattern for principal select and address book search fields. ([feb021b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/feb021bad70737a0bf0e9209c9ce2de8c909bd42))
* DFE-2703 add fallback to use local translations if api fails ([930e27f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/930e27f292ade20694a6d491bab6dc397fb7de64))
* DFE-2703 fetch translations from dynamic labels api ([c4b707c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c4b707c43f425344f0aa8b06a5adfa444fcc1027))
* DFE-2703 fix prepare tests script ([670e24a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/670e24aff2d1cf97691484e1985a8f368c4d7f11))
* DFE-2703 remove generate translations script ([29199cd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/29199cd30dae3ab15ac4db4253c6fb24d9930c52))
* DFE-2703 update dynamiclabel api url ([32e4235](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/32e42351c42890fc20182fd9d192532e5fe56510))
* DFE-2792 Adjust logic for deleting documents ([cbeccf4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cbeccf4efd9cb331932869c5e916bbcb5dfb9a82))
* DFE-3464 Update label id´s ([275b2e1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/275b2e12f556711ff22cf55c3cc2f30e4b43a404))
* DFE-3465 Update model value when clicking checkbox ([12832c5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/12832c5f65b1555239268a56467d02498c1ca5cc))
* DFE-3704 Do conversion for eircode ([4c4ec33](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4c4ec33f7f454fe03c4d28eaea1022c217fa40c1))
* DFE-3704 Fix placeholder visibility for town county list ([a196d15](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a196d157e2b75f4baca6a4cb1530b1c31e5e61d7))
* DFE-3704 Implement query for fetching town/county list from BE. ([7a5ac4e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7a5ac4e8472b60cc51786dcf572ee94bce93743e))
* DFE-3704 Implement regex for validation rule and show eircode and hide supplement when ireland is selected ([cfc6ec5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cfc6ec550778baaebd7eb03d86af16473e7b0fc3))
* DFE-3704 Implement search for Town/County when entering eircode ([1714499](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1714499d4fe0a9d6f214a223358dbd77629bb43a))
* DFE-3704 Implement Town/County Autocomplete field with mock list ([3fb1593](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3fb15933f060ee485a44b84367a5b668f4959858))
* DFE-3704 Use dfe-frontend-composables for handling ireland ([9bcc68d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9bcc68d737978fe959c807c764b4d135c69ae0f6))
* DFE-3712 Show order texts based on pam configuration ([e8f480e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e8f480e8d3671c9b746b18f75d73e0f497ba4270))
* DFE-3712 Use composable for order texts ([36bf25f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/36bf25f35e057203b1929cc45676e4ec5e9ae7ef))
* DFE-3782 fix lint errors, add tests for orderLine store, remove unused v-model ([ea07bbe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ea07bbecba36f6e5a10c1769709ad08ea90a83b8))
* DFE-3782 prettier run changes ([596e7f7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/596e7f7d1cafa02254a8a43bd07bdafc95000cdd))
* DFE-3782 prettier run changes ([d3d7f71](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d3d7f71b8868abd0b5db0cda3cc5bae5c68c68c5))
* DFE-3782 remove extra space, fix items type for autocomplete ([5eda0bc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5eda0bc6b55576196099255fcd7296119b0a9299))
* DFE-3782 revert generic for autocomplete component because of internal vuetify types ([4efc6d5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4efc6d52562bb2a03ca30022fdc478124c1596a7))
* DFE-3782 revert types to any because of vuetify internal types ([681ea2c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/681ea2c6fcd01b1d13071e5afb07a3b309027bfd))
* DFE-3820: Also use contact behavior of ASL when handling with delivery option ROAD ([1542ecf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1542ecf2c782420f22ad0e1c134364495bc4fe38))

## [2.3.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.3.0...2.3.1) (2024-08-12)


### Bug Fixes

* DFE-3785 wait until locale is loaded and use as accept-language header ([34d12cd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/34d12cd4906a1dbde306a40f512ed2c0f01a8e0a))

# [2.3.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.2.1...2.3.0) (2024-08-08)


### Bug Fixes

* DFE-3236 Simplify code ([9e3c297](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9e3c29760d5664ab8b166b74ebaaf71cd02c1014))
* DFE-3603 - all countries does not contain countries from favorites ([24bd3bb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/24bd3bb200bc63a9a1a71d37114868fdf3d79ee0))
* DFE-3603 - all countries does not contain countries from favorites ([fa1fe6c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fa1fe6c55c9e535f95e7b1bb758d8192d4c7c8b5))
* DFE-3656 Fix address editor for sea orders validation ([29f9fea](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/29f9fea895beb174f8404b3f9093c69dd7955d6c))
* DFE-3656 Simplify code ([873c9f9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/873c9f9c4f4c25122b79ffa1f24d05a55f1a661d))
* DFE-3707 i18n locale mock ([3026444](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/30264441910c08bcc2394c5e130f509657ca5cf8))
* DFE-3716 close menus when form is scrolled ([d3b133c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d3b133ceea28dfd4fd694d9faff5aeecd14a4d00))
* DFE-3730 Allow current day as input for collection date ([952518c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/952518cbc6c296ad0ef18a41273de57e82a14582))
* DFE-3730 Remove workaround ([77ea8fd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/77ea8fdda55e5bde1cf68c4e5bfee322b94452ef))
* Save as draft information text is cut off. ([1c5932c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1c5932cb150b88cfc6fbc343f70c6fe68f160c75))
* Show the unsaved changes dialog also when clicking on the X-button in the dialog. ([40105a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/40105a7d30f9aea316dd994d6a4a3e9fa72df0eb))
* Within the airport delivery section the info icon is overlapping the text delivery date at airport. ([c3a7248](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c3a724850fb394a41bd4fa7b01a1f6d77ed3af91))


### Features

* add custom heap event when order is saved / drafted ([f629f02](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f629f028f7cd2ee6ebb5320c13baa7fbbc64808a))
* Add link to incoTerm PDF for Road formTerms. ([d1e8983](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d1e898351c33acdc9412bb0bbe3b026b8f5152bb))
* BO Address - Add confirmation dialog in case of unsaved changes. ([63470d5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/63470d5959537d86c66f7444e8461ba2d880388a))
* DFE-1282 Check if order number is mandatory for specific customer ([48084d6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/48084d604894104e4c960e3d61a1967f62d73611))
* DFE-3236 Apply order address presets when selecting consignee. ([7e27466](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7e27466c96c1b76803f58d04dc1b3f5e6685ace8))
* DFE-3236 Use .find() instead of .findIndex() ([c7b4270](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c7b427050575a21c5d50226fd66f06a3b3dc015f))
* Disable principle switch after advice was send. ([0690b9c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0690b9c9ac4e33f9f48e35b33399616e20e96e1c))
* Preselect incoTerm, if only one available. ([4e0835d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4e0835df4349435dc6e8531382fe81b0224327f9))
* Styling for goodsGroup quantity field. ([0cc8fd5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0cc8fd5b29c4650c0460981641a20b916c0f368b))

## [2.2.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.2.0...2.2.1) (2024-07-30)


### Bug Fixes

* DFE-3730 Allow current date as collection date for road orders ([68c222f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/68c222fcd28472e622272511dc1ec82065fbfae1))
* DFE-3730 Allow current day as input for collection date ([b7826f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b7826f8cc59e8b7b031c3082a8ba65a9e1621afb))
* DFE-3730 Remove workaround ([da99752](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/da997520e185498ea955f4b3b5c6d7ed04a9c4f7))

# [2.2.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.1.0...2.2.0) (2024-07-25)


### Bug Fixes

* add complete adress to check for partial adress display ([6b57f31](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6b57f3133f3fe91a5434032750ba3af4d108bb5e))
* adjust spacing ([150f2fe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/150f2fe4cdd0325c7f2f94803b6251bad5ca2a76))
* DFE-1638 mark orderline measurement fields as required ([e699afc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e699afc888a8faa6b4bc0a0de08e451f2e7532e7))
* DFE-1638 max value of volume and pallet location ([df780e1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/df780e1dfdf9ef6b89d96fceeb9601cba0c5bcbc))
* DFE-1638 min decimal values ([1af0c0d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1af0c0d149a3a00d79d66aaf2ee9fe8c2aa9befc))
* DFE-1638 number field validation ([d069b80](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d069b8033a21c82e9dea247099078694ed117fe4))
* DFE-1638 numeric-only input validation for number field in safari ([532afa3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/532afa3b0a4aad81be48e8689ffccf6ecd705422))
* DFE-1638 numeric-only input validation for number field in safari ([7f010e1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7f010e122a5028ee25fcba82cdc11d13fe9ffde1))
* DFE-1869 Restrict complete orders for specific sections. ([3c68c8c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3c68c8cd2c57af786b4598a0bf0e1ed75cd045c7))
* DFE-2554: Dont disable ordernumber in status complete when it is not set ([2d75fd4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d75fd4a1f35755e5026e1ac0d9b45f68abdc854))
* DFE-2554: Dont disable ordernumber in status complete when it is not set ([3fb3a93](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3fb3a935585f41d1e980621dcea96a3b7e1bdf3a))
* DFE-2956: Implement user profile for checking quote access ([54b6bd4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/54b6bd4da6f6e2777a55b33b9efbcf7ce4e5428a))
* DFE-2956: Use user profile plugin in app ([b21d824](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b21d8248d7959faa25ea3664a260f9a667bfdf03))
* DFE-3383 add loader and no-data text to autocomplete address field ([9a7acef](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9a7acef2cd545a1cd9ce563fde5121a2a0dc8408))
* DFE-3471 checkbox hover color ([80d3afa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/80d3afa58ee30f7346c052850df400885404d9b2))
* DFE-3473 DFE-3470 global button styles ([e6a8b6e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e6a8b6ed1988359f15977f84b5aee0a3e192e5c1))
* DFE-3475 form navigation colors ([cf606b7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cf606b7323798195c88e9bd7eacc619af9ee2f8e))
* DFE-3494 use info button with tooltip in form label ([6d4307c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6d4307c233d73b44ea4ff0b32758d8280f60e68a))
* DFE-3505 ([b6c594b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b6c594bdde852c14ff804eca30f89ccf524d9bda))
* DFE-3691 include loading/unloading of quotation reference when saving order ([314dc3e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/314dc3e8410fdfadd986e377dd891770fc707b06))
* DFE-3691 quotation reference default values ([d4b724c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d4b724c69d4ddecfcc85dfac4d8656a1724312e4))
* DFE-3699 manual number ssccs in shown in edit form ([b730a72](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b730a725871eee36500a30ebcce72756a21c6bba))
* DFE-3708 fix shippers reference data handling ([7697e8a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7697e8a22a5c52a3c996a3466be481ac0ddb7e62))
* DFE-3711 - Added no whitespace validation for required orderline content field ([3e8415b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3e8415b4a6b8f0c3bddf72ee8c81c0796294f37f))
* DFE-3715 reset contact data when deleting address in address card ([2c8430c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2c8430cad6c935061d78e6e1a32dc20b3b2e8b2b))
* DFE-3715 reset contact data when deleting address in address card ([822c004](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/822c004d414b61a2fb9185984ee1a4cb711e1d40))
* DFE-688 Add generated sscc to payload of forwarding order ([acdb142](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/acdb1425ca5f21aade7ef9fd1afc1fea48fef9ba))
* DFE-688 Reset modal on cancel ([d9d5c71](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d9d5c711936587e4b81deb9e2f05a4960c89ee34))
* fix linter errors introduced in DFE-3252 ([90901a7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/90901a762bf910121d362bc26ce4c74f4a907c7d))
* Fix type ([e42b62a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e42b62a5e53dc22b3f2e5a2e3f0845022320b063))
* Fixed back button in unsaved changes overlay. ([4b111b8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4b111b80984a1b92232702bd4049b7bd91e1b5fe))
* Fixed locally failed tests. ([1996f86](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1996f864636b6532a48d70f56f81823720214451))
* principal not loaded when book is opened too quickly ([95fa25c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/95fa25c4750a5fe209ded614f08710d0292dc360))
* radio button color ([319e32b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/319e32bf56ce2d4189187bc1646cd0fc35ed0e0c))
* Sonar issues. ([16c93bd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/16c93bd82eac79d345cfcedc0a17e1055635c847))
* Sonar issues. ([cc46ee7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cc46ee7caa38fb8861be364033fe94cbee2e65f6))
* Sonar issues. ([2b99bbe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2b99bbe81a4d7d9220b964fba7b5dfe385e7d14f))
* spacing and styling DFE-3515 DFE-3531 ([b0515bf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b0515bfbe1066d63a49ab894daa4eaf1ea2d62d7))
* spacing bottom from mb-6 to mb-4 ([18e4351](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/18e435154dda6f4cfdbed022fe8370794636a96c))
* switch computed to lodash ([f3e0be9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f3e0be9f3364fc867d9c0515aed7d129fae1db7a))


### Features

* Changed confirm action in unsaved changes dialog from "save draft" to "discard changes". ([2c323a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2c323a28e7ae2d4b752ebf22b3a2d06405eb098c))
* Correct wording in usaved changes dialog. ([b46a009](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b46a009756467e148d4ce79eba4f2d22dc5587c3))
* DFE-1075 preselection filter products ([bac8d4f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bac8d4f57a528bd1b1dcbd526e043910c6ff5757))
* DFE-2867 Implement new AutoComplete field for contact name ([299c50e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/299c50efc002f92354db58cffc0885afed9a801a))
* DFE-3699 manual number ssccs in shown in edit form ([501a9d8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/501a9d89edd88fd007befaf1b107dbf78d34fd7b))
* DFE-3708 fix shippers reference data handling ([cd80045](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cd80045936deeb85d575ffdfa442f7573c9557ea))
* DFE-688 Add Modal for removing overflow ssccs ([45c9756](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/45c975649dba90bae7044fd0319f7853b5216ce5))
* FE adaptions decimal numbers LDM field. ([5a4d447](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5a4d447b0d23889a3d90b30cc644ae0b5d2b3493))
* Tests. ([665e36e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/665e36e2bd4f60228bb41848f2fd10cf65801691))

# [2.1.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.0.1...2.1.0) (2024-07-04)


### Bug Fixes

* add data-test attributes to dynamic fields ([14e45dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/14e45dde3fcad49ee0c6d768970899d9ff67771c))
* adjust further margins ([a1e7421](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a1e74213ab3c5514cc2b6ab2d5162728ba5a5b9b))
* adjust space between headline DFE-3534 ([181cf7c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/181cf7c9f9fc54a89d594c6f3dea0e36c62fc4bf))
* AutocompleteField ([26ac309](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/26ac309ea3a5a01f12ce8a0f3c34e76f7b08167b))
* checkbox alignment ([2dad153](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2dad153ff6c1e4ba1711618f089a794590eb7b53))
* data test attributes missing on add-buttons ([09f76cc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/09f76cc43b71690886099bdb4984a4aad0e59af2))
* DFE-1280 Only show drop of location for ROAD orders ([d0f63b7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d0f63b7841162546744d3e7aedc2e75b3a542c2b))
* DFE-1280 Reset boolean on create ([77e693f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/77e693fc390bf06ee55a20e33b03057f0bc68111))
* DFE-1280 Reset boolean onMount ([0fc9f9b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0fc9f9bc05167634f34315e46ab638543473c1cc))
* DFE-3252 Adjust address transport handover when toggling switch ([7f067ce](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7f067ce6264d442a9730b2310684bcd63d728ce1))
* DFE-3491 address and contact form spacing ([6e2c7fe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6e2c7fe527b7518243bec1d04cc403f8d4126f54))
* DFE-3492 select field menu items highlight colors ([eaa35e8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/eaa35e84924f2a03b1990b8558973278a8238d12))
* DFE-3495 address layer footer spacing ([8e37e60](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8e37e6074ecb58a1a0806daf7c4200203cb47903))
* DFE-3506 add spacing to delivery option ([de33ad8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/de33ad805d99a00931e65f7a061bf01bc591d5af))
* DFE-3507 add placeholder to contact name ([560983a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/560983a634053490915c63d0628bdeb1ee927bb8))
* DFE-3557 Fix behaviour of loading point - no loading point is created when none is selected ([0900162](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/09001626ed5b782657755af129867c9546b9f998))
* DFE-3562: Don´t add default contact data to shipper when order is ROAD order ([9888208](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9888208ca4889ae677f819f0fe09c6b49a4091ab))
* DFE-3581 Trigger max char validation for air references ([57fe419](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/57fe41930ab3139a721d7ce5014ccecbf21aa93b))
* DFE-3600 Remove false input emit ([df36aa4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/df36aa49234e1be45684732806777cc29e643130))
* DFE-3604 Add required flag for air and sea when entering email ([a3b26eb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a3b26eb16a22766311381d4e41ab9a231f8f7e75))
* DFE-3607 Add date range of 14 days for sea orders collection date ([d465d8b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d465d8ba288841c79fccbf8bcd574e2f99fde782))
* Don't use empty string as default ([af3706c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/af3706c7b3ee969399e8ac0c9488d1b2cb649b38))
* Fixed prettier format and lint errors. ([82201d9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/82201d91fb9126d42a4d92c72abbb0f4fe5bc3bf))
* incoterms wrong stylings of banner and link ([984d0d7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/984d0d779d69c89199d7a392ff2946e3ef3fc8d5))
* ModalWrapper attach ([1970a11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1970a11187d7f128dcebd55ad0eb5b9fe1a5eaca))
* preferences printer () ([343616f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/343616f71aaf75f44f6d5fc5cf9376c25b0b114f))
* rebase conflicts (DFE2-1086) ([2b2d3ab](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2b2d3ab8af62a7b16124cad4d4ed7aba33024182))
* rebase conflicts (DFE2-1086) ([894bc7b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/894bc7b028cdcec365423bb6f47f61fee1394d25))
* Removed search icon from address name field. Same with for all input fields in address form. ([91ed00d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/91ed00db4b69664992baf305280881cadc52be6f))
* Send orderId on document upload if existing. ([a6521fb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a6521fbd9828abf4741f2f5ee67062f81c644b10))
* Set delivery option default to "None" and fixed placeholder text for delivery product field. ([655a660](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/655a660f39e85a917b0e9b89a15a9e3071f35ab4))
* set disabled false on default ([d34483b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d34483b14316df1b8764e9546f3f72faf2c3ea03))
* Show number of max characters and the already entered ones. ([d215789](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d21578921d730513447336db33d8625a9093bbcc))
* spacing in order form ([2413f20](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2413f203e3f57ec96e9ab94487d8d6857cb46f30))
* specalize data test attributes ([70fb9a4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/70fb9a426881672b5b529db5b7871ab39088345b))
* ui feedback ([af8c3bb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/af8c3bb21bea5b7367f47b69dabcc4bac94cb4bd))
* ui select-input (DFE2-1086) ([14a32a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/14a32a9502e05fb2a1e2e994079fea0c27f7621c))


### Features

* DFE-1280 Add drop of location to consignee ([7aa16f0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7aa16f057671bb85495c6761b29881054de5fa03))
* DFE-1280 Adjust book api version ([e743cea](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e743cead2f104834e446ceaf0b974cc566327af8))
* DFE-1280 Only show drop of location for none food logistics customers ([66775f5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/66775f5db97bff520e83b6acfa68db625482fa39))

## [2.0.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/2.0.0...2.0.1) (2024-06-19)


### Bug Fixes

* DFE-3600 Remove false input emit ([6b72400](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6b72400d0bfcf4ddaa8224041245e980c93e90a2))
* DFE-3604 Add required flag for air and sea when entering email ([9305954](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9305954932fc63f17388fb578b2d6c7fb37aca2e))
* DFE-3607 Add date range of 14 days for sea orders collection date ([e51b947](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e51b947bd2adc56321962cfb84f487a787ab5f76))
* DFE-3607 Add date range of 14 days for sea orders collection date ([e062293](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e0622930c36fe46a435adf3065c118c220f21a6a))

# [2.0.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.11.1...2.0.0) (2024-06-13)


### Bug Fixes

* **FormContactPerson:**  Fix objectObject issue ([ac74a96](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ac74a96e50ff559b3a4265697fce4158f9a9877b))
* **AutocompleteField:** Add missing import ([2b0067d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2b0067d30c44aa10ca1abd86a78763283430fb39))
* **AutocompleteField:** Added back accidentally removed icon slot. ([4929d72](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4929d72ec55057d94cd8be8a5202e101b0389d23))
* Address Selection dropdown ([de58313](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/de58313e48bf3b5020fb99429ccfa6c537f22408))
* **SelectAddress:** attach dropdown to appRoot ([6e95b7b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6e95b7b92c2460741796c5418b41e308ed0ed6c2))
* build error due to ts error ([80b06bd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/80b06bdb44896472418cbb5ea0b0d4ac3322468e))
* Change dep to vue-pdf-embed ([faca555](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/faca55555894debb54f9f5bf1415e299495b77f6))
* Code review adjustments. ([621fc7f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/621fc7f4a8948ccd5a4712dcf8bd0d195ddb7bd6))
* Code review adjustments. ([337103d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/337103d6c614186325a135485cee0ed9b18a9a1c))
* Code review adjustments. ([59164c3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/59164c3b48e484d4eda1784e866578607a1627d4))
* collection wrong spacings and toggle missing ([3d7c60c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3d7c60c3ddb8c10d250fb0c4324d08f99aac970f))
* **FormAddress:** Country dropdown ([57e152a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/57e152a32c6b3e1ec41ec2a4e5e07ac464dc2098))
* data test details attributes and tests ([e7bd544](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e7bd544c73fa7b761531399e6cdb1def47d1effb))
* data test details attributes and tests ([684b963](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/684b96354611c627032d6c8e74fc894f679e1818))
* data test details attributes and tests ([ad7334b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ad7334b45a63ebadd11fcf1d5e857aeafc0d9065))
* data test details attributes and tests ([8013015](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8013015965c8e659564f25c347fc6795e53cec5e))
* DFE-1442 - Display of select field ([5711965](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/57119654f6f3df28d75070118d6ee99474d8545a))
* DFE-1442 - resolved missing refactorings for addresses ([f7d0740](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f7d0740b90a45ce1b606b4d29ee83ea221f0fd6b))
* DFE-1442 Select field can no decided if the template is used or not ([8594ab1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8594ab1ac075393c06ac1ae1737844d1e0577c3a))
* DFE-2573 Reset dateDelivery when product has no fixedDeliveryDate ([e9ddccc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e9ddcccf975bce0e9c1a1a5cd7d46f3b45e9becd))
* DFE-2758 - disabled delete orderline on price relevant changes ([5ebe600](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5ebe6002c014b402cce25c7adb3e2ca2def43943))
* DFE-2884 Add empty space for better readability ([6bcf2f5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6bcf2f516561f2043ec8fc10266e494a8d392fc8))
* DFE-2909 Adjust border for address cards ([4b96a86](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4b96a86bbcd394ee14d65f2a940266399eb6af3f))
* DFE-2909 Fix contact attachment ([936decf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/936decf5dfc3e391e1985a4f55e98edd68bf734e))
* DFE-2909 Fix contact attachment ([56b9ec7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/56b9ec7d6cc83b6ced80ef27885a0ae63e7b1329))
* DFE-2909 Fix duplicate delivery option "None" ([4d44b74](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4d44b74fb14922a71b8fa9fb49382aada2044ed3))
* DFE-2909 Fix duplicate delivery option "None" ([fe079e8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fe079e89be0306c48cf84bc591b66d765f0a55b4))
* DFE-2909 Fix navigation for sea orders ([5fafec1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5fafec13835c54f7dbf1dba43b51bdd9954ea478))
* DFE-2909 Fix portRouting and uploadList when editing order ([d84fa57](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d84fa57ed1a92b1b44c4ecaa86903bc70930066c))
* DFE-2909 Fix UploadList and AutocompleteField according to vue 3 ([bbac122](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bbac1221d8ad7a392ec7423be4229d8e19f0e14e))
* DFE-2909 Fix UploadList and AutocompleteField according to vue 3 ([cd50a49](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cd50a49004bb10dd93d854d833c5859f0c76626f))
* DFE-2909 load customers on init ([24729ad](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/24729add1b446ef6a5fa6c03f13e76e6153803b7))
* DFE-2909 Load customers on mounted ([f2ed30c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f2ed30c27c6dbe62d762ed4684d3f54e107a3c08))
* DFE-2909 NumberField input details z-index ([7848fad](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7848fad60d3762653510d95fb0f74907a64dac6d))
* DFE-2909 Readjust package-lock.json ([ad6169a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ad6169a49a78d9b280255f453795882a23089194))
* DFE-2909 Update model ([0e85238](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0e85238289240d79c7f723a89b9f217479e3bcc6))
* DFE-2909 Update model ([aa1327a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/aa1327a90ed0362e8c92bcdddea0107d2da11dc5))
* DFE-2970 Default selection for customsType is now CUSTOMER ([078c717](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/078c717a86d4677cf8e7c08894745460a8e86b39))
* DFE-3293 Fix additional addresses section for ROAD orders ([a96ff83](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a96ff8398b939b56320701d7a8c69dd6d1f2e49e))
* DFE-3293 Use model-value for UploadList Document Category ([1f41842](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1f418422a765fbda13ac4eec1fb591320bff7a5c))
* DFE-3324 Document category are now getting viewed correctly ([9a09a4a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9a09a4aa8a5c8dae216b5d0f27df4d18ce747510))
* DFE-3336 Adjust form label for applicable ([20cad18](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/20cad186c772d714bac47268c6448ffc2a8e427e))
* DFE-3352 Adjust address card border to match figma ([0fdc696](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0fdc696d021dc02c30f75aae02ba89a2de45d8e3))
* DFE-3356 Adjust translation in validationRules ([6920a33](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6920a33514053a2e7251a166f598746388bfca2f))
* DFE-3363 Load contactDataDeliver when editing order ([6391612](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/63916123079c4196c94a999d976afbec0f376b82))
* DFE-3375 Fix width and height suggestions ([17accaa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/17accaa3069b0c7400d1cbb30d4adc54d024631d))
* DFE-3375 Fix width and height suggestions ([3518da5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3518da51bd636f25b8ee0022a155a6c748720f8c))
* DFE-3378 Fix loading overlay to be positioned in center ([f3255f4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f3255f443c2f61787f174dc1819d7abcd2c1e01c))
* DFE-3388 Fix blur event ([4fb471a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4fb471a8973cc1da9be89838d06419689779d628))
* DFE-3388 Fix blur event ([497b7dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/497b7ddc35216f3400d8931909feae111ea4b2f5))
* DFE-3388 Fix updateValue for modelValue ([6cbb40e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6cbb40e6cd540c3078cd1863b1238ff0a96ac87a))
* DFE-3390 Fix orderLine - values can now be saved ([0ae70d1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0ae70d100960e15b434c46cba48a7f0654745f2a))
* DFE-3393 Fix Order Text - values can now be saved ([df56767](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/df56767721a6411b62652479772ac69476230b7d))
* DFE-3396 Fix self collection for ROAD orders ([0578707](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/057870774a810cab5db210241a9295a1039a1eb5))
* DFE-3398 Fix order groups for ROAD orders ([2964f9b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2964f9b5be433101ac3943b64e84607a5276a6d0))
* DFE-3402 Use right style ([7cfc155](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7cfc155ed1046ea2b83bd387d24c6f5496a9cb28))
* DFE-3410 Uncomment styles for radio-group ([873c963](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/873c9635bbf9c27159e3aa3f4cd5c95b819b682b))
* DFE-3431 Adjust products selection to work properly with vue3 ([dcb4753](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dcb47534038a1ea643662fa1cf1960f2cedda9ee))
* DFE-3434 Reimplement max-length for text field ([4c68cac](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4c68cacc4c2922f1f4ea29ded7f5cc4b6f7cdf14))
* DFE-3441 Set packaging to null on edit, when there is no code ([47d4aef](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/47d4aef6ac25e6dd07797bbd034153bb8b5b8b39))
* ESLint + code warnings and issues ([e851285](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e851285825a979fce2425a4a3375e1e12739a122))
* Fix Accounting / Additional services section. ([85aa9b3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/85aa9b3d798188b5af36fd0aaa95e3e7318a1f5e))
* **AutocompleteAddressField:** Fix autocomplete item styles ([fadcfbf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fadcfbf45ae6a2a148006f921db1e360ba368522))
* Fix broken tests. ([ad0fa3d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ad0fa3d98c4e0192ebd42f44a5a0b811627d3cab))
* Fix CheckboxField and RadioField functionality. ([f907799](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f9077999c78df71ad1b8432b77d158e0b5456828))
* **SwitchField:** Fix dimensions ([c1c566c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c1c566c574a05951e10cbe94ca69c8cc2a558ce1))
* **AddressesSectionWithHandOver:** Fix hand over selection. ([f1dfaa8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f1dfaa8597cabe454352871c05669b60e0957f05))
* **AutocompleteField:** Fix hidden overlay of autocomplete field behind card. ([d676ae4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d676ae4a535c152c49c84bfe3dd1bc7a7a4d49c3))
* **AutocompleteField:** Fix hidden overlay of autocomplete field behind card. ([9d9c6be](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9d9c6be167bf4c4b2c3c3c6c6f4a3acf8aef9c37))
* **InfoButtonWithTooltip:** fix hover styles for icon ([b4265bb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b4265bbbfffab5f8afc13503d4a7959a3c56ab9c))
* **OrderLine:** Fix HsCode input ([1bd550c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1bd550c4ef996070ab3ee26841bfd7171027d537))
* **AddressCard:** Fix instant application of addresses ([623d96d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/623d96dd3f1b3a0799e864a87b904ce4f102d5cd))
* **FormContactPerson:** Fix model binding ([efe5762](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/efe5762a8748bb6d95e0cb6fd25531e17bcc370f))
* **OrderLine:** Fix packaging measurement proposals ([e10c8e9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e10c8e921edf56f3aa027920d81d489b3106cae5))
* **Orderline:** Fix packaging options not displayed. ([d263728](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d263728aea4c98ebbfa921a6acab7f3b643af5bb))
* **LabelPrint:** Fix pdf rendering issue ([9270560](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/927056059261dee95fd974569ff85ad6084b8c39))
* Fix some problems after merge. ([1cc2138](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1cc21384d495af0c376aaad86fa4ef8219b4b1f9))
* **InfoButtonWithTooltip:** fix spacing + alignment ([73e2267](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/73e2267403d2364e06e743ae5a7259c65f3fad6e))
* Fix style for AutocompleteField and placeholder issue for SelectField ([e7905ff](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e7905ffa90fde403d53e8196bd7ed011a65b4f18))
* **NumberField:** Fix styling ([6b302a1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6b302a1112d02badf41d89442969a76cf63e139e))
* **RadioCard:** Fix styling ([e2eb431](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e2eb431046504dc0aa66e571fe99c13f1695615b))
* Fix tanstack not sending requests ([3d706d4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3d706d4310c6c540407e78517ee6d2d6ca409f7a))
* **AddressCard:** Fix type error ([47e0892](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/47e0892a60fc42d2aec444cf7a31f78e236e27bc))
* **UploadList:** Fix type error ([f5a2ba1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f5a2ba19ff901966709aa634237490eb3d9dbc5f))
* Fix typescript error. ([1dea1bf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1dea1bf337e64373d66ac9af3c5ed21d66233ffd))
* Fix typescript error. ([8e510ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8e510ba52d999b8af87eba91b367bff55b0ca8fe))
* **Documents:** Fix upload documents fail ([e979fa5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e979fa5b4e738be972638d1f32c8a30ad1651178))
* **FormNavigation:** Fix validation rendering ([22f00fc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/22f00fca9369886cc489fbb41603e9d58849529f))
* **OrderLine:** Fix wrong merge conflict resolved ([d2624cf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d2624cf05cdc3728b96af683f299dc777f95a889))
* Fixed "x" for deleting airport missing in airport selection field. ([f6b7596](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f6b75968159e3978c007292695c524159df68c7c))
* **AdditionalAddresses:** Fixed additional addresses buttons style. ([124a263](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/124a263adfe24da66fb79d247e310ab70a1c05ed))
* Fixed DatePicker -1 day selection, fixed ButtonDropdownMenu styling. ([95ed802](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/95ed802474bd8498e42b84a8dc0f87640974c0f8))
* **AutocompleteField:** Fixed displaying categories in AutocompleteField for Packaging and Document category. ([37b0ece](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/37b0ecee89578587133696c073b713f53b5e2dd6))
* **AirOrder:** Fixed form navigation. ([cc3a483](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cc3a48344d7fe817bc3c4c0a29ec28290ead7c1f))
* **DatePicker:** Fixed highlight selected date. ([175fd86](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/175fd8616fa69a8cdb974b3326539bdd3a9914e3))
* **FormContactPerson:** Fixed input fields. ([2d42423](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d42423cd63461a301b0075f7ca7355271de8d53))
* **AddressCard:** Fixed invalid undefined error in tests. ([6f4c85a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6f4c85a6507d4325dbfa2dbfbf27dcfcd73b4222))
* **OrderLine:** Fixed length value. ([4a46401](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4a464013532c78391cf98618e4eb5b41cccdc3fb))
* Fixed loading point in edit mode not working correctly. ([9bd52b3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9bd52b31a4a375dd9fd807def1bb33e0f7ed94a6))
* **AddressCard:** Fixed missing contact data in Output address. ([4920132](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4920132574c6f8a461d64d62cde7c086fc4ede0e))
* Fixed modal not opening. ([d912442](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d9124428706f5a6f08fef68153b38f37787104ed))
* Fixed overlay for documents section. ([a833df5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a833df590238c6001dfc5a543ba868c85f8fb6e1))
* **AutocompleteAddressField:** Fixed select search address. ([50e64db](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/50e64dbfd80f1d566fad610809f58ae2d137effa))
* Fixed some minor warnings. ([ea25852](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ea258522c4858f9600efc6a1c28dd3195a5ac7e3))
* Fixed spacing for mandatory asterisks. ([1ce4db8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1ce4db897070e25e5a4fe28b6e460d0f31f5b067))
* Fixed styling and functionality of Address Overlay. ([6f33fbd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6f33fbd9a381e76abadef39fbff364fa6a9eafa6))
* Fixed styling for Button, Checkbox, DatePicker, List, Radiobox, Switch and Tabs. ([52b3619](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/52b36196f1379766b5195545f51920398845607b))
* **formSectionIncoTerms:** Fixed styling. ([153701a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/153701a9d76ee44ca72502daa90931444793ade8))
* **ButtonDropDownMenu:** Fixed test. ([3565e02](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3565e02719ceb1c746bee0eacf0a96004e8c6d73))
* **ConfirmPrompt:** Fixed test. ([520e751](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/520e751b817fc73c6beb4c20cc23ca4a5c1d11f4))
* **DatePicker:** Fixed test. ([88a5abe](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/88a5abe16d979bdecaa8199a77b28934b138ec1b))
* **FormAddress:** Fixed test. ([a4e6202](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a4e6202e98ab7fe46c3802126e1c91a2ca89eb9b))
* **MainFooter:** Fixed test. ([fdc7697](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fdc7697d69b65b943f5f2b69d815afaa3446493c))
* **SelectField:** Fixed test. ([bd8d0ec](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bd8d0ec2b359af7e51849453dc572f52a5b1f6ea))
* **AddressCard:** Fixed tests. ([3702dd2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3702dd2d1e5245a12d5f4a683b34fbed3c2c42ec))
* **AddressCard:** Fixed tests. ([064a229](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/064a2290bdf84f8628e8c841535515b21477f3ec))
* **OrderLine:** Fixed tests. ([5ed372f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5ed372fcd35185f87358134b4d3125b2f7b77488))
* **UploadList:** Fixed tests. ([1071042](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/10710428c9c13d7a4d30feb14132eabe699562d3))
* Fixed validation in address form overlay. ([1af5e40](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1af5e409f12cc6f2d3c1cc4856d1b06a0c7456f0))
* Fixed width of layer, removed search icon in name field, fixed placeholder for country field. ([052d925](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/052d925911934f877d898b31445f725e853ad8c7))
* Hide indication on initial page load. ([649b146](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/649b14652aca8e1bcdd543457af751381f5c23cb))
* **LabelPrint:** inline dep ([8f9b661](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8f9b661def226891b043f385838ff3e1b1001f93))
* Input gets locked on form contact person name field when deleting content. ([fb4b4aa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fb4b4aaa18397069b0dc284ed650cc44dee366c3))
* Menu icon for several fields ([ee1b9e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ee1b9e640962ea44b80399480e374146fb47299d))
* **ButtonDropdownMenu:** menu-location in relation to button ([0eecb0d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0eecb0d8580da5a460307e9955dadf751d7ea82f))
* **validation:** migrate FormSection ([89037c4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/89037c49d73da46e9af4dbe872b4beebc5d64a74))
* Missing placeholder for product and delivery options fields. ([c498482](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c4984823f8e7b4bc114509598353ac89f7e2e91f))
* package-lock update to fix tooltip ([a79a70d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a79a70d679495ddcbc4a5839d3729a6bc37bd31a))
* **ConfirmPrompt:** Remove duplicate v-model ([b977d23](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b977d23c1797ea21a7a03b8691e598e0174f0943))
* **useEditOrder:** Remove initPinia ([2ff130f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2ff130f3e1cc75a325d3ae2e5dc467b333d36518))
* **EditModal:** Removed unneeded test. ([e27568a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e27568aef3eab730aafe849578f2817cde6a7423))
* Resetting airport after changing Handover selection to default or alternate address. ([b71b52e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b71b52eb22cc4ebfb94ed10fa4b0481bab142006))
* reverted some file changes ([e0165fd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e0165fd182d26a28288d5185d5171ef3e276b2ed))
* String param type of mock requests ([2860368](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/28603688ab8d152de35a64e8ad3a2f896541c6f1))
* **AddressCard:** Tooltip style for individualId ([bec3591](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bec3591662f82eb9c4f9c352b557c9e873f6a8df))
* trigger release ([351ce42](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/351ce42b14149c39079fd8a0f95b4935962dc118))
* Type errors ([a85ad08](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a85ad08fe87737c3b02e76d1a0c3cb6d94867922))
* Type errors ([e51b86f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e51b86f4e067bb78fdf673df05db3742889499f9))
* Type errors after merge ([9daac86](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9daac86b07803ae32a17b501018c2efc49a13616))
* update snapshot ([94e6017](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/94e6017620731c1cca19f12df623daba16acd805))
* updated snapshot ([cfd1049](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cfd104970097edb67f1baa796b61b63314f2dc09))
* **AddressCard:** use correct event name ([5879a0f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5879a0f3568a0bbeb02a8fdcaafe22e8d1733d99))
* **OrderLine:** use correct event name ([a50f15f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a50f15f432820e80804d2736e8c044e6f682b06f))
* **ConfirmPrompt:** Use vue3 v-model ([f31c46e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f31c46ee935b4af600fa96f467906e287481aa45))


* chore!: Mark breaking change ([b904fe5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b904fe545a365fed5ba3b0b00bb8e57dfd790ab5))


### Features

* add data text attributes DFE-3122 ([e270a50](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e270a50486cd5a36f614831cbcbc75d4694ad394))
* add data text attributes DFE-3122 ([58c02c5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/58c02c5b92205bce05559cc9df9e8283417ae65c))
* add data text attributes DFE-3122 ([a288af2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a288af2bb10e23b28eedfaebeafa49da2195ee4f))
* add data text attributes DFE-3122 ([fb91a31](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fb91a31a95fda8edd4e60201d99a2847120f8841))
* add decimal sanitation to combobox measurement field DFE-3138 ([56f140c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/56f140c81ecf65d7fe9801a905198814acb10457))
* Addresses use correct indication when contact info is missing. ([1b71946](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1b71946bde60fd44234e62b6d7aa4c28c2932f25))
* Adjust spacing between icon and text. ([d24fdfa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d24fdfa675e3cff7ad2497058960fb38ffb3ffdf))
* change text in additional services DFE-1619 ([2cf9b65](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2cf9b652b029420867ea53e32b17bd861941cd15))
* change text in additional services DFE-1619 ([05b7212](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/05b7212cc5c47ea61097f04476e0e18e7b3ee99a))
* DFE-1832 show error dialog on print label error - service not available ([8e594c4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8e594c4f1a816eca011dc171c6bf5e97db7e07f3))
* DFE-1832 show error dialog on print label error - service not available ([d64060c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d64060cbec2311f0e3b26a1adc6bf316e1dcae8f))
* DFE-2519 - updated API to v4.6.0 ([f4a3895](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f4a389569ded4833b027e2868d505e10f486cc29))
* DFE-2758 - ePricing not editable WIP ([d59a308](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d59a3082da87274da76caceb06bb90676071cfa1))
* DFE-2884 Add none option to loading point ([24dbf60](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/24dbf603ad97749973911c5566884d3a5560e136))
* DFE-2884 Adjust translation ([cc48328](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cc483287508f67830299fe5296e8a99693054cd3))
* DFE-3210 cloning an order, show hint that order has been cloned ([4443f87](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4443f873833aaacd314862af17971fde544d7cd2))
* DFE-3384 add item type ([2609098](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2609098d7f6e32ad7f71e6cd8ee1578c9d121ca0))
* DFE-3384 change text and increase type safety ([e06f13d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e06f13d5ebf324482d0e498ee799c4c70e23ddc2))
* DFE-3456 ([4122f3b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4122f3b6f3aaedbe798e664f9dc6251259902932))
* Remove pallet locations hint. ([9927d9a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9927d9a9d860bccb17389db36360e53f023a44f0))
* Styling for ClonedOrderBanner. ([b0c83d7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b0c83d76dda10faba8aef9a4d536c9256e273bdb))


### Reverts

* Revert "build: trigger" ([a8d2b6d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a8d2b6dc6c4efcede0005ed699a4d7432754aab9))
* Fix link styling for incoterms ([e7da3ab](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e7da3ab3a3e45b626e8dd920888810ec1de3ff46))


### BREAKING CHANGES

* Vue Upgrade
Refs: DFE-2909 - Vue3 upgrade

## [1.11.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.11.0...1.11.1) (2024-05-16)


### Bug Fixes

* create change to trigger build ([e9e5bf5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e9e5bf54238a9bb24c6f73e3e397d059ab5fdac4))

# [1.11.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.10.0...1.11.0) (2024-04-18)


### Bug Fixes

* DFE-2960 Adjust translation ([a76f773](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a76f773a75a7f84fb6fa7b5dfd77b5c75584944e))
* DFE-2960 Show banner only for road orders ([d885f4b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d885f4b37a7403b4c5a2cb015b0f2a867cf6f520))
* DFE-3191 Shipper is now able to save address to address book ([f2fb9df](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f2fb9df8c01a9b28129e53256a4f9f0984623153))
* DFE-3226 Remove None option for order groups and make them required if available ([895281d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/895281ddcff767e297eb9408207cc860acc6d9a5))
* DFE-3253 Reset PortRouting after creating order ([9cf452e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9cf452e4c90f51fa833b5c8bfc5fe7a6bee63bec))
* DFE-3257 No contacts should get directly validated when entering order edit mode ([60b66a4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/60b66a4505957e03e195683075db4042b2ce2eae))
* DFE-3258 add package.json ([28fb012](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/28fb0121bb28fe273078f4e84037829b57253b06))
* DFE-3258 Rebase develop ([9bd971e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9bd971eb123ca9d091387466a1e6eb2de2eac266))
* DFE-3258 Reset inco term if it invalid ([e1f7d16](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e1f7d16b6c1841b04b5a121ab7507d416dd41324))
* DFE-3273 Set mobileNumber/phoneNumber when readding address ([058a13c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/058a13c2f1fe520eaae93d45089e3b0125ad49da))
* DFE-3278 Measurements are mandatory for SEA and AIR orders ([9800d16](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9800d1679c8dcb3b35793322241bb96894189d35))
* DFE-3330 Add alphabetic sorting for principal selection ([64826d8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/64826d851c1d4442f674fa0590bf95f1308cda9a))
* DFE-3330 Add alphabetic sorting for principal selection ([37a67fa](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/37a67fa24249630fc81159533fd419c1a7181bc4))
* **AirOrder:** Fixed Fehleranzeige Adresseingabe fehlerhaft. ([81922d0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/81922d0254f0397b794c6bdf4ba22b7532e5e691))


### Features

* DFE-2960 Show Banner for custom documents - disable category input and delete button when document is processed. ([70bd496](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/70bd496a87f1c5bde5a44ee5e15606494f0f878c))
* DFE-3143 Adjust Loading/Unloading according to Figma ([08786e4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/08786e4410eaa02248b99e1a2a9113033b5fa4c4))
* DFE-3143 shippers reference defaults and validation changes ([17a1372](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/17a13720e66fb863ca209c8e84aaf93d96b76b62))

# [1.10.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.9.2...1.10.0) (2024-04-04)


### Bug Fixes

* DFE-3216 Remove sorting for customers in SelectAddress.vue ([8bf5b34](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8bf5b34180c230d7ca2329d5047e9836010894ec))
* **formAddresses:** Fixed shipperAddress id empty when entering edit mode. ([505c8fb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/505c8fb5790eb9a2723ad8142c7dba4ac4f4b347))
* **formAddresses:** Fixed shipperAddress id empty when entering edit mode. ([6be9bfd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6be9bfd28864670ecc90d923dd227b9646058ea2))
* **useSaveOrder:** Send boolean flag even if it's false ([73afc2e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/73afc2ee11548d311e9bf3f7618299508d8bb3c0))
* **useSaveOrder:** Send boolean flag even if it's false ([2d33cbc](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d33cbc7c1f262b071964a398bb22a0bf483832e))


### Features

* DFE-1191 harbour routing integration ([ab894ae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ab894ae95ec882fc44ab84d540d7ec91206d501e))
* DFE-1191 Integrate Port-Routing for SEA ([2eaef66](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2eaef662317a6fe3bd4eed80244419fb70b4e5f0))
* DFE-1191 Simplifies code ([c5d2d60](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c5d2d60a590faa43a161da309193b302ea391d6a))
* DFE-1191 Simplifies code ([512b58d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/512b58d488d0656e408a7b1d95dcf5df5a441036))
* DFE-2868 Only show checkbox for save to address book when address is not shipper and address from address book or no edit mode ([f5976e3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/f5976e356d840241f181b08528f36fa14248f109))
* DFE-2868 Update translation and condition for showing address bock checkbox ([6f1fb24](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6f1fb2476073acc73299995f27e97dfed8b9c410))
* DFE-3259 Remove contact save to address book for two scenarios. ([d4b5c94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d4b5c94a8b5431cd2187c688823292dce626b35b))
* DFE-3259 SEA: Make email mandatory for sea - remove supplement - add taxId and street2 ([7fd3d06](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7fd3d060904c5cbd1ec77a50d78521b84d285f53))
* DFE-3502 When switching from import to export or vice versa the selection (default/alternateAddress/port) is not getting deleted. ([a784ad1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a784ad1e141ba00d88e0738a922e900917eed5f3))

## [1.9.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.9.1...1.9.2) (2024-03-11)


### Bug Fixes

* DFE-3185 Do emit when no value is given for port selection ([dda1c4c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dda1c4cf6d7cdf29947315085a0800e2c2cf3683))
* DFE-3185 Do emit when no value is given for port selection ([b61fd32](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b61fd32223fd9223e13e9e7a1bafbeaf03732ebf))
* DFE-3188 Add condition for air import order from quote ([9480f20](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9480f2049fe23cface27f380a68869683caddd19))

# [1.9.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.8.1...1.9.0) (2024-02-28)


### Bug Fixes

* Add missing imports ([1e052e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1e052e6f70f65dcda3b729f8e7f2889f0183f74c))
* **useTransportDirection:** added pinia-store getter for "isDisabledForPriceRelevantChanges" ([9adbe63](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9adbe63ca5e5b8789b759745d0ecf8da98d90d5f))
* **useTransportDirection:** convert watch to function ([4f492b3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4f492b3dcaae7845edfecac00785c67873d8fc44))
* DFE-2462 Fix tests ([9297d06](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9297d06b84d91063d2df39043732b9ffa1d9a715))
* DFE-2462 Hide Principal section if no customer is available ([c4efe11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c4efe114bf16466ba702ee6828d8d385d72003cd))
* DFE-2462 Only show Principal in order form when there are more than one ([23bdf19](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/23bdf1914ef103342f20fe7e54456a4208c540e8))
* DFE-2462 Only show Principal in order form when there are more then one ([cb495c8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cb495c87eecf4e1e723a4df78fb787d9b3e1360f))
* DFE-2870 Prevent submit for form sections ([d2743d3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d2743d34b1f61947b6af590619b63c81a35fbb0e))
* DFE-2962 Remove focus event to prevent doubleclick on arrow icon ([e2baadd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e2baadd60615341593303b269dc17e1dafa01703))
* DFE-3107 Get DeliveryOption None now from BE ([6640a00](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6640a001f2c1b38f6c5cae3e9ec925a4d153a443))
* DFE-3107 Should set initial delivery contact data when delivery option is set to none - trigger release ([bf6e35f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bf6e35f0eacf02d96314544bab9774a05fe570b8))
* Edit q2b order ([0b676e0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0b676e0d031a5a4eb9eb58c4562b314e02dec334))
* **DFE-3119:** Fix address entry not possible. ([c907e22](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c907e22bea154c4030f4b4726c04c5456e4e9945))
* **AddressCard:** Fix contact data for consignee is filled incorrectly. ([c75f3bb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c75f3bb6bee172b5134783f0f829d623fae10f79))
* **AddressesTransport:** Fix contact data for shipper is filled incorrectly. ([b7c7fc3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b7c7fc3e5941b3480706bae4a373184d288ea1ed))
* fix merge conflict ([a606d8c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a606d8c1d6a77190cc4fa60f2b7275e56a9003d8))
* **DFE-3142:** Fix not setting shipper contact to consignee contact, if shipper contact was empty. ([796adae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/796adae98872b790b0f6de0e04f376f3e64a6778))
* **AddressCard:** Fix road addresses falsely marked red when order is validated. ([24f952a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/24f952a857fbc0f8ce32352c71356fd0480a664c))
* **AdditionalServicesAdvanced:** Fix road order group error when  switching principal. ([cc4a6f8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cc4a6f8795fad50a944066a43f5adee6021e36de))
* **OrderLine:** recalculating volume automatically ([76dfd98](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/76dfd98431901279878484b1913ca464a3d89ed8))
* **OrderLine:** recalculating volume automatically ([478da4d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/478da4da5c9d3f11d5f887a25e865c54b08ebd4b))
* **useTransportDirection:** removing test case for watching "orderType" ([da63c09](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/da63c0953b8e890836e13aeee91ad48a50fbbaa5))
* **useTransportDirection:** updated snapshots ([2d30ee8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2d30ee8dfbdf8fa948072cef9698295077383303))
* Updating shipper/consignee address ([ff991cf](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ff991cf184e246d84bd28374e14755e6f7d97cd1))
* **useTransportDirection:** used pinia-store getter in CardMain.vue ([a419d07](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a419d07c4a930d126739e39641f971b3e650ca7b))
* **DFE-3125:** Validate missing contact data. ([e91e5b5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e91e5b587379dd093404cfe6ed8f24ab87ee9c32))
* **DFE-3125:** Validate missing contact data. ([d2d2e99](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d2d2e99c865a7ee315020db3751f58d5bff726f1))
* validation error for address card ([2e7def6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2e7def64394a45d7d1bd6b26deb8e3c0d44b06a3))


### Features

* **DFE-3110:** AIR-Import-Q2B: Deactivate price related fields. ([dde854b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dde854bedea1f8e67d27039cede81cbda0fa2d6c))
* DFE-3110 - AIR-Import-Q2B: Deactivate price related fields ([fe50ea6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fe50ea642fff4522555036b5aee0ecb46a5b2996))

## [1.8.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.8.0...1.8.1) (2024-02-14)


### Bug Fixes

* DFE-3124 After quote to book the airport is not shown ([b8a662d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b8a662d28b0dfa6541571ebd5fa86502d7b897f0))

# [1.8.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.1...1.8.0) (2024-02-07)


### Bug Fixes

* **useOrderGroups:** 403 response on getOrderGroups ([0ca4f0c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0ca4f0cc69773b8571944db639ed6547c0182dc3))
* delete contactData.id ([74e24e5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/74e24e597dbd117fca691d9534c9795fa22d95ab))
* **address:** delete obsolete fragments ([8b9381e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/8b9381e94c4ebc4d115d9ba301f527abd855a216))
* DFE-2575 - change goodsValue to two digit floating point decimal. ([019e75e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/019e75ea146364404203e9e1302445b39f9e394a))
* DFE-2974 incoterm for door to port is now correctly dislplayed for sea import order ([3bb51f5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3bb51f501a5870074264d71768996708ec6b663a))
* DFE-3018 - clickable area for checkboxes ROAD ([4ca3e64](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4ca3e6453eb37aa999ddce889ccceb33831233ba))
* DFE-3018 Prevent auto close for autocomplete after enter ([e4b25fd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e4b25fd91a06079ab7d19f23314540b9bc6109d5))
* DFE-3054 Don´t show contact data form when delivery option is none ([9c9427e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9c9427eeebab9d6cbd66109144edcd31a89fbefc))
* DFE-3054 Update test ([5943a99](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5943a99cb55fc19df1c197af44e59e942cdb2bc9))
* DFE-3054 Use code instead of label for checking ([3c33fe6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3c33fe6a9bef38ad92ae1532390433e04ee83180))
* DFE-3057 delivery data aka fixDate is changed to previous date on order save ([fbfe7a2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/fbfe7a28142b6111b3760dba55a877884bc28cb0))
* DFE-3078 Restore function for adding further address ([61c21c6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/61c21c6832bf6216414829a00a1c5178e2091392))
* DFE-3079 Detect countryCode as optional ([cc19e0a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/cc19e0a0e809498a3a0cabe9186706647af3f393))
* **AddressCard:** Saving after previous validation error in contact ([d2ca67f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d2ca67f8fa7868ef5575a1728f18499ae90d7c53))


### Features

* DFE-2484 Add preferences from user to contact data ([54182ce](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/54182ce6846dffb0907ffee21b3a54b4ad85ce62))
* DFE-2602 Show error modal wehen error booking a order. ([2f1f022](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/2f1f022b81fe86070c444453265ddb295aa4b17c))
* DFE-2772 Add individualid to addressform ([85cf405](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/85cf405a09abce77fb6ccd12dd34b21fca75e9cf))
* DFE-2772 Update margin top to match figma ([120b746](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/120b746347cc8c69ffcf311f70c553e144e558f0))
* DFE-2881 Display toast after save order ([42beae6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/42beae68d98249d7aee6ea3d3d12d98758493b7f))
* DFE-2996 Adjust max validation for quantity - 99999 to 999 ([9fa285a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9fa285a88b1c5607526082bd50c532b1a14ba359))
* **DFE-2404:** Fix tests. ([700e537](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/700e537c44738f906ac2cff121f2f0d963abb7d4))
* **DFE-2404:** Show banner when packaging type has changed from quote order. ([a5f414d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a5f414d7250d376be84cab159567efe17c0bd2e2))

## [1.7.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0...1.7.1) (2024-01-17)


### Bug Fixes

* Add clearAllTimers in createOrder tests. ([52b2bf7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/52b2bf77b4cdc6d6f6cd2372fc5bc004e68a9b51))
* **DFE-2756:** Code review adjustments. ([44506ae](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/44506ae7a9ca7d50f4c02338f5a8a509295b7c8d))
* DFE-2961 - fix clickable area ([08cbf90](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/08cbf9007a5d9edd957a9e1a758248c2f48a8e55))
* DFE-2986 Update label id ([37ee180](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/37ee180d8f5f86feaa5d1e8846ec256e807d9ebb))
* DFE-3038 Dont use toString() method. ([bf28f7c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bf28f7c4a5f948e8089b8c87fdf0d5aeecae651a))
* DFE-3042 Use contact data instead of blue print. ([ac2d539](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/ac2d5392265d921cd50f215ce1bc77df1bf2f978))
* eslint error ([32fad8f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/32fad8f92027348072f2d7120396e3070e1b00f1))
* Fix tsc errors ([bd16197](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bd1619708256eb539e51d9aa5edc364b6207374e))
* **DFE-2756:** Fixed multiple modifications not detected as unsaved changes. ([4118b36](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4118b3671303befe60db54b8adc7606bf308adc0))
* **DFE-2756:** Tests for code coverage. ([b8ae265](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b8ae265833c5e35b4ca648a99905e184937b7100))
* **DFE-2756:** Tests for code coverage. ([dd967b7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/dd967b796daa2642bbeff72d91bf80cd8d2b34fc))

# [1.7.0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.6.0...1.7.0) (2023-12-20)


### Bug Fixes

* **DFE-2865:** Code review adjustments. ([5c02a35](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5c02a359c29e6ddf53a6e048f5773d543fa0127f))
* Comparison of change detection ([0370904](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0370904a7a878e76e31ead4849f201a076810594))
* DFE-2547 ([a83aca5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a83aca5bf1446f77173df537111702913eb48492))
* DFE-2811 - Changes to alternate address are not getting applied. ([76835c1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/76835c178dde0c847ce289ac3eba2f7edfca1e23))
* DFE-2815 Change Column definition for request arrangement toggle ([922e82e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/922e82e4d9fe7af5637ce0cd220494b50ff5232d))
* DFE-2824 Remove falsely implemented if-condition for road order ([9440215](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/944021558b92d3507f3cf9b7ea3f8f41d69e8be1))
* DFE-2838 Remove not mandatory postcode from port routing ([6d308c9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6d308c936333dcdd38703abd6ec1c8f367ad17f6))
* DFE-2838 Show addresscard shortform when country code is given ([51e61a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/51e61a92e944930a67b964f7638feb87a2303069))
* DFE-2838 Use right type for address creation/update in addressbook ([bf67aa3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bf67aa3338a91fc2291a11c1c918724ab04e22cb))
* DFE-2865 When there is no contact on update address then use profile contact ([54296e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/54296e695c6a97287a3761f3d212f47dfab50a34))
* DFE-2917 Retrigger scrollToGenericErrorBanner after second submission to BE ([69fd20d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/69fd20d8a58510d7a425bdbe3a7a31421fa371bb))
* DFE-2918 Adjust min and max for quantity. ([a3b7ce3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a3b7ce382ee02d64c6919de43f706947b0ee82ae))
* **air-products:** Fix alignment and order of error-banner ([71ae41b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/71ae41b391f8a5b0dbf013067bed7833d10f4aa6))
* **DFE-2865:** Fix contact data gets lost with principal change. ([5e3ed8b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5e3ed8b00535171afcec64b62cdc053d7cdb5814))
* **DFE-2756:** Fixed multiple modifications not detected as unsaved changes. ([9a16ac7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9a16ac7f32010c1636b4597b50980e4b2a6ea7ba))
* **DFE-2756:** Fixed multiple modifications not detected as unsaved changes. ([3c99df8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3c99df85a9d0dcc790984b75bd22bb9c0c651e6d))
* **DFE-2780:** Fixed scrolling to documents from details view. ([b935567](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b935567b851db2e5f5ebe5c9db3a8bd6a9031078))
* **DFE-2756:** Revert change. ([75aafa0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/75aafa043599cb09e34e1ea16f07723d15225891))
* **DFE-2865:** Test fix contact data gets lost with principal change. ([7d1d785](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7d1d785b4f84fce8a2aefa946eb1bc5fae3fd2dd))


### Features

* **DFE-2846:** Change usages of generated api to api from npm packages. ([5d30366](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5d30366118d4ed0161d68f816e63cf64e37a71ad))
* DFE-2489 Add delivery section for air and sea ([e13b820](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e13b82002378b236634680db1a2643141dcc6bc4))
* DFE-2502 Adjust saving and loading port for sea order ([43adf94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/43adf94869f7623588c23ff6932f1a86fe7f920f))
* DFE-2547 ([6ba1e6f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6ba1e6f4fc5d953fc0c25537067e9b50d27d808e))
* DFE-2788 Only show Checkbox for create another, when form is in create mode ([c1791b2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c1791b2a9963568d2c652c7f370d64d8fabfee6f))
* DFE-2793 Remove deletion success snackbar when uploading a document. ([57526b3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/57526b30b2c9b3ca8609dd8e17abc8d2efab235d))
* DFE-2803 - Adjust decimal places for AIR + SEA ([9312782](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/93127827050b1bc361aa11b50ede3efeaefb8df9))
* DFE-2803 - Decimal place adjustments ([0fd7c5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0fd7c5a436e2a672dd2ae64a38c02527227477fa))
* DFE-2812 Add hover state to v-radio - products/incoterms ([64f613e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/64f613ea9b7109db0f7b757c383865a32140c37b))
* DFE-2832 Disable Save as draft Button when order is in status COMPLETE or LABEL_PENDING ([78723dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/78723ddd011122a2d8c1c5796d548ab03fa46bdf))
* DFE-2832 Disable Save as draft Button when order is in status COMPLETE or LABEL_PENDING ([1bbc9ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1bbc9ba78d0eef00128ea5333718553c5422d127))
* DFE-2838 Change condition for short form display of address ([d724d27](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d724d2781c40246cb53dff8431a3c885c106b4de))
* DFE-2838 Disable postCode when countryCode is empty or post code is not mandatory for country. ([0b8c60c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0b8c60cdaf5867277ed416c1e94ba104bd56a602))
* DFE-2838 Disable postCode when countryCode is empty or post code is not mandatory for country. ([9f3d29b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9f3d29ba435f8e89773ebd5eaf1037dfabfc635c))
* DFE-2838 Update disabled flag for postcode ([3868abb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3868abb0a05b9204b222b4199621c339ca785760))
* DFE-2907 Hide request arrangement for air and sea ([4c9d857](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4c9d857c1408c334f270dea06826166eb4a2eba7))
* Products (incl. mandatory) DFE-2665 ([4fef870](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4fef870700fad9552d37404adc6f142668fb3471))
* Products disabled Quote2Book DFE-2624 ([258f718](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/258f718b74c89f4394baf9e6e6d9ecd145941b89))
* trigger build pipeline ([639fa1c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/639fa1cb19d445c4102a463a011bc1a6853df872))

# [1.7.0-beta.31](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.30...1.7.0-beta.31) (2023-12-19)


### Bug Fixes

* DFE-2838 Remove not mandatory postcode from port routing ([6d308c9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6d308c936333dcdd38703abd6ec1c8f367ad17f6))
* DFE-2838 Show addresscard shortform when country code is given ([51e61a9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/51e61a92e944930a67b964f7638feb87a2303069))

# [1.7.0-beta.30](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.29...1.7.0-beta.30) (2023-12-18)


### Bug Fixes

* DFE-2838 Use right type for address creation/update in addressbook ([bf67aa3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/bf67aa3338a91fc2291a11c1c918724ab04e22cb))

# [1.7.0-beta.29](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.28...1.7.0-beta.29) (2023-12-15)


### Bug Fixes

* DFE-2865 When there is no contact on update address then use profile contact ([54296e6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/54296e695c6a97287a3761f3d212f47dfab50a34))

# [1.7.0-beta.28](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.27...1.7.0-beta.28) (2023-12-15)


### Bug Fixes

* Comparison of change detection ([0370904](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0370904a7a878e76e31ead4849f201a076810594))
* **DFE-2756:** Fixed multiple modifications not detected as unsaved changes. ([9a16ac7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9a16ac7f32010c1636b4597b50980e4b2a6ea7ba))

# [1.7.0-beta.27](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.26...1.7.0-beta.27) (2023-12-14)


### Features

* DFE-2838 Update disabled flag for postcode ([3868abb](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3868abb0a05b9204b222b4199621c339ca785760))

# [1.7.0-beta.26](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.25...1.7.0-beta.26) (2023-12-13)


### Bug Fixes

* DFE-2824 Remove falsely implemented if-condition for road order ([9440215](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/944021558b92d3507f3cf9b7ea3f8f41d69e8be1))

# [1.7.0-beta.25](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.24...1.7.0-beta.25) (2023-12-13)


### Features

* DFE-2838 Change condition for short form display of address ([d724d27](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/d724d2781c40246cb53dff8431a3c885c106b4de))

# [1.7.0-beta.24](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.23...1.7.0-beta.24) (2023-12-12)


### Features

* DFE-2838 Disable postCode when countryCode is empty or post code is not mandatory for country. ([0b8c60c](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0b8c60cdaf5867277ed416c1e94ba104bd56a602))

# [1.7.0-beta.23](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.22...1.7.0-beta.23) (2023-12-12)


### Features

* DFE-2838 Disable postCode when countryCode is empty or post code is not mandatory for country. ([9f3d29b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/9f3d29ba435f8e89773ebd5eaf1037dfabfc635c))

# [1.7.0-beta.22](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.21...1.7.0-beta.22) (2023-12-12)


### Bug Fixes

* DFE-2918 Adjust min and max for quantity. ([a3b7ce3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a3b7ce382ee02d64c6919de43f706947b0ee82ae))

# [1.7.0-beta.21](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.20...1.7.0-beta.21) (2023-12-05)


### Bug Fixes

* **DFE-2865:** Code review adjustments. ([5c02a35](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5c02a359c29e6ddf53a6e048f5773d543fa0127f))

# [1.7.0-beta.20](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.19...1.7.0-beta.20) (2023-12-05)


### Features

* DFE-2812 Add hover state to v-radio - products/incoterms ([64f613e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/64f613ea9b7109db0f7b757c383865a32140c37b))

# [1.7.0-beta.19](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.18...1.7.0-beta.19) (2023-12-05)


### Bug Fixes

* **DFE-2865:** Fix contact data gets lost with principal change. ([5e3ed8b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5e3ed8b00535171afcec64b62cdc053d7cdb5814))
* **DFE-2865:** Test fix contact data gets lost with principal change. ([7d1d785](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/7d1d785b4f84fce8a2aefa946eb1bc5fae3fd2dd))


### Features

* DFE-2907 Hide request arrangement for air and sea ([4c9d857](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4c9d857c1408c334f270dea06826166eb4a2eba7))

# [1.7.0-beta.18](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.17...1.7.0-beta.18) (2023-12-04)


### Bug Fixes

* DFE-2917 Retrigger scrollToGenericErrorBanner after second submission to BE ([69fd20d](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/69fd20d8a58510d7a425bdbe3a7a31421fa371bb))

# [1.7.0-beta.17](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.16...1.7.0-beta.17) (2023-11-30)


### Bug Fixes

* **DFE-2756:** Revert change. ([75aafa0](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/75aafa043599cb09e34e1ea16f07723d15225891))

# [1.7.0-beta.16](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.15...1.7.0-beta.16) (2023-11-30)


### Bug Fixes

* **DFE-2756:** Fixed multiple modifications not detected as unsaved changes. ([3c99df8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/3c99df85a9d0dcc790984b75bd22bb9c0c651e6d))

# [1.7.0-beta.15](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.14...1.7.0-beta.15) (2023-11-29)


### Features

* DFE-2803 - Adjust decimal places for AIR + SEA ([9312782](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/93127827050b1bc361aa11b50ede3efeaefb8df9))
* DFE-2803 - Decimal place adjustments ([0fd7c5a](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/0fd7c5a436e2a672dd2ae64a38c02527227477fa))

# [1.7.0-beta.14](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.13...1.7.0-beta.14) (2023-11-27)


### Features

* DFE-2502 Adjust saving and loading port for sea order ([43adf94](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/43adf94869f7623588c23ff6932f1a86fe7f920f))

# [1.7.0-beta.13](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.12...1.7.0-beta.13) (2023-11-27)


### Bug Fixes

* DFE-2815 Change Column definition for request arrangement toggle ([922e82e](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/922e82e4d9fe7af5637ce0cd220494b50ff5232d))

# [1.7.0-beta.12](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.11...1.7.0-beta.12) (2023-11-24)


### Bug Fixes

* **DFE-2780:** Fixed scrolling to documents from details view. ([b935567](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/b935567b851db2e5f5ebe5c9db3a8bd6a9031078))

# [1.7.0-beta.11](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.10...1.7.0-beta.11) (2023-11-24)


### Bug Fixes

* **air-products:** Fix alignment and order of error-banner ([71ae41b](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/71ae41b391f8a5b0dbf013067bed7833d10f4aa6))

# [1.7.0-beta.10](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.9...1.7.0-beta.10) (2023-11-22)


### Features

* DFE-2832 Disable Save as draft Button when order is in status COMPLETE or LABEL_PENDING ([78723dd](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/78723ddd011122a2d8c1c5796d548ab03fa46bdf))

# [1.7.0-beta.9](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.8...1.7.0-beta.9) (2023-11-21)


### Features

* **DFE-2846:** Change usages of generated api to api from npm packages. ([5d30366](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/5d30366118d4ed0161d68f816e63cf64e37a71ad))

# [1.7.0-beta.8](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.7...1.7.0-beta.8) (2023-11-20)


### Features

* Products disabled Quote2Book DFE-2624 ([258f718](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/258f718b74c89f4394baf9e6e6d9ecd145941b89))

# [1.7.0-beta.7](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.6...1.7.0-beta.7) (2023-11-17)


### Bug Fixes

* DFE-2811 - Changes to alternate address are not getting applied. ([76835c1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/76835c178dde0c847ce289ac3eba2f7edfca1e23))

# [1.7.0-beta.6](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.5...1.7.0-beta.6) (2023-11-17)


### Features

* DFE-2832 Disable Save as draft Button when order is in status COMPLETE or LABEL_PENDING ([1bbc9ba](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/1bbc9ba78d0eef00128ea5333718553c5422d127))

# [1.7.0-beta.5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.4...1.7.0-beta.5) (2023-11-16)


### Features

* Products (incl. mandatory) DFE-2665 ([4fef870](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/4fef870700fad9552d37404adc6f142668fb3471))

## [Unreleased]

### Fixed

### Added

- Air Products ([DFE-2665](https://dil-itd.atlassian.net/browse/DFE-2665))

### Changed

### Removed

### Deprecated

### Security

# [1.7.0-beta.4](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.3...1.7.0-beta.4) (2023-11-15)


### Features

* DFE-2489 Add delivery section for air and sea ([e13b820](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/e13b82002378b236634680db1a2643141dcc6bc4))

# [1.7.0-beta.3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.2...1.7.0-beta.3) (2023-11-15)


### Features

* DFE-2788 Only show Checkbox for create another, when form is in create mode ([c1791b2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/c1791b2a9963568d2c652c7f370d64d8fabfee6f))

# [1.7.0-beta.2](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.7.0-beta.1...1.7.0-beta.2) (2023-11-14)


### Features

* DFE-2793 Remove deletion success snackbar when uploading a document. ([57526b3](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/57526b30b2c9b3ca8609dd8e17abc8d2efab235d))

# [1.7.0-beta.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.6.1-beta.1...1.7.0-beta.1) (2023-11-14)


### Bug Fixes

* DFE-2547 ([a83aca5](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/a83aca5bf1446f77173df537111702913eb48492))


### Features

* DFE-2547 ([6ba1e6f](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/commit/6ba1e6f4fc5d953fc0c25537067e9b50d27d808e))

## [1.6.1-beta.1](https://bitbucket.dach041.dachser.com/scm/dfe/dfe-book-frontend/compare/1.6.0...1.6.1-beta.1) (2023-11-11)

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.6.0]

### Fixed
- Fixing the calculation bug for the volume field ([DFE-2319](https://dil-itd.atlassian.net/browse/DFE-2319)).
- Changing quantity after deleting volume in orders leads to error message ([DFE-2540](https://dil-itd.atlassian.net/browse/DFE-2540))
- Fix: Adjust order summary for sea to be the same as for air ([DFE-2522](https://dil-itd.atlassian.net/browse/DFE-2522)).
- Fix wrong possible selections in address autocomplete ([DFE-2488](https://dil-itd.atlassian.net/browse/DFE-2488)).
- Fix: When selecting address from suggestions, the contact details are adopted ([DFE-2700](https://dil-itd.atlassian.net/browse/DFE-2700)).
- Fix behaviour of combobox field ([DFE-2700](https://dil-itd.atlassian.net/browse/DFE-2700)).
- Activating "Is principal"-toggle twice will lead to prefilled contact details when adding a new address ([DFE-2538](https://dil-itd.atlassian.net/browse/DFE-2538)).
- Fix: Disable Import/Export toggle for quote orders. ([DFE-2765](https://dil-itd.atlassian.net/browse/DFE-2765))
- Fix: Update contact the right way ([DFE-2762](https://dil-itd.atlassian.net/browse/DFE-2762))
- Activating "Is principal"-toggle twice will lead to prefilled contact details when adding a new address ([DFE-2538](https://dil-itd.atlassian.net/browse/DFE-2538)).

### Added

### Changed
- Feature: Remove confirmation notification after successful document upload. ([DFE-2612](https://dil-itd.atlassian.net/browse/DFE-2612))
- Destination airport is disabled for air import orders ([DFE-2721](https://dil-itd.atlassian.net/browse/DFE-2721))
- Adjust delete and cancel dialog labels ([DFE-2620](https://dil-itd.atlassian.net/browse/DFE-2620))
- Changed EN date form to ISO-8106 ([DFE-2582](https://dil-itd.atlassian.net/browse/DFE-2582))

### Removed

### Deprecated

### Security

## [1.5.3]

### Fixed
- Hotfix: Fixed shipper address not changeable ([DFE-2766](https://dil-itd.atlassian.net/browse/DFE-2766))

### Added

### Changed

### Removed

### Deprecated

### Security

## [1.5.2]

### Fixed

### Added

- Banner for missing commercial invoice is shown when validating in Backend ([DFE-2310](https://dil-itd.atlassian.net/browse/DFE-2310)).
- Air product selection only for specific routing ([DFE-2653](https://dil-itd.atlassian.net/browse/DFE-2653)).
- Product selection for air orders ([DFE-2552](https://dil-itd.atlassian.net/browse/DFE-2552)).

### Changed

- Hotfix: Deactivate Save to address book button for shipper. ([DFE-2768](https://dil-itd.atlassian.net/browse/DFE-2768))

### Removed

### Deprecated

### Security

## [1.5.1] - 2023-10-20

### Fixed

- Use correct contactData for customer. ([DFE-2691](https://dil-itd.atlassian.net/browse/DFE-2691))
- Fixed principal address data not changeable, only contact data. ([DFE-2746](https://dil-itd.atlassian.net/browse/DFE-2746))

### Added

### Changed

### Removed

### Deprecated

### Security

## [1.5.0] - 2023-10-16

### Fixed
- Fix wrong possible selections in address autocomplete ([DFE-2488](https://dil-itd.atlassian.net/browse/DFE-2488)).
- Fixed missing contact details for further addresses. ([DFE-2518](https://dil-itd.atlassian.net/browse/DFE-2518)).
- Changing quantity after deleting volume in orders leads to error message ([DFE-2540](https://dil-itd.atlassian.net/browse/DFE-2540))
- Fixing the calculation bug for the volume field ([DFE-2319](https://dil-itd.atlassian.net/browse/DFE-2319)).

- Fix: Adjust order summary for sea to be the same as for air ([DFE-2522](https://dil-itd.atlassian.net/browse/DFE-2522)).

### Added

### Changed

- Change the way the API Client is generated by using the `@dfe/dfe-frontend-api-template` ([DFE-2470](https://dil-itd.atlassian.net/browse/DFE-2470))
- Changed behavior of HS Code input field. Search is triggered after input of three characters. Input can now be deleted via
  button. ([DFE-2408](https://dil-itd.atlassian.net/browse/DFE-2408)).
- Shipper can be edited when order is from quote. ([DFE-2687](https://dil-itd.atlassian.net/browse/DFE-2687))

### Removed

- Removed green notification when document error banner is closed ([DFE-2527](https://dil-itd.atlassian.net/browse/DFE-2527)).
- Removed feature flag for principal toggle ([DFE-2561](https://dil-itd.atlassian.net/browse/DFE-2561))

### Deprecated

### Security

## [1.4.0] - 2023-10-02

### Fixed

- Upload cancellation has an effect on all documents that are currently uploaded ([DFE-1210](https://dil-itd.atlassian.net/browse/DFE-1210)).
- selecting an address from the suggestions inside the edit address modal caused a crash when saving the order ([DFE-2609](https://dil-itd.atlassian.net/browse/DFE-2609)).
- Deletion of documents on already persisted orders is showing correct layer and deletes document if wanted ([DFE-2534](https://dil-itd.atlassian.net/browse/DFE-2534))
- Fix buggy behavior when picking selected customer on search address field ([DFE-2488](https://dil-itd.atlassian.net/browse/DFE-2488)).
- Contact suggestion in address modal fixed ([DFE-2590](https://dil-itd.atlassian.net/browse/DFE-2590)).
- Contact details are adopted when saving new address. ([DFE-2539](https://dil-itd.atlassian.net/browse/DFE-2539))

### Added

- Hotfix: Deactivate Principal-Toggle ([DFE-2560](https://dil-itd.atlassian.net/browse/DFE-2560))
- Feature: Add Feature-Flag for Principal-Toggle ([DFE-2574](https://dil-itd.atlassian.net/browse/DFE-2574))
- Feature: Adjusted incoterms api call to receive incoterms based on ordertype ([DFE-2503](https://dil-itd.atlassian.net/browse/DFE-2503))
- Feature: Add Delivery Section for Air and Sea order ([DFE-2489](https://dil-itd.atlassian.net/browse/DFE-2489))

### Changed

- Change the way the API Client is generated by using the `@dfe/dfe-frontend-api-template` ([DFE-2470](https://dil-itd.atlassian.net/browse/DFE-2470))

### Deprecated

### Removed

### Security

- Change the way the API Client is generated by using the `@dfe/dfe-frontend-api-template` ([DFE-2470](https://dil-itd.atlassian.net/browse/DFE-2470))

### Deprecated

## [1.3.2] - 2023-09-14

### Added

- Address field check for incomplete address or contact data ([DFE-1697](https://dil-itd.atlassian.net/browse/DFE-1697)).

### Fixed

- Hotfix resetting "is Principal"-toggle on discarding changes. ([DFE-2541](https://dil-itd.atlassian.net/browse/DFE-2541))

## [1.3.1] - 2023-09-11

### Changed

- Adapt Frontend to new API Order Structure ([DFE-2422](https://dil-itd.atlassian.net/browse/DFE-2422)).

## [1.3.0] - 2023-09-06

### Added

- Changelog
- Toggle to switch between Import and Export orders ([DFE-1106](https://dil-itd.atlassian.net/browse/DFE-1106)).

### Changed

- (Air-)Port Routing refactored ([DFE-2441](https://dil-itd.atlassian.net/browse/DFE-2441)).

### Fixed

- Fix extended menu in Footer ([DFE-2525](https://dil-itd.atlassian.net/browse/DFE-2525)).

## [1.2.0] - 2023-08-21

### Added

- This section is only a template to visualize how the changelog will look like.

### Changed

- This section is only a template to visualize how the changelog will look like.

### Fixed

- This section is only a template to visualize how the changelog will look like.

### Removed

- This section is only a template to visualize how the changelog will look like.
