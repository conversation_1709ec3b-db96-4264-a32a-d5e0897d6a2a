import { define<PERSON><PERSON> } from 'pinia';
import { TextTypes } from '@/enums';
import type { OrderText } from '@dfe/dfe-book-api';

export interface CreateOrderText extends OrderText {
  active: boolean;
}

export interface CreateOrderTextsState {
  texts: CreateOrderText[];
}

export const useCreateOrderTextsStore = defineStore('createOrderTexts', {
  state: (): CreateOrderTextsState => ({
    texts: [
      {
        id: undefined,
        textType: TextTypes.CollectionInstructions,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.DeliveryInstructions,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.GoodsDescription,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.InvoiceText,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.OtherInformation,
        value: undefined,
        active: false,
      },
      {
        id: undefined,
        textType: TextTypes.SpecialRegulation,
        value: undefined,
        active: false,
      },
    ],
  }),
  getters: {
    getTexts(): CreateOrderText[] {
      return this.texts.map(({ id, textType, value, active }) => ({
        id,
        textType,
        value,
        active,
      }));
    },
  },
});
