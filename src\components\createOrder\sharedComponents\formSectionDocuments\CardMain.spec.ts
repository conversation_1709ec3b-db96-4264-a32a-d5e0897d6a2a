import CardMain from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';
import DocumentUploader from '@/components/createOrder/sharedComponents/formSectionDocuments/documentUploader/DocumentUploader.vue';
import { storeToRefs } from 'pinia';

describe('Sections Documents CardMain component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = shallowMount(CardMain);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('hide uploadMandatory label', async () => {
    const uploadMandatory = wrapper.find('.upload-mandatory');
    expect(uploadMandatory.exists()).toBe(false);
  });

  it('displays uploadMandatory label', async () => {
    const formStore = useCreateOrderFormStore();

    const { transportType, orderType } = storeToRefs(formStore);
    transportType.value = Segment.AIR;
    orderType.value = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const uploadMandatory = wrapper.find('.upload-mandatory');

    expect(uploadMandatory.exists()).toBe(true);
  });

  it('displays document-uploader', async () => {
    const documentUploader = wrapper.findComponent(DocumentUploader);
    expect(documentUploader.exists()).toBe(true);
  });
});
