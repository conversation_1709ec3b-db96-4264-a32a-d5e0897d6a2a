<template>
  <VTooltip
    location="top"
    :text="
      !selectedItems.includes(index) || isSingleSelect
        ? t('labels.select_label.text')
        : t('labels.unselect_label.text')
    "
  >
    <template #activator="{ props: tooltipActivator }">
      <VRadioGroup
        v-if="isSingleSelect"
        :model-value="selectedItems[0]"
        v-bind="tooltipActivator"
        hide-details="auto"
        @update:model-value="onSelectItem"
      >
        <VRadio :value="index" label="" color="primary" density="compact" />
      </VRadioGroup>
      <VCheckbox
        v-else
        :model-value="selectedItems"
        :value="index"
        color="primary"
        density="compact"
        hide-details="auto"
        label=""
        v-bind="tooltipActivator"
        @update:model-value="onSelectItem"
      />
    </template>
  </VTooltip>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface Props {
  isSingleSelect?: boolean;
  selectedItems: number[];
  index: number;
}

const { isSingleSelect = false, selectedItems, index } = defineProps<Props>();
const emits = defineEmits(['item-selected']);

const { t } = useI18n();

const onSelectItem = () => {
  emits('item-selected');
};
</script>

<style scoped lang="scss">
@use '@/styles/base' as base;

.v-checkbox,
.v-radio-group {
  width: base.space(6) !important;
}
</style>
