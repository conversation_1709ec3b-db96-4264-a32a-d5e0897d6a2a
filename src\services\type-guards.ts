import type { DocumentResponse, HttpResponse, OrderProcessResult } from '@dfe/dfe-book-api';

export function isHttpResponse<T>(
  response: unknown,
  contentCheck: (content: unknown) => content is T,
): response is HttpResponse<T> {
  return (
    response !== null &&
    typeof response === 'object' &&
    'data' in response &&
    'error' in response &&
    contentCheck((response as HttpResponse<T>).data)
  );
}

export function isErrorHttpResponse<T>(
  response: unknown,
  contentCheck: (content: unknown) => content is T,
): response is HttpResponse<null, T> {
  return (
    response !== null &&
    typeof response === 'object' &&
    'data' in response &&
    'error' in response &&
    !contentCheck((response as HttpResponse<T>).data) &&
    contentCheck((response as HttpResponse<null, T>).error)
  );
}

export function isDocumentResponse(content: unknown): content is DocumentResponse {
  return !!(
    content &&
    typeof content === 'object' &&
    'documentId' in content &&
    'documentName' in content &&
    'uploadStatus' in content
  );
}

export function isOrderProcessResult(content: unknown): content is OrderProcessResult {
  return !!(
    content &&
    typeof content === 'object' &&
    'order' in content &&
    'orderLabel' in content &&
    'validationResult' in content
  );
}
