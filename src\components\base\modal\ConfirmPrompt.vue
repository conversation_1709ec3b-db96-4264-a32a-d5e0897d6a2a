<template>
  <ModalWrapper
    v-model="isOpen"
    :headline="headline"
    :size="size"
    :has-border="hasBorder"
    @close="close"
  >
    <template #body>
      <slot />
    </template>
    <template #footer>
      <VBtn v-data-test="'confirm-btn'" size="small" color="primary" @click.prevent="confirm">
        {{ confirmText }}
      </VBtn>
      <VBtn v-data-test="'cancel-btn'" size="small" color="primary" variant="text" @click="cancel">
        {{ cancelText }}
      </VBtn>
    </template>
  </ModalWrapper>
</template>

<script setup lang="ts">
import type { Size } from '@/components/base/modal/ModalWrapper.types';
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';
import type { TranslateResult } from 'vue-i18n';

type ConfirmPromptProps = {
  headline: TranslateResult;
  confirmText?: TranslateResult;
  cancelText?: TranslateResult;
  size?: Size;
  hasBorder?: boolean;
};

withDefaults(defineProps<ConfirmPromptProps>(), {
  confirmText: '',
  cancelText: '',
  size: 'sm',
  hasBorder: true,
});
const isOpen = defineModel<boolean>();
const emit = defineEmits(['close', 'cancel', 'confirm']);

const close = () => {
  emit('close');
};
const cancel = () => {
  emit('cancel');
};
const confirm = () => {
  emit('confirm');
};
</script>
