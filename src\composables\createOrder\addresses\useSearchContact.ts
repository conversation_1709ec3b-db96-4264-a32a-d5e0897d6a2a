import { computed, ref, watch } from 'vue';
import { debounce } from 'lodash';
import type { ContactData } from '@dfe/dfe-book-api';

type ContactSearchOptions = {
  contacts: ContactData[] | undefined;
};

const useSearchContact = (options: ContactSearchOptions) => {
  const searchTerm = ref<string>('');
  const contacts = ref<ContactData[]>([]);
  const isLoading = ref<boolean>(false);

  const searchContact = async (value: string): Promise<ContactData[]> => {
    if (options.contacts == undefined) return [];
    return options.contacts.filter(
      (contact) => !value || contact.name.toLowerCase().includes(value.toLowerCase()),
    );
  };

  const updateResults = debounce(async (value: string) => {
    isLoading.value = true;
    contacts.value = await searchContact(value);
    isLoading.value = false;
  }, 200);

  watch(searchTerm, updateResults);

  return {
    searchTerm,
    contacts: computed(() => contacts.value),
    isLoading: computed(() => isLoading.value),
    triggerForceSearch: () => updateResults(searchTerm.value),
  };
};

export { useSearchContact };
