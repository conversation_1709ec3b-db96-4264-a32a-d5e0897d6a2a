<template>
  <VBtn variant="text" class="pa-0" :disabled="disabled">
    <MaterialSymbol :size="size">
      <slot />
    </MaterialSymbol>
  </VBtn>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';

type Props = {
  size?: string | number;
  disabled?: boolean;
};
defineProps<Props>();
</script>

<style lang="scss" scoped>
.v-btn.v-btn--size-default {
  --v-btn-height: unset;

  line-height: 0;
  border-radius: 0;
  min-width: auto;

  :deep(.v-btn__overlay) {
    display: none;
  }

  &.v-btn--disabled {
    opacity: 1;
  }

  &:hover .material-symbol {
    color: var(--color-hover, var(--color-base-grey-900));
  }

  &[disabled] .material-symbol {
    color: var(--color-disabled, var(--color-base-grey-500));
  }

  .material-symbol {
    color: var(--color, var(--color-base-grey-700));
  }
}
</style>
