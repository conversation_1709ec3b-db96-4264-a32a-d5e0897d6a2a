import type { Server } from 'miragejs';
import { DFE_ADDRESS_API_URL } from '@/env';
import type { Fixtures, Models } from '../server';

function withURL(path: string) {
  return `${DFE_ADDRESS_API_URL}${path}`;
}

export function useRoutes(
  server: Server,
  fixtures?: Fixtures<Models>,
  environment = 'development',
) {
  // Mock routes in test environment only
  if (environment === 'test') {
    // CREATE new address
    server.post(withURL('/v1/addresses'), () => {
      return fixtures?.addresses?.[0] || [];
    });

    server.post(withURL('/v1/addresses/:addressId'), () => {
      return fixtures?.addresses?.[0] || [];
    });

    server.put(withURL('/v1/addresses/:addressId'), () => {
      return fixtures?.addresses?.[0] || [];
    });

    server.post(withURL('/v1/addresses/:addressId/contacts'), () => {
      return fixtures?.contactData || [];
    });

    server.get(withURL('/v1/addresses/search'), (_, request) => {
      const value = request?.queryParams?.query || '';

      if (value?.length) {
        return (
          fixtures?.addresses?.filter((item) =>
            item.name?.toLowerCase().includes((value as string).toLowerCase()),
          ) || []
        );
      }

      return [];
    });

    server.get(withURL('/v1/addresses/:addressId/contacts'), () => {
      return [fixtures?.contactData];
    });

    server.put(withURL('/v1/addresses/:addressId/contacts/:contactId'), () => {
      return fixtures?.contactDataUpdated || [];
    });
  }
}
