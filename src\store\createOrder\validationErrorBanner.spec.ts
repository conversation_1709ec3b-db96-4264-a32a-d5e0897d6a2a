import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import { storeToRefs } from 'pinia';
import { initPinia } from '../../../test/util/init-pinia';
import { useCreateOrderFormStore } from './form';
import type { ValidationResultEntry } from '@dfe/dfe-book-api';

describe('Error Banner Store', () => {
  let errorBannerStore: ReturnType<typeof useErrorBanner>;

  beforeEach(() => {
    initPinia();
    errorBannerStore = useErrorBanner();
  });

  it('should be visible when there are validation errors', () => {
    errorBannerStore.updateErrors([{ description: 'error1' }, { description: 'error2' }]);
    expect(errorBannerStore.isVisible).toBeTruthy();
  });

  it('should not be visible when there are no validation errors', () => {
    errorBannerStore.updateErrors([]);
    expect(errorBannerStore.isVisible).toBeFalsy();
  });

  it('should return the validation message', () => {
    expect(typeof errorBannerStore.validationMessage).toEqual('string');
  });

  it('should update the validation errors', () => {
    errorBannerStore.updateErrors([{ description: 'error1' }, { description: 'error2' }]);
    expect(errorBannerStore.validationErrors[0].description).toEqual('error1');
    expect(errorBannerStore.validationErrors[1].description).toEqual('error2');
  });

  it('should import the validation errors from a validation result', () => {
    errorBannerStore.importFromValidationResult({
      results: [
        {
          description: 'error1',
        },
        {
          description: 'error2',
        },
      ],
    });
    errorBannerStore.updateErrors([{ description: 'error1' }, { description: 'error2' }]);
    expect(errorBannerStore.validationErrors[0].description).toEqual('error1');
    expect(errorBannerStore.validationErrors[1].description).toEqual('error2');
  });
  it('returns CustomType true', () => {
    const results: ValidationResultEntry[] = [
      {
        field: 'customsType',
        description: 'error1',
      },
      {
        field: 'customType1',
        description: 'error2',
      },
    ];
    errorBannerStore.pushErrors(results);
    const createOrderFormStore = useCreateOrderFormStore();
    const { customTypeValidationError } = storeToRefs(createOrderFormStore);
    if (results[0].field === 'customsType') {
      customTypeValidationError.value = true;
    }
    expect(customTypeValidationError.value).toBeTruthy();
  });

  it('should return empty object if description is not present', () => {
    errorBannerStore.pushErrors([
      { field: 'customsType' },
      { field: 'customType1', description: 'error2' },
    ]);
    expect(errorBannerStore.validationErrors[0]).toStrictEqual({});
    expect(errorBannerStore.validationErrors[1]).toStrictEqual({
      field: 'customType1',
      description: 'error2',
    });
  });

  it('should add frontend errors', () => {
    errorBannerStore.addFrontendErrors();
    expect(errorBannerStore.hasFrontendErrors).toBeTruthy();
  });

  it('should clear errors', () => {
    errorBannerStore.importFromValidationResult({
      results: [
        {
          description: 'error1',
        },
      ],
    });
    errorBannerStore.clearErrors();
    expect(errorBannerStore.validationErrors).toEqual([]);
  });

  it('should clear frontend errors', () => {
    errorBannerStore.addFrontendErrors();
    errorBannerStore.clearErrors();
    expect(errorBannerStore.hasFrontendErrors).toBeFalsy();
  });

  it('should group errors by description', () => {
    errorBannerStore.updateErrors([
      { description: 'error1' },
      { description: 'error1' },
      { description: 'error2' },
    ]);
    expect(errorBannerStore.deduplicatedErrorDescriptions).toStrictEqual(['error1', 'error2']);
  });

  it('should return true if there is a commercial invoice error', () => {
    errorBannerStore.updateErrors([{ field: 'orderDocuments' }]);
    expect(errorBannerStore.hasCommercialInvoiceError).toBeTruthy();
  });

  it('should return false if there is no commercial invoice error', () => {
    errorBannerStore.updateErrors([{ field: 'notOrderDocuments' }]);
    expect(errorBannerStore.hasCommercialInvoiceError).toBeFalsy();
  });
});
