import ButtonWithMenu from '@/components/base/ButtonWithMenu.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { beforeAll } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';

const props = {
  label: 'label',
  header: 'header',
  value: 'Cocoa and cocoa preparations, chocolate products, additional product groups',
};

describe('ButtonWithMenu component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(ButtonWithMenu, {
      props,
    });
  });

  it('matches desktop markup', () => {
    const buttonWithMenu = wrapper.findComponent(ButtonWithMenu);
    expect(buttonWithMenu.exists()).toBe(true);

    expect(buttonWithMenu.element).toMatchSnapshot();
  });

  it('check click on chip', async () => {
    wrapper.findAllComponents({ name: 'v-chip' }).at(0)?.trigger('click');
    await wrapper.vm.$nextTick();

    const menu = wrapper.findComponent({ name: 'v-menu' });
    expect(menu.exists()).toBe(true);
    expect(menu.findAllComponents({ name: 'v-card' }).length).toBe(1);

    expect(menu.element).toMatchSnapshot();
  });
});
