import { ref } from 'vue';

export const useVirtualKeyboard = () => {
  const keyboardVisible = ref(false);

  /**
   * As the Virtual Keyboard API isn't fully supported yet, I decided to use a classical approach for checking the state of the virtual keyboard.
   * This approach is based on the fact that the virtual keyboard is shown when the height of the visual viewport is less than 75% of the height of the layout viewport.
   *
   * @see https://stackoverflow.com/a/74618784/6695532
   * @see https://developer.mozilla.org/en-US/docs/Web/API/Visual_Viewport_API
   * @see https://developer.mozilla.org/en-US/docs/Web/API/Visual_Viewport_API/Using_the_visual_viewport
   * @see https://developer.mozilla.org/en-US/docs/Web/API/VirtualKeyboard_API
   */
  const VIEWPORT_VS_CLIENT_HEIGHT_RATIO = 0.75;

  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', function ({ target }) {
      if (!target) {
        keyboardVisible.value = false;
        return;
      }
      const { height, scale } = target as unknown as EventTarget & {
        height: number;
        scale: number;
      };
      keyboardVisible.value =
        (height * scale) / window.innerHeight < VIEWPORT_VS_CLIENT_HEIGHT_RATIO;
    });
  }

  return { keyboardVisible };
};
