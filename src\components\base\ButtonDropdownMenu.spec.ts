import ButtonDropdownMenu from '@/components/base/ButtonDropdownMenu.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { mockResizeObserver } from 'jsdom-testing-mocks';

describe('Order references - ButtonDropdownMenu', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();

    wrapper = mount(ButtonDropdownMenu, {
      attachTo: document.body,
      props: {
        items: [],
      },
      slots: {
        default: 'Button <span>Label</span>',
      },
    });
  });

  it('should show button label', () => {
    expect(wrapper.find('.activator-button').html()).toContain('Button <span>Label</span>');
  });

  it('should show dropdown menu items', async () => {
    const items = [
      {
        text: 'label1',
        value: 'value1',
      },
      {
        text: 'label2',
        value: 'value2',
      },
    ];

    await wrapper.setProps({
      items,
    });

    await wrapper.find('.activator-button').trigger('click');

    const menuItems = wrapper.findAllComponents({ name: 'v-list-item' });

    expect(menuItems).toHaveLength(2);
    expect(menuItems.at(0)?.text()).toBe('label1');
    expect(menuItems.at(1)?.text()).toBe('label2');
  });

  it('should emit selected item', async () => {
    const menuItems = wrapper.findAllComponents({ name: 'v-list-item' });

    await menuItems.at(0)?.trigger('click');

    expect(wrapper.emitted('input')).toHaveLength(1);
    expect(wrapper.emitted('input')?.[0]).toEqual(['value1']);
  });
});
