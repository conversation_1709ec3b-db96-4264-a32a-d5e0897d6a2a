import { mockServer } from '@/mocks/server';
import * as uuid from '@/utils/createUuid';
import type { TestUtils } from '@test/test-utils';
import { mount } from '@vue/test-utils';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { Server } from 'miragejs';
import { afterEach, expect } from 'vitest';
import { getEmptyUnNumberDangerousGood } from '@/store/createOrder/orderLine';
import UnNumber from './UnNumberDangerousGood.vue';

const uuidSpy = vi.spyOn(uuid, 'createUuid');

const props = {
  computedLineCount: '1.1',
};

describe('UnNumber component', () => {
  let wrapper: TestUtils.VueWrapper<typeof UnNumber>;
  let server: Server;
  beforeAll(() => {
    mockResizeObserver();
    server = mockServer({
      environment: 'test',
      fixtures: {},
    });
  });
  afterAll(() => {
    server.shutdown();
  });
  beforeEach(() => {
    wrapper = mount(UnNumber, {
      props,
    });
  });

  afterEach(() => {
    uuidSpy.mockClear();
    wrapper.unmount();
  });

  it('renders the title correctly', () => {
    wrapper.setProps({
      modelValue: getEmptyUnNumberDangerousGood(1),
    });
    expect(wrapper.find('.order-line--title').text()).toBe(
      'labels.dangerous_good_position.text ( labels.order_line_header.text 1.1 )',
    );
  });

  it('send invalid form', async () => {
    wrapper.setProps({
      modelValue: getEmptyUnNumberDangerousGood(1),
    });
    const result = await wrapper.vm.triggerValidation();
    expect(result).toBe(false);
  });
});
