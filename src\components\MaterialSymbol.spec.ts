import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { beforeEach } from 'vitest';

describe('MaterialSymbol component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(MaterialSymbol);
  });

  it('sets font-size depending on size prop', async () => {
    await wrapper.setProps({ size: 24 });
    expect(wrapper.attributes('style')).toContain('font-size: 24px');
  });

  it('adds left and right classes', async () => {
    await wrapper.setProps({ left: true, right: true });
    expect(wrapper.classes()).toContain('left');
    expect(wrapper.classes()).toContain('right');
  });

  it('adds color classes', async () => {
    await wrapper.setProps({ color: 'primary' });
    expect(wrapper.classes()).toContain('text-primary');

    await wrapper.setProps({ color: 'grey darken-2' });
    expect(wrapper.classes()).toContain('text-grey');
    expect(wrapper.classes()).toContain('text--darken-2');
  });
});
