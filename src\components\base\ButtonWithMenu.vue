<template>
  <div>
    <VMenu
      v-model="menu"
      :close-on-content-click="false"
      :min-width="280"
      :offset="8"
      location="bottom center"
      :attach="$appRoot"
    >
      <template #activator="{ props: menuProps }">
        <VChip
          size="small"
          class="custom-chip"
          variant="outlined"
          :class="[
            'default-activator d-inline-block rounded-xl pa-0 my-0 text-label-3',
            { active: menu },
          ]"
          v-bind="menuProps"
          @click.stop
        >
          <VTooltip location="top" offset="-4px" max-width="320px" :disabled="!isTruncated">
            <template #activator="{ props: tooltipProps }">
              <div v-bind="tooltipProps" class="d-flex pl-1 py-1 pr-6">
                <slot name="chipIcon" />
                {{ truncatedText }}
              </div>
            </template>
            <span class="text-body-2">{{ label }}</span>
          </VTooltip>
        </VChip>
      </template>
      <VCard class="d-flex flex-column menu-card flex-grow-0 rounded-lg" :min-width="280">
        <VCardTitle class="pb-1 pt-3">
          <VRow no-gutters class="align-center">
            <VCol
              ><h5 v-if="header" :class="`text-h5 word-break-all`">
                {{ header }}
              </h5>
            </VCol>
            <VCol cols="auto">
              <DfeIconButton :tooltip="t('labels.close_label.text')" @click="menu = false">
                <MaterialSymbol size="20">
                  <CloseIcon />
                </MaterialSymbol>
              </DfeIconButton>
            </VCol>
          </VRow>
        </VCardTitle>
        <div ref="menuCardContent" class="pa-4 pt-0">
          <slot> {{ value }} </slot>
        </div>
      </VCard>
    </VMenu>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, ref, toRefs } from 'vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import CloseIcon from '@dfe/dfe-frontend-styles/assets/icons/close-24px.svg';
import { useTruncateText } from '@/composables/useTruncateText';
import { useI18n, type TranslateResult } from 'vue-i18n';
import { DfeIconButton } from '@dfe/dfe-frontend-shared-components';

const { t } = useI18n();
const props = defineProps({
  label: {
    type: String as PropType<TranslateResult>,
    default: '',
  },
  header: {
    type: String as PropType<TranslateResult>,
    default: '',
  },
  value: {
    type: String,
    default: '',
  },
});

const menu = ref(false);
const truncatedLimit = 40;
const { label } = toRefs(props);

const labelString = computed(() => label.value.toString());

const { truncatedText, isTruncated } = useTruncateText(labelString, truncatedLimit);
</script>

<style lang="scss" scoped>
span.v-chip.v-chip--outlined.v-chip.v-chip.default-activator {
  .v-ripple__container {
    display: none !important;
    &::before,
    &::after {
      content: none;
    }
  }
}

.word-break-all {
  word-break: break-word;
}

.custom-chip:hover,
.custom-chip:hover * {
  color: var(--color-base-blue-400);
}

.custom-chip.active,
.custom-chip.active * {
  border-color: var(--color-base-blue-400);
  color: var(--color-base-blue-500);
}

.custom-chip:focus {
  border-color: var(--color-base-blue-500);
}

span.v-chip__overlay {
  background-color: none !important;
  opacity: 1 !important;
}
</style>
