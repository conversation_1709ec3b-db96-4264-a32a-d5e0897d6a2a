import type { DocumentTypes } from '@dfe/dfe-book-api';

export const supportedExtensions = [
  {
    label: 'pdf',
    description: 'application/pdf',
  },
  {
    label: 'xls',
    description: 'application/vnd.ms-excel',
  },
  {
    label: 'xlsx',
    description: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  },
];

export const supportedExtensionsPdfOnly = [
  {
    label: 'pdf',
    description: 'application/pdf',
  },
];

export const documentTypes: DocumentTypes = [
  {
    typeId: 1,
    description: 'Customs Invoice',
    category: 'document',
    supportedExtensions,
  },
  {
    typeId: 2,
    description: 'Customs Proforma Invoice',
    category: 'document2',
    supportedExtensions,
  },
  {
    typeId: 3,
    description: 'Customs Export Declaration',
    category: 'document3',
    supportedExtensions,
  },
  {
    typeId: 4,
    description: 'Customs Delivery Note',
    category: 'document2',
    supportedExtensions,
  },
  {
    typeId: 5,
    description: 'Customs Licence',
    category: 'document3',
    supportedExtensions,
  },
  {
    typeId: 6,
    description: 'Customs Packing List',
    category: 'document2',
    supportedExtensions,
  },
  {
    typeId: 7,
    description: 'Customs Original Certificate',
    category: 'document',
    supportedExtensions,
  },
  {
    typeId: 8,
    description: 'Customs Certificate Other',
    category: 'document3',
    supportedExtensions,
  },
  {
    typeId: 9,
    description: 'Customs Other Document Type',
    category: 'document4',
    supportedExtensions: supportedExtensionsPdfOnly,
  },
  {
    typeId: 10,
    description: 'Customs Preference Document',
    category: 'document4',
    supportedExtensions: supportedExtensionsPdfOnly,
  },
  {
    typeId: 11,
    description: 'Customs Excise Document',
    category: 'document4',
    supportedExtensions: supportedExtensionsPdfOnly,
  },
  {
    typeId: 12,
    description: 'Customs Transit Document',
    category: 'document1',
    supportedExtensions,
  },
  {
    typeId: 13,
    description: 'Customs T1 Document',
    category: 'document1',
    supportedExtensions,
  },
  {
    typeId: 14,
    description: 'Customs T2 Document',
    category: 'document1',
    supportedExtensions,
  },
  {
    typeId: 15,
    description: 'Customs Export List',
    category: 'document',
    supportedExtensions,
  },
  {
    typeId: 16,
    description: 'Dangerous Goods Note',
    category: 'document3',
    supportedExtensions,
  },
  {
    typeId: 17,
    description: 'Dangerous Goods Note Other',
    supportedExtensions,
  },
];
