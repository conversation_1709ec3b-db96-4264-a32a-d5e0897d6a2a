import { useApplyOrderAddressPresets } from '@/composables/createOrder/addresses/useApplyOrderAddressPresets';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { OrderTypes, TextTypes } from '@/enums';
import { createPinia, setActivePinia } from 'pinia';
import { DeliveryOptionTypes } from '@dfe/dfe-address-api';

describe('applyOrderAddressPresets', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('returns false if isRoadOrder is false', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;

    const result = useApplyOrderAddressPresets({}, undefined);

    expect(result).toBe(false);
  });

  it('applies tailLiftDelivery preset', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();
    createOrderFormCollectionAndDeliveryStore.tailLiftDelivery = false;

    const result = useApplyOrderAddressPresets({ tailLiftDelivery: true }, undefined);

    expect(result).toBe(true);
    expect(createOrderFormCollectionAndDeliveryStore.tailLiftDelivery).toBe(true);
  });

  it('applies deliveryOption preset', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();
    createOrderFormCollectionAndDeliveryStore.deliveryOption = '';

    const result = useApplyOrderAddressPresets(
      { deliveryOption: DeliveryOptionTypes.AP },
      undefined,
    );

    expect(result).toBe(true);
    expect(createOrderFormCollectionAndDeliveryStore.deliveryOption).toBe(DeliveryOptionTypes.AP);
  });

  it('applies deliveryOption preset with contact', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const defaultContact = {
      id: 3105,
      name: 'Moritz 2',
      email: '<EMAIL>',
      telephone: '3333',
      mobile: '4444',
      isMainContact: true,
    };

    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();
    createOrderFormCollectionAndDeliveryStore.deliveryOption = '';

    const result = useApplyOrderAddressPresets(
      { deliveryOption: DeliveryOptionTypes.AP },
      defaultContact,
    );

    expect(result).toBe(true);
    expect(createOrderFormCollectionAndDeliveryStore.deliveryOption).toBe(DeliveryOptionTypes.AP);
    expect(createOrderFormCollectionAndDeliveryStore.contactDataDelivery).toStrictEqual(
      defaultContact,
    );
  });

  it('applies deliveryInstructions preset', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const createOrderTextsStore = useCreateOrderTextsStore();
    createOrderTextsStore.texts = [
      { textType: TextTypes.DeliveryInstructions, active: false, value: '' },
    ];

    const result = useApplyOrderAddressPresets(
      { deliveryInstructions: 'Leave at door' },
      undefined,
    );

    expect(result).toBe(true);
    expect(createOrderTextsStore.texts[0].active).toBe(true);
    expect(createOrderTextsStore.texts[0].value).toBe('Leave at door');
  });

  it('applies goodsDescription preset', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const createOrderTextsStore = useCreateOrderTextsStore();
    createOrderTextsStore.texts = [
      { textType: TextTypes.GoodsDescription, active: false, value: '' },
    ];

    const result = useApplyOrderAddressPresets({ goodsDescription: 'Fragile items' }, undefined);

    expect(result).toBe(true);
    expect(createOrderTextsStore.texts[0].active).toBe(true);
    expect(createOrderTextsStore.texts[0].value).toBe('Fragile items');
  });

  it('applies invoiceText preset', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const createOrderTextsStore = useCreateOrderTextsStore();
    createOrderTextsStore.texts = [{ textType: TextTypes.InvoiceText, active: false, value: '' }];

    const result = useApplyOrderAddressPresets(
      { invoiceText: 'Payment due in 30 days' },
      undefined,
    );

    expect(result).toBe(true);
    expect(createOrderTextsStore.texts[0].active).toBe(true);
    expect(createOrderTextsStore.texts[0].value).toBe('Payment due in 30 days');
  });

  it('returns true if at least one preset is applied', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();
    createOrderFormCollectionAndDeliveryStore.tailLiftDelivery = false;

    const result = useApplyOrderAddressPresets({ tailLiftDelivery: true }, undefined);

    expect(result).toBe(true);
  });

  it('returns false if no presets are applied', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    const result = useApplyOrderAddressPresets({}, undefined);

    expect(result).toBe(false);
  });
});
