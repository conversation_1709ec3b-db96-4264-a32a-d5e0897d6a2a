<template>
  <SelectField
    v-model="selectedPackingPositionId"
    :items="packingPositionOptions"
    item-value="localId"
    item-text="name"
    :label="t('labels.packing_position_header.text')"
  />
</template>

<script setup lang="ts">
import SelectField from '@/components/form/SelectField.vue';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';

const selectedPackingPositionId = defineModel<number | null>({ default: null });

const { t } = useI18n();

const { packingPositions } = storeToRefs(useCreateOrderOrderLineFormStore());

const packingPositionOptions = computed(() => {
  return [
    {
      localId: null,
      quantity: 0,
      name: t('labels.dfe_none.text'),
    },
    ...packingPositions.value.map((packingPosition, index) => {
      return { name: index + 1, ...packingPosition };
    }),
  ];
});
</script>
