<template>
  <div>
    <h5 v-if="header" class="text-h5 mb-2">
      {{ header }}<span v-if="isRequired" class="label-indicator">*</span>
    </h5>
    <AutocompleteAddressField
      v-model="address"
      item-value="uuid"
      item-text="name"
      :items="getItems"
      :show-customer-addresses-title="showCustomerAddressesTitle"
      :loading="isLoading"
      :show-loader-after-ms="1000"
      :return-object="true"
      :static-menu="!isCustomer"
      :placeholder="t('labels.search_address_placeholder.text')"
      :rules="rules"
      :message="t('labels.validation_select_input_required.text')"
      @search-input="onSearchAddressInput"
    />
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed } from 'vue';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { storeToRefs } from 'pinia';
import { useSearchAddress } from '@/composables/createOrder/addresses/useSearchAddress';
import AutocompleteAddressField from '@/components/form/AutocompleteAddressField.vue';
import { debounce } from 'lodash';
import { Timeouts } from '@/enums';
import type { Address } from '@dfe/dfe-address-api';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { createUuid } from '@/utils/createUuid';

const { t } = useI18n();

const props = defineProps({
  header: {
    type: [String, Object] as PropType<TranslateResult>,
    required: false,
    default: '',
  },
  isCustomer: { type: Boolean as PropType<boolean>, required: false, default: false },
  rules: {
    type: Array as PropType<ValidationRule[]>,
    required: false,
    default: () => [],
  },
  isRequired: { type: Boolean as PropType<boolean>, required: false, default: false },
});

const address = defineModel<Partial<Address>>({ required: true });

const createOrderFormStore = useCreateOrderFormStore();
const { selectedCustomer } = storeToRefs(createOrderFormStore);
const createAddressStore = useCreateAddressDataStore();
const { addresses } = storeToRefs(createAddressStore);
const { customers } = storeToRefs(useCreateOrderDataStore());

const isLoading = computed(() => addresses.value.loading);

const onSearchAddressInput = debounce((value: string) => {
  useSearchAddress(value);
}, Timeouts.SearchInput);

const customerAddresses = computed(() => customers?.value?.map((customer) => customer.address));

const showCustomerAddressesTitle = computed(
  () =>
    addresses.value.search.length === 0 &&
    customerAddresses.value &&
    customerAddresses.value.length > 0,
);

const getItems = computed<Address[]>(() => {
  return (
    props.isCustomer && !addresses.value.search.length
      ? (customerAddresses.value?.filter((address) => address === selectedCustomer.value.address) ??
        [])
      : addresses.value.search
  ).map((address) => ({ ...address, uuid: createUuid() })) as Address[];
});
</script>
