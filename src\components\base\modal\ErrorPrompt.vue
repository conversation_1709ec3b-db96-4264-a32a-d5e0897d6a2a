<template>
  <ModalWrapper v-model="isOpen" :headline="headline" :size="size" @close="close">
    <template #body>
      <v-banner class="notification--error">
        <div class="d-flex">
          <MaterialSymbol class="mr-2" size="24"> <ErrorIcon /></MaterialSymbol>
          <div class="mr-auto">
            <h5 class="text-h5 mb-2">{{ title }}</h5>
            <div class="text-body-2">{{ text }}</div>
          </div>
        </div>
      </v-banner>
    </template>
    <template #footer>
      <VBtn v-data-test="'confirm-btn'" size="small" color="primary" @click.prevent="close">
        {{ confirmText }}
      </VBtn>
      <VBtn
        v-data-test="'cancel-btn'"
        size="small"
        color="primary"
        variant="text"
        @click="closeOrderForm"
      >
        {{ cancelText }}
      </VBtn>
    </template>
  </ModalWrapper>
</template>

<script setup lang="ts">
import type { Size } from '@/components/base/modal/ModalWrapper.types';
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import ErrorIcon from '@dfe/dfe-frontend-styles/assets/icons/error-24px.svg';
import type { TranslateResult } from 'vue-i18n';
type ErrorPromptProps = {
  headline: TranslateResult;
  confirmText?: TranslateResult;
  cancelText?: TranslateResult;
  size?: Size;
  title?: string;
  text?: string;
};

withDefaults(defineProps<ErrorPromptProps>(), {
  confirmText: '',
  cancelText: '',
  size: 'sm',
  title: '',
  text: '',
});
const isOpen = defineModel<boolean>();
const emit = defineEmits(['close', 'closeOrderForm']);

const close = () => {
  emit('close');
};
const closeOrderForm = () => {
  emit('closeOrderForm');
};
</script>
<style lang="scss" scoped>
@use '@/styles/base' as base;

.v-banner {
  background-color: var(--background-color);
  color: var(--color-base-grey-900);
  padding: base.space(4);
  border: 0;
  border-radius: base.space(2);

  &.notification--error {
    --background-color: var(--color-base-red-100);
    --icon-color: var(--color-base-red-500);
    --border-color: var(--color-base-red-300);
  }
  .material-symbol {
    color: var(--color-base-red-500);
  }
  :deep(.v-banner__content) {
    display: block;
  }
}
</style>
