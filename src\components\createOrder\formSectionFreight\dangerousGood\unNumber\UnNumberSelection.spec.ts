import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { unNumberSearchResults } from '@/mocks/fixtures/unNumberSearchResults';
import type { TestUtils } from '@test/test-utils';
import UnNumberSelection from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberSelection.vue';
import { DfeInputTextField } from '@dfe/dfe-frontend-shared-components';
import { DangerousGoodDataItem } from '@dfe/dfe-book-api';

function mountComponent(
  wrapper: TestUtils.VueWrapper<typeof UnNumberSelection>,
  propsData: DangerousGoodDataItem,
) {
  return mount(UnNumberSelection, {
    props: {
      unNumberData: propsData,
    },
  });
}

describe('UnNumberSelection.vue', () => {
  let wrapper: TestUtils.VueWrapper<typeof UnNumberSelection>;

  beforeEach(() => {
    wrapper = mountComponent(wrapper, unNumberSearchResults[0]);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('renders content', () => {
    const nosInput = wrapper.findComponent(DfeInputTextField);

    expect(wrapper.text()).toContain('1203');
    expect(wrapper.text()).toContain('BENZIN\nKraftstoff\nleicht entzündlich');
    expect(nosInput.exists()).toBe(true);
  });

  it('emits update of NOS field', async () => {
    const nosInput = wrapper.findComponent(DfeInputTextField);
    nosInput.setValue('test123');

    expect(wrapper.emitted('update:nos')?.[0]).toEqual(['test123']);
  });

  it('renders content without NOS field', () => {
    wrapper = mountComponent(wrapper, unNumberSearchResults[1]);
    const nosInput = wrapper.findComponent(DfeInputTextField);

    expect(wrapper.text()).toContain('1993');
    expect(nosInput.exists()).toBe(false);
  });
});
