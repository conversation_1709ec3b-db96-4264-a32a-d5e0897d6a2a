import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import AddressesTransport from '@/components/createOrder/formSectionAddresses/AddressesTransport.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import SelectAddress from '@/components/form/SelectAddress.vue';
import SwitchField from '@/components/form/SwitchField.vue';
import { useCheckFurtherAddresses } from '@/composables/createOrder/addresses/useCheckAdditionalAddresses';
import { ConsigneeAddressType, DeliveryOptions, OrderTypes } from '@/enums';
import { customers, customersWithAddresses } from '@/mocks/fixtures/customers';
import { loadingPoints } from '@/mocks/fixtures/loadingPoints';
import { airExportOrder, roadCollectionOrder } from '@/mocks/fixtures/order';
import { ports } from '@/mocks/fixtures/ports';
import { validateAddressData } from '@/mocks/fixtures/validateAddressData';
import { mockServer } from '@/mocks/server';
import { i18n } from '@/plugins/i18n';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { ClientKey } from '@/types/client';
import { HandOverSelection } from '@/types/hand-over';
import type {
  AirExportQuoteInformation,
  OrderAddress,
  OrderContactData,
  Port,
  RoadCollectionQuoteInformation,
} from '@dfe/dfe-book-api';
import { CollectionOption, Segment } from '@dfe/dfe-book-api';
import { initPinia } from '@test/util/init-pinia';
import { createClientMock } from '@test/util/mock-client';
import type { VueWrapper } from '@vue/test-utils';
import { mount, shallowMount } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import { afterEach, beforeEach, expect } from 'vitest';
import { nextTick } from 'vue';
import { VBtn } from 'vuetify/components';
import AddButton from '../AddButton.vue';

const mockAddress = {
  id: 123,
  name: 'name',
  street: 'street',
  postcode: '12345',
  city: 'city',
  countryCode: 'DE',
};

const mockContact: OrderContactData = {
  name: 'name',
  email: 'email',
  telephone: '12345',
  mobile: '12345',
  fax: '12345',
};

const noneAddress = {
  supplement: 'labels.dfe_none.text',
  name: 'labels.dfe_none.text',
};

describe('Addresses ForwardingOrder AddressesTransport component', () => {
  let wrapper: VueWrapper;
  const client = createClientMock();
  const formStore = useCreateOrderFormStore();
  const addressStore = useCreateOrderAddressesStore();

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customers,
        airExportOrder,
        ports,
        loadingPoints,
        validateAddressData,
      },
    });
  });

  beforeEach(() => {
    initPinia();
    wrapper = mount(AddressesTransport, {
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
    formStore.$reset();
    addressStore.$reset();
    vi.clearAllMocks();
  });

  describe('watcher: selectedCustomer', () => {
    afterEach(() => {
      formStore.$reset();
      addressStore.$reset();
    });

    it('sets customer address to shipper for road (forwarding) orders', async () => {
      expect(addressStore.setCustomerAddressToPrincipal).toHaveBeenCalledTimes(1);

      formStore.customerNumber = '456';
      await wrapper.vm.$nextTick();
      expect(addressStore.setCustomerAddressToPrincipal).toHaveBeenCalledTimes(2);
    });

    it("doesn't set customer address to shipper for air export orders - if customer is shipper default", async () => {
      formStore.orderType = OrderTypes.AirExportOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.AirExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as AirExportQuoteInformation;
      formStore.customerNumber = '789';
      await wrapper.vm.$nextTick();
      expect(addressStore.setCustomerAddressToPrincipal).toHaveBeenCalledTimes(1);
    });

    it('sets customer address to shipper for air export orders - if customer is not shipper default', async () => {
      formStore.customerNumber = '890';
      addressStore.shipperHandOverSelection.selection = HandOverSelection.alternateAddress;
      await wrapper.vm.$nextTick();
      expect(addressStore.setCustomerAddressToPrincipal).toHaveBeenCalledTimes(2);

      formStore.customerNumber = '901';
      addressStore.shipperHandOverSelection.selection = HandOverSelection.port;
      await wrapper.vm.$nextTick();
      expect(addressStore.setCustomerAddressToPrincipal).toHaveBeenCalledTimes(3);
    });

    it("doesn't set customer address to shipper for air export order - if customer shipper address was updated (and shipper ist not default)", async () => {
      await setupAddresses(Segment.AIR);

      wrapper.findAllComponents(AddressCard).at(0)?.vm.$emit('update-address', mockAddress);
      formStore.customerNumber = '456';
      addressStore.shipperHandOverSelection.selection = HandOverSelection.alternateAddress;
      await wrapper.vm.$nextTick();
      expect(addressStore.setCustomerAddressToPrincipal).toHaveBeenCalledTimes(5);
    });

    it('set contactDataConsignee when quote2book order and Roadcollection Order', async () => {
      formStore.orderType = OrderTypes.RoadCollectionOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.RoadCollectionOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as RoadCollectionQuoteInformation;
      await wrapper.vm.$nextTick();
      formStore.customerNumber = '890';
      const { contactDataConsignee } = storeToRefs(addressStore);
      await wrapper.vm.$nextTick();
      expect(contactDataConsignee.value).toMatchObject({
        email: 'email',
        name: 'firstName lastName',
        mobile: undefined,
        telephone: undefined,
      });
    });

    it('set contactDataShipper when quote2book order and address is imported from address book', async () => {
      addressStore.shipperAddress.address.originAddressId = 1;

      formStore.orderType = OrderTypes.RoadCollectionOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.RoadCollectionOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as RoadCollectionQuoteInformation;
      await wrapper.vm.$nextTick();
      formStore.customerNumber = '890';
      const { contactDataShipper } = storeToRefs(addressStore);
      await wrapper.vm.$nextTick();
      expect(contactDataShipper.value).toMatchObject({
        email: 'email',
        name: 'firstName lastName',
        mobile: undefined,
        telephone: undefined,
      });
    });
  });

  it('should show drop of location when clicking add button', async () => {
    await setupAddresses(Segment.AIR);
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    await wrapper
      .find('[data-test="book-drop-of-location-btn"]')
      .findComponent({ name: 'v-btn' })
      .trigger('click');

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'dropOfLocationRef' }).exists()).toBe(true);
    });
    await wrapper
      .find('[data-test="book-drop-of-location-remove-btn"]')
      .findComponent('button')
      .trigger('click');
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test="book-drop-of-location-btn"]').exists()).toBe(true);
  });

  it('should show add button when creating order', async () => {
    await setupAddresses(Segment.ROAD);
    await wrapper
      .find('[data-test="book-drop-of-location-btn"]')
      .findComponent({ name: 'v-btn' })
      .trigger('click');

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'dropOfLocationRef' }).exists()).toBe(true);
    });

    client.events.emit('createOrder', { transportType: Segment.ROAD });

    await vi.waitFor(() => {
      expect(wrapper.findComponent(AddButton).exists()).toBe(true);
    });
  });

  it('should show add button when creating ROAD order', async () => {
    await setupAddresses(Segment.ROAD);
    formStore.orderType = OrderTypes.RoadForwardingOrder;
    await wrapper
      .find('[data-test="book-drop-of-location-btn"]')
      .findComponent({ name: 'v-btn' })
      .trigger('click');

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'dropOfLocationRef' }).exists()).toBe(true);
    });
  });

  it('should not show add button when creating AIR order', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.SEA);

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.customerNumber = dataStore.customers?.[0]?.customerNumber ?? '';
    expect(wrapper.findComponent(AddButton).exists()).toBe(false);
  });

  it('should not show add button when creating SEA order', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.SEA);

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.SeaExportOrder;
    formStore.customerNumber = dataStore.customers?.[0]?.customerNumber ?? '';
    expect(wrapper.findComponent(AddButton).exists()).toBe(false);
  });

  it('should not show add button when creating ROAD order and customer is food logistics', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;
    formStore.customerNumber = dataStore.customers?.[2]?.customerNumber ?? '';

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test="book-drop-of-location-btn"]').exists()).toBe(false);
  });

  it('should show add button for collection option - collecting order ', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);
    const { shipperAddress } = storeToRefs(useCreateOrderAddressesStore());
    shipperAddress.value.address = <OrderAddress>airExportOrder.shipperAddress;

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    formStore.customerNumber = dataStore.customers?.[1]?.customerNumber ?? '';

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(AddButton).exists()).toBe(true);
    expect(wrapper.findComponent(AddButton).props('label')).toBe('labels.collection_option.text');
  });

  it('should delete collection option - collecting order ', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);
    const { shipperAddress, collectionOption } = storeToRefs(useCreateOrderAddressesStore());
    shipperAddress.value.address = <OrderAddress>airExportOrder.shipperAddress;
    collectionOption.value = CollectionOption.BOOKING;

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    formStore.customerNumber = dataStore.customers?.[1]?.customerNumber ?? '';

    await wrapper.vm.$nextTick();

    // Delete collection option
    await wrapper.findComponent(VBtn).trigger('click');

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(AddButton).exists()).toBe(true);
    collectionOption.value = undefined;
  });

  it('should show add button when creating ROAD order and customer is not food logistics', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;
    formStore.customerNumber = dataStore.customers?.[0]?.customerNumber ?? '';

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test="book-drop-of-location-btn"]').exists()).toBe(true);
  });

  it('should remove drop of location when clicking delete button', async () => {
    await setupAddresses(Segment.AIR);
    await wrapper
      .find('[data-test="book-drop-of-location-btn"]')
      .findComponent({ name: 'v-btn' })
      .trigger('click');

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'dropOfLocationRef' }).exists()).toBe(true);
    });

    await wrapper.findComponent(VBtn).trigger('click');
  });

  it('should reset fromPortRouting on delete shipper address', async () => {
    const { fromPortRouting } = storeToRefs(useCreateOrderDataStore());
    const { shipperHandOverSelection, fromIATA } = storeToRefs(useCreateOrderAddressesStore());

    await setupAddresses(Segment.AIR);
    formStore.orderType = OrderTypes.AirExportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.default;

    fromIATA.value = <Port>airExportOrder.fromIATA;
    fromPortRouting.value = [<Port>airExportOrder.fromIATA];

    await wrapper.vm.$nextTick();

    const shipperAddressCard = wrapper.findAllComponents(AddressCard).at(0);

    shipperAddressCard?.vm.$emit('delete-address');

    await wrapper.vm.$nextTick();

    expect(fromPortRouting.value).toEqual([]);
    expect(fromIATA.value).toBeUndefined();
  });

  it('should reset toPortRouting on delete consignee address', async () => {
    const { toPortRouting } = storeToRefs(useCreateOrderDataStore());
    const { consigneeHandOverSelection, toIATA } = storeToRefs(useCreateOrderAddressesStore());

    await setupAddresses(Segment.AIR);

    formStore.orderType = OrderTypes.AirExportOrder;

    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    toIATA.value = <Port>airExportOrder.toIATA;
    toPortRouting.value = [<Port>airExportOrder.toIATA];

    await wrapper.vm.$nextTick();

    const consigneeAddressCard = wrapper.findAllComponents(AddressCard).at(1);

    consigneeAddressCard?.vm.$emit('delete-address');

    await wrapper.vm.$nextTick();

    expect(toPortRouting.value).toEqual([]);
    expect(toIATA.value).toBeUndefined();
  });

  it('sets props isEditable and isDeletable for AddressCard component when is AirExportOrder', async () => {
    wrapper = shallowMount(AddressesTransport);

    await setupAddresses(Segment.AIR);

    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const addressCards = wrapper.findAllComponents(AddressCard);
    let shipper = addressCards.at(0);
    let consignee = addressCards.at(1);

    expect(shipper?.props('isEditable')).toBe(true);
    expect(shipper?.props('isDeletable')).toBe(true);
    expect(consignee?.props('isEditable')).toBe(true);
    expect(consignee?.props('isDeletable')).toBe(true);

    shipper?.vm.$emit('update-address', mockAddress);
    await wrapper.vm.$nextTick();

    await vi.waitFor(async () => {
      expect(wrapper.findAllComponents(AddressCard).at(0)?.props('isEditable')).toBe(true);
    });

    formStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    addressStore.shipperHandOverSelection.selection = HandOverSelection.default;
    addressStore.consigneeHandOverSelection.selection = HandOverSelection.default;
    await wrapper.vm.$nextTick();

    shipper = wrapper.findAllComponents(AddressCard).at(0);
    consignee = wrapper.findAllComponents(AddressCard).at(1);

    expect(shipper?.props('isEditable')).toBe(true);
    await vi.waitFor(() => {
      expect(shipper?.props('isDeletable')).toBe(false);
    });
    expect(consignee?.props('isEditable')).toBe(true);
    expect(consignee?.props('isDeletable')).toBe(false);
  });

  it('should be able to edit the consignee address contact data for RoadCollectionOrders', async () => {
    formStore.orderType = OrderTypes.RoadCollectionOrder;

    await setupAddresses(Segment.ROAD);

    await wrapper.vm.$nextTick();

    const consignee = wrapper.findAllComponents(AddressCard).at(1);

    expect(consignee?.props('isEditable')).toBe(true);
  });

  it('should update shipper address', async () => {
    const { shipperAddress } = storeToRefs(useCreateOrderAddressesStore());

    await setupAddresses(Segment.AIR);

    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const shipperAddressCard = wrapper.findAllComponents(AddressCard).at(0);

    shipperAddressCard?.vm.$emit('update:model-value', mockAddress);

    expect(shipperAddress.value.address).toEqual(mockAddress);

    shipperAddressCard?.vm.$emit('update:model-value', <OrderAddress>airExportOrder.shipperAddress);

    expect(shipperAddress.value.address).toEqual(<OrderAddress>airExportOrder.shipperAddress);
  });

  it('should update shipper address contact', async () => {
    const { contactDataShipper } = storeToRefs(useCreateOrderAddressesStore());

    await setupAddresses(Segment.AIR);

    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const shipperAddressCard = wrapper.findAllComponents(AddressCard).at(0);

    shipperAddressCard?.vm.$emit('update-contact', mockContact);

    expect(contactDataShipper.value).toEqual(mockContact);
  });

  it('should update consignee address', async () => {
    const { consigneeAddress } = storeToRefs(useCreateOrderAddressesStore());

    await setupAddresses(Segment.AIR);

    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const consigneeAddressCard = wrapper.findAllComponents(AddressCard).at(1);

    consigneeAddressCard?.vm.$emit('update:model-value', mockAddress);

    expect(consigneeAddress.value.address).toEqual(mockAddress);

    consigneeAddressCard?.vm.$emit(
      'update:model-value',
      <OrderAddress>airExportOrder.consigneeAddress,
    );

    expect(consigneeAddress.value.address).toEqual(<OrderAddress>airExportOrder.consigneeAddress);
  });

  it('should update consignee address contact', async () => {
    const { contactDataConsignee } = storeToRefs(useCreateOrderAddressesStore());

    await setupAddresses(Segment.AIR);

    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const consigneeAddressCard = wrapper.findAllComponents(AddressCard).at(1);

    consigneeAddressCard?.vm.$emit('update-contact', mockContact);

    expect(contactDataConsignee.value).toEqual(mockContact);
  });
  it('should set contactDataShipper to the logged in user contact data, when updated', async () => {
    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    const { contactDataShipper } = storeToRefs(addressStore);

    expect(contactDataShipper.value).toMatchObject({
      email: 'email',
      name: 'firstName lastName',
      mobile: undefined,
      telephone: undefined,
    });
  });

  it('should disable principal switch and have a tooltip when OrderStatus is set', async () => {
    const { orderData } = storeToRefs(useCreateOrderFormStore());

    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.customerNumber = '456';

    await wrapper.vm.$nextTick();

    orderData.value = {
      orderType: OrderTypes.AirExportOrder,
      orderId: 123,
    };

    await wrapper.vm.$nextTick();

    wrapper.findAllComponents(SwitchField).map((switchField) => {
      expect(switchField.props('disabled')).toBe(true);
    });

    wrapper.findAllComponents({ name: 'v-tooltip' }).map((tooltip) => {
      expect(tooltip.exists()).toBe(true);
      expect(tooltip.text()).toBe(i18n.global.t('order.cannot_change_saved_order.text'));
    });
  });

  it('resets differentConsigneeAddress when consigneeAddressType is set to PRINCIPALS_ADDRESS', async () => {
    addressStore.consigneeAddressType = ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;
    addressStore.differentConsigneeAddress.address = { city: 'Test City' };

    await nextTick();
    addressStore.consigneeAddressType = ConsigneeAddressType.PRINCIPALS_ADDRESS;
    await nextTick();

    expect(addressStore.differentConsigneeAddress.address.city).toBe('');
  });

  it('sets consigneeAddressType to PRINCIPALS_ADDRESS by default', () => {
    expect(addressStore.consigneeAddressType).toBe(ConsigneeAddressType.PRINCIPALS_ADDRESS);
  });

  it('should NOT have a neutralize address checkbox field for principal and different consignee', async () => {
    await setupAddresses(Segment.ROAD);

    addressStore.consigneeAddressType = ConsigneeAddressType.PRINCIPALS_ADDRESS;
    formStore.orderType = OrderTypes.RoadCollectionOrder;

    const dataStore = useCreateOrderDataStore();
    dataStore.customers = customersWithAddresses;

    await wrapper.vm.$nextTick();

    addressStore.shipperAddress.address = roadCollectionOrder.shipperAddress as OrderAddress;
    addressStore.differentConsigneeAddress.address =
      roadCollectionOrder.differentConsigneeAddress as OrderAddress;

    await wrapper.vm.$nextTick();

    const checkboxFieldPrincipal = wrapper.findAllComponents(CheckboxField).at(0);
    const checkboxFieldDifferentConsignee = wrapper.findAllComponents(CheckboxField).at(1);

    expect(checkboxFieldPrincipal).toBeUndefined();
    expect(checkboxFieldDifferentConsignee).toBeUndefined();
  });

  it('should have a neutralize address checkbox field for principal and different consignee', async () => {
    await setupAddresses(Segment.ROAD);

    addressStore.consigneeAddressType = ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;
    formStore.orderType = OrderTypes.RoadCollectionOrder;

    const dataStore = useCreateOrderDataStore();
    dataStore.customers = customersWithAddresses;

    await wrapper.vm.$nextTick();

    addressStore.shipperAddress.address = roadCollectionOrder.shipperAddress as OrderAddress;
    addressStore.differentConsigneeAddress.address =
      roadCollectionOrder.differentConsigneeAddress as OrderAddress;

    await wrapper.vm.$nextTick();

    const checkboxFieldPrincipal = wrapper.findAllComponents(CheckboxField).at(0);
    const checkboxFieldDifferentConsignee = wrapper.findAllComponents(CheckboxField).at(1);

    expect(checkboxFieldPrincipal?.exists()).toBe(true);
    expect(checkboxFieldDifferentConsignee?.exists()).toBe(true);
  });

  it('should render correct tooltip labels for principal and different consignee address', async () => {
    await setupAddresses(Segment.ROAD);

    addressStore.consigneeAddressType = ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;
    formStore.orderType = OrderTypes.RoadCollectionOrder;

    const dataStore = useCreateOrderDataStore();
    dataStore.customers = customersWithAddresses;

    await wrapper.vm.$nextTick();

    addressStore.shipperAddress.address = roadCollectionOrder.shipperAddress as OrderAddress;
    addressStore.differentConsigneeAddress.address =
      roadCollectionOrder.differentConsigneeAddress as OrderAddress;

    await wrapper.vm.$nextTick();

    const infoButtonWithTooltipPrincipal = wrapper.findAllComponents(InfoButtonWithTooltip).at(0);
    const infoButtonWithTooltipDifferentConsignee = wrapper
      .findAllComponents(InfoButtonWithTooltip)
      .at(1);

    expect(infoButtonWithTooltipPrincipal?.exists()).toBe(true);
    expect(infoButtonWithTooltipPrincipal?.props('label')).toBe(
      'labels.neutralize_collection_address_info.text',
    );
    expect(infoButtonWithTooltipDifferentConsignee?.exists()).toBe(true);
    expect(infoButtonWithTooltipDifferentConsignee?.props('label')).toBe(
      'labels.neutralize_diff_consignee_info.text',
    );
  });

  it('triggers further Addresses check if orderLoaded changes', async () => {
    vi.mock('@/composables/createOrder/addresses/useCheckAdditionalAddresses', () => ({
      useCheckFurtherAddresses: vi.fn(),
    }));

    await setupAddresses(Segment.ROAD);
    const formStore = useCreateOrderFormStore();
    formStore.isOrderLoaded = true;

    await wrapper.vm.$nextTick();

    expect(useCheckFurtherAddresses).toHaveBeenCalledTimes(2);
  });

  it('Shows correct principal address switch', async () => {
    await setupAddresses(Segment.SEA);
    const dataStore = useCreateOrderDataStore();

    formStore.orderType = OrderTypes.SeaExportOrder;
    formStore.transportType = Segment.SEA;
    formStore.customerNumber = dataStore.customers?.[1]?.customerNumber ?? '';
    dataStore.customers = customersWithAddresses;

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test="book-principal-switch-default"]').exists()).toBe(true);

    formStore.orderData = airExportOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test="book-principal-switch-default"]').exists()).toBe(false);
    expect(wrapper.find('[data-test="book-principal-switch-saved"]').exists()).toBe(true);
  });

  it('triggers update of loading points', async () => {
    await setupAddresses(Segment.ROAD);

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent(SelectAddress).exists()).toBe(true);
    });

    const selectAddress = wrapper.findComponent(SelectAddress);

    selectAddress.vm.$emit('update:modelValue', noneAddress);

    expect(addressStore.loadingPoint).toStrictEqual(noneAddress);
  });

  it('should have set apply presets for consignee address - ROAD FORWARDING order', async () => {
    await setupAddresses(Segment.ROAD);

    formStore.orderType = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    const consigneeAddressCard = wrapper.findAllComponents(AddressCard).at(1);

    await wrapper.vm.$nextTick();

    expect(consigneeAddressCard?.props('applyPresets')).toBeTruthy();
  });

  it('should have set apply presets for different consignee address - ROAD COLLECTION order', async () => {
    await setupAddresses(Segment.ROAD);

    formStore.orderType = OrderTypes.RoadCollectionOrder;
    addressStore.consigneeAddressType = ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;

    await wrapper.vm.$nextTick();

    const differentConsigneeAddressCard = wrapper.findAllComponents(AddressCard).at(2);

    await wrapper.vm.$nextTick();

    expect(differentConsigneeAddressCard?.props('applyPresets')).toBeTruthy();
  });

  it('should reset the deliveryProduct when deleting the consignee address - ROAD', async () => {
    const { deliveryProduct, deliveryOption } = storeToRefs(
      useCreateOrderFormCollectionAndDeliveryStore(),
    );

    deliveryProduct.value = 'TEST_PRODUCT';

    await setupAddresses(Segment.ROAD);

    formStore.orderType = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    const consigneeAddressCard = wrapper.findAllComponents(AddressCard).at(1);

    consigneeAddressCard?.vm.$emit('delete-address');

    expect(deliveryProduct.value).toBeNull();
    expect(deliveryOption.value).toEqual(DeliveryOptions.None);
  });

  it('should reset the deliveryProduct when deleting the consignee address - ROAD', async () => {
    const { deliveryProduct, deliveryOption } = storeToRefs(
      useCreateOrderFormCollectionAndDeliveryStore(),
    );

    deliveryProduct.value = 'TEST_PRODUCT';

    await setupAddresses(Segment.ROAD);

    formStore.orderType = OrderTypes.RoadCollectionOrder;

    await wrapper.vm.$nextTick();

    const consigneeAddressCard = wrapper.findAllComponents(AddressCard).at(1);

    consigneeAddressCard?.vm.$emit('delete-address');

    expect(deliveryProduct.value).toBeNull();
    expect(deliveryOption.value).toEqual(DeliveryOptions.None);
  });

  it('should update loadingPointRef when loadingPoint is update from edit mode', async () => {
    await setupAddresses(Segment.ROAD);

    await wrapper.vm.$nextTick();
    await vi.waitFor(() => {
      expect(wrapper.findComponent(SelectAddress).exists()).toBe(true);
    });
    const select = wrapper.findComponent(SelectAddress);

    const { loadingPoint } = storeToRefs(useCreateOrderAddressesStore());
    loadingPoint.value = noneAddress;

    await wrapper.vm.$nextTick();

    expect(select.text()).toBe('labels.differing_loading_point.textlabels.dfe_none.text');
  });
});

async function setupAddresses(segment: Segment) {
  const dataStore = useCreateOrderDataStore();
  await dataStore.fetchCustomers(segment);

  const formStore = useCreateOrderFormStore();
  formStore.customerNumber = dataStore.customers?.[0]?.customerNumber ?? '';
}
