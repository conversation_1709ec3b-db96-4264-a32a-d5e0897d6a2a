import DocumentUploader from '@/components/createOrder/sharedComponents/formSectionDocuments/documentUploader/DocumentUploader.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import UploadList from '@/components/createOrder/sharedComponents/formSectionDocuments/documentUploader/UploadList.vue';

describe('Sections Documents DocumentUploader component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = shallowMount(DocumentUploader);
  });

  it('shows upload list', async () => {
    expect(wrapper.findComponent(UploadList).exists()).toBe(true);
  });
});
