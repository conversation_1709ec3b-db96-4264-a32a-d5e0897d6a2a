import CardMain from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import RoadReferences from '@/components/createOrder/formSectionOrderReferences/road/RoadReferences.vue';
import AirAndSeaReferences from '@/components/createOrder/formSectionOrderReferences/airAndSea/AirAndSeaReferences.vue';

describe('Order references - CardMain component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(CardMain);
  });

  it('mounts with references fields for road forwarding (by default)', () => {
    expect(wrapper.exists()).toBe(true);

    expect(wrapper.findComponent(RoadReferences).exists()).toBe(true);
    expect(wrapper.findComponent(AirAndSeaReferences).exists()).toBe(false);
  });

  it('shows references fields for air export forwarding', async () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadReferences).exists()).toBe(false);
    expect(wrapper.findComponent(AirAndSeaReferences).exists()).toBe(true);

    store.orderType = OrderTypes.SeaExportOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadReferences).exists()).toBe(false);
    expect(wrapper.findComponent(AirAndSeaReferences).exists()).toBe(true);
  });
});
