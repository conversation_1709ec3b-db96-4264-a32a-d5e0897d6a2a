import { mount } from '@vue/test-utils';
import QuoteToBookBanner from '@/components/base/banner/QuoteToBookBanner.vue';
import { Routes } from '@/enums';
import { VBtn } from 'vuetify/components';
import type { TestUtils } from '../../../../test/test-utils';
import { createClientMock } from '../../../../test/util/mock-client';
import { nextTick } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import { Access, getParsedToken } from '@dfe/dfe-frontend-user-profile';
import { storeToRefs } from 'pinia';
import { ClientKey } from '@/types/client';

const props = {
  title: '',
};

describe('QuoteToBookRoadBanner', () => {
  let wrapper: TestUtils.VueWrapper<typeof QuoteToBookBanner>;

  it('displays banner headline and text', async () => {
    wrapper = mount(QuoteToBookBanner, {
      props,
    });
    await wrapper.setProps({ title: 'banner title' });

    expect(wrapper.text()).toContain('banner title');
    expect(wrapper.text()).toContain('messages.id6612.text');

    await wrapper.setProps({ orderIsPriceDependent: true });
    expect(wrapper.text()).toContain('messages.id6627.description');
  });

  it('emits remote navigate event & close dialog on click', async () => {
    const client = createClientMock({
      token: getParsedToken({
        quote: Access.Full,
        customerNumbers: {
          user: [{ number: '00000001', segment: 'ROAD' }],
          company: [{ number: '00000001', segment: 'ROAD' }],
        },
      }),
    });

    wrapper = mount(QuoteToBookBanner, {
      props,
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });

    await wrapper.setProps({ quoteId: 123, orderIsPriceDependent: true });
    const formStore = useCreateOrderFormStore();
    const { transportType, customerNumber } = storeToRefs(formStore);
    transportType.value = Segment.ROAD;
    customerNumber.value = '00000001';

    const closeDialogEvent = vi.fn(() => {
      console.log('emitted');
    });
    client.events.on('closeBookDialog', closeDialogEvent);

    const navigateEvent = vi.fn();
    client.router.onRemoteNavigate(navigateEvent);

    await nextTick();

    await wrapper.getComponent(VBtn).trigger('click');

    expect(closeDialogEvent).toHaveBeenCalledTimes(1);

    expect(navigateEvent).toHaveBeenCalledTimes(1);
    expect(navigateEvent).toHaveBeenCalledWith(`${Routes.QUOTE}/${String(123)}`);
  });

  it('dont show cross link quote button when no access', async () => {
    const client = createClientMock({
      token: getParsedToken({
        quote: Access.None,
        customerNumbers: {
          user: [{ number: '00000001', segment: 'ROAD' }],
          company: [{ number: '00000001', segment: 'ROAD' }],
        },
      }),
    });

    wrapper = mount(QuoteToBookBanner, {
      props,
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });

    await wrapper.setProps({ quoteId: 123, orderIsPriceDependent: true });
    const formStore = useCreateOrderFormStore();
    const { transportType, customerNumber } = storeToRefs(formStore);
    transportType.value = Segment.ROAD;
    customerNumber.value = '00000001';

    createClientMock({
      token: getParsedToken({
        quote: Access.None,
        customerNumbers: {
          user: [{ number: '00000001', segment: 'ROAD' }],
          company: [{ number: '00000001', segment: 'ROAD' }],
        },
      }),
    });

    await nextTick();

    expect(wrapper.findComponent(VBtn).exists()).toBe(false);
  });
});
