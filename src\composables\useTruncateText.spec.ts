import { useTruncateText } from '@/composables/useTruncateText';
import { ref } from 'vue';

const mockText = 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr';
const label = ref(mockText);

describe('useTruncateText composables', () => {
  it('returns truncated text', () => {
    const length = 20;
    const { truncatedText, isTruncated } = useTruncateText(label, length);
    expect(truncatedText.value).toEqual('Lorem ipsum dolor si…');
    expect(isTruncated.value).toEqual(true);
  });

  it('returns full text if length is greater than text length', () => {
    const length = 100;
    const { truncatedText, isTruncated } = useTruncateText(label, length);
    expect(truncatedText.value).toEqual(mockText);
    expect(isTruncated.value).toEqual(false);
  });
});
