import type { AirProductRoutingConfig, IncotermConfig } from '@dfe/dfe-configserver-api-module';

export const incotermConfig: IncotermConfig = {
  air: {
    export: {
      from_address: {
        to_port: ['CPT', 'CIP'],
        to_address: ['DPU', 'DAP', 'DDP'],
      },
      from_port: {
        to_address: ['DPU', 'DAP', 'DDP'],
      },
    },
    import: {
      from_address: {
        to_address: ['EXW', 'FC1'],
      },
      from_port: {
        to_address: ['FOA'],
      },
    },
  },
  sea: {
    export: {
      from_address: {
        to_port: ['CPT', 'CIP', 'CFR', 'CIF'],
        to_address: ['DPU', 'DAP', 'DDP'],
      },
      from_port: {
        to_address: ['DAP', 'DPU', 'DDP'],
      },
    },
    import: {
      from_address: {
        to_address: ['EXW'],
        to_port: ['EXW'],
      },
      from_port: {
        to_address: ['FAS', 'FOB'],
      },
    },
  },
};

export const airProductRoutingConfig: AirProductRoutingConfig = {
  product_related_airports: [
    {
      country: 'DE',
      airports: [
        'BER',
        'BRE',
        'CGN',
        'DUS',
        'FMM',
        'FMO',
        'FRA',
        'HAJ',
        'HAM',
        'LEJ',
        'MUC',
        'NUE',
        'SCN',
        'STR',
      ],
    },
  ],
};
