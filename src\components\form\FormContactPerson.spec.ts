import AutocompleteMultipleContactsField from '@/components/form/AutocompleteMultipleContactsField.vue';
import FormContactPerson from '@/components/form/FormContactPerson.vue';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { ContactData } from '@dfe/dfe-address-api';
import { DfePhoneInput } from '@dfe/dfe-frontend-shared-components';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import { expect } from 'vitest';

const hint = 'Hint';

const props = {
  modelValue: {
    name: '<PERSON> Musterman',
    telephone: '000000000',
    email: '<EMAIL>',
    mobile: '00000000',
  },
  requiredFields: ['name'] as (keyof ContactData)[],
};

describe('FormContactPerson component', () => {
  const event = vi.fn();
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(FormContactPerson, {
      props,
    });
  });

  afterEach(() => {
    event.mockClear();
  });

  it('shows hint depending on prop', async () => {
    expect(wrapper.find('.hint').exists()).toBe(false);

    await wrapper.setProps({ hint });

    const hintElement = wrapper.find('.hint');
    expect(hintElement.exists()).toBe(true);
    expect(hintElement.text()).toEqual(hint);
  });

  it('should set hasUnsavedAddressChanges to true, if contact data has changed', async () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { hasUnsavedAddressChanges } = storeToRefs(createOrderAddressesStore);

    expect(hasUnsavedAddressChanges.value).toBe(false);
    await wrapper.setProps({
      modelValue: {
        name: 'John Doe',
      },
    });

    expect(hasUnsavedAddressChanges.value).toBe(true);
  });

  it('emits update event change', () => {
    const inputTelephone = wrapper.findAllComponents(DfePhoneInput).at(0);
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    orderType.value = OrderTypes.AirExportOrder;
    inputTelephone?.vm.$emit('update:modelValue', '0123456789');

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([
      { ...props.modelValue, telephone: '0123456789' },
    ]);
  });

  it('emits update event change contact', () => {
    const inputName = wrapper.findAllComponents(AutocompleteMultipleContactsField).at(0);

    inputName?.vm.$emit('update:modelValue', {
      name: 'Dachser name',
      telephone: '0123456789',
      email: '<EMAIL>',
      mobile: '0123456789',
    });

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([
      {
        ...props.modelValue,
        name: 'Dachser name',
        telephone: '0123456789',
        email: '<EMAIL>',
        mobile: '0123456789',
      },
    ]);
  });
});
