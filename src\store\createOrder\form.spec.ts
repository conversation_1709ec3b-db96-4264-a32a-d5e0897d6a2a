import useResetCreateOrderFormData from '@/composables/createOrder/useResetCreateOrderFormData';
import { useInit } from '@/composables/useInit';
import { ErrorCode, OrderTypes } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { creationPreferences } from '@/mocks/fixtures/creationPreferences';
import { customers, customersWithAddresses } from '@/mocks/fixtures/customers';
import { airExportOrder, airImportOrder, roadForwardingOrder } from '@/mocks/fixtures/order';
import { validationError, validationSuccess } from '@/mocks/fixtures/validationResults';
import { mockServer } from '@/mocks/server';
import { assertError } from '@/store/assert-error';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import {
  type AirExportOrder,
  AirExportQuoteInformation,
  Division,
  OrderStatus,
  RoadForwardingQuoteInformation,
  Segment,
  ValidationResult,
} from '@dfe/dfe-book-api';
import { createClientMock } from '@test/util/mock-client';
import { withSetup } from '@test/util/with-setup';
import { expect } from 'vitest';

const getModifiedAirOrderData = (modifiedData: Partial<AirExportOrder>): AirExportOrder => {
  return {
    ...airExportOrder,
    ...modifiedData,
  };
};

describe('createOrderForm store', () => {
  const client = createClientMock();

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        creationPreferences,
        customers,
        addresses,
        roadForwardingOrder,
        airExportOrder,
        validationSuccess,
        validationError,
      },
    });
  });

  it('fetches creation preferences', async () => {
    const store = useCreateOrderFormStore();
    await store.fetchCreationPreferences();

    expect(store.orderType).toEqual(creationPreferences.orderType);
    expect(store.preferredCurrency).toEqual(creationPreferences.preferredCurrency);
  });

  it('returns selected customer', () => {
    const store = useCreateOrderFormStore();
    const storeData = useCreateOrderDataStore();

    storeData.customers = customers;
    store.customerNumber = '00000001';

    expect(store.selectedCustomer).toBeDefined();
    expect(store.selectedCustomer).toEqual(customers[2]);
  });

  it("returns true for 'isRoadForwardingOrder' and false for 'isRoadCollectionOrder' if order type is forwarding", () => {
    const store = useCreateOrderFormStore();

    expect(store.isRoadForwardingOrder).toBeTruthy();
    expect(store.isRoadCollectionOrder).toBeFalsy();
  });

  it("returns true for 'isRoadCollectionOrder' and false for 'isRoadForwardingOrder' if order type is collecting", () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.RoadCollectionOrder;

    expect(store.isRoadCollectionOrder).toBeTruthy();
    expect(store.isRoadForwardingOrder).toBeFalsy();
  });

  it("returns true for 'isAirExportOrder' and false for 'isAirImportOrder' if order type is air export", () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.AirExportOrder;

    expect(store.isAirExportOrder).toBeTruthy();
    expect(store.isAirImportOrder).toBeFalsy();
  });

  it("returns true for 'isAirImportOrder' and false for 'isAirExportOrder' if order type is air import", () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.AirImportOrder;

    expect(store.isAirImportOrder).toBeTruthy();
    expect(store.isAirExportOrder).toBeFalsy();
  });

  it("correctly identifies 'order' based on order type", () => {
    const store = useCreateOrderFormStore();

    const airOrderTypes = [OrderTypes.AirExportOrder, OrderTypes.AirImportOrder];
    const seaOrderTypes = [OrderTypes.SeaExportOrder, OrderTypes.SeaImportOrder];
    const roadOrderTypes = [OrderTypes.RoadForwardingOrder, OrderTypes.RoadCollectionOrder];

    airOrderTypes.forEach((orderType) => {
      store.orderType = orderType;
      expect(store.isAirOrder).toBeTruthy();
      expect(store.isSeaOrder).toBeFalsy();
      expect(store.isRoadOrder).toBeFalsy();
    });

    seaOrderTypes.forEach((orderType) => {
      store.orderType = orderType;
      expect(store.isSeaOrder).toBeTruthy();
      expect(store.isAirOrder).toBeFalsy();
      expect(store.isRoadOrder).toBeFalsy();
    });

    roadOrderTypes.forEach((orderType) => {
      store.orderType = orderType;
      expect(store.isRoadOrder).toBeTruthy();
      expect(store.isSeaOrder).toBeFalsy();
      expect(store.isAirOrder).toBeFalsy();
    });
  });

  it("returns true for 'isSeaExportOrder' and false for 'isSeaImportOrder' if order type is sea export", () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.SeaExportOrder;

    expect(store.isSeaExportOrder).toBeTruthy();
    expect(store.isSeaImportOrder).toBeFalsy();
  });

  it("returns true for 'isSeaImportOrder' and false for 'isSeaExportOrder' if order type is sea import", () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.SeaImportOrder;

    expect(store.isSeaImportOrder).toBeTruthy();
    expect(store.isSeaExportOrder).toBeFalsy();
  });

  it('returns fromCountry and toCountry for forwarding orders', async () => {
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchCustomers(Segment.ROAD);

    const formStore = useCreateOrderFormStore();
    const addressStore = useCreateOrderAddressesStore();
    const { consigneeAddress } = addressStore;

    formStore.orderType = OrderTypes.RoadForwardingOrder;
    formStore.customerNumber = '00000001';
    consigneeAddress.address.countryCode = 'FR';

    expect(formStore.transportCountry).toEqual({
      fromCountry: customersWithAddresses[2].address?.countryCode,
      fromPostcode: customersWithAddresses[2].address?.postcode,
      toCountry: 'FR',
      toPostcode: null,
    });

    formStore.$reset();
    addressStore.$reset();
  });

  it('returns only fromCountry for forwarding orders', async () => {
    const formStore = useCreateOrderFormStore();

    formStore.orderType = OrderTypes.RoadForwardingOrder;
    formStore.customerNumber = '00000001';

    expect(formStore.transportCountry).toEqual({
      fromCountry: customersWithAddresses[2].address?.countryCode,
      fromPostcode: customersWithAddresses[2].address?.postcode,
      toCountry: null,
      toPostcode: null,
    });

    formStore.$reset();
  });

  it('returns fromCountry and toCountry for collecting order', () => {
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress, consigneeAddress } = addressStore;
    const formStore = useCreateOrderFormStore();

    formStore.orderType = OrderTypes.RoadCollectionOrder;
    addressStore.isCustomer = false;
    shipperAddress.address.countryCode = 'DE';
    consigneeAddress.address.countryCode = 'IT';

    expect(formStore.transportCountry).toEqual({
      fromCountry: 'DE',
      fromPostcode: null,
      toCountry: 'IT',
      toPostcode: null,
    });

    formStore.$reset();
    addressStore.$reset();
  });

  it('returns only fromCountry for collecting order, consignee is not customer', () => {
    const formStore = useCreateOrderFormStore();
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = addressStore;

    formStore.orderType = OrderTypes.RoadCollectionOrder;
    addressStore.isCustomer = false;
    shipperAddress.address.countryCode = 'DE';

    expect(formStore.transportCountry).toEqual({
      fromCountry: 'DE',
      fromPostcode: null,
      toCountry: null,
      toPostcode: null,
    });

    formStore.$reset();
    addressStore.$reset();
  });

  it('returns only toCountry for collecting order, consignee is not customer', () => {
    const formStore = useCreateOrderFormStore();
    const addressStore = useCreateOrderAddressesStore();
    const { consigneeAddress } = addressStore;

    formStore.orderType = OrderTypes.RoadCollectionOrder;
    addressStore.isCustomer = false;
    consigneeAddress.address.countryCode = 'HU';

    expect(formStore.transportCountry).toEqual({
      fromCountry: null,
      fromPostcode: null,
      toCountry: 'HU',
      toPostcode: null,
    });

    formStore.$reset();
    addressStore.$reset();
  });

  it('returns no countries for collecting order, consignee is not customer', () => {
    const formStore = useCreateOrderFormStore();
    const addressStore = useCreateOrderAddressesStore();

    formStore.orderType = OrderTypes.RoadCollectionOrder;
    addressStore.isCustomer = false;

    expect(formStore.transportCountry).toEqual({
      fromCountry: null,
      fromPostcode: null,
      toCountry: null,
      toPostcode: null,
    });

    formStore.$reset();
    addressStore.$reset();
  });
  it('save order road', async () => {
    const formStore = useCreateOrderFormStore();
    await formStore.saveOrder(roadForwardingOrder);

    await formStore.getOrder(1);

    expect(formStore.orderData).toEqual(roadForwardingOrder);
  });

  it('save order air', async () => {
    const formStore = useCreateOrderFormStore();
    await formStore.saveOrder(airExportOrder);

    await formStore.getOrder(2);

    expect(formStore.orderData).toEqual(airExportOrder);
  });

  it('saveOrder should log an error if it fails', async () => {
    const { api } = useInit();
    const formStore = useCreateOrderFormStore();
    await assertError(
      () => formStore.saveOrder(roadForwardingOrder),
      api.book.v2,
      'saveDraftOrderV2',
    );
  });

  it('get order road', async () => {
    const formStore = useCreateOrderFormStore();
    await formStore.getOrder(1);

    expect(formStore.orderData).toEqual(roadForwardingOrder);
  });

  it('get order air', async () => {
    const formStore = useCreateOrderFormStore();
    await formStore.getOrder(2);

    expect(formStore.orderData).toEqual(airExportOrder);
  });

  it('getOrder should log an error if it fails', async () => {
    const { api } = useInit();
    await assertError(
      () => useCreateOrderFormStore().getOrder(1),
      api.book.orders,
      'getOrderDetails',
    );
  });

  it('should reset form data stores', () => {
    const storeDefs = [
      useCreateOrderAddressesStore,
      useCreateOrderOrderLineFormStore,
      useCreateOrderFormCollectionAndDeliveryStore,
      useCreateOrderFormAccountingAdditionalServices,
      useCreateOrderOrderReferencesFormStore,
      useCreateOrderTextsStore,
      useCreateOrderDocumentsStore,
    ];
    storeDefs.forEach((useStore) => {
      useStore().$reset = vi.fn();
    });

    const { clearFormData } = withSetup(useResetCreateOrderFormData)[0];
    clearFormData();

    storeDefs.forEach((useStore) => {
      expect(useStore().$reset).toHaveBeenCalled();
    });
  });

  it('should validate an order', async () => {
    const store = useCreateOrderFormStore();

    await store.validateOrder(roadForwardingOrder);

    expect(store.$state.validateData).toEqual(validationSuccess);
  });

  it('should return validation result on error in validate an order api', async () => {
    const store = useCreateOrderFormStore();
    store.api.book.v2.saveOrderV2 = vi.fn().mockRejectedValue({
      error: {
        validationResult: {
          valid: false,
          newOrderStatus: 'DRAFT',
          results: [
            {
              field: 'orderLines[0].weight',
              errorType: 'REQUIRED_FIELD_MISSING',
              description: 'must not be empty',
            },
            {
              field: 'shipperAddress.name',
              errorType: 'REQUIRED_FIELD_MISSING',
              description: 'must not be empty',
            },
            {
              field: 'orderReferences.referenceType.delivery_note_number',
              errorType: 'REQUIRED_FIELD_MISSING',
              description: 'must not be empty',
            },
          ],
        },
      },
    });

    const validationResult = (await store.validateOrder(roadForwardingOrder)) as ValidationResult;
    expect(validationResult.results).toEqual(validationError.results);
  });
  it('should return saved true while saving', async () => {
    const store = useCreateOrderFormStore();

    const validationResult = await store.saveOrder(roadForwardingOrder);

    expect(validationResult.saved).toEqual(true);
  });
  it('should return Generic error while order as Draft', async () => {
    const store = useCreateOrderFormStore();

    const error = {
      oasDiscriminator: 'JsonGeneralProblem',
      type: 'book:about:blank',
      errorId: ErrorCode.OrderExpiryErrorCode,
      title: 'label.text.title_sv_04',
      status: 409,
      detail: 'label.text.get_a_new_quote',
      severity: 'High',
      traceId: null,
      timestamp: '2025-02-10T14:40:56.736330138Z',
    };

    store.api.book.v2.saveDraftOrderV2 = vi.fn().mockRejectedValue({ data: null, error: error });

    const validationResult = await store.saveOrder(airImportOrder);
    expect(validationResult.problem).toEqual(error);
  });

  it('Returns shipment number', () => {
    const store = useCreateOrderFormStore();
    expect(store.shipmentNumber).toBe(undefined);

    store.saveOrderData = {
      orderType: OrderTypes.AirExportOrder,
      shipmentNumber: 2,
    };

    expect(store.shipmentNumber).toBe(2);
  });

  it('returns true if order is from quote', () => {
    const store = useCreateOrderFormStore();
    expect(store.isOrderFromQuote).toBe(false);
    store.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    expect(store.isOrderFromQuote).toBe(true);
  });

  it('returns true if road forwarding order is from quote', () => {
    const store = useCreateOrderFormStore();
    store.quoteInformation = {
      orderType: OrderTypes.RoadForwardingOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as RoadForwardingQuoteInformation;
    store.orderType = OrderTypes.RoadForwardingOrder;
    expect(store.isRoadForwardingOrderFromQuote).toBe(true);

    const referencesStore = useCreateOrderOrderReferencesFormStore();
    expect(store.isRoadOrderFromQuoteWithDailyPrice).toBe(false);

    referencesStore.dailyPriceReference = '060';
    expect(store.isRoadOrderFromQuoteWithDailyPrice).toBe(true);
  });

  it('returns true if air export order is from quote', () => {
    const store = useCreateOrderFormStore();
    expect(store.isAirExportOrderFromQuote).toBe(false);
    store.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    store.orderType = OrderTypes.AirExportOrder;
    expect(store.isAirExportOrderFromQuote).toBe(true);
  });

  it('should set initial order', () => {
    const store = useCreateOrderFormStore();

    expect(store.initialOrder).toBe(null);
    store.setInitialOrder(airExportOrder);
    expect(store.initialOrder).toEqual(airExportOrder);
  });

  it('should show modal for technical error with errorId "errVA-01"', () => {
    const store = useCreateOrderFormStore();
    const modalShowMock = vi.fn();
    client.modal.show = modalShowMock;
    const error = {
      error: {
        oasDiscriminator: 'BadGatewayProblem',
        type: 'https://example.com/probs/technical-error',
        errorId: 'errVA-01',
        title: 'Technical Error',
        status: 502,
        detail: 'A technical error occurred.',
        severity: 'Critical',
        timestamp: '2023-10-01T00:00:00Z',
      },
    };

    store.checkForTechnicalError(error);

    expect(client.modal.show).toHaveBeenCalledWith('error', {
      headline: 'Technical Error',
      message: 'A technical error occurred.',
    });
  });

  it('should not show modal for non-technical error', () => {
    const store = useCreateOrderFormStore();
    const modalShowMock = vi.fn();
    client.modal.show = modalShowMock;

    const error = {
      error: {
        oasDiscriminator: 'ValidationError',
        type: 'https://example.com/probs/validation-error',
        errorId: 'errVA-02',
        title: 'Validation Error',
        status: 400,
        detail: 'A validation error occurred.',
        severity: 'High',
        timestamp: '2023-10-01T00:00:00Z',
      },
    };

    store.checkForTechnicalError(error);

    expect(client.modal.show).not.toHaveBeenCalled();
  });

  it('returns correct current order status', () => {
    const store = useCreateOrderFormStore();
    store.orderData = airExportOrder;

    expect(store.currentOrderStatus).toEqual(OrderStatus.DRAFT);

    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.LABEL_PENDING,
      },
    });

    expect(store.currentOrderStatus).toEqual(OrderStatus.LABEL_PENDING);

    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.COMPLETE,
      },
    });

    expect(store.currentOrderStatus).toEqual(OrderStatus.COMPLETE);
  });

  it('returns correct is cloned draft order', () => {
    const store = useCreateOrderFormStore();
    store.orderData = airExportOrder;

    expect(store.isClonedDraftOrder).toBe(false);

    store.orderData = getModifiedAirOrderData({
      clonedOrder: true,
    });

    expect(store.isClonedOrder).toBe(true);
  });

  it('returns correct is complete road order', () => {
    const store = useCreateOrderFormStore();

    store.orderType = OrderTypes.AirExportOrder;
    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.DRAFT,
      },
    });

    expect(store.isCompleteRoadOrder).toBe(false);

    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.COMPLETE,
      },
    });

    expect(store.isCompleteRoadOrder).toBe(false);

    store.orderType = OrderTypes.RoadCollectionOrder;
    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.DRAFT,
      },
    });

    expect(store.isCompleteRoadOrder).toBe(false);

    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.COMPLETE,
      },
    });

    expect(store.isCompleteRoadOrder).toBe(true);
  });

  it('returns correct is draft order', () => {
    const store = useCreateOrderFormStore();
    store.orderData = null;

    expect(store.isDraftOrder).toBe(true);

    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.DRAFT,
      },
    });

    expect(store.isDraftOrder).toBe(true);

    store.orderData = getModifiedAirOrderData({
      orderStatus: {
        status: OrderStatus.COMPLETE,
      },
    });

    expect(store.isDraftOrder).toBe(false);
  });

  it('returns correct is order saved', () => {
    const store = useCreateOrderFormStore();
    store.saveOrderData = null;
    store.orderData = null;

    expect(store.isOrderSaved).toBe(false);

    store.saveOrderData = airExportOrder;
    store.orderData = null;

    expect(store.isOrderSaved).toBe(true);

    store.saveOrderData = null;
    store.orderData = airExportOrder;

    expect(store.isOrderSaved).toBe(true);
  });

  it('Returns true if Food Logistic division', () => {
    const store = useCreateOrderFormStore();
    const storeData = useCreateOrderDataStore();

    storeData.customers = customers;
    store.customerNumber = '00000001';

    expect(store.selectedCustomer.division).toBe(Division.FOOD_LOGISTICS);
    expect(store.isFoodLogistics).toBe(true);
  });

  it('Returns true if European Logistic division', () => {
    const store = useCreateOrderFormStore();
    const storeData = useCreateOrderDataStore();

    storeData.customers = customers;
    store.customerNumber = '00000002';

    expect(store.selectedCustomer.division).toBe(Division.EUROPEAN_LOGISTICS);
    expect(store.isEuropeanLogistics).toBe(true);
  });

  it('Returns true if Cash On Delivery', () => {
    const store = useCreateOrderFormStore();
    const storeData = useCreateOrderDataStore();

    storeData.customers = customers;
    store.customerNumber = '00000002';

    expect(store.selectedCustomer.cashOnDelivery).toBe(false);

    storeData.customers = customers;
    store.customerNumber = '00000001';

    expect(store.selectedCustomer.cashOnDelivery).toBe(true);
  });
});
