<template>
  <DfeBanner v-if="hasDuplicateErrors" type="error">
    <span class="text-h5">{{ t('labels.unique_order_reference.text') }}</span>
  </DfeBanner>

  <div class="grid-container-form | align-start mt-4 mb-4">
    <TextField
      v-for="(item, index) in items"
      :id="item.id"
      :key="item.id"
      v-model="item.value"
      :label="index === 0 ? label : ''"
      :required="!!getIsDeliveryNoteNumberFieldRequired(item.id)"
      :class="['grid-item', { 'mt-5': isMarginTopNeeded(index) }]"
      :max-length="maxLength"
      :rules="getRules(item.id)"
      :error="hasDuplicateError(item)"
      :prefix="prefix"
      :show-delete-icon="true"
      @delete="removeField"
    />
  </div>
</template>

<script setup lang="ts">
import TextField from '@/components/form/TextField.vue';
import { useValidationRules, ValidationRule } from '@/composables/form/useValidationRules';
import type { MultipleReferenceNumber } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { TranslateResult } from 'vue-i18n';
import { useDisplay } from 'vuetify';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useI18n } from 'vue-i18n';
import { useDuplicateValidation } from '@/composables/createOrder/useDuplicateValidation';
import { OrderReferenceType } from '@dfe/dfe-book-api';

const { t } = useI18n();

interface Props {
  items: MultipleReferenceNumber[];
  label?: TranslateResult;
  required?: boolean;
  maxLength?: number;
  prefix?: string;
  rules?: ValidationRule[];
  type?: string;
}

const props = defineProps<Props>();

const { hasDuplicateErrors, hasDuplicateError } = useDuplicateValidation(props.items);
const { deliveryNoteNumberRequiredFields, deleteReferenceNumber } =
  useCreateOrderOrderReferencesFormStore();

const getIsDeliveryNoteNumberFieldRequired = (id: string) =>
  deliveryNoteNumberRequiredFields.find((item) => item.id === id)?.required;

const isMarginTopNeeded = (index: number) => {
  const { lg, xl } = useDisplay();
  const indexWithMarginTop = [1];
  if (lg.value) {
    indexWithMarginTop.push(2);
  }
  if (xl.value) {
    indexWithMarginTop.push(2, 3);
  }
  return indexWithMarginTop.includes(index);
};
function removeField(id: string) {
  deleteReferenceNumber(props.type as OrderReferenceType, id);
}
function getRules(id: string): ValidationRule[] {
  const rules = getIsDeliveryNoteNumberFieldRequired(id) ? [useValidationRules.required] : [];
  if (props.rules) {
    rules.push(...props.rules);
  }

  return rules;
}
</script>
