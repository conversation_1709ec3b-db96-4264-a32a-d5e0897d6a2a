import RoadForwarding from '@/views/createOrder/road/RoadForwarding.vue';
import { shallowMount } from '@vue/test-utils';
import CustomersForwardingCardMain from '@/components/createOrder/formSectionCustomer/forwardingOrder/CardMain.vue';
import AddressesCardMain from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import FreightCardMain from '@/components/createOrder/formSectionFreight/CardMain.vue';
import CollectionAndDeliveryCardMain from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import AccountingAndAdditionalServicesCollectionCardMain from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/CardMain.vue';
import OrderReferencesCardMain from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import OrderDocuments from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { OrderTypes } from '@/enums';
import SelectAddress from '@/components/form/SelectAddress.vue';
import { addresses } from '@/mocks/fixtures/addresses';
import { TestUtils } from '@test/test-utils';
import SectionTexts from '@/components/createOrder/formSectionTexts/CardMain.vue';

describe('Road - RoadForwarding view', () => {
  let wrapper: TestUtils.VueWrapper<typeof RoadForwarding>;

  beforeEach(() => {
    wrapper = shallowMount(RoadForwarding);
  });

  it.each([
    ['Customers', CustomersForwardingCardMain],
    ['Addresses', AddressesCardMain],
    ['Freight', FreightCardMain],
    ['Collection & Delivery', CollectionAndDeliveryCardMain],
    ['Accounting & Additional Services', AccountingAndAdditionalServicesCollectionCardMain],
    ['Order References', OrderReferencesCardMain],
    ['Order Documents', OrderDocuments],
  ])(`shows %s form section`, async (sectionName, component) => {
    const { customers } = storeToRefs(useCreateOrderDataStore());

    if (customers) {
      customers.value = addresses;
    }

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(component).exists()).toBe(true);
  });

  it.each([
    ['Customers', CustomersForwardingCardMain],
    ['Addresses', AddressesCardMain],
    ['Freight', FreightCardMain],
    ['Collection & Delivery', CollectionAndDeliveryCardMain],
    ['Accounting & Additional Services', AccountingAndAdditionalServicesCollectionCardMain],
    ['Order References', OrderReferencesCardMain],
    ['Order Text', SectionTexts],
    ['Order Documents', OrderDocuments],
  ])(`shows %s form section with order texts`, async (sectionName, component) => {
    const { customers } = storeToRefs(useCreateOrderDataStore());
    vi.mock('@/composables/createOrder/useOrderTexts', () => ({
      useOrderTexts() {
        return {
          filteredTexts: [
            {
              label: 'test',
              type: 'WA',
              maxLength: 600,
              visible: true,
              active: true,
            },
          ],
          isAtLeastOneOrderTextVisible: true,
        };
      },
    }));
    if (customers) {
      customers.value = addresses;
    }

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(component).exists()).toBe(true);
  });

  it('should not show principal if not available', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const store = useCreateOrderDataStore();

    orderType.value = OrderTypes.RoadForwardingOrder;
    store.customers = [];

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(SelectAddress).exists()).toBe(false);
  });
});
