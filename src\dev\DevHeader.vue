<template>
  <VLayout>
    <header>
      <VBtn
        color="transparent"
        class="px-0 position-fixed navigation-drawer__trigger"
        style="z-index: 9999"
        @click="devHeaderOpen = !devHeaderOpen"
      >
        <MaterialSymbol size="24">
          <MenuIcon />
        </MaterialSymbol>
      </VBtn>
      <VNavigationDrawer v-model="devHeaderOpen" location="right" temporary>
        <VListItem>
          <h3>Create new order</h3>
        </VListItem>
        <VListItem class="text-center" @click="onCreateRoadOrder">Road 🚛</VListItem>
        <VListItem class="text-center" @click="onCreateAirOrder">Air ✈️</VListItem>
        <VListItem class="text-center" @click="onCreateSeaOrder">Sea 🚢</VListItem>
        <VDivider />

        <VListItem>
          <h3>Edit Existing order</h3>
        </VListItem>
        <VListItem>
          <VTextField v-model="customerNumber" placeholder="Customer Number" />
          <VTextField v-model="orderId" placeholder="Order ID" />
          <VBtn color="primary" class="w-100 mb-2" @click="onEditOrder">Edit Order</VBtn>
        </VListItem>
        <VDivider />

        <VListItem>
          <h3>Misc. Form Actions</h3>
        </VListItem>
        <VListItem>
          <VBtn color="secondary" class="w-100 mb-2" @click="onCloseBookForm">Close Book Form</VBtn>
          <VBtn color="secondary" class="w-100 mb-2" @click="onClearFormData">Clear Form Data</VBtn>
          <VBtn color="secondary" class="w-100 mb-2" @click="onEditOrderScrollToDocuments"
            >Scroll to documents</VBtn
          >
        </VListItem>
        <VDivider />

        <VListItem>
          <h3>Change Preferences</h3>
        </VListItem>
        <VListItem>
          <VSelect
            label="Language"
            density="compact"
            :model-value="locale"
            :items="availableLocales"
            @update:model-value="onChangePreferences('locale', $event)"
          />
          <VSelect
            label="Date format"
            density="compact"
            :model-value="dateFormat"
            :items="availableDateFormats"
            @update:model-value="onChangePreferences('dateFormat', $event)"
          />
          <VSelect
            label="Time format"
            density="compact"
            :model-value="timeFormat"
            :items="availableTimeFormats"
            @update:model-value="onChangePreferences('timeFormat', $event)"
          />
          <VSelect
            label="Number format"
            density="compact"
            :model-value="numberFormat"
            item-title="locale"
            :items="availableNumberFormats"
            @update:model-value="onChangePreferences('numberFormat', $event)"
          />
        </VListItem>
      </VNavigationDrawer>
    </header>
  </VLayout>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { ClientKey } from '@/types/client';
import { Segment } from '@dfe/dfe-book-api';
import { Preferences, PreferencesApi } from '@dfe/dfe-frontend-client';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import MenuIcon from '@dfe/dfe-frontend-styles/assets/icons/menu-24px.svg';
import { inject, ref } from 'vue';

const client = inject(ClientKey);
const { locale, dateFormat, timeFormat, numberFormat } = usePreferences();
const orderId = ref(14579);
const customerNumber = ref(6005028);

const devHeaderOpen = ref(true);

const availableLocales = import.meta.env.VITE_APP_DFE_AVAILABLE_LANGUAGES.split(',');
const availableDateFormats: PreferencesApi['dateFormat'][] = [
  'DD_MM_YYYY|dot',
  'DD_MM_YYYY|dash',
  'YYYY_MM_DD|slash',
];
const availableTimeFormats: PreferencesApi['timeFormat'][] = ['12h', '24h'];
const availableNumberFormats: PreferencesApi['numberFormat'][] = ['de|grouped', 'en|grouped'];

const closeDevHeader = () => {
  devHeaderOpen.value = false;
};

const onCreateRoadOrder = () => {
  closeDevHeader();
  client?.events.emit('createOrder', {
    transportType: Segment.ROAD,
  });
};

const onCreateAirOrder = () => {
  closeDevHeader();
  client?.events.emit('createOrder', { transportType: Segment.AIR });
};

const onCreateSeaOrder = () => {
  closeDevHeader();
  client?.events.emit('createOrder', { transportType: Segment.SEA });
};

const onChangePreferences = <T extends keyof Preferences>(key: T, value: Preferences[T]) => {
  closeDevHeader();
  client?.preferences.update({ [key]: value });
};

const onEditOrder = () => {
  closeDevHeader();
  client?.events.emit('editOrder', {
    orderId: orderId.value,
    customerNumber: customerNumber.value,
  });
};

const onEditOrderScrollToDocuments = () => {
  closeDevHeader();
  client?.events.emit('editOrder', {
    orderId: orderId.value,
    customerNumber: customerNumber.value,
    scrollToDocuments: true,
  });
};
const onClearFormData = () => {
  closeDevHeader();
  client?.events.emit('clearCreateOrderFormData');
};

const onCloseBookForm = () => {
  closeDevHeader();
  client?.events.emit('preventUnsavedChanges');
};
client?.toast.onShow((toast) => {
  console.info('Show toast', toast);
});

client?.events.on('printLabels', (data) => {
  console.info('Print labels', data);
});
</script>

<style lang="scss">
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';

@font-face {
  font-family: vars.$font-family-base;
  font-weight: vars.$font-weight-base;
  font-style: normal;
  src: url('@dfe/dfe-frontend-styles/' + vars.$asset-font-base-woff2-regular) format('truetype');
}

@font-face {
  font-family: vars.$font-family-base;
  font-weight: vars.$font-weight-heading;
  font-style: normal;
  src: url('@dfe/dfe-frontend-styles/' + vars.$asset-font-base-woff2-bold) format('truetype');
}

@font-face {
  font-family: vars.$font-family-base;
  font-weight: vars.$font-weight-label;
  font-style: normal;
  src: url('@dfe/dfe-frontend-styles/' + vars.$asset-font-base-woff2-medium) format('truetype');
}

:root {
  --color-base-white: #{vars.$color-base-white};

  --color-base-grey-50: #{vars.$color-base-grey-50};
  --color-base-grey-100: #{vars.$color-base-grey-100};
  --color-base-grey-200: #{vars.$color-base-grey-200};
  --color-base-grey-300: #{vars.$color-base-grey-300};
  --color-base-grey-400: #{vars.$color-base-grey-400};
  --color-base-grey-500: #{vars.$color-base-grey-500};
  --color-base-grey-600: #{vars.$color-base-grey-600};
  --color-base-grey-700: #{vars.$color-base-grey-700};
  --color-base-grey-800: #{vars.$color-base-grey-800};
  --color-base-grey-900: #{vars.$color-base-grey-900};

  --color-base-blue-50: #{vars.$color-base-blue-50};
  --color-base-blue-100: #{vars.$color-base-blue-100};
  --color-base-blue-200: #{vars.$color-base-blue-200};
  --color-base-blue-300: #{vars.$color-base-blue-300};
  --color-base-blue-400: #{vars.$color-base-blue-400};
  --color-base-blue-500: #{vars.$color-base-blue-500};
  --color-base-blue-600: #{vars.$color-base-blue-600};
  --color-base-blue-700: #{vars.$color-base-blue-700};

  --color-base-green-100: #{vars.$color-base-green-100};
  --color-base-green-500: #{vars.$color-base-green-500};
  --color-base-green-800: #{vars.$color-base-green-800};

  --color-base-orange-100: #{vars.$color-base-orange-100};
  --color-base-orange-500: #{vars.$color-base-orange-500};
  --color-base-orange-800: #{vars.$color-base-orange-800};

  --color-base-red-100: #{vars.$color-base-red-100};
  --color-base-red-500: #{vars.$color-base-red-500};
  --color-base-red-700: #{vars.$color-base-red-700};
  --color-base-red-800: #{vars.$color-base-red-800};

  --color-base-yellow-500: #{vars.$color-base-yellow-500};

  --color-text-base: #{vars.$color-text-base};
  --color-text-primary: #{vars.$color-text-primary};
  --color-text-grey: #{vars.$color-text-grey};
}

body {
  margin: 0;
}
.navigation-drawer__trigger {
  top: 0;
  right: 0;
}
</style>

<style lang="scss" scoped>
header input {
  width: 75px;
}
</style>
