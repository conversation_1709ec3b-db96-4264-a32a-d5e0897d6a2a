<template>
  <SectionCard v-data-test="'section-accounting'">
    <template #headline>
      {{ t('labels.accountingAndAdditionalServices.text') }}
    </template>
    <DfeBanner :value="!hasAddresses" type="info" class="mt-0 mb-4">
      <span class="text-h5">{{ $t('messages.id7110.text') }}</span>
    </DfeBanner>
    <div class="d-flex ga-6 flex-wrap">
      <AccountingForwarding :has-addresses="hasAddresses" />
      <AdditionalServicesAdvanced :has-addresses="hasAddresses" />
      <div>
        <div class="text-label-3 mb-2">
          {{ t('labels.additionalServices.text') }}
        </div>
        <AdditionalServices />
      </div>
    </div>
  </SectionCard>
</template>

<script setup lang="ts">
import AdditionalServices from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AdditionalServices.vue';
import AdditionalServicesAdvanced from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AdditionalServicesAdvanced.vue';
import AccountingForwarding from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AccountingForwarding.vue';
import SectionCard from '@/components/base/SectionCard.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useCreateOrderFormStore } from '@/store/createOrder/form';

const { t } = useI18n();
const createOrderFormStore = useCreateOrderFormStore();
const { transportCountry } = storeToRefs(createOrderFormStore);

const hasAddresses = computed(() => {
  return !!transportCountry.value.fromCountry && !!transportCountry.value.toCountry;
});
</script>
