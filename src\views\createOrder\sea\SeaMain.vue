<template>
  <div class="mb-6 d-flex">
    <FormNavigation :items="seaNavigationItems" class="d-none d-md-block" />
    <div class="flex-grow-1">
      <ClonedOrderBanner />
      <GenericErrorBanner />
      <CreateOrderSea @sea-order-navigation-items="setNavigationItems($event)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import CreateOrderSea from '@/views/createOrder/sea/SeaOrder.vue';
import FormNavigation from '@/components/form/FormNavigation.vue';
import GenericErrorBanner from '@/components/createOrder/sharedComponents/GenericErrorBanner.vue';
import { Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';
import ClonedOrderBanner from '@/components/base/banner/ClonedOrderBanner.vue';

const createOrderFormStore = useCreateOrderFormStore();
const createOrderDataStore = useCreateOrderDataStore();
const { orderType } = storeToRefs(createOrderFormStore);

const seaNavigationItems = ref([]);

const setNavigationItems = (items: []) => {
  seaNavigationItems.value = items;
};

onMounted(() => {
  orderType.value = OrderTypes.SeaExportOrder;
});

onMounted(async () => {
  await createOrderDataStore.fetchCountries(Segment.SEA);
});
</script>
