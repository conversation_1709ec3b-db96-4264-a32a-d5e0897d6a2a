import { mount } from '@vue/test-utils';

import { VBtn, VCardTitle, VDialog } from 'vuetify/lib/components/index.mjs';
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';
import { TestUtils } from '../../../../test/test-utils';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';

const props = {
  modelValue: false,
  headline: 'Headline',
  size: 'sm',
  attach: 'body',
  id: 'modal-id',
  persistent: false,
  hasBorder: true,
} as const;

describe('ModalLayout component', () => {
  let wrapper: TestUtils.VueWrapper<typeof ModalWrapper>;

  beforeEach(() => {
    wrapper = mount(ModalWrapper, {
      props,
    });
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  it('close button is visible', async () => {
    await wrapper.setProps({ modelValue: true });
    expect(wrapper.findComponent('.modal-close-button')?.exists()).toBe(true);
  });

  it('emits close event', async () => {
    await wrapper.setProps({ modelValue: true });

    const closeBtn = wrapper.getComponent('.modal-close-button').findComponent(VBtn);
    await closeBtn.trigger('click');
    expect(wrapper.emitted('update:modelValue')).toHaveLength(1);
  });

  it('has sm max size', async () => {
    expect(document.querySelector('.modal-sm')).toBeNull();
    await wrapper.setProps({ modelValue: true });
    expect(document.querySelector('.modal-sm')).not.toBeNull();
  });

  it('has md max size', async () => {
    expect(document.querySelector('.modal-md')).toBeNull();
    await wrapper.setProps({ modelValue: true, size: 'md' });
    expect(document.querySelector('.modal-md')).not.toBeNull();
  });

  it('has lg max size', async () => {
    expect(document.querySelector('.modal-lg')).toBeNull();
    await wrapper.setProps({ modelValue: true, size: 'lg' });
    expect(document.querySelector('.modal-lg')).not.toBeNull();
  });

  it('display border by default', async () => {
    const text = 'Body';

    wrapper = mount(ModalWrapper, {
      props: {
        ...props,
        'model-value': true,
      },
      attachTo: document.body,
      slots: {
        body: `<div>${text}</div>`,
      },
    });

    expect(document.querySelector('.has-border')).not.toBeNull();
  });

  it('can hidden border', async () => {
    const text = 'Body';

    wrapper = mount(ModalWrapper, {
      props: {
        ...props,
        'model-value': true,
      },
      attachTo: document.body,
      slots: {
        body: `<div>${text}</div>`,
      },
    });

    expect(document.querySelector('.has-border')).not.toBeNull();
    await wrapper.setProps({ modelValue: true, hasBorder: false });
    expect(document.querySelector('.has-border')).toBeNull();
  });

  it('displays headline', async () => {
    const headline = 'Headline';
    await wrapper.setProps({ modelValue: true, headline });
    expect(wrapper.getComponent(VCardTitle).text()).toBe(headline);
  });

  it('displays header slot', async () => {
    const headline = 'Headline';
    const button = 'Click me';

    wrapper = mount(ModalWrapper, {
      props: {
        ...props,
        'model-value': true,
      },
      attachTo: document.body,
      slots: {
        header: `<div id="headline">${headline}</div><button id="button">${button}</button>`,
      },
    });

    const header = wrapper.getComponent('.modal-header');
    expect(header.find('#headline').text()).toBe(headline);
    expect(header.find('#button').text()).toBe(button);
  });

  it('displays body slot', async () => {
    const text = 'Body';

    wrapper = mount(ModalWrapper, {
      props: {
        ...props,
        'model-value': true,
      },
      attachTo: document.body,
      slots: {
        body: `<div>${text}</div>`,
      },
    });

    expect(wrapper.getComponent('.modal-body').text()).toBe(text);
  });

  it('displays footer slot', async () => {
    const text = 'Footer';

    wrapper = mount(ModalWrapper, {
      props: {
        ...props,
        'model-value': true,
      },
      attachTo: document.body,
      slots: {
        footer: `<div>${text}</div>`,
      },
    });

    expect(wrapper.getComponent('.modal-footer').text()).toBe(text);
  });

  it('should emit close event when address form has unsaved changes', async () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { hasUnsavedAddressChanges } = storeToRefs(createOrderAddressesStore);

    await wrapper.setProps({ modelValue: true });

    hasUnsavedAddressChanges.value = true;

    const closeBtn = wrapper.findComponent('.modal-close-button').findComponent(VBtn);
    await closeBtn.trigger('click');

    expect(wrapper.emitted('close')).toHaveLength(1);
  });

  it('should set modelValue to false when address form has no unsaved changes', async () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { hasUnsavedAddressChanges } = storeToRefs(createOrderAddressesStore);

    await wrapper.setProps({ modelValue: true });

    hasUnsavedAddressChanges.value = false;

    const closeBtn = wrapper.getComponent('.modal-close-button').findComponent(VBtn);
    await closeBtn.trigger('click');

    expect(wrapper.findComponent(VDialog).props('modelValue')).toBe(false);
  });
});
