import { defineStore, storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { AddressCardType, FurtherAddressTypesList } from '@/enums';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { HandOverSelection } from '@/types/hand-over';

interface FormEmbargoState {
  hasEmbargo: boolean;
}

let hasEmbargoCancelToken: symbol | string | number;

export const useEmbargoStore = defineStore('formEmbargo', {
  state: (): FormEmbargoState => ({
    hasEmbargo: false,
  }),
  actions: {
    resetHasEmbargo() {
      this.hasEmbargo = false;
    },
    async fetchHasEmbargo(
      shipperCountryCode: string | undefined,
      consigneeCountryCode: string | undefined,
    ) {
      this.api.book.abortRequest(hasEmbargoCancelToken);
      const cancelToken = this.createCancelToken();
      hasEmbargoCancelToken = cancelToken;

      try {
        if (shipperCountryCode && consigneeCountryCode) {
          const { data } = await this.api.book.v1.hasEmbargo(
            {
              fromCountryCode: shipperCountryCode,
              toCountryCode: consigneeCountryCode,
            },
            { cancelToken },
          );
          this.hasEmbargo = data;
        }
      } catch (error) {
        this.client.log.error('Error fetching embargo', 'dfe-book-frontend', error);
      }
    },
    checkIfCurrentAddressIsFinalAddress(addressCardType: AddressCardType) {
      const { isRoadCollectionOrder, isRoadForwardingOrder, isAirAndSeaOrder } =
        storeToRefs(useCreateOrderFormStore());
      const {
        differentConsigneeAddress,
        getFurtherAddress,
        shipperHandOverSelection,
        consigneeHandOverSelection,
      } = useCreateOrderAddressesStore();

      if (isRoadForwardingOrder.value && addressCardType === AddressCardType.CONSIGNEE) {
        return !getFurtherAddress(FurtherAddressTypesList.finalDeliveryAddress).address.name;
      }

      if (isRoadCollectionOrder.value && addressCardType === AddressCardType.CONSIGNEE) {
        return differentConsigneeAddress.address.name == '';
      }

      if (isAirAndSeaOrder.value) {
        if (addressCardType === AddressCardType.SHIPPER) {
          return shipperHandOverSelection.selection === HandOverSelection.default;
        }
        if (addressCardType === AddressCardType.CONSIGNEE) {
          return consigneeHandOverSelection.selection === HandOverSelection.default;
        }
      }

      return true;
    },
  },
});
