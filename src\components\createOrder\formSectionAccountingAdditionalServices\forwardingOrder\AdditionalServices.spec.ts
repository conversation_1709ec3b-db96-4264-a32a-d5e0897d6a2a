import AdditionalServices from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AdditionalServices.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import { mockServer } from '@/mocks/server';
import { OrderTypes } from '@/enums';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { storeToRefs } from 'pinia';

describe('Additional Services component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = shallowMount(AdditionalServices);
  });

  it("Shows 'Frost protection' checkbox when customer settings are set to true and is forwarding order", async () => {
    const server = mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: {
          frostProtection: true,
        },
      },
    });
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    await vi.waitFor(() => {
      expect(wrapper.find("[data-test='book-frost-protection']").exists()).toBe(true);
    });
    server.shutdown();
  });

  it('display mandatory to custom declaration field if custom documents it upload ', async () => {
    const customDeclaration = wrapper.find("[data-test='book-customs-declaration-field']");

    expect(customDeclaration.attributes('required')).toBe('false');

    const createOrderDocuments = useCreateOrderDocumentsStore();
    const { documents } = storeToRefs(createOrderDocuments);
    documents.value.push({
      documentType: 'CDINV',
      documentName: 'custom.pdf',
      extension: 'pdf',
      startProcessing: true,
    });

    await wrapper.vm.$nextTick();

    expect(customDeclaration.attributes('required')).toBe('true');
  });
});
