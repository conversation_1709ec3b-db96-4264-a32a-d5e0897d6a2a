import type { Country, FavoriteCountries } from '@dfe/dfe-book-api';
import type { Ref } from 'vue';
import type { TranslateResult } from 'vue-i18n';

export const useFavoriteCountriesList = (
  favoritesHeader: TranslateResult | string,
  header: TranslateResult | string,
  countries: Ref<Country[]>,
  favoriteCountries: Ref<FavoriteCountries>,
  isShipperAddress: boolean,
) => {
  const { shipperCountries, consigneeCountries } = favoriteCountries.value;

  const allCountries = countries.value;

  const favorites = isShipperAddress ? shipperCountries : consigneeCountries;

  if (favorites?.length) {
    const favoritesCountryCodes = favorites?.map((country) => country.countryCode);
    return [
      { header: favoritesHeader },
      ...favorites,
      { divider: true },
      { header },
      ...allCountries.filter((country) => !favoritesCountryCodes.includes(country.countryCode)),
    ];
  }

  return allCountries;
};
