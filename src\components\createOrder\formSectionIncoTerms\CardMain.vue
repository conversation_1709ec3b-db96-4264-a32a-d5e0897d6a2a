<template>
  <SectionCard v-data-test="'section-incoterms'">
    <template #headline>
      {{ t('labels.incoterms_title.text') }}
    </template>
    <SelectionMandatoryLabel />
    <DfeBanner v-if="bannerActive" type="error" class="my-4">
      <span class="text-h5">{{ t('labels.select_option.text') }}</span>
    </DfeBanner>
    <VRow dense>
      <VCol cols="12" md="8">
        <div v-if="isDisabled" class="mt-6 mb-0 d-flex disabled-message">
          <DfeBanner type="info" class="mt-0 mb-6">
            <span class="text-h5">{{ t('messages.id6598.text') }}</span>
          </DfeBanner>
        </div>
        <VCard
          v-else-if="selectedIncoTerm && isDisabledForPriceRelevantChanges"
          class="rounded-lg py-4 px-4 mt-6"
          variant="outlined"
        >
          <h5 class="text-h5">
            {{ [selectedIncoTerm.code, selectedIncoTerm.label].filter(Boolean).join(' - ') }}
          </h5>
          <div v-if="selectedIncoTerm.description" class="text-body-3 mt-1">
            {{ selectedIncoTerm.description }}
          </div>
        </VCard>
        <VRadioGroup
          v-else
          v-model="selectedIncoTerm"
          hide-details="auto"
          density="compact"
          :rules="[useValidationRules.requiredWithoutText]"
          class="mb-6"
        >
          <SelectCard
            v-for="term in incoTermsOptions"
            :key="term.dachserCode"
            v-data-test="'incoterms'"
            name="term"
            :label="term.code + ' - ' + term.label"
            :description="term.description"
            :value="term"
            :data-test-details="'bo-incoterms-' + term.code + '-' + term.id"
          />
        </VRadioGroup>
      </VCol>
    </VRow>
    <LinkElement
      :text="t('labels.incoterm_details.text')"
      external-link="https://www.dachser.de/de/mediaroom/downloads/Corporate/Marktinfo/Incoterms2020.pdf"
      class="text-label-2"
      icon
    >
      <template #icon>
        <CustomFilePdf />
      </template>
    </LinkElement>
  </SectionCard>
</template>

<script setup lang="ts">
import LinkElement from '@/components/base/LinkElement.vue';
import SectionCard from '@/components/base/SectionCard.vue';
import SelectionMandatoryLabel from '@/components/base/SelectionMandatoryLabel.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import SelectCard from '@/components/form/SelectCard.vue';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { useConfigStore } from '@/store/config';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useTermStore } from '@/store/createOrder/formTerms';
import { useValidationDataStore } from '@/store/validation';
import { IncoTerm } from '@dfe/dfe-book-api';
import CustomFilePdf from '@dfe/dfe-frontend-styles/assets/icons/custom_file_pdf-16px.svg';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const termStore = useTermStore();
const dataStore = useCreateOrderDataStore();
const configStore = useConfigStore();
const createOrderFormStore = useCreateOrderFormStore();
const { incoTermsOptions, selectedIncoTerm } = storeToRefs(termStore);
const addressStore = useCreateOrderAddressesStore();
const { shipperAddress, consigneeAddress } = storeToRefs(addressStore);
const validationStore = useValidationDataStore();
const { getForwardingOrderIncoterms } = storeToRefs(validationStore);
const { isDisabledForPriceRelevantChanges } = storeToRefs(createOrderFormStore);

const incoTermsLoaded = ref(false);

onMounted(async () => {
  await configStore.fetchIncotermsConfig();
  await dataStore.fetchIncoTerms();
  await configStore.fetchAirProductRoutingConfig();
  incoTermsLoaded.value = true;
});

const isDisabled = computed(
  () => !shipperAddress.value.address.name || !consigneeAddress.value.address.name,
);

const bannerActive = computed(() => !getForwardingOrderIncoterms.value);

watchEffect(() => {
  if (!incoTermsLoaded.value) {
    return;
  }

  // Preselect incoterm if only one is available
  if (incoTermsOptions.value.length === 1) {
    selectedIncoTerm.value = incoTermsOptions.value[0];
  }

  // Reset selected incoterm if it's not valid anymore
  if (
    selectedIncoTerm.value &&
    !incoTermsOptions.value.find((term: IncoTerm) => term.code === selectedIncoTerm.value?.code)
  ) {
    selectedIncoTerm.value = null;
  }
});
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfe-vars;
@use '@/styles/variables' as vars;
@use '@/styles/base' as base;

.v-banner {
  max-width: 50vw;
}

.select-card {
  margin-top: base.space(2);

  &:first-child {
    margin-top: 0;
  }
}

:deep(div.v-banner.v-sheet.banner) {
  max-width: 50vw;
}
</style>
