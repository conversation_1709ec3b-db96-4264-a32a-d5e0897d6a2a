<template>
  <div v-data-test="'address-form'">
    <h4 v-if="addressType" class="text-h4">
      {{ t('labels.address_type_' + addressType.toLowerCase() + '.text') }}
    </h4>
    <div>
      <RequiredHint class="mb-3" />
      <TextField
        v-model="formAddress.name"
        v-data-test="'address-form-name'"
        class="mt-3"
        :label="t('labels.name_label.text')"
        :required="true"
        :rules="maxLengthRuleDefault"
        :disabled="isDisabled('name').value || (isShipperAddressDisabled && isShipperAddress)"
      />
      <TextField
        v-model="formAddress.name2"
        v-data-test="'address-form-name2'"
        class="mt-2"
        :rules="maxLengthRuleDefault"
      />
      <TextField
        v-model="formAddress.name3"
        v-data-test="'address-form-name3'"
        class="mt-2"
        :rules="maxLengthRuleDefault"
      />
      <AutocompleteField
        v-model="formAddress.countryCode"
        v-data-test="'address-form-country'"
        class="mt-3"
        :items="smartProposalsEnabled ? favoriteCountriesOptionList : (countries ?? [])"
        item-title="label"
        item-value="countryCode"
        :label="t('labels.country_label.text')"
        :required="true"
        :rules="[useValidationRules.required]"
        :placeholder="t('labels.select_option.text')"
        :disabled="
          isDisabled('countryCode').value || (isShipperAddressDisabled && isShipperAddress)
        "
        :menu-icon="ArrowDropDownIcon"
      />
      <TextField
        v-model="formAddress.street"
        v-data-test="'address-form-street'"
        class="mt-3"
        :label="t('labels.street_label.text')"
        :required="true"
        :rules="[useValidationRules.required]"
        :max-length="maxLengthDefault"
        :disabled="isDisabled('street').value || (isShipperAddressDisabled && isShipperAddress)"
      />
      <template v-if="isAirOrder || isSeaOrder">
        <TextField
          v-model="formAddress.street2"
          v-data-test="'address-form-street2'"
          class="mt-2"
          :required="false"
          :max-length="MaxLengthsAirAndSeaOrder.Default"
        />
      </template>
      <DfeBanner
        v-if="showTechnicalError && isIrelandSelected"
        type="error"
        class="mt-3"
        :title="t('messages.id6996.text')"
        :message="t('messages.id6607.text')"
      >
        <span class="text-h5">{{ t('messages.id6996.text') }}</span>
        <div class="text-body-2 mt-2">{{ t('messages.id6607.text') }}</div>
      </DfeBanner>
      <VContainer class="pa-0 mb-8">
        <VRow v-if="isIrelandSelected">
          <VCol cols="4" class="pr-1">
            <TextField
              v-model="formAddress.postcode"
              v-data-test="'address-form-postcode'"
              :error-message="
                showUnknownEirCode && isEircodeValid ? t('labels.unknown_eircode_label.text') : ''
              "
              class="mt-3"
              :label="t('labels.eircode_label.text')"
              :required="isPostcodeRequired"
              :rules="eirCodeRules"
              :max-length="8"
              :disabled="
                isPostcodeDisabled ||
                isDisabled('postcode').value ||
                (isShipperAddressDisabled && isShipperAddress)
              "
              @input="doPostCodeToEirCodeConversion"
            />
          </VCol>
          <VCol cols="8" class="pl-2">
            <AutocompleteField
              v-model="townCounty"
              v-data-test="'town-county-form-country'"
              class="mt-3"
              :items="selectableTownCounty"
              item-title="label"
              item-value="data"
              :label="t('labels.town_county_label.text')"
              :required="true"
              :rules="[useValidationRules.required]"
              :placeholder="t('labels.select_option.text')"
              :disabled="isTownCountyDisabled"
              :menu-icon="ArrowDropDownIcon"
              :return-object="true"
            />
          </VCol>
        </VRow>
        <VRow v-else>
          <VCol cols="4" class="pr-1">
            <TextField
              v-model="formAddress.postcode"
              v-data-test="'address-form-postcode'"
              class="mt-3"
              :label="t('labels.postcode_label.text')"
              :required="isPostcodeRequired"
              :rules="postcodeRules"
              :max-length="maxLengthZipCode"
              :disabled="
                isPostcodeDisabled ||
                isDisabled('postcode').value ||
                (isShipperAddressDisabled && isShipperAddress)
              "
            />
          </VCol>
          <VCol cols="8" class="pl-2">
            <TextField
              v-model="formAddress.city"
              v-data-test="'address-form-city'"
              class="mt-3"
              :label="t('labels.city_label.text')"
              :required="true"
              :rules="[useValidationRules.required]"
              :max-length="maxLengthCity"
              :disabled="isDisabled('city').value || (isShipperAddressDisabled && isShipperAddress)"
            />
          </VCol>
        </VRow>
      </VContainer>
      <TextField
        v-if="isRoadOrder && !isIrelandSelected"
        v-model="formAddress.supplement"
        class="mt-3"
        :label="t('labels.supplement_label.text')"
        :max-length="MaxLengthsRoadOrder.Default"
      />
      <TextField
        v-model="formAddress.individualId"
        class="mt-8"
        :label="$t('labels.individual_id.text')"
        :tooltip="$t('labels.individual_id_tooltip.text')"
        :rules="[useValidationRules.maxChars(17)]"
        :max-length="17"
      />
      <TextField
        v-model="formAddress.gln"
        class="mt-3"
        :label="t('labels.gln_label.text')"
        :rules="[useValidationRules.gln]"
        :max-length="13"
      />
      <TextField
        v-if="isAirOrder || isSeaOrder"
        v-model="formAddress.taxID"
        class="mt-3"
        :required="false"
        :label="t('labels.tax_vat_label.text')"
        :max-length="MaxLengthsAirAndSeaOrder.Default"
      />
      <ConfirmPrompt
        v-model="isUnsavedAddressChangesDialogOpen"
        :headline="getUnsavedChangesTitle"
        :confirm-text="t('labels.discard_label.text')"
        :cancel-text="t('labels.cancel_label.text')"
        @confirm="discardChanges"
        @cancel="cancel"
        @close="cancel"
      >
        <h5 class="text-body-2 text-grey-darken-4">{{ getUnsavedChangesText }}</h5>
      </ConfirmPrompt>
    </div>
  </div>
</template>

<script setup lang="ts">
import TextField from '@/components/form/TextField.vue';
import type { Ref } from 'vue';
import { computed, onMounted, ref, watch } from 'vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { storeToRefs } from 'pinia';
import type { Address, Country, FavoriteCountries } from '@dfe/dfe-book-api';
import type { FurtherAddressTypes } from '@/types/createOrder';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { MaxLengthsAirAndSeaOrder, MaxLengthsRoadOrder, MaxLengthCoreSystemAddress } from '@/enums';
import { useFavoriteCountriesList } from '@/composables/createOrder/useFavoriteCountriesList';
import { useDisabledFormAddressFields } from '@/composables/createOrder/addresses/useDisabledFormAddressFields';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useI18n } from 'vue-i18n';
import RequiredHint from '@/components/form/RequiredHint.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { orderAddress } from '@/store/sharedInitialStates';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import { useTownCountyList } from '@/composables/data/useTownCountyList';
import {
  eirCodePattern,
  isIrelandCountryCode,
  doEirCodeConversion,
  isEirCodeValid,
} from '@dfe/dfe-frontend-composables';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useValidatePostcode } from '@/composables/createOrder/useValidatePostcode';

interface Props {
  addressType?: FurtherAddressTypes;
  disableSearch?: boolean;
  smartProposalsEnabled?: boolean;
  isShipperAddress?: boolean;
  customerNumber?: string;
}

const { t } = useI18n();

const props = defineProps<Props>();
const emit = defineEmits(['input', 'update-contact']);

const {
  isShipperAddressDisabled,
  hasUnsavedAddressChanges,
  isUnsavedAddressChangesDialogOpen,
  townCounty,
  formAddress,
} = storeToRefs(useCreateOrderAddressesStore());
const { selectableTownCounty, search, showTechnicalError, showUnknownEirCode } =
  useTownCountyList();

const { isAirOrder, isSeaOrder, isRoadOrder } = storeToRefs(useCreateOrderFormStore());

const createOrderDataStore = useCreateOrderDataStore();
const { countries, favoriteCountries } = storeToRefs(createOrderDataStore);

const { isDisabled } = useDisabledFormAddressFields(formAddress);

const addressModelInitial = ref<Address>({ ...formAddress.value });
const { data: validatePostcode } = useValidatePostcode();

const isIrelandPrincipalAddress = computed(
  () => props.customerNumber && formAddress.value.countryCode === 'IE',
);
const isTownCountyDisabled = computed(
  () =>
    (isIrelandSelected.value && !isEircodeValid.value) ||
    showUnknownEirCode.value ||
    showTechnicalError.value,
);

const eirCodeRules = computed(() => {
  const rules = [
    useValidationRules.regex(eirCodePattern, 'labels.invalid_postcode_structure.text', 'H54 YR28'),
  ];

  if (isPostcodeRequired.value) {
    rules.unshift(useValidationRules.required);
  }

  return !isIrelandPrincipalAddress.value ? rules : [];
});

const postcodeRegex = (pattern: string, label: string, examplePostcode: string) => {
  return useValidationRules.regex(new RegExp(`^${pattern}$`), label, examplePostcode);
};

const postcodeRules = computed(() => {
  const rules =
    validatePostcode.value?.valid === false
      ? [
          postcodeRegex(
            validatePostcode.value?.validatedPattern ?? '',
            'labels.invalid_postcode_structure.text',
            validatePostcode.value?.examplePostcode ?? '',
          ),
        ]
      : [];

  if (isPostcodeRequired.value) {
    rules.unshift(useValidationRules.required);
  }

  return rules;
});

const maxLengthDefault = computed(() =>
  formAddress.value.customerNumber
    ? MaxLengthCoreSystemAddress.Default
    : isAirOrder.value
      ? MaxLengthsAirAndSeaOrder.Default
      : MaxLengthsRoadOrder.Default,
);

const maxLengthZipCode = computed(() =>
  formAddress.value.customerNumber
    ? MaxLengthCoreSystemAddress.ZipCode
    : isAirOrder.value
      ? MaxLengthsAirAndSeaOrder.ZipCode
      : MaxLengthsRoadOrder.ZipCode,
);

const maxLengthCity = computed(() =>
  formAddress.value.customerNumber
    ? MaxLengthCoreSystemAddress.Default
    : isAirOrder.value
      ? MaxLengthsAirAndSeaOrder.City
      : MaxLengthsRoadOrder.City,
);

const maxLengthRuleDefault = computed(() => [useValidationRules.maxChars(maxLengthDefault.value)]);

const cancel = () => {
  isUnsavedAddressChangesDialogOpen.value = false;
};

const discardChanges = () => {
  hasUnsavedAddressChanges.value = false;
  cancel();
  emit('input');
};

const getUnsavedChangesTitle = computed(() => {
  return JSON.stringify(addressModelInitial.value) === JSON.stringify({ ...orderAddress() })
    ? t('labels.discard_data.text')
    : t('labels.discard_title.text');
});

const getUnsavedChangesText = computed(() => {
  return JSON.stringify(addressModelInitial.value) === JSON.stringify({ ...orderAddress() })
    ? t('labels.unsaved_data_info.text')
    : t('labels.unsaved_address_info.text');
});

const hasCountryCode = computed(() => !!formAddress.value.countryCode);
const isPostcodeRequired = computed(
  () =>
    hasCountryCode.value && !createOrderDataStore.isPostcodeOptional(formAddress.value.countryCode),
);
const isPostcodeDisabled = computed(() => hasCountryCode.value && !isPostcodeRequired.value);
const isIrelandSelected = computed(() => isIrelandCountryCode(formAddress.value.countryCode ?? ''));
const isEircodeValid = computed(
  () => isEirCodeValid(formAddress.value.postcode ?? '') || isIrelandPrincipalAddress.value,
);

const updateHasUnsavedChanges = () => {
  hasUnsavedAddressChanges.value =
    JSON.stringify(formAddress.value) !== JSON.stringify(addressModelInitial.value);
};

const doPostCodeToEirCodeConversion = () => {
  formAddress.value.postcode = doEirCodeConversion(formAddress.value.postcode);
};

const favoriteCountriesOptionList = computed(() => {
  const countryOptions = favoriteCountries as Ref<FavoriteCountries>;
  const countryList = countries as Ref<Country[]>;
  if (!countryOptions?.value) {
    return [];
  }
  return useFavoriteCountriesList(
    t('labels.frequently_used.text'),
    t('labels.more_options.text'),
    countryList,
    countryOptions,
    props.isShipperAddress || false,
  );
});

onMounted(() => {
  hasUnsavedAddressChanges.value = false;
});

watch(isEircodeValid, () => {
  if (!isEircodeValid.value) {
    townCounty.value = undefined;
  }
});

watch(
  () => formAddress.value.postcode,
  () => {
    if (formAddress.value.postcode && formAddress.value.postcode.length >= 3) {
      search.value = formAddress.value.postcode.slice(0, 3);
    }
  },
);

watch(formAddress.value, () => {
  updateHasUnsavedChanges();
});
</script>
