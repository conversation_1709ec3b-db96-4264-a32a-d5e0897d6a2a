import { useInit } from '@/composables/useInit';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useQuery } from '@tanstack/vue-query';
import { computed, ref, watch } from 'vue';
import { TownCounty } from '@/store/createOrder/formAddresses';
import { IrelandPostalCode } from '@dfe/dfe-book-api';
import { useUpperFirst } from '@dfe/dfe-frontend-composables';

export const useTownCountyList = () => {
  const search = ref('');
  const { api } = useInit();
  const createOrderFormStore = useCreateOrderFormStore();
  const { customerNumber, orderType } = storeToRefs(createOrderFormStore);
  const queryKey = ref(['townCounty', search.value, customerNumber, orderType]);

  const query = useQuery({
    queryKey: queryKey,
    staleTime: Infinity,
    placeholderData() {
      return [];
    },
    async queryFn() {
      const result = await api.book.v1
        .getIrelandDachserPostalCodes({
          postalCode: search.value,
        })
        .then((res) => {
          showTechnicalError.value = false;
          return res.data;
        })
        .catch(() => {
          showTechnicalError.value = true;
          return [];
        });

      const townCounties = mapApiResponseToTownCountyArray(result);
      showUnknownEirCode.value = townCounties.length === 0;
      return townCounties;
    },
  });

  watch(
    () => search.value,
    async (newVal) => {
      if (newVal.length <= 3) {
        queryKey.value = ['townCounty', newVal, customerNumber.value, orderType.value];
        await query.refetch();
      }
    },
  );

  const selectableTownCounty = computed(() => [...(query.data?.value ? query.data.value : [])]);

  const showTechnicalError = ref(false);
  const showUnknownEirCode = ref(false);

  const mapApiResponseToTownCountyArray = (response: IrelandPostalCode[]): TownCounty[] =>
    response.map((item) => ({
      label: `${useUpperFirst(item.town)}/${useUpperFirst(item.county)}`,
      data: {
        town: useUpperFirst(item.town),
        county: useUpperFirst(item.county),
        dachserPlz: useUpperFirst(item.dachserPLZ),
      },
    }));

  return {
    showUnknownEirCode,
    showTechnicalError,
    selectableTownCounty,
    ...query,
    search,
  };
};
