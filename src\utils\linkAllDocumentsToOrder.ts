import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';

async function linkAllDocumentsToOrder() {
  const { customerNumber, saveOrderData } = storeToRefs(useCreateOrderFormStore());

  return await useCreateOrderDocumentsStore().linkAllDocumentsToOrder(
    +customerNumber.value,
    saveOrderData.value?.orderId,
  );
}

export default linkAllDocumentsToOrder;
