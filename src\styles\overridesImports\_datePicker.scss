@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
[dfe-book-frontend] {
:deep(.v-theme--light.v-picker.v-sheet) {
  display: block;
  width: 300px;
  box-shadow: vars.$box-shadow-s !important;
  max-height: 320px;

  .v-picker__body {
    .v-date-picker-controls {
      padding-inline-start: 12px;
      .v-date-picker-controls__month-btn,
      .v-date-picker-controls__mode-btn {
        color: rgb(var(--v-theme-blue-500));
        font-weight: vars.$font-weight-label;
        font-size: vars.$font-size-label-1;

        &:hover,
        &:focus-within {
          background-color: rgb(var(--v-theme-blue-50));
        }
      }
      .v-date-picker-controls__month {
        .v-btn {
          color: rgb(var(--v-theme-grey-700));
          &--disabled {
            color: rgb(var(--v-theme-grey-500));
          }
          &:hover,
          &:focus-within {
            color: rgb(var(--v-theme-grey-900));
            background-color: rgb(var(--v-theme-blue-50));
          }
        }
      }
    }
    .v-date-picker-months {
      max-height: min-content;
      padding-bottom: 12px;
      .v-date-picker-months__content {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(4, 0fr);
        grid-gap: 24px;
        padding-top: 24px;
      }
    }
    .v-date-picker-years,
    .v-date-picker-months,
    .v-date-picker-month {
      .v-btn {
        border-radius: 32px;
        font-size: vars.$font-size-body-2;
        &--disabled {
          color: rgb(var(--v-theme-grey-500));
        }
        &--active {
          background-color: rgb(var(--v-theme-blue-500));
          color: rgb(var(--v-theme-white));
        }
        &.v-btn--variant-outlined {
          border-color: rgb(var(--v-theme-blue-500));
          color: rgb(var(--v-theme-blue-500));
        }
        &:not(.v-btn--active) {
          &:hover,
          &:focus-within {
            background-color: rgb(var(--v-theme-blue-50));
          }
        }
      }
    }
    .v-date-picker-month {
      min-width: auto;
      .v-date-picker-month__day {
        height: 32px;
        width: 32px;
        .v-btn {
          height: 32px;
          width: 32px;
          font-size: vars.$font-size-body-3;
        }
      }
    }
    .v-date-picker-month__day--selected {
      .v-btn {
        background-color: rgb(var(--v-theme-blue-500)) !important;
        color: rgb(var(--v-theme-white)) !important;
        font-weight: vars.$font-weight-label;
      }
    }
  }
}
}