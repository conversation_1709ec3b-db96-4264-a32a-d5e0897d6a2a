import { useCreateOrderFormStore } from '@/store/createOrder/form';
import {
  ADRDangerousGood4Store,
  EQDangerousGood4Store,
  LQDangerousGood4Store,
  OrderLine4Store,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import {
  FullContainerLoad,
  isAirOrder,
  isRoadForwardingOrder,
  isSeaOrder,
  OrderResponseBody,
  PackingPosition,
  RoadOrder,
  SeaOrder,
} from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { createUuid } from '@/utils/createUuid';
import { i18n } from '@/plugins/i18n';

function setOrderLineItem(
  orderLine: OrderLine4Store,
  items: OrderResponseBody['orderLineItems'],
  i: number,
) {
  if (items) {
    Object.assign(orderLine, items[i]);
  }

  if (orderLine.packaging?.code === null) {
    orderLine.packaging = undefined;
  }

  setHsCodes(orderLine);
}

function setDangerousGoodsData(
  dangerousGoods:
    | (ADRDangerousGood4Store | EQDangerousGood4Store | LQDangerousGood4Store)[]
    | undefined,
) {
  const { t } = i18n.global;

  dangerousGoods?.forEach((dangerousGood) => {
    dangerousGood.localId = createUuid();

    const packaging = (dangerousGood as EQDangerousGood4Store | ADRDangerousGood4Store).packaging;

    if (!packaging) {
      return;
    }

    packaging.translationKey = t(`generalData.packagingOptionRoad.${packaging.code}.text`);
  });
}

function getAllOrderLineItems(
  packingPositions?: PackingPosition[],
  fullContainerLoads?: FullContainerLoad[],
  unassignedOrderLines?: OrderResponseBody['orderLineItems'],
) {
  const packingPositionOrderLines = packingPositions?.reduce(
    (orderLines, packingPosition) => {
      if (packingPosition.lines) {
        orderLines?.push(...packingPosition.lines);
      }
      return orderLines;
    },
    [] as OrderResponseBody['orderLineItems'],
  );

  const fullContainerLoadOrderLines = fullContainerLoads?.reduce(
    (orderLines, fullContainerLoad) => {
      if (fullContainerLoad.lines) {
        orderLines?.push(...fullContainerLoad.lines);
      }
      return orderLines;
    },
    [] as OrderResponseBody['orderLineItems'],
  );

  if (!unassignedOrderLines && !packingPositionOrderLines && !fullContainerLoadOrderLines) {
    return;
  }

  return [
    ...(unassignedOrderLines ?? []),
    ...(packingPositionOrderLines ?? []),
    ...(fullContainerLoadOrderLines ?? []),
  ];
}

function setHsCodes(orderLine: OrderLine4Store) {
  const orderLineStore = useCreateOrderOrderLineFormStore();

  const addGoodsClassificationItem = (index: number, localId: number) => {
    if (index > 0) {
      orderLineStore.addGoodsClassificationItem(localId);
    }
  };

  orderLine.hsCodes?.forEach((hsCode, index) => {
    addGoodsClassificationItem(index, orderLine.localId);

    const goodsClassificationItem = orderLineStore.getGoodsClassificationItem(
      orderLine.localId,
      index,
    );

    if (!goodsClassificationItem) {
      return;
    }

    goodsClassificationItem.id = hsCode.id;
    goodsClassificationItem.goods = <string>hsCode.goods;
    if (hsCode.hsCode) {
      goodsClassificationItem.hsCode = {
        code: hsCode.hsCode,
      };
    } else {
      goodsClassificationItem.hsCode = undefined;
      return;
    }

    orderLineStore.loadAndSetHsCode(orderLine.localId, index, hsCode.hsCode);
  });
}

export function useSetOrderLineData(editOrderData: OrderResponseBody) {
  const orderLineStore = useCreateOrderOrderLineFormStore();
  const {
    orderLines,
    totalValue,
    manualNumberOfLabelsOnLoad,
    generatedSSccs,
    manualNumberOfLabels,
    generatedSSccsOnLoad,
    stackable,
    shockSensitive,
  } = storeToRefs(orderLineStore);
  const createOrderFormStore = useCreateOrderFormStore();

  const { preferredCurrency } = storeToRefs(createOrderFormStore);
  const allOrderLines = getAllOrderLineItems(
    (editOrderData as RoadOrder).packingPositions,
    (editOrderData as SeaOrder).fullContainerLoads,
    editOrderData.orderLineItems,
  );

  while (!allOrderLines || allOrderLines?.length > orderLines.value.length) {
    orderLineStore.addOrderLine();
  }

  if (isRoadForwardingOrder(editOrderData)) {
    generatedSSccs.value = editOrderData.generatedSsccs ?? [];
    manualNumberOfLabels.value = editOrderData.manualNumberSscc;
  }
  generatedSSccsOnLoad.value = generatedSSccs.value.length;
  manualNumberOfLabelsOnLoad.value = generatedSSccs.value.length;

  orderLines.value.forEach((orderLine, i) => {
    setOrderLineItem(orderLine, allOrderLines, i);
  });

  orderLines.value.forEach((orderLine) => {
    if (!orderLine.id) {
      orderLineStore.removeOrderLine(orderLine.localId);
      return;
    }

    setDangerousGoodsData(orderLine.dangerousGoods);
  });

  totalValue.value = editOrderData?.goodsValue;
  if (isRoadForwardingOrder(editOrderData)) {
    manualNumberOfLabels.value = editOrderData?.manualNumberSscc;
  }

  if (preferredCurrency) {
    preferredCurrency.value = editOrderData?.goodsCurrency;
  }

  if (isAirOrder(editOrderData) || isSeaOrder(editOrderData)) {
    stackable.value = editOrderData.stackable ?? false;
    shockSensitive.value = editOrderData.shockSensitive ?? false;
  } else {
    stackable.value = false;
    shockSensitive.value = false;
  }
}
