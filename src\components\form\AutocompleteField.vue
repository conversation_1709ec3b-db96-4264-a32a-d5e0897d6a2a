<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VAutocomplete
      :id="id"
      v-model="modelValue"
      v-model:search="searchText"
      :clearable="clearable"
      class="autocomplete"
      :items="menuItems"
      :placeholder="placeholder"
      :item-title="itemTitle"
      :item-value="itemValue"
      single-line
      variant="outlined"
      item-props.color="grey darken-4"
      :menu-props="{
        scrollStrategy: 'close',
        attach: $appRoot,
        maxWidth: '100%',
        maxHeight: '234px',
      }"
      :menu-icon="menuIcon"
      hide-no-data
      bg-color="white"
      :custom-filter="filter"
      :no-filter="noFilter || staticMenu || showLoader"
      hide-details="auto"
      :return-object="returnObject"
      :rules="validationRules"
      :disabled="disabled"
      validate-on="blur"
      :error-messages="errorMessage"
      @update:search="onSearchInput"
      @focus="$event.target.click()"
      @update:model-value="onChange"
      @keydown.enter.prevent
    >
      <template v-if="multilineMenu && !showLoader" #item="{ item, props: slotProps }">
        <VListItem v-bind="slotProps">
          <VListItemTitle v-if="item" class="text-wrap">
            <!-- eslint-disable vue/no-v-html -->
            <div
              class="text-label-2"
              v-html="setHighlight((item as unknown as Record<string, any>).raw[itemValueKey])"
            />
            <div
              class="text-body-3 text-grey-darken-2"
              v-html="setHighlight((item as unknown as Record<string, any>).raw[itemTextKey])"
            />
            <!-- eslint-enable -->
          </VListItemTitle>
        </VListItem>
      </template>
      <template v-else-if="!showLoader" #item="{ item, props: slotProps }">
        <VListSubheader v-if="item.raw?.header" color="grey-700" class="text-caption">
          {{ item.raw.header }}
        </VListSubheader>
        <VDivider v-if="item.raw?.divider" />
        <VListItem
          v-if="!item.raw?.divider && !item.raw?.header"
          v-bind="slotProps"
          :data-test-details="item.raw[itemValue]"
          active-class="bg-blue-50 text-grey-900"
        >
          <template #title>
            <!-- eslint-disable vue/no-v-html -->
            <span v-if="item.raw[itemTitle]" v-html="setHighlight(item.raw[itemTitle])" />
            <!-- eslint-enable -->
          </template>
        </VListItem>
      </template>
      <template v-else #item>
        <VListItem class="py-6" disabled>
          <ProgressCircular color="primary" size="24" indeterminate />
        </VListItem>
      </template>
      <template v-if="append || $slots.icon" #append-inner>
        <span class="v-text-field__suffix text-body-2">{{ append }}</span>
        <span class="text-grey-darken-2 mt-1">
          <slot name="icon" />
        </span>
      </template>
    </VAutocomplete>
  </div>
</template>

<script setup lang="ts">
import ProgressCircular from '@/components/ProgressCircular.vue';
import useHighlight from '@/composables/createOrder/useHighlight';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { useValidationRules, withMessage } from '@/composables/form/useValidationRules';
import { useDebouncedLoader } from '@/composables/useDebouncedLoader';
import { SanitizeFallback, SanitizeKey } from '@/types/sanitize';
import type { FilterFunction } from '@/types/vuetify';
import { createUuid } from '@/utils/createUuid';
import { computed, inject, ref, toRefs, watch } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import type { IconValue } from 'vuetify/lib/composables/icons.d.ts';

interface Props {
  items: object[];
  itemTitle?: string;
  itemValue?: string;
  maxLength?: number;
  label?: TranslateResult;
  append?: TranslateResult;
  errorMessage?: string | readonly string[] | null;
  required?: boolean;
  staticMenu?: boolean;
  multilineMenu?: boolean;
  loading?: boolean;
  showLoaderAfterMs?: number;
  returnObject?: boolean;
  placeholder?: TranslateResult;
  message?: string;
  filter?: FilterFunction;
  noFilter?: boolean;
  disabled?: boolean;
  rules?: ValidationRule[];
  clearable?: boolean;
  menuIcon?: string | IconValue;
  allowManualInput?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  itemTitle: '',
  itemValue: '',
  maxLength: 0,
  label: '',
  append: undefined,
  required: false,
  staticMenu: false,
  multilineMenu: false,
  loading: false,
  showLoaderAfterMs: 1000,
  returnObject: false,
  placeholder: '',
  message: '',
  noFilter: false,
  filter: undefined,
  disabled: false,
  rules: undefined,
  clearable: false,
  menuIcon: '',
  errorMessage: null,
  allowManualInput: false,
});

const emit = defineEmits(['update:modelValue', 'focus', 'search-input']);

const modelValue = defineModel<string | null | undefined | object>({
  default: undefined,
});

const sanitize = inject(SanitizeKey, SanitizeFallback);
const id = `autocomplete-${createUuid()}`;

const { loading, showLoaderAfterMs } = toRefs(props);
const showLoader = useDebouncedLoader(loading, showLoaderAfterMs);
const searchText = ref('');

const onChange = (value: Record<string, never>) => {
  emit('update:modelValue', value);
};

const onSearchInput = (value: string | null) => {
  if (props.allowManualInput) {
    emit('update:modelValue', value);
  }

  emit('search-input', value);
};

const menuItems = computed(() => (showLoader.value ? [''] : props.items));

const itemValueKey = computed(() => props.itemValue ?? 'value');
const itemTextKey = computed(() => props.itemTitle ?? 'label');

const setHighlight = (text: string) => {
  return sanitize(useHighlight(text, searchText.value), {
    allowedAttributes: { mark: ['class'] },
  });
};

const validationRules = computed(() => [
  ...(props.required
    ? [
        props.message
          ? withMessage(useValidationRules.required, props.message)
          : useValidationRules.required,
      ]
    : []),

  ...(props.maxLength ? [useValidationRules.maxChars(props.maxLength)] : []),
  ...(props.rules ?? []),
]);

watch(
  () => modelValue.value,
  () => {
    if (!modelValue.value) {
      searchText.value = '';
    }
  },
);
</script>
<style lang="scss" scoped>
:deep(.autocomplete) {
  position: relative;
  z-index: 1;
}

@at-root {
  [dfe-book-frontend] {
    .v-list-item.v-list-item--active {
      background-color: var(--color-base-blue-50) !important;
      .v-list-item__overlay {
        display: none !important;
      }
    }
  }
}

:deep(.v-list-item-title),
:deep(.v-list-subheader) {
  white-space: normal;
}
</style>
