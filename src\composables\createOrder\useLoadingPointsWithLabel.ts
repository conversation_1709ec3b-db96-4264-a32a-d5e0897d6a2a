import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { loadingPointAddress } from '@/store/sharedInitialStates';
import type { Addresses } from '@dfe/dfe-book-api';
import { isEqual } from 'lodash';
import { storeToRefs } from 'pinia';
import type { Ref } from 'vue';
import { computed } from 'vue';

function useLoadingPointsWithLabel(loadingPoints?: Ref<Addresses | undefined>) {
  const addressStore = useCreateOrderAddressesStore();
  const { loadingPoint } = storeToRefs(addressStore);
  const createOrderFormStore = useCreateOrderFormStore();
  const { isEditMode } = storeToRefs(createOrderFormStore);

  const setLoadingPoint = () => {
    if (isEditMode.value) return;
    if (!loadingPoints?.value?.length) {
      addressStore.resetLoadingPoint();
    } else if (isEqual(loadingPoint.value, loadingPointAddress())) {
      // Set to Customer if not set manually
      addressStore.setCustomerAddressToLoadingPoint();
    } else if (!loadingPoints.value.some((item) => isEqual(item, loadingPoint.value))) {
      // Reset if loading point is not included in list
      addressStore.resetLoadingPoint();
    }
  };

  const loadingPointsWithLabel = computed(() => {
    if (!loadingPoints?.value?.length) return [];

    return loadingPoints?.value?.map((address) => {
      let labelParts = address.name;

      if (address.name2) labelParts += ', ' + address.name2;
      if (address.name3) labelParts += ', ' + address.name3;
      labelParts += ', ' + address.city;
      return {
        id: address.id,
        label: labelParts,
        address,
      };
    });
  });

  return {
    setLoadingPoint,
    loadingPointsWithLabel,
  };
}

export default useLoadingPointsWithLabel;
