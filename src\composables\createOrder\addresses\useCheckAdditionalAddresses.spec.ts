import { ref } from 'vue';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { FurtherAddressTypeKeys } from '@/enums';
import { FurtherAddressTypesList } from '@/enums';
import {
  useCheckFurtherAddresses,
  useShowAddButtons,
} from '@/composables/createOrder/addresses/useCheckAdditionalAddresses';
import { initPinia } from '../../../../test/util/init-pinia';

type ShowAddress = {
  [key in FurtherAddressTypeKeys]?: boolean;
};

describe('useShowAddButtons composable', () => {
  it('returns true when at least one item in items is not present in showAddress', () => {
    const items = [
      { code: FurtherAddressTypesList.customsAgent },
      { code: FurtherAddressTypesList.importer },
      { code: FurtherAddressTypesList.N1 },
    ];
    const showAddress = ref({
      [FurtherAddressTypesList.customsAgent]: false,
      [FurtherAddressTypesList.importer]: true,
    });
    const result = useShowAddButtons(items, showAddress.value);
    expect(result).toBe(true);
  });

  it('returns false when all items in items are present in showAddress', () => {
    const items = [
      { code: FurtherAddressTypesList.customsAgent },
      { code: FurtherAddressTypesList.importer },
    ];
    const showAddress = ref({
      [FurtherAddressTypesList.customsAgent]: true,
      [FurtherAddressTypesList.importer]: true,
    });
    const result = useShowAddButtons(items, showAddress.value);
    expect(result).toBe(false);
  });
});

describe('useCheckFurtherAddresses composable', () => {
  beforeAll(() => {
    initPinia();
  });

  it('updates showAddress correctly based on getFurtherAddress values', () => {
    const addressStore = useCreateOrderAddressesStore();
    addressStore.furtherAddresses = [
      {
        address: {
          name: 'Ray Sono AG',
          name2: 'rear annex',
          name3: 'string',
          street: 'Tumblinger Str. 32',
          postcode: '80337',
          city: 'Munich',
          countryCode: 'DE',
          supplement: 'Tel. 0815',
          gln: '4313920192301',
        },
        addressType: FurtherAddressTypesList.importer,
      },
    ];

    const showAddress = ref<ShowAddress>({
      [FurtherAddressTypesList.customsAgent]: false,
      [FurtherAddressTypesList.importer]: false,
      [FurtherAddressTypesList.N1]: false,
    });

    useCheckFurtherAddresses(showAddress.value);

    expect(showAddress.value[FurtherAddressTypesList.customsAgent]).toBe(false);
    expect(showAddress.value[FurtherAddressTypesList.importer]).toBe(true);
    expect(showAddress.value[FurtherAddressTypesList.N1]).toBe(false);
  });
});
