import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import MainFooter from '@/components/createOrder/formSectionFooter/MainFooter.vue';
import MoreButton from '@/components/createOrder/formSectionFooter/MoreButton.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import useValidateOrder from '@/composables/form/useValidateOrder';
import { OrderTypes, Routes } from '@/enums';
import { labels } from '@/mocks/fixtures/labels';
import { roadForwardingOrder } from '@/mocks/fixtures/order';
import { submitOrderResult } from '@/mocks/fixtures/submitOrderResult';
import { validationError } from '@/mocks/fixtures/validationResults';
import { mockServer } from '@/mocks/server';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import { useValidationDataStore } from '@/store/validation';
import { ClientKey } from '@/types/client';
import type { AirExportQuoteInformation, OrderProcessResult } from '@dfe/dfe-book-api';
import { OrderStatus } from '@dfe/dfe-book-api';
import { createClientMock } from '@test/util/mock-client';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import flushPromises from 'flush-promises';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import type { Server } from 'miragejs';
import { storeToRefs } from 'pinia';
import { afterEach, beforeAll, beforeEach, expect } from 'vitest';
import { nextTick } from 'vue';
import { VBtn } from 'vuetify/lib/components/index.mjs';

const createOrderValidateSpy = vi.fn();

vi.mock('@/composables/createOrder/useCreateOrderValidation', () => ({
  useCreateOrderValidation() {
    return {
      createOrderValidate: createOrderValidateSpy,
    };
  },
}));

describe('MainFooter component', () => {
  let wrapper: VueWrapper;
  let formStore: ReturnType<typeof useCreateOrderFormStore>;
  let server: Server;
  const client = createClientMock();

  beforeAll(() => {
    mockResizeObserver();

    server = mockServer({
      environment: 'test',
      fixtures: { roadForwardingOrder, submitOrderResult },
    });
  });

  beforeEach(() => {
    wrapper = shallowMount(MainFooter, {
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });

    formStore = useCreateOrderFormStore();
    createOrderValidateSpy.mockResolvedValue(true);
  });

  afterEach(() => {
    wrapper.unmount();
    formStore.$reset();
  });

  afterAll(() => {
    server.shutdown();
  });

  it('emits navigation if orderId is different after save', async () => {
    const formStore = useCreateOrderFormStore();

    vi.spyOn(formStore, 'saveOrder').mockResolvedValue({ saved: true, problem: null });
    vi.spyOn(client.router, 'remoteNavigate');
    vi.stubGlobal('location', { ...location, pathname: `${Routes.ORDER}${Routes.BOOK}` });

    formStore.saveOrderData = roadForwardingOrder;
    formStore.orderData = { ...roadForwardingOrder, orderId: 2 };

    wrapper.getComponent(MoreButton).vm.$emit('save-as-draft');

    await flushPromises();
    vi.unstubAllGlobals();

    expect(client.router.remoteNavigate).toHaveBeenCalledOnce();
  });

  it('should show a toast if avisSent is true', async () => {
    const toast = vi.spyOn(client.toast, 'success');

    if (roadForwardingOrder.orderStatus) {
      roadForwardingOrder.orderStatus.status = OrderStatus.SENT;
    }

    await wrapper.find("[data-test='book-book-btn']").trigger('click');

    await vi.waitFor(() => {
      expect(toast).toHaveBeenCalledWith('labels.avis_sent_success.text');
    });
  });

  it('should show a toast when an order is saved as draft', async () => {
    const toast = vi.spyOn(client.toast, 'success');

    const formStore = useCreateOrderFormStore();

    vi.spyOn(formStore, 'saveOrder').mockResolvedValue({ saved: true, problem: null });

    formStore.saveOrderData = roadForwardingOrder;
    formStore.customerNumber = '00000001';

    wrapper.getComponent(MoreButton).vm.$emit('save-as-draft');

    await flushPromises();

    expect(toast).toHaveBeenCalledWith(
      'labels.order_saved.text',
      {
        handler: expect.anything(),
        text: 'labels.order_view.text',
      },
      'create-order-success',
    );
  });

  it('hides checkbox to create another order if it was created by quote', async () => {
    expect(wrapper.findComponent(CheckboxField).exists()).toBe(true);

    formStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'checkbox-field' }).exists()).toBe(false);
    expect(wrapper.findComponent({ name: 'checkbox-field' }).exists()).toBe(false);
  });

  it('show error dialog when order has Generic error', async () => {
    const formStore = useCreateOrderFormStore();

    vi.spyOn(formStore, 'saveOrder').mockResolvedValue({
      saved: false,
      problem: {
        oasDiscriminator: 'GeneralProblem',
        type: 'https://example.com/probs/technical-error',
        errorId: 'errRT-01',
        title: 'Technical Error',
        status: 502,
        detail: 'A technical error occurred.',
        severity: 'Critical',
        timestamp: '2023-10-01T00:00:00Z',
      },
    });
    wrapper.getComponent(MoreButton).vm.$emit('save-as-draft');

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'error-prompt' }).exists()).toBe(true);
  });

  it('hides checkbox to create another order if order form is in edit mode', async () => {
    expect(wrapper.findComponent(CheckboxField).exists()).toBe(true);

    formStore.quoteInformation = null;
    formStore.isEditMode = true;
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(CheckboxField).exists()).toBe(false);
  });

  it('emits printLabels event - on submitSendOrder', async () => {
    const createOrderFormStore = useCreateOrderFormStore();

    // Register spys
    const submitOrderSpy = vi.spyOn(createOrderFormStore, 'submitOrder');

    const clearCreateOrderFormDataEvent = vi.fn();
    client.events.on('clearCreateOrderFormData', clearCreateOrderFormDataEvent);

    const printLabelsEvent = vi.fn();
    client.events.on('printLabels', printLabelsEvent);

    // Action
    wrapper.findComponent<typeof VBtn>('[data-test=book-book-btn]').trigger('click');
    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      // Just wait for async operation to finish
    });

    await vi.waitFor(() => {
      // Expect fetchLabels to be called
      expect(submitOrderSpy).toHaveBeenCalledTimes(1);
    });

    await vi.waitFor(() => {
      // Expect clearCreateOrderFormDataEvent event to be emitted
      expect(clearCreateOrderFormDataEvent).toHaveBeenCalledTimes(1);

      // Expect printLabels event to be emitted
      expect(printLabelsEvent).toHaveBeenCalledTimes(1);
      expect(printLabelsEvent).toHaveBeenCalledWith({
        file: labels[0].orderLabel,
        fileName: 'labels.pdf',
        modalHeadline: 'labels.labels_label.text',
        orderId: submitOrderResult.order?.orderId,
      });
    });
  });

  it('emits printLabels event - on printLabels', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const labelStore = useLabelStore();

    // Register spys
    const validateOrderSpy = vi
      .spyOn(createOrderFormStore, 'validateOrder')
      .mockResolvedValueOnce({ valid: true });

    vi.spyOn(labelStore, 'fetchLabels').mockResolvedValueOnce({
      orderLabel: labels[0].orderLabel,
      order: {
        orderId: submitOrderResult.order?.orderId,
        orderType: OrderTypes.RoadCollectionOrder,
      },
    });

    const clearCreateOrderFormDataEvent = vi.fn();
    client.events.on('clearCreateOrderFormData', clearCreateOrderFormDataEvent);

    const printLabelsEvent = vi.fn();
    client.events.on('printLabels', printLabelsEvent);

    // Action
    const moreButton = wrapper.findComponent(MoreButton);
    await moreButton.trigger('save-print-labels');
    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      // Just wait for async operation to finish
    });

    await vi.waitFor(() => {
      // Expect fetchLabels to be called
      expect(validateOrderSpy).toHaveBeenCalledTimes(1);
    });

    await vi.waitFor(() => {
      // Expect printLabels event to be emitted
      expect(printLabelsEvent).toHaveBeenCalledTimes(1);
      expect(printLabelsEvent).toHaveBeenCalledWith({
        file: labels[0].orderLabel,
        fileName: 'labels.pdf',
        modalHeadline: 'labels.labels_label.text',
        orderId: submitOrderResult.order?.orderId,
      });
    });
  });

  it('adds frontend errors on failed frontend validation - on printLabels', async () => {
    const errorBannerStore = useErrorBanner();

    // Register spys
    createOrderValidateSpy.mockResolvedValueOnce(false);

    const errorBannerStoreSpy = vi.spyOn(errorBannerStore, 'addFrontendErrors');

    // Action
    const moreButton = wrapper.findComponent(MoreButton);
    await moreButton.trigger('save-print-labels');
    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      // Just wait for async operation to finish
    });

    await vi.waitFor(() => {
      // Expect fetchLabels to be called
      expect(errorBannerStoreSpy).toHaveBeenCalledTimes(1);
    });
  });

  it('adds errors on failed validation - on printLabels', async () => {
    const errorBannerStore = useErrorBanner();
    const createOrderFormStore = useCreateOrderFormStore();

    // Register spys
    vi.spyOn(createOrderFormStore, 'validateOrder').mockResolvedValueOnce({
      valid: false,
      results: validationError.results,
    });

    const errorBannerStoreSpy = vi.spyOn(errorBannerStore, 'pushErrors');

    // Action
    const moreButton = wrapper.findComponent(MoreButton);
    await moreButton.trigger('save-print-labels');
    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      // Just wait for async operation to finish
    });

    await vi.waitFor(() => {
      // Expect fetchLabels to be called
      expect(errorBannerStoreSpy).toHaveBeenCalledTimes(1);
    });
  });

  it('trigger error modal on fetch labels error - on printLabels', async () => {
    const labelStore = useLabelStore();

    // Register spys
    vi.spyOn(labelStore, 'fetchLabels').mockRejectedValueOnce({
      data: null,
      error: {
        order: null,
        orderLabel: null,
        validationResult: null,
        error: {
          title: 'title',
          detail: 'detail',
        },
      },
    });

    const modalSpy = vi.spyOn(client.modal, 'show');

    // Action
    const moreButton = wrapper.findComponent(MoreButton);
    await moreButton.trigger('save-print-labels');
    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      // Just wait for async operation to finish
    });

    await vi.waitFor(() => {
      // Expect fetchLabels to be called
      expect(modalSpy).toHaveBeenCalledTimes(1);
    });
  });

  it('should set isValidationTriggered to true, if submitSendOrder is called', async () => {
    expect(formStore.isValidationTriggered).toBe(false);
    await wrapper.find("[data-test='book-book-btn']").trigger('click');
    expect(formStore.isValidationTriggered).toBe(true);
  });

  it('should set isValidationTriggered to true, if finishOrder is called', async () => {
    expect(formStore.isValidationTriggered).toBe(false);
    await wrapper.find("[data-test='book-finish-btn']").trigger('click');
    expect(formStore.isValidationTriggered).toBe(true);
  });

  it('should not show the discard changes overlay when it has no unsaved changes', async () => {
    const formStore = useCreateOrderFormStore();
    const { initialOrder } = storeToRefs(formStore);
    const { order } = useValidateOrder();

    initialOrder.value = { ...order.value };

    const confirmPrompt = wrapper.findComponent({ ref: 'discard-changes-prompt' });

    client.events.emit('preventUnsavedChanges');

    await wrapper.vm.$nextTick();

    expect(confirmPrompt.vm.$props.modelValue).toBe(false);
  });

  it('should show the discard changes overlay when it has unsaved changes', async () => {
    const formStore = useCreateOrderFormStore();
    const { initialOrder } = storeToRefs(formStore);
    const { order } = useValidateOrder();

    initialOrder.value = { ...order.value };

    const confirmPrompt = wrapper.findComponent({ ref: 'discard-changes-prompt' });

    formStore.customerNumber = '00000002';

    client.events.emit('preventUnsavedChanges');

    await wrapper.vm.$nextTick();

    expect(confirmPrompt.vm.$props.modelValue).toBe(true);
  });

  it('should push errors into errorBannerStore, if validationResult is invalid', async () => {
    const errorBannerStore = useErrorBanner();

    const newSubmitOrderResult: OrderProcessResult = {
      ...submitOrderResult,
      validationResult: {
        valid: false,
        results: [
          {
            field: 'test',
            errorType: 'REQUIRED_FIELD_MISSING',
            description: 'test',
          },
        ],
      },
    };

    server.shutdown();

    server = mockServer({
      environment: 'test',
      fixtures: { submitOrderResult: { ...newSubmitOrderResult } },
    });

    await nextTick();

    await wrapper.find('[data-test=book-book-btn]').trigger('click');

    await vi.waitFor(() => {
      expect(errorBannerStore.validationErrors.length).toBe(1);
    });
  });

  it('should add frontendErrors', async () => {
    const errorBannerStore = useErrorBanner();
    const { formValidationSectionsRoad } = storeToRefs(useValidationDataStore());

    createOrderValidateSpy.mockResolvedValueOnce(false);
    formValidationSectionsRoad.value.roadOrderAddresses = false;

    await wrapper.vm.$nextTick();

    await wrapper.find('[data-test=book-finish-btn]').trigger('click');

    await flushPromises();
    await wrapper.vm.$nextTick();

    expect(errorBannerStore.hasFrontendErrors).toBeTruthy();
  });

  it('should show the discard changes overlay when it has document changes', async () => {
    const { documents, initialDocuments } = storeToRefs(useCreateOrderDocumentsStore());
    const { initialOrder } = storeToRefs(formStore);
    const { order } = useValidateOrder();

    initialOrder.value = { ...order.value };

    documents.value = [
      {
        documentId: 1,
        documentName: 'invoice.xlsx',
        documentType: 'ABC',
        extension: 'xlsx',
        startProcessing: true,
      },
    ];

    initialDocuments.value = [...documents.value];

    initialOrder.value.documentIds = [1];

    const confirmPrompt = wrapper.findComponent({ ref: 'discard-changes-prompt' });

    client.events.emit('preventUnsavedChanges');

    await wrapper.vm.$nextTick();

    expect(confirmPrompt.vm.$props.modelValue).toBe(false);

    documents.value = [
      {
        documentId: 1,
        documentName: 'invoice.xlsx',
        documentType: 'DEF',
        extension: 'xlsx',
        startProcessing: true,
      },
    ];

    await wrapper.vm.$nextTick();

    client.events.emit('preventUnsavedChanges');

    await wrapper.vm.$nextTick();

    expect(confirmPrompt.vm.$props.modelValue).toBe(true);
  });

  it('should emit clearCreateOrderFormData and close overlay when confirming the unsaved changes modal', async () => {
    const formStore = useCreateOrderFormStore();
    const event = vi.fn();

    vi.spyOn(client.events, 'emit').mockImplementation(event);

    formStore.createAnother = true;

    wrapper.getComponent(ConfirmPrompt).vm.$emit('confirm');

    expect(formStore.createAnother).toBe(false);
    expect(event).toHaveBeenCalledTimes(2);
  });
});
