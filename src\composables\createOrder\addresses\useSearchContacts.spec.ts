import { describe, it, expect, beforeEach } from 'vitest';
import { useSearchContact } from '@/composables/createOrder/addresses/useSearchContact';
import { ContactData } from '@dfe/dfe-book-api';

describe('useSearchContact composable', () => {
  let searchContact = {} as ReturnType<typeof useSearchContact>;

  beforeEach(() => {
    searchContact = useSearchContact({
      contacts: [
        { name: 'Alpha' } as ContactData,
        { name: 'Bravo' } as ContactData,
        { name: '<PERSON>' } as ContactData,
      ],
    });
  });

  it('should return all contacts when search term is empty', async () => {
    searchContact.searchTerm.value = '';
    await searchContact.triggerForceSearch();
    await vi.waitFor(() => {
      expect(searchContact.contacts.value.length).toBe(3);
    });
  });

  it('should return matching contact when search term is provided', async () => {
    searchContact.searchTerm.value = 'Bravo';
    await searchContact.triggerForceSearch();
    await vi.waitFor(() => {
      expect(searchContact.contacts.value).toEqual([{ name: 'Bravo' } as ContactData]);
    });
  });

  it('should return no contacts when search term does not match', async () => {
    searchContact.searchTerm.value = 'Delta';
    await searchContact.triggerForceSearch();
    expect(searchContact.contacts.value).toEqual([]);
  });

  it('should handle undefined contacts gracefully', async () => {
    const searchContactWithUndefined = useSearchContact({ contacts: undefined });
    searchContactWithUndefined.searchTerm.value = 'Alpha';
    await searchContactWithUndefined.triggerForceSearch();
    expect(searchContactWithUndefined.contacts.value).toEqual([]);
  });

  it('should not change isLoading state in case of immediate search result', async () => {
    searchContact.searchTerm.value = 'Alpha';
    await searchContact.triggerForceSearch();
    expect(searchContact.isLoading.value).toBe(false);
  });
});
