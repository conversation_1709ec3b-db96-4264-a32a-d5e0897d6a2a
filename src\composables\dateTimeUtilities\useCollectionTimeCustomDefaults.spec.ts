import useCollectionTimeCustomDefaults from '@/composables/dateTimeUtilities/useCollectionTimeCustomDefaults';

describe('useCollectionTimeCustomDefaults composable', () => {
  it('returns an object with default times for collection time (24h)', () => {
    expect(useCollectionTimeCustomDefaults('HH:mm')).toEqual({
      from: `08:00`,
      to: `16:00`,
    });
  });

  it('returns an object with default times for collection time (12h)', () => {
    expect(useCollectionTimeCustomDefaults('hh:mm a')).toEqual({
      from: `08:00 AM`,
      to: `04:00 PM`,
    });
  });
});
