/* eslint @typescript-eslint/no-explicit-any: 0 */
import { mount, VueWrapper } from '@vue/test-utils';
import { afterAll, afterEach, beforeEach, describe, expect } from 'vitest';
import DfeDataTable from '@/components/tableComponent/DfeDataTable.vue';
import {
  DfeColumnAlignment,
  DfeTableHeader,
  DfeTableOptions,
  DfeTableSortDirection,
} from '@/components/tableComponent/DfeTableTypes';
import { VPagination, VSelect } from 'vuetify/components';
import { mockResizeObserver } from 'jsdom-testing-mocks';

const mockFormattingFunction = vi.fn((value: string) => value.toUpperCase());

const mockHeaders: DfeTableHeader[] = [
  {
    key: 'column1',
    label: 'Column 1',
    visible: true,
    sortable: true,
    minWidth: 100,
    justification: DfeColumnAlignment.LEFT,
    noLink: true,
  },
  {
    key: 'column2',
    label: 'Column 2',
    visible: true,
    sortable: false,
    minWidth: 150,
    justification: DfeColumnAlignment.CENTER,
  },
  {
    key: 'column3',
    label: 'Column 3',
    visible: true,
    sortable: true,
    minWidth: 120,
    justification: DfeColumnAlignment.RIGHT,
    component: () => {
      return "<div class='test-component'>test component</div>";
    },
  },
  {
    key: 'column4',
    label: 'Column 4',
    visible: true,
    sortable: false,
    minWidth: 200,
    justification: DfeColumnAlignment.LEFT,
    fixedWidth: 200,
    formatting: mockFormattingFunction,
  },
];

const tableOptionsWithStickyColumn: DfeTableOptions = {
  totalItems: 100,
  itemsPerPage: 10,
  currentPage: 1,
  totalPages: 10,
  allSelectable: true,
  itemsSelectable: true,
  firstColumnSticky: true,
};

const tableOptionsWithoutStickyColumn: DfeTableOptions = {
  totalItems: 50,
  itemsPerPage: 5,
  currentPage: 1,
  totalPages: 10,
  allSelectable: false,
  itemsSelectable: false,
  firstColumnSticky: false,
};

const mockItems = [
  {
    link: 'https://example.com/item1',
    column1: 'Item 1A',
    column2: 'Item 1B',
    column3: 'Item 1C',
    column4: 'Item 1D',
  },
  {
    column1: 'Item 2A',
    column2: 'Item 2B',
    column3: 'Item 2C',
    column4: 'Item 2D',
    link: 'https://example.com/item2',
  },
  {
    column1: 'Item 3A',
    column2: 'Item 3B',
    column3: 'Item 3C',
    column4: 'Item 3D',
    link: 'https://example.com/item3',
  },
  {
    column1: 'Item 4A',
    column2: 'Item 4B',
    column3: 'Item 4C',
    column4: 'Item 4D',
    link: 'https://example.com/item4',
  },
];

const testProps = {
  headers: mockHeaders,
  tableOptions: tableOptionsWithStickyColumn,
  items: mockItems,
  trackingPrefix: 'testPrefix',
};

describe('DfeDataTable', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(DfeDataTable, {
      props: testProps,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  it('mounts', () => {
    expect(wrapper).toBeDefined();
  });

  describe('table shows items', () => {
    it('shows all columns', () => {
      expect(wrapper.html()).includes('Column 1');
      expect(wrapper.html()).includes('Column 2');
      expect(wrapper.html()).includes('Column 3');
      expect(wrapper.html()).includes('Column 4');
    });

    it('shows all items', () => {
      expect(wrapper.html()).includes('Item 1A');
      expect(wrapper.html()).includes('Item 2A');
      expect(wrapper.html()).includes('Item 3A');
      expect(wrapper.html()).includes('Item 4A');
    });

    it('shows the correct number of items', () => {
      const items = wrapper.find('tbody').findAll('tr');
      expect(items.length).toBe(mockItems.length);
    });

    it('shows the correct number of columns', () => {
      const columns = wrapper.findAll('th');
      expect(columns.length).toBe(mockHeaders.length + 1);
    });

    it('shows table footer', () => {
      const footer = wrapper.find('tfoot');

      expect(footer.exists()).toBe(true);
    });
  });

  describe('table without items', () => {
    beforeEach(() => {
      wrapper = mount(DfeDataTable, {
        props: {
          ...testProps,
          items: [],
        },
        slots: {
          emptyTable: `<div class="empty-state">No items available</div>`,
        } as any,
      });
    });

    it('shows no items', () => {
      const body = wrapper.find('tbody');

      expect(body.exists()).toBe(false);
    });

    it('shows no columns', () => {
      const columns = wrapper.findAll('th');
      expect(columns.length).toBe(mockHeaders.length + 1);
    });

    it('shows empty state', () => {
      const emptyState = wrapper.find('#empty-table-message');

      expect(emptyState.exists()).toBe(true);
      expect(emptyState.text()).toBe('No items available');
    });
  });

  describe('table first column sticky', () => {
    describe('when firstColumnSticky is true', () => {
      it('should have sticky class on first column', () => {
        const firstColumn = wrapper.find('thead th:first-child');

        expect(firstColumn.classes()).toContain('position-sticky');
        expect(firstColumn.classes()).toContain('left-0');
      });

      it('should have sticky class on first column in table rows', () => {
        const firstColumn = wrapper.find('tbody tr:first-child td:first-child');
        expect(firstColumn.classes()).toContain('position-sticky');
        expect(firstColumn.classes()).toContain('left-0');
      });
    });

    describe('when firstColumnSticky is false', () => {
      beforeEach(async () => {
        await wrapper.setProps({
          ...testProps,
          tableOptions: tableOptionsWithoutStickyColumn,
        });
      });

      it('should not have sticky class if firstColumnSticky is false', async () => {
        const firstColumn = wrapper.find('thead th:first-child');

        expect(firstColumn.classes()).toContain('position-sticky');
        expect(firstColumn.classes()).not.toContain('left-0');
      });

      it('should not have sticky class on first column in table rows', async () => {
        const firstColumn = wrapper.find('tbody tr:first-child td:first-child');
        expect(firstColumn.classes()).not.toContain('position-sticky');
        expect(firstColumn.classes()).not.toContain('left-0');
      });
    });
  });

  describe('table checkboxes and item selection', () => {
    it('shows select all checkbox', () => {
      const selectAllCheckbox = wrapper.find("thead input[type='checkbox']");

      expect(selectAllCheckbox.exists()).toBe(true);
    });

    it('shows item checkboxes', () => {
      const itemCheckboxes = wrapper.findAll("tbody input[type='checkbox']");

      expect(itemCheckboxes.length).toBe(mockItems.length);
    });

    it('emits event on select all checkbox click', async () => {
      (wrapper.vm as any).onSelectAll();

      expect(wrapper.emitted('selectItems')?.length).toBe(1);
      expect(wrapper.emitted('selectItems')?.at(0)?.at(0)).toEqual(mockItems);
    });

    it('emits event on item checkbox click', async () => {
      (wrapper.vm as any).onSelectItem(0);

      expect(wrapper.emitted('selectItems')?.length).toBe(1);
      expect(wrapper.emitted('selectItems')?.at(0)?.at(0)).toEqual([mockItems[0]]);
    });

    describe('doesnt show select all checkbox', () => {
      beforeEach(() => {
        wrapper = mount(DfeDataTable, {
          props: {
            ...testProps,
            tableOptions: tableOptionsWithoutStickyColumn,
          },
        });
      });

      it('does not show select all checkbox', () => {
        const selectAllCheckbox = wrapper.find("thead input[type='checkbox']");

        expect(selectAllCheckbox.exists()).toBe(false);
      });
    });

    describe('shows selectAll slot instead of select all checkbox', () => {
      beforeEach(() => {
        wrapper = mount(DfeDataTable, {
          props: {
            ...testProps,
            tableOptions: { ...tableOptionsWithoutStickyColumn, allSelectable: true },
          },
          slots: {
            selectAllColumn: `<div class="select-all">Select All</div>`,
          } as any,
        });
      });

      it('shows selectAll slot', () => {
        const selectAllSlot = wrapper.find('.select-all');

        expect(selectAllSlot.exists()).toBe(true);
        expect(selectAllSlot.text()).toBe('Select All');
      });
    });
  });

  describe('table caption', () => {
    beforeEach(() => {
      wrapper = mount(DfeDataTable, {
        props: {
          ...testProps,
        },
        slots: {
          caption: `<div class="caption">Table Caption</div>`,
        } as any,
      });
    });

    it('shows caption', () => {
      const caption = wrapper.find('caption');

      expect(caption.exists()).toBe(true);
      expect(caption.text()).toBe('Table Caption');
    });
  });

  describe('column sorting', () => {
    it('only sortable columns are sortable', () => {
      const headers = wrapper.findAll('th');
      const sortableColumns = headers.at(1)?.find('button');
      const nonSortableColumns = headers.at(2)?.find('button');

      sortableColumns?.trigger('click');
      nonSortableColumns?.trigger('click');

      expect(nonSortableColumns?.classes()).toContain('no-hover');
      expect(wrapper.emitted('updateOptions')?.length).toBe(1);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        sortBy: { key: 'column1', order: DfeTableSortDirection.DESC },
      });
    });

    it('sorts in ascending order', () => {
      const headers = wrapper.findAll('th');
      const sortableColumn = headers.at(1)?.find('button');

      sortableColumn?.trigger('click');
      sortableColumn?.trigger('click');

      expect(wrapper.emitted('updateOptions')?.length).toBe(2);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        sortBy: { key: 'column1', order: DfeTableSortDirection.ASC },
      });
    });
  });

  describe('cell link', () => {
    it('does not show link in cell', () => {
      const firstRow = wrapper.find('tbody tr:first-child');
      const link = firstRow.findAll('td').at(1);

      expect(link?.find('a').exists()).toBe(false);
      expect(link?.text()).toContain('Item 1A');
    });
  });

  describe('row actionMenu', () => {
    beforeEach(() => {
      wrapper = mount(DfeDataTable, {
        props: {
          ...testProps,
        },
        slots: {
          itemActionMenu: `<div class="action-menu">Action Menu</div>`,
        } as any,
      });
    });

    it('shows action menu', () => {
      const actionMenu = wrapper.find('.action-menu');

      expect(actionMenu.exists()).toBe(true);
      expect(actionMenu.text()).toBe('Action Menu');
    });
  });

  describe('cell formatting', () => {
    it('formats cell value', () => {
      const firstRow = wrapper.find('tbody tr:first-child');
      const formattedCell = firstRow.findAll('td').at(4);

      expect(formattedCell?.text()).toBe('ITEM 1D');
      expect(mockFormattingFunction).toHaveBeenCalledWith('Item 1D');
    });

    it('does not format cell value if no formatting function is provided', () => {
      const firstRow = wrapper.find('tbody tr:first-child');
      const unformattedCell = firstRow.findAll('td').at(2);

      expect(unformattedCell?.text()).toBe('Item 1B');
    });
  });

  describe('cell component', () => {
    it('renders custom component in cell', () => {
      const firstRow = wrapper.find('tbody tr:first-child');
      const columnWithCustomComponent = firstRow.findAll('td')?.at(3);

      expect(wrapper.html()).toContain('test-component');
      expect(columnWithCustomComponent?.text()).toBe(
        "<div class='test-component'>test component</div>",
      );
    });
  });

  describe('table footer', () => {
    it('shows correct number of items in footer', () => {
      const footer = wrapper.find('tfoot');
      const itemsCount = footer.find('label');

      expect(itemsCount.text()).toBe('labels.rows_per_page.text');
      expect(footer.findComponent(VSelect).text()).toBe('10');
    });

    it('shows current page in footer', () => {
      const tablePage = wrapper.find('#selected-table-page');

      expect(tablePage.exists()).toBe(true);
      expect(tablePage.text()).toBe('1–4 labels.of_label.text 100');
    });
  });

  describe('pagination', () => {
    it('shows pagination', () => {
      const pagination = wrapper.findComponent(VPagination);

      expect(pagination.exists()).toBe(true);
    });

    it('emits event on first page click ', async () => {
      await wrapper.setProps({
        ...testProps,
        tableOptions: { ...tableOptionsWithStickyColumn, currentPage: 3 },
      });

      const pagination = wrapper
        .findComponent(VPagination)
        .find('.v-pagination__first')
        .find('button');

      await pagination.trigger('click');

      expect(wrapper.emitted('updateOptions')?.length).toBe(1);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        currentPage: 1,
      });
    });

    it('emits event on prev page click', async () => {
      await wrapper.setProps({
        ...testProps,
        tableOptions: { ...tableOptionsWithStickyColumn, currentPage: 3 },
      });

      const pagination = wrapper
        .findComponent(VPagination)
        .find('.v-pagination__prev')
        .find('button');

      await pagination.trigger('click');

      expect(wrapper.emitted('updateOptions')?.length).toBe(1);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        currentPage: 2,
      });
    });

    it('emits event on next page click', async () => {
      await wrapper.setProps({
        ...testProps,
        tableOptions: { ...tableOptionsWithStickyColumn, currentPage: 3 },
      });

      const pagination = wrapper
        .findComponent(VPagination)
        .find('.v-pagination__next')
        .find('button');

      await pagination.trigger('click');

      expect(wrapper.emitted('updateOptions')?.length).toBe(1);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        currentPage: 4,
      });
    });

    it('emits event on last page click', async () => {
      await wrapper.setProps({
        ...testProps,
        tableOptions: { ...tableOptionsWithStickyColumn, currentPage: 3 },
      });

      const pagination = wrapper
        .findComponent(VPagination)
        .find('.v-pagination__last')
        .find('button');

      await pagination.trigger('click');

      expect(wrapper.emitted('updateOptions')?.length).toBe(1);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        currentPage: 10,
      });
    });
  });

  describe('page size select', () => {
    it('shows select page size', () => {
      const selectPageSize = wrapper.findComponent(VSelect);

      expect(selectPageSize.exists()).toBe(true);
    });

    it('emits event on page size change', async () => {
      const selectPageSize = wrapper.findComponent(VSelect);

      await selectPageSize.setValue(20);

      expect(wrapper.emitted('updateOptions')?.length).toBe(1);
      expect(wrapper.emitted('updateOptions')?.at(0)?.at(0)).toEqual({
        ...tableOptionsWithStickyColumn,
        itemsPerPage: 20,
        currentPage: 1,
      });
    });
  });
});
