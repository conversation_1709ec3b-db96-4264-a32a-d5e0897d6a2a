import useValidateOrder from '@/composables/form/useValidateOrder';
import { ErrorCode } from '@/enums';
import { airExportOrder } from '@/mocks/fixtures/order';
import { validationError, validationSuccess } from '@/mocks/fixtures/validationResults';
import { mockServer } from '@/mocks/server';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { GeneralProblem, OrderReferenceType } from '@dfe/dfe-book-api';
import { initPinia } from '../../../test/util/init-pinia';

describe('useValidateOrder', () => {
  let orderValidate: ReturnType<typeof useValidateOrder>;

  beforeEach(() => {
    initPinia();

    orderValidate = useValidateOrder();
  });

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        validationSuccess,
        validationError,
      },
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should call validateOrder', async () => {
    const validateOrderSpy = vi.spyOn(orderValidate, 'validateOrder');

    await orderValidate.validateOrder();

    expect(validateOrderSpy).toHaveBeenCalledTimes(1);
  });

  it('should add a required delivery note number field', async () => {
    const createOrderDocuments = useCreateOrderDocumentsStore();
    const { documents } = createOrderDocuments;

    const { deliveryNoteNumberRequiredFields } = useCreateOrderOrderReferencesFormStore();

    const { setIsDeliveryNoteNumberFieldRequired } = useValidateOrder();

    documents.push({
      documentType: 'edn',
      documentName: 'EDN.pdf',
      extension: 'pdf',
      startProcessing: true,
    });

    setIsDeliveryNoteNumberFieldRequired();

    expect(deliveryNoteNumberRequiredFields).toHaveLength(1);
    expect(deliveryNoteNumberRequiredFields[0].required).toBeTruthy();
  });

  it('should set an existing delivery note number field to required', async () => {
    const createOrderDocuments = useCreateOrderDocumentsStore();
    const { documents } = createOrderDocuments;
    const { deliveryNoteNumberRequiredFields } = useCreateOrderOrderReferencesFormStore();
    const formOrderReferencesStore = useCreateOrderOrderReferencesFormStore();
    const { setIsDeliveryNoteNumberFieldRequired } = useValidateOrder();

    formOrderReferencesStore.addReference(OrderReferenceType.DELIVERY_NOTE_NUMBER, {
      setFocus: false,
    });

    expect(deliveryNoteNumberRequiredFields[0].required).toBeFalsy();

    documents.push({
      documentType: 'edn',
      documentName: 'EDN.pdf',
      extension: 'pdf',
      startProcessing: true,
    });

    setIsDeliveryNoteNumberFieldRequired();

    expect(deliveryNoteNumberRequiredFields[0].required).toBeTruthy();
  });

  it('should return Generic error in validate an order api', async () => {
    const store = useCreateOrderFormStore();
    store.api.book.v2.saveOrderV2 = vi.fn().mockRejectedValue({
      error: {
        oasDiscriminator: 'JsonGeneralProblem',
        type: 'book:about:blank',
        errorId: ErrorCode.OrderExpiryErrorCode,
        title: 'label.text.title_sv_04',
        status: 409,
        detail: 'label.text.get_a_new_quote',
        severity: 'High',
        traceId: null,
        timestamp: '2025-02-10T14:40:56.736330138Z',
      },
    });

    const validationResult = (await store.validateOrder(airExportOrder)) as GeneralProblem;

    expect(validationResult.errorId).toEqual(ErrorCode.OrderExpiryErrorCode);
  });
});
