import CardMain from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import PortRouting from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortRouting.vue';
import AddressesSectionWithHandOver from '@/components/createOrder/formSectionAddresses/handoverSelection/AddressesSectionWithHandOver.vue';
import { OrderTypes } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { contactDataMock } from '@/mocks/fixtures/contactData';
import { customersWithAddresses } from '@/mocks/fixtures/customers';
import { airExportOrder } from '@/mocks/fixtures/order';
import { mockServer } from '@/mocks/server';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import { HandOverSelection } from '@/types/hand-over';
import type { ContactData } from '@dfe/dfe-address-api';
import type {
  AirExportQuoteInformation,
  AirImportQuoteInformation,
  Port,
  SeaExportQuoteInformation,
} from '@dfe/dfe-book-api';
import { initPinia } from '@test/util/init-pinia';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import { afterEach, beforeEach } from 'vitest';
import { nextTick } from 'vue';
import HandOver from './HandOver.vue';

const setCustomerData = async () => {
  const { customerNumber } = storeToRefs(useCreateOrderFormStore());
  const { customers } = storeToRefs(useCreateOrderDataStore());

  if (customers && customers.value) {
    customers.value = customersWithAddresses;
  }

  customerNumber.value = '00000001';

  await nextTick();
};

const validAddressHandover = () => ({ ...orderAddress(), postcode: '12345', countryCode: 'DE' });

describe('AddressesSectionWithHandOver component', () => {
  let wrapper: VueWrapper;
  let formStore: ReturnType<typeof useCreateOrderFormStore>;
  let addressStore: ReturnType<typeof useCreateOrderAddressesStore>;
  let dataStore: ReturnType<typeof useCreateOrderDataStore>;

  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {
        addresses,
        airExportOrder,
        contactData: contactDataMock,
      },
    });
  });

  beforeEach(async () => {
    wrapper = mount(AddressesSectionWithHandOver);
    formStore = useCreateOrderFormStore();
    addressStore = useCreateOrderAddressesStore();
    dataStore = useCreateOrderDataStore();

    await setCustomerData();
  });

  afterEach(() => {
    wrapper.unmount();
    formStore.$reset();
    addressStore.$reset();
    dataStore.$reset();
  });

  it('shows addresses form section', () => {
    expect(wrapper.findComponent(CardMain).exists()).toBe(true);
  });

  it('shows routings - air', async () => {
    const { orderType } = storeToRefs(formStore);

    orderType.value = OrderTypes.AirExportOrder;

    await nextTick();

    expect(wrapper.findComponent(PortRouting).exists()).toBe(true);
  });

  it('shows routings - sea', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.SeaExportOrder;

    await nextTick();

    expect(wrapper.findComponent(PortRouting).exists()).toBe(true);
  });

  it('should reset fromPortRouting on delete shipper handover selection', async () => {
    const { fromPortRouting } = storeToRefs(dataStore);
    const { shipperHandOverSelection, fromIATA } = storeToRefs(addressStore);
    const { orderType } = storeToRefs(formStore);

    orderType.value = OrderTypes.AirExportOrder;
    shipperHandOverSelection.value.selection = HandOverSelection.alternateAddress;

    fromIATA.value = <Port>airExportOrder.fromIATA;
    fromPortRouting.value = [<Port>airExportOrder.fromIATA];

    await nextTick();

    const shipperAddressCard = wrapper.findAllComponents(HandOver).at(0);

    shipperAddressCard?.vm.$emit('delete-address');

    await nextTick();

    expect(fromPortRouting.value).toEqual([]);
    expect(fromIATA.value).toBeUndefined();
  });

  it('should reset toPortRouting on delete consignee handover selection', async () => {
    const { toPortRouting } = storeToRefs(dataStore);
    const { consigneeHandOverSelection, toIATA } = storeToRefs(addressStore);
    const { orderType } = storeToRefs(formStore);

    orderType.value = OrderTypes.AirExportOrder;
    consigneeHandOverSelection.value.selection = HandOverSelection.alternateAddress;

    toIATA.value = <Port>airExportOrder.toIATA;
    toPortRouting.value = [<Port>airExportOrder.toIATA];

    const consigneeAddressCard = wrapper.findAllComponents(HandOver).at(1);

    consigneeAddressCard?.vm.$emit('delete-address');

    await nextTick();

    expect(toPortRouting.value).toEqual([]);
    expect(toIATA.value).toBeUndefined();
  });

  describe('updateToData', () => {
    it('sets consigneeHandOverSelection in addressStore (when switching between radio buttons)', async () => {
      // Default state
      expect(addressStore.consigneeHandOverSelection.selection).toEqual(HandOverSelection.default);

      // Switch to alternate address
      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');
      handoverComp?.vm.$emit('update:model-value', {
        address: { ...validAddressHandover() },
        selection: HandOverSelection.alternateAddress,
      });
      await nextTick();

      expect(addressStore.consigneeHandOverSelection.selection).toEqual(
        HandOverSelection.alternateAddress,
      );

      // Switch back to default
      await handoverComp?.vm.$emit('update:model-value', {
        selection: HandOverSelection.default,
      });
      await nextTick();

      expect(addressStore.consigneeHandOverSelection.selection).toStrictEqual(
        HandOverSelection.default,
      );

      // Switch to port
      await handoverComp?.vm.$emit('update:model-value', {
        selection: HandOverSelection.port,
        port: null,
      });
      await nextTick();

      expect(addressStore.consigneeHandOverSelection.selection).toEqual(HandOverSelection.port);
    });

    it('moves consignee address & contact from default to alternate delivery – for air export orders from quote', async () => {
      addressStore.$reset();

      const addressDataStore = useCreateAddressDataStore();

      const { orderType, quoteInformation } = storeToRefs(formStore);

      const { consigneeAddress, contactDataConsignee, consigneeHandOverSelection } =
        storeToRefs(addressStore);

      orderType.value = OrderTypes.AirExportOrder;
      quoteInformation.value = {
        orderType: OrderTypes.AirExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as AirExportQuoteInformation;

      await nextTick();

      consigneeAddress.value = {
        address: { ...addresses[1] },
      };
      contactDataConsignee.value = { ...contactDataMock };

      await nextTick();

      const consigneeAddressCard = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

      consigneeAddressCard?.vm.$emit('update:modelValue', {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      });
      await nextTick();

      expect(consigneeAddress.value.address).toEqual(orderAddress());
      expect(contactDataConsignee.value).toEqual(contactData());

      expect(consigneeHandOverSelection.value).toEqual({
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      });
      // TODO COMPARE WITH
      // expect(contactsAlternativeDelivery.value[0]).toEqual(contactDataMock);
      expect(addressDataStore.contactsAlternativeDelivery[0]).toEqual(contactDataMock);

      addressStore.$reset();
      formStore.$reset();
      addressDataStore.$reset();
    });

    it('Port selection – for sea export orders from quote - consignee', async () => {
      const addressStore = useCreateOrderAddressesStore();
      const formStore = useCreateOrderFormStore();
      const addressDataStore = useCreateAddressDataStore();

      formStore.orderType = OrderTypes.SeaExportOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.SeaExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as SeaExportQuoteInformation;
      await nextTick();

      const handoverComp = wrapper.findComponent<typeof HandOver>(
        '[data-test=book-consignee-hand-over]',
      );
      handoverComp.vm.$emit('update:model-value', {
        selection: HandOverSelection.port,
      });
      await nextTick();

      expect(addressStore.consigneeHandOverSelection).toEqual({
        selection: HandOverSelection.port,
      });

      addressStore.$reset();
      formStore.$reset();
      addressDataStore.$reset();
    });

    it('Default selection – for sea export orders from quote - consignee', async () => {
      const addressStore = useCreateOrderAddressesStore();
      const formStore = useCreateOrderFormStore();
      const addressDataStore = useCreateAddressDataStore();

      formStore.orderType = OrderTypes.SeaExportOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.SeaExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as SeaExportQuoteInformation;
      await nextTick();

      const handoverComp = wrapper.findComponent<typeof HandOver>(
        '[data-test=book-consignee-hand-over]',
      );
      handoverComp.vm.$emit('input', {});
      await nextTick();

      expect(addressStore.consigneeHandOverSelection.selection).toEqual(HandOverSelection.default);

      addressStore.$reset();
      formStore.$reset();
      addressDataStore.$reset();
    });

    it('Port selection – for sea export orders from quote - shipper', async () => {
      const addressStore = useCreateOrderAddressesStore();
      const formStore = useCreateOrderFormStore();
      const addressDataStore = useCreateAddressDataStore();

      formStore.orderType = OrderTypes.SeaExportOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.SeaExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as SeaExportQuoteInformation;
      await nextTick();

      const handoverComp = wrapper.findComponent<typeof HandOver>(
        '[data-test=book-shipper-hand-over]',
      );
      handoverComp.vm.$emit('update:model-value', {
        selection: HandOverSelection.port,
      });
      await nextTick();

      expect(addressStore.shipperHandOverSelection.selection).toEqual(HandOverSelection.port);

      addressStore.$reset();
      formStore.$reset();
      addressDataStore.$reset();
    });

    it('Default selection – for sea export orders from quote - shipper', async () => {
      const addressStore = useCreateOrderAddressesStore();
      const formStore = useCreateOrderFormStore();
      const addressDataStore = useCreateAddressDataStore();

      formStore.orderType = OrderTypes.SeaExportOrder;
      formStore.quoteInformation = {
        orderType: OrderTypes.SeaExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as SeaExportQuoteInformation;
      await nextTick();

      const handoverComp = wrapper.findComponent<typeof HandOver>(
        '[data-test=book-shipper-hand-over]',
      );

      handoverComp.vm.$emit(
        'update:model-value',
        {
          selection: HandOverSelection.default,
        },
        'from',
      );

      await nextTick();

      expect(addressStore.shipperHandOverSelection.selection).toEqual('defaultSelection');

      addressStore.$reset();
      formStore.$reset();
      addressDataStore.$reset();
    });

    it('moves address & contact from alternate delivery to default (consignee) – for air export orders from quote', async () => {
      const { orderType, quoteInformation } = storeToRefs(formStore);
      const { consigneeHandOverSelection, consigneeAddress, contactDataConsignee } =
        storeToRefs(addressStore);

      orderType.value = OrderTypes.AirExportOrder;
      quoteInformation.value = {
        orderType: OrderTypes.SeaExportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as SeaExportQuoteInformation;

      await nextTick();

      consigneeHandOverSelection.value = {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      };

      await nextTick();

      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

      await handoverComp?.vm.$emit('update:model-value', {
        selection: HandOverSelection.default,
      });
      await nextTick();

      expect(consigneeAddress.value.address).toEqual({
        ...addresses[1],
      });
      expect(contactDataConsignee.value).toEqual(contactDataMock);
      expect(consigneeHandOverSelection.value.selection).toEqual(HandOverSelection.default);
    });

    it('moves address & contact from alternate pickup to default (shipper) – for air export orders from quote', async () => {
      const { orderType, quoteInformation } = storeToRefs(formStore);
      const { shipperHandOverSelection, shipperAddress, contactDataShipper } =
        storeToRefs(addressStore);

      orderType.value = OrderTypes.AirImportOrder;
      quoteInformation.value = {
        orderType: OrderTypes.AirImportOrder,
        quoteExpiryDate: '2124-12-31',
        customerNumber: '03011149',
        collectionDate: '2024-12-31',
        termCode: 'CFR',
        quoteRequestId: 123,
      } as AirImportQuoteInformation;

      await nextTick();

      shipperHandOverSelection.value = {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      };

      await nextTick();

      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-shipper-hand-over');

      await handoverComp?.vm.$emit('update:model-value', {
        selection: HandOverSelection.default,
      });
      await nextTick();

      expect(shipperAddress.value.address).toEqual({
        ...addresses[1],
      });
      expect(contactDataShipper.value).toEqual(contactDataMock);
      expect(shipperHandOverSelection.value.selection).toEqual(HandOverSelection.default);
    });
  });

  describe('update alternate address and contact data', () => {
    const updatedContactData: ContactData = {
      id: 2,
      name: 'newName',
      email: 'newEmail',
      telephone: 'newTelephone',
      mobile: 'newMobile',
    };

    it('updates alternate from-address / pickup address (updateShipperAlternateAddress)', async () => {
      addressStore.shipperHandOverSelection = {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      };
      await nextTick();

      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-shipper-hand-over');
      await handoverComp?.vm.$emit('update:alternate-address', addresses[2]);
      await nextTick();

      expect(addressStore.shipperHandOverSelection).toEqual({
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[2],
        },
      });
    });

    it('updates alternate from-contact / pickup address contact (updateShipperAlternateContact)', async () => {
      const { shipperHandOverSelection } = storeToRefs(addressStore);
      shipperHandOverSelection.value = {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      };
      await nextTick();

      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-shipper-hand-over');
      await handoverComp?.vm.$emit('update:alternate-contact', updatedContactData);
      await nextTick();

      expect(shipperHandOverSelection.value).toEqual({
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: updatedContactData,
        },
      });
    });

    it('updates alternate to-address / delivery address (updateConsigneeAlternateAddress)', async () => {
      const { consigneeHandOverSelection } = storeToRefs(addressStore);
      consigneeHandOverSelection.value = {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: contactDataMock,
        },
      };
      await nextTick();

      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');
      await handoverComp?.vm.$emit('update:alternate-address', addresses[2]);
      await nextTick();

      expect(consigneeHandOverSelection.value).toEqual({
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[2],
        },
      });
    });

    it('updates alternate to-contact / delivery address contact (updateConsigneeAlternateContact)', async () => {
      const { consigneeHandOverSelection } = storeToRefs(addressStore);
      consigneeHandOverSelection.value = {
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
        },
      };
      await nextTick();

      const handoverComp = wrapper
        .findAllComponents(HandOver)
        .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

      console.log(handoverComp);
      await handoverComp?.vm.$emit('update:alternate-contact', updatedContactData);
      await nextTick();

      expect(consigneeHandOverSelection.value).toEqual({
        selection: HandOverSelection.alternateAddress,
        address: {
          ...addresses[1],
          contact: updatedContactData,
        },
      });
    });
  });

  it('updates ports-disabled label for airImport order (determineLabelForPortSelectionDisabledReason)', async () => {
    const { orderType } = storeToRefs(formStore);
    orderType.value = OrderTypes.AirImportOrder;

    await nextTick();

    const handoverComp = wrapper
      .findAllComponents(HandOver)
      .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

    expect(handoverComp?.props('airportSelectionDisabledReason')).toBe(
      'labels.door_to_airport_not_possible.text',
    );
  });

  it('updates ports-disabled label for airExport order (determineLabelForPortSelectionDisabledReason)', async () => {
    const { orderType } = storeToRefs(formStore);
    const { shipperHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());

    orderType.value = OrderTypes.AirExportOrder;
    await nextTick();
    shipperHandOverSelection.value.selection = HandOverSelection.port;
    await nextTick();

    const handoverComp = wrapper
      .findAllComponents(HandOver)
      .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

    expect(handoverComp?.props('airportSelectionDisabledReason')).toBe(
      'labels.airport_to_airport_not_possible.text',
    );
  });

  it('updates ports-disabled label for sea order (determineLabelForPortSelectionDisabledReason)', async () => {
    const { orderType } = storeToRefs(formStore);
    const { shipperHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());

    orderType.value = OrderTypes.SeaExportOrder;
    await nextTick();
    shipperHandOverSelection.value.selection = HandOverSelection.port;
    await nextTick();

    const handoverComp = wrapper
      .findAllComponents(HandOver)
      .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

    expect(handoverComp?.props('airportSelectionDisabledReason')).toBe(
      'labels.port_to_port_not_possible.text',
    );
  });

  it('updates ports-disabled label defaults to null (determineLabelForPortSelectionDisabledReason)', async () => {
    dataStore.$reset();
    formStore.$reset();

    const { orderType } = storeToRefs(formStore);
    const { shipperHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());

    orderType.value = OrderTypes.AirExportOrder;
    await nextTick();
    shipperHandOverSelection.value.selection = HandOverSelection.default;
    await nextTick();

    const handoverComp = wrapper
      .findAllComponents(HandOver)
      .find((c) => c.attributes('data-test') === 'book-consignee-hand-over');

    expect(handoverComp?.props('airportSelectionDisabledReason')).toBe(undefined);
  });
});
