import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { addDays } from 'date-fns/fp';
import { toISODate } from '@/utils/converters/toISODate';
import { MaxOrderPeriods } from '@/enums';

export default function () {
  const { isRoadForwardingOrder, isAirAndSeaOrder } = storeToRefs(useCreateOrderFormStore());
  const today = new Date();

  const allowedPeriodInDays = computed(() => {
    if (isAirAndSeaOrder.value) {
      return MaxOrderPeriods.Forwarding;
    }

    return isRoadForwardingOrder.value ? MaxOrderPeriods.Forwarding : MaxOrderPeriods.Collecting;
  });

  const collectionDate = computed(() => {
    const { customCollectionTimeSlot } = storeToRefs(
      useCreateOrderFormCollectionAndDeliveryStore(),
    );

    return customCollectionTimeSlot.value.collectionDate
      ? toISODate(new Date(customCollectionTimeSlot.value.collectionDate))
      : toISODate(today);
  });

  const minAllowedDateCollection = computed(() => {
    return toISODate(today);
  });

  const maxAllowedDateCollection = computed(() => {
    return toISODate(addDays(allowedPeriodInDays.value, new Date(minAllowedDateCollection.value)));
  });

  const minAllowedDateDelivery = computed(() =>
    toISODate(addDays(1, new Date(collectionDate.value))),
  );

  const maxAllowedDateDelivery = computed(() => {
    return toISODate(addDays(allowedPeriodInDays.value, new Date(minAllowedDateDelivery.value)));
  });

  return {
    minAllowedDateCollection,
    maxAllowedDateCollection,
    minAllowedDateDelivery,
    maxAllowedDateDelivery,
  };
}
