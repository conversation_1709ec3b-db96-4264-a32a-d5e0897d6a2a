import type { OrderProcessResult } from '@dfe/dfe-book-api';
import { OrderStatus } from '@dfe/dfe-book-api';
import { roadForwardingOrder } from '@/mocks/fixtures/order';
import { labels } from '@/mocks/fixtures/labels';

export const submitOrderResult: OrderProcessResult = {
  order: roadForwardingOrder,
  orderLabel: labels[0].orderLabel,
  validationResult: {
    valid: true,
    results: [],
    newOrderStatus: OrderStatus.SENT,
    avisSent: true,
  },
};
