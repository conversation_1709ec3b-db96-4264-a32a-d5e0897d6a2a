import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import ErrorBanner from '@/components/base/banner/DfeBanner.vue';
import { VBanner } from 'vuetify/components';
import type { BannerType } from '@/components/base/banner/banner.types';

describe('DfeBanner', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(ErrorBanner);
  });

  it('mounts', () => {
    expect(wrapper).toBeDefined();
  });

  it.each([
    ['Shows', true],
    ["Doesn't show", false],
  ])('%s VBanner when prop is %s', async (prop) => {
    await wrapper.setProps({ value: prop });
    await nextTick();
    expect(wrapper.findComponent(VBanner).isVisible()).toBeTruthy();
  });

  it.each<[BannerType]>([['error'], ['info'], ['warning']])(
    'sets banner type correctly',
    async (type) => {
      await wrapper.setProps({ type });
      await nextTick();
      expect(wrapper.findComponent(VBanner).classes()).toContain(`banner--${type}`);
    },
  );
});
