<template>
  <div>
    <FormLabel v-if="label" :for="id" :required="required" :tooltip="labelTooltip">{{
      label
    }}</FormLabel>
    <!--Component assumes items and modelValue are of the same type, which isn't always true, so we typecast the values to never-->
    <VCombobox
      :id="id"
      ref="combobox"
      type="number"
      :items="items as never"
      item-title="length"
      :model-value="modelValue as never"
      :single-line="true"
      variant="outlined"
      density="compact"
      item-props.color="grey darken-4"
      :menu-props="{ minWidth: 'max-content', scrollStrategy: 'close', attach: $appRoot }"
      :no-filter="staticMenu"
      hide-details="auto"
      hide-spin-buttons
      menu-icon=""
      :rules="validationRules"
      :error-messages="errorMessages"
      :min="min ? min : null"
      :max="max ? max : null"
      :disabled="disabled"
      validate-on="blur"
      @update:model-value="onInputChange"
      @update:focused="onFocus"
    >
      <template v-if="itemHtmlText" #item="{ item, props: inputProps }">
        <VListSubheader v-if="item.raw?.header" color="grey-700" class="text-caption pb-2 pt-3">
          {{ item.raw.header }}
        </VListSubheader>
        <VDivider v-if="item.raw?.divider" />
        <VListItem v-if="!item.raw?.header && !item.raw?.divider" v-bind="inputProps">
          <template v-if="item.raw?.[itemHtmlText]" #title>
            <!-- eslint-disable vue/no-v-html -->
            <span v-html="sanitize?.(item.raw[itemHtmlText])"></span>
            <!-- eslint-enable -->
          </template>
        </VListItem>
      </template>

      <template #append-inner>
        <span class="v-text-field__suffix text-body-2 text-grey-700">{{ append }}</span>
      </template>
    </VCombobox>
  </div>
</template>

<script setup lang="ts">
import FormLabel from '@/components/form/FormLabel.vue';
import {
  getMessages,
  useValidationRules,
  type ValidationRule,
} from '@/composables/form/useValidationRules';
import { MeasurementProposalsMenu, OrderLineLengthValueObject } from '@/types/createOrder';
import { SanitizeKey } from '@/types/sanitize';
import { createUuid } from '@/utils/createUuid';
import { computed, inject, nextTick, ref } from 'vue';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  items?: NonNullable<MeasurementProposalsMenu>;
  label?: TranslateResult;
  append?: string;
  required?: boolean;
  staticMenu?: boolean;
  itemHtmlText?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  allowedDecimals?: number;
  rules?: ValidationRule[];
  labelTooltip?: TranslateResult;
}

const modelValue = defineModel<number | string | null>({ required: true });

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  label: undefined,
  append: undefined,
  itemHtmlText: undefined,
  min: 0,
  max: 999,
  allowedDecimals: undefined,
  rules: undefined,
  labelTooltip: undefined,
});
const emit = defineEmits(['update:modelValue', 'inputMultiple']);

const id = `combobox-${createUuid()}`;
const sanitize = inject(SanitizeKey);
const errors = ref<string[]>([]);
const showErrors = ref(true);
const combobox = ref();

const validationRules = computed(() => [
  ...(props.required ? [useValidationRules.required] : []),
  useValidationRules.numbers,
  useValidationRules.min(props.min),
  useValidationRules.max(props.max),
  ...(props.allowedDecimals
    ? [useValidationRules.decimalWithMaxPlaces(props.allowedDecimals)]
    : []),
  ...(props.rules ?? []),
]);

const errorMessages = computed(() => {
  return showErrors.value === true ? errors.value : [];
});

const onInputChange = (value: OrderLineLengthValueObject | HTMLInputElement['value'] | null) => {
  if (value && typeof value === 'object') {
    // Prevent buggy combobox behaviour (by double emit)
    emit('update:modelValue', null);
    nextTick(() => {
      emit('inputMultiple', {
        length: value.length,
        width: value.width,
      });
    });
  } else if (value) {
    emit('update:modelValue', +value);
  } else {
    emit('update:modelValue', null);
  }
};

// fixes numeric-only input validation in safari
const onFocus = (value: boolean) => {
  if (value) {
    combobox.value?.resetValidation();
    showErrors.value = false;
    return;
  }

  showErrors.value = true;

  if (!combobox.value?.$el) {
    return;
  }

  const { valid } = combobox.value.validity;
  if (valid) {
    errors.value = [];
  } else {
    errors.value = getMessages(validationRules.value, modelValue.value);
  }
};
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables';
:deep(.v-autocomplete.v-select.v-input--is-focused) input {
  min-width: 0;
}

:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}
</style>
