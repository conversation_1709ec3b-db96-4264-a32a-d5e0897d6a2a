import { useTownCountyList } from '@/composables/data/useTownCountyList';
import { initPinia } from '@test/util/init-pinia';
import { mockServer } from '@/mocks/server';
import { withSetup } from '@test/util/with-setup';
import { nextTick } from 'vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';

describe('useTownCountyList', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        townCounty: [{ county: 'Leitrim', town: 'Aghavas', eircode: '12005', dachserPLZ: '12005' }],
      },
    });
  });

  beforeEach(() => {
    initPinia();
  });

  it('returns town and county list based on search input', async () => {
    const { selectableTownCounty, search } = withSetup(() => useTownCountyList())[0];
    search.value = 'H12';

    await nextTick();

    await vi.waitFor(() => {
      expect(selectableTownCounty.value).toEqual([
        {
          label: 'Aghavas/Leitrim',
          data: { town: 'Aghavas', county: 'Leitrim', dachserPlz: '12005' },
        },
      ]);
    });
  });

  it('returns empty list when no matching town and county found', async () => {
    const { selectableTownCounty, search } = withSetup(() => useTownCountyList())[0];
    search.value = '99999';

    await vi.waitFor(() => {
      expect(selectableTownCounty.value).toEqual([]);
    });
  });

  it('shows unknown EirCode when no results are returned', async () => {
    const { showUnknownEirCode, search } = withSetup(() => useTownCountyList())[0];
    search.value = '00000';

    await vi.waitFor(() => {
      expect(showUnknownEirCode.value).toBe(true);
    });
  });

  it('sets showTechnicalError to true when API call fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.v1.getIrelandDachserPostalCodes = vi
      .fn()
      .mockRejectedValue(new Error('API Error'));
    const { showTechnicalError, search } = withSetup(() => useTownCountyList())[0];
    search.value = 'H12';

    await vi.waitFor(() => {
      expect(showTechnicalError.value).toBe(true);
    });
  });
});
