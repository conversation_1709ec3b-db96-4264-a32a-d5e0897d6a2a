import useSaveOrder from '@/composables/createOrder/useSaveOrder';
import {
  airExportOrder,
  airImportOrder,
  roadCollectionOrder,
  roadForwardingOrder,
  seaExportOrder,
  seaImportOrder,
} from '@/mocks/fixtures/order';
import { mockServer } from '@/mocks/server';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import type {
  AirOrder,
  BasicOrder,
  FullContainerLoad,
  PackingPosition,
  RoadCollectionOrder,
  RoadForwardingOrder,
  RoadOrder,
  SeaOrder,
} from '@dfe/dfe-book-api';
import { Segment } from '@dfe/dfe-book-api';
import { hsCodeOptions } from '@/mocks/fixtures/hsCodeOptions';
import { ports } from '@/mocks/fixtures/ports';
import { afterEach, describe, expect } from 'vitest';
import { LoadOrderOptions, useEditOrder } from '@/composables/createOrder/editOrder/useEditOrder';
import { withSetup } from '@test/util/with-setup';

function getPackingPositionsLineCount(packingPositions?: PackingPosition[]) {
  return (
    packingPositions?.reduce((count, packingPosition) => {
      count += packingPosition.lines?.length ?? 0;
      return count;
    }, 0) ?? 0
  );
}

function getFullContainerLoadsLineCount(fullContainerLoads?: FullContainerLoad[]) {
  return (
    fullContainerLoads?.reduce((count, fullContainerLoad) => {
      count += fullContainerLoad.lines?.length ?? 0;
      return count;
    }, 0) ?? 0
  );
}

function assertBasicOrderValues(basicOrderActual: BasicOrder, basicOrderExpected: BasicOrder) {
  expect(basicOrderActual.customerNumber).toEqual(basicOrderExpected.customerNumber);
  expect(basicOrderActual.orderType).toEqual(basicOrderExpected.orderType);
  expect(basicOrderActual.orderId).toEqual(basicOrderExpected.orderId);
  expect(basicOrderActual.shipperAddress?.name).toEqual(basicOrderExpected.shipperAddress?.name);
  expect(basicOrderActual.consigneeAddress?.name).toEqual(
    basicOrderExpected.consigneeAddress?.name,
  );
  expect(basicOrderActual.deliveryOption).toEqual(basicOrderExpected.deliveryOption);
  expect(basicOrderActual.texts).toEqual(basicOrderExpected.texts);
  expect(basicOrderActual.goodsValue).toEqual(basicOrderExpected.goodsValue);
  expect(basicOrderActual.goodsCurrency).toEqual(basicOrderExpected.goodsCurrency);
  expect(basicOrderActual.customsType).toEqual(basicOrderExpected.customsType);
}

function assertRoadOrder(
  roadOrderActual: RoadOrder,
  roadOrderExpected: RoadOrder,
  enablePackingPositions?: boolean,
) {
  expect(roadOrderActual.tailLiftDelivery).toEqual(roadOrderExpected.tailLiftDelivery);
  expect(roadOrderActual.freightTerm).toEqual(roadOrderExpected.freightTerm);
  expect(roadOrderActual.labelPrinted).toEqual(roadOrderExpected.labelPrinted);
  expect(roadOrderActual.transferListPrinted).toEqual(roadOrderExpected.transferListPrinted);
  expect(roadOrderActual.frostProtection).toEqual(roadOrderExpected.frostProtection);
  expect(roadOrderActual.ekaerNotRequired).toEqual(roadOrderExpected.ekaerNotRequired);
  expect(roadOrderActual.product).toEqual(roadOrderExpected.product);
  if (
    roadOrderActual.orderLineItems != undefined &&
    roadOrderExpected.orderLineItems != undefined
  ) {
    expect(roadOrderActual.orderLineItems[0].quantity).toEqual(
      roadOrderExpected.orderLineItems[0].quantity,
    );
  }

  if (enablePackingPositions) {
    expect(roadOrderActual.orderLineItems?.length).toEqual(
      roadOrderExpected.orderLineItems?.length,
    );
    expect(roadOrderActual.packingPositions?.length).toEqual(
      roadOrderExpected.packingPositions?.length,
    );
    expect(roadOrderActual.packingPositions?.[0].lines?.length).toEqual(
      roadOrderExpected.packingPositions?.[0].lines?.length,
    );
  }

  if (!enablePackingPositions) {
    const packingPositionsOrderLinesLength = getPackingPositionsLineCount(
      roadOrderExpected.packingPositions,
    );
    const orderLinesLength = roadOrderExpected.orderLineItems?.length ?? 0;

    expect(roadOrderActual.orderLineItems?.length).toEqual(
      orderLinesLength + packingPositionsOrderLinesLength,
    );
    expect(roadOrderActual.packingPositions).toEqual(undefined);
  }

  expect(roadOrderActual.references?.length).toEqual(3);
}

function assertAirOrder(orderActual: AirOrder, orderExpected: AirOrder) {
  expect(orderActual.fromIATA?.code).toEqual(orderExpected.fromIATA?.code);
  expect(orderActual.toIATA?.code).toEqual(orderExpected.toIATA?.code);
  expect(orderActual.deliverToAirport).toEqual(orderExpected.deliverToAirport);
  expect(orderActual.collectFromAirport).toEqual(orderExpected.collectFromAirport);
  expect(orderActual.pickupAddress?.name).toEqual(orderExpected.pickupAddress?.name);
  expect(orderActual.pickupAddress?.contact?.name).toEqual(
    orderExpected.pickupAddress?.contact?.name,
  );
  expect(orderActual.deliveryAddress?.name).toEqual(orderExpected.deliveryAddress?.name);
  expect(orderActual.deliveryAddress?.contact?.name).toEqual(
    orderExpected.deliveryAddress?.contact?.name,
  );
  expect(orderActual.incoTerm).toEqual(orderExpected.incoTerm);
  expect(orderActual.product).toEqual(orderExpected.product);
  if (orderActual.orderLineItems != undefined && orderExpected.orderLineItems != undefined) {
    expect(orderActual.orderLineItems[0].quantity).toEqual(
      orderExpected.orderLineItems[0].quantity,
    );
  }
  expect(orderActual.references?.length).toEqual(12);
}

function assertSeaOrder(
  orderActual: SeaOrder,
  orderExpected: SeaOrder,
  enableFullContainerLoads: boolean,
) {
  expect(orderActual.fromPort?.code).toEqual(orderExpected.fromPort?.code);
  expect(orderActual.toPort?.code).toEqual(orderExpected.toPort?.code);
  expect(orderActual.deliverToPort).toEqual(orderExpected.deliverToPort);
  expect(orderActual.collectFromPort).toEqual(orderExpected.collectFromPort);
  expect(orderActual.pickupAddress?.name).toEqual(orderExpected.pickupAddress?.name);
  expect(orderActual.pickupAddress?.contact?.name).toEqual(
    orderExpected.pickupAddress?.contact?.name,
  );
  expect(orderActual.deliveryAddress?.name).toEqual(orderExpected.deliveryAddress?.name);
  expect(orderActual.deliveryAddress?.contact?.name).toEqual(
    orderExpected.deliveryAddress?.contact?.name,
  );
  if (
    orderActual.orderLineItems != undefined &&
    orderActual.orderLineItems.length > 0 &&
    orderExpected.orderLineItems != undefined
  ) {
    expect(orderActual.orderLineItems[0].quantity).toEqual(
      orderExpected.orderLineItems[0].quantity,
    );
  }

  if (!enableFullContainerLoads) {
    const fullContainerLoadsOrderLinesLength = getFullContainerLoadsLineCount(
      orderExpected.fullContainerLoads,
    );
    const orderLinesLength = orderExpected.orderLineItems?.length ?? 0;

    expect(orderActual.orderLineItems?.length).toEqual(
      orderLinesLength + fullContainerLoadsOrderLinesLength,
    );
    expect(orderActual.fullContainerLoads).toEqual(undefined);
  }

  expect(orderActual.references?.length).toEqual(12);
}

function assertRoadForwardingOrder(
  roadOrderForwardingActual: RoadForwardingOrder,
  roadOrderForwardingExpected: RoadForwardingOrder,
) {
  expect(roadOrderForwardingActual.selfCollection).toEqual(
    roadOrderForwardingExpected.selfCollection,
  );
  expect(roadOrderForwardingActual.transportName).toEqual(
    roadOrderForwardingExpected.transportName,
  );
  expect(roadOrderForwardingActual.manualNumberSscc).toEqual(
    roadOrderForwardingExpected.manualNumberSscc,
  );
  expect(roadOrderForwardingActual.palletLocations).toEqual(
    roadOrderForwardingExpected.palletLocations,
  );
  expect(roadOrderForwardingActual.generatedSsccs).toEqual(
    roadOrderForwardingExpected.generatedSsccs,
  );
  expect(roadOrderForwardingActual.cashOnDelivery).toEqual(
    roadOrderForwardingExpected.cashOnDelivery,
  );
  expect(roadOrderForwardingActual.cashOnDeliveryAmount).toEqual(
    roadOrderForwardingExpected.cashOnDeliveryAmount,
  );
}

function assertRoadCollectionOrder(
  roadOrderCollectionActual: RoadCollectionOrder,
  roadOrderCollectionExpected: RoadCollectionOrder,
) {
  expect(roadOrderCollectionActual.differentConsigneeAddress?.name).toEqual(
    roadOrderCollectionExpected.differentConsigneeAddress?.name,
  );
  expect(roadOrderCollectionActual.interpreter).toEqual(roadOrderCollectionExpected.interpreter);
  expect(roadOrderCollectionActual.collectionOption).toEqual(
    roadOrderCollectionExpected.collectionOption,
  );
  expect(roadOrderCollectionActual.tailLiftCollection).toEqual(
    roadOrderCollectionExpected.tailLiftCollection,
  );
}

describe('useSaveOrder composable', () => {
  const formStore = useCreateOrderFormStore();
  const orderLineStore = useCreateOrderOrderLineFormStore();

  const loadOrderOptions: LoadOrderOptions = {
    timeFormat: 'HH:mm',
    locale: 'en',
  };

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        roadForwardingOrder,
        roadCollectionOrder,
        airExportOrder,
        airImportOrder,
        seaExportOrder,
        seaImportOrder,
        hsCodeOptions,
        ports,
      },
    });
  });

  afterEach(() => {
    orderLineStore.$reset();
  });

  describe('Road Forwarding Order', () => {
    it('should return values for road forwarding order', async () => {
      orderLineStore.packagingAidPosition = false;
      await withSetup(() =>
        useEditOrder().loadOrder(roadForwardingOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const forwardingOrder = useSaveOrder();

      assertBasicOrderValues(forwardingOrder as BasicOrder, roadForwardingOrder);
      assertRoadOrder(forwardingOrder as RoadOrder, roadForwardingOrder);
      assertRoadForwardingOrder(forwardingOrder as RoadForwardingOrder, roadForwardingOrder);
    });

    it('should return values for road forwarding order with packing posiitons', async () => {
      orderLineStore.packagingAidPosition = true;
      await withSetup(() =>
        useEditOrder().loadOrder(roadForwardingOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const forwardingOrder = useSaveOrder();

      assertRoadOrder(forwardingOrder as RoadOrder, roadForwardingOrder, true);
    });
  });

  describe('Road Collection Order', () => {
    it('should return values for road collection order', async () => {
      orderLineStore.packagingAidPosition = false;
      await withSetup(() =>
        useEditOrder().loadOrder(roadCollectionOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const savedOrder = useSaveOrder();

      assertBasicOrderValues(savedOrder as BasicOrder, roadCollectionOrder);
      assertRoadOrder(savedOrder as RoadOrder, roadCollectionOrder);
      assertRoadCollectionOrder(savedOrder as RoadCollectionOrder, roadCollectionOrder);
    });

    it('should return values for road collection order with packing positions', async () => {
      orderLineStore.packagingAidPosition = true;
      await withSetup(() =>
        useEditOrder().loadOrder(roadCollectionOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const savedOrder = useSaveOrder();

      assertRoadOrder(savedOrder as RoadOrder, roadCollectionOrder, true);
    });
  });

  describe('Air Export Order', () => {
    it('should return values for air export order', async () => {
      formStore.orderType = OrderTypes.AirExportOrder;
      formStore.transportType = Segment.AIR;
      await withSetup(() =>
        useEditOrder().loadOrder(airExportOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const savedOrder = useSaveOrder();

      assertBasicOrderValues(savedOrder as BasicOrder, airExportOrder);
      assertAirOrder(savedOrder as AirOrder, airExportOrder);
    });
  });

  describe('Air Import Order', () => {
    it('should return values for air import order', async () => {
      formStore.orderType = OrderTypes.AirImportOrder;
      formStore.transportType = Segment.AIR;
      await withSetup(() =>
        useEditOrder().loadOrder(airImportOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const savedOrder = useSaveOrder();

      assertBasicOrderValues(savedOrder as BasicOrder, airImportOrder);
      assertAirOrder(savedOrder as AirOrder, airImportOrder);
    });
  });

  it('should build correct payload for address when updating order', () => {
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = storeToRefs(addressStore);

    const formStore = useCreateOrderFormStore();

    formStore.customerNumber = '00000001';

    shipperAddress.value.address = {
      id: 1,
      originAddressId: 2,
      name: 'Ray Sono AG',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblingr Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    };

    const forwardingOrder = useSaveOrder();

    expect(forwardingOrder.shipperAddress?.id).toEqual(1);
    expect(forwardingOrder.shipperAddress?.originAddressId).toEqual(2);
  });

  it('should build correct payload for address when creating order', () => {
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = storeToRefs(addressStore);

    const formStore = useCreateOrderFormStore();

    formStore.customerNumber = '00000001';

    shipperAddress.value.address = {
      id: 1,
      name: 'Ray Sono AG',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    };

    const forwardingOrder = useSaveOrder();

    expect(forwardingOrder.shipperAddress?.originAddressId).toEqual(undefined);
  });

  it('should send customerNumber in shipperAddress to Backend, if shipperAddress is equal to selectedCustomerAddress', () => {
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = storeToRefs(addressStore);

    const createOrderFormStore = useCreateOrderFormStore();
    const { selectedCustomer } = storeToRefs(createOrderFormStore);

    const formStore = useCreateOrderFormStore();

    formStore.customerNumber = '00000001';

    selectedCustomer.value.address = {
      name: 'Ray Sono AG',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    };

    shipperAddress.value.address = {
      name: 'Ray Sono AG',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    };

    const forwardingOrder = useSaveOrder();

    expect(forwardingOrder.shipperAddress?.customerNumber).toEqual(formStore.customerNumber);
  });

  it('should not set customerNumber for shipperAddress, if shipperAddress is not equal to selectedCustomerAddress', () => {
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = storeToRefs(addressStore);

    const createOrderFormStore = useCreateOrderFormStore();
    const { selectedCustomer } = storeToRefs(createOrderFormStore);

    const formStore = useCreateOrderFormStore();

    formStore.customerNumber = '00000001';

    selectedCustomer.value.address = {
      name: 'Ray Sono AG',
      name2: 'rear annex',
      name3: 'string',
      street: 'Tumblinger Str. 32',
      postcode: '80337',
      city: 'Munich',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    };

    shipperAddress.value.address = {
      name: 'Test AG',
      name2: '',
      name3: '',
      street: 'Teststr. 1',
      postcode: '13254',
      city: 'Teststadt',
      countryCode: 'DE',
      supplement: 'Tel. 0815',
      gln: '4313920192301',
    };

    const forwardingOrder = useSaveOrder();

    expect(forwardingOrder.shipperAddress?.customerNumber).toBeUndefined();
  });

  it('should set hsCodes for AirOrder', () => {
    const formStore = useCreateOrderFormStore();

    formStore.orderType = OrderTypes.AirExportOrder;

    const orderLineFormStore = useCreateOrderOrderLineFormStore();

    orderLineFormStore.orderLines = [
      {
        localId: 123,
        id: undefined,
        number: 999,
        quantity: 99999,
        packaging: {
          code: 'string',
          description: 'string',
        },
        content: 'string',
        weight: 99999,
        length: 999,
        width: 999,
        height: 999,
        volume: 0,
        loadingMeter: 0,
        goodsGroup: {
          code: '',
          quantity: undefined,
        },
        goodsClassifications: [
          {
            hsCode: {
              code: '12345678',
              description: 'Test',
            },
            goods: 'Test',
          },
        ],
        dangerousGoods: [],
      },
    ];

    const airOrder = useSaveOrder() as AirOrder;

    expect(airOrder?.orderLineItems && airOrder?.orderLineItems[0].hsCodes).toEqual([
      {
        hsCode: '12345678',
        goods: 'Test',
      },
    ]);
  });

  describe('Sea Export Order', () => {
    it('should return values for sea export order', async () => {
      formStore.orderType = OrderTypes.SeaExportOrder;
      formStore.transportType = Segment.SEA;
      orderLineStore.isFullContainerLoad = true;
      await withSetup(() =>
        useEditOrder().loadOrder(seaExportOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const savedOrder = useSaveOrder();

      assertBasicOrderValues(savedOrder as BasicOrder, seaExportOrder);
      assertSeaOrder(savedOrder as SeaOrder, seaExportOrder, true);
    });
  });

  describe('Sea Import Order', () => {
    it('should return values for sea import order', async () => {
      formStore.orderType = OrderTypes.SeaImportOrder;
      formStore.transportType = Segment.SEA;
      orderLineStore.isFullContainerLoad = true;
      await withSetup(() =>
        useEditOrder().loadOrder(seaImportOrder, { ...loadOrderOptions, locale: 'en' }),
      )[0];

      const savedOrder = useSaveOrder();

      assertBasicOrderValues(savedOrder as BasicOrder, seaImportOrder);
      assertSeaOrder(savedOrder as SeaOrder, seaImportOrder, true);
    });
  });
});
