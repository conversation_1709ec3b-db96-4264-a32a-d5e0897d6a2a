import ProductSelection from '@/components/createOrder/formSectionProducts/ProductSelection.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { airDeliveryProducts } from '@/mocks/fixtures/deliveryProducts';
import SelectCard from '@/components/form/SelectCard.vue';
import { nextTick } from 'vue';

describe('ProductSelection.vue', () => {
  let wrapper: VueWrapper;
  beforeEach(() => {
    wrapper = shallowMount(ProductSelection, {
      props: {
        products: airDeliveryProducts,
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('should display all product radios', () => {
    expect(wrapper.findAllComponents(SelectCard).filter((el) => el.isVisible()).length).toBe(
      airDeliveryProducts.length,
    );
  });

  it('should disable the selection on quote 2 book', async () => {
    expect(wrapper.getComponent({ name: 'v-radio-group' }).attributes('disabled')).toBe('false');
    await wrapper.setProps({
      isDisabled: true,
    });
    await nextTick();
    expect(wrapper.getComponent({ name: 'v-radio-group' }).attributes('disabled')).toBe('true');
  });
});
