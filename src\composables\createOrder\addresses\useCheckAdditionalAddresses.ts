import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { FurtherAddressTypes } from '@dfe/dfe-book-api';
import type { FurtherAddressTypeKeys } from '@/enums';
import { useCheckEmptyValuesWithIgnoredKeys } from '@/composables/useCheckEmptyValuesWithIgnoredKeys';

type ShowAddress = {
  [key in FurtherAddressTypeKeys]: boolean;
};

const useShowAddButtons = (items: FurtherAddressTypes, showAddress: Partial<ShowAddress>) => {
  if (!items) {
    return false;
  }
  return items.some((item) => !showAddress[item.code as FurtherAddressTypeKeys]);
};

const useCheckFurtherAddresses = (showAddress: Partial<ShowAddress>) => {
  const addressStore = useCreateOrderAddressesStore();
  const { getFurtherAddress } = addressStore;

  (<FurtherAddressTypeKeys[]>Object.keys(showAddress)).forEach((key) => {
    const furtherAddress = getFurtherAddress(key)?.address;
    showAddress[key as FurtherAddressTypeKeys] = furtherAddress
      ? !!useCheckEmptyValuesWithIgnoredKeys(furtherAddress, ['id'])
      : false;
  });
};

export { useShowAddButtons, useCheckFurtherAddresses };
