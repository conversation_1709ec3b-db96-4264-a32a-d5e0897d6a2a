import { storeToRefs } from 'pinia';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';

function convertCollectionDateToISO(date: string) {
  if (!date) {
    return '';
  }
  const { customCollectionTimeSlot } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());

  const newDate = customCollectionTimeSlot.value.collectionDate
    ? new Date(customCollectionTimeSlot.value.collectionDate)
    : new Date();

  const year = newDate.getFullYear();
  const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
  const day = newDate.getDate().toString().padStart(2, '0');

  return `${year}-${month}-${day}`;
}

export default convertCollectionDateToISO;
