import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { initPinia } from '@test/util/init-pinia';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { mockServer } from '@/mocks/server';
import { withSetup } from '@test/util/with-setup';

describe('useCustomerSettings', () => {
  let collectionAndDeliveryStore: ReturnType<typeof useCreateOrderFormCollectionAndDeliveryStore>;
  let accountingAdditionalServicesStore: ReturnType<
    typeof useCreateOrderFormAccountingAdditionalServices
  >;
  let orderLineStore: ReturnType<typeof useCreateOrderOrderLineFormStore>;

  beforeEach(() => {
    initPinia();

    collectionAndDeliveryStore = useCreateOrderFormCollectionAndDeliveryStore();
    accountingAdditionalServicesStore = useCreateOrderFormAccountingAdditionalServices();
    orderLineStore = useCreateOrderOrderLineFormStore();

    accountingAdditionalServicesStore.frostProtectionRequired = true;
    accountingAdditionalServicesStore.palletLocationsNumber = 12;
    collectionAndDeliveryStore.selfCollection = true;
    orderLineStore.manualNumberOfLabels = 34;
  });

  it.each([
    [
      {
        frostProtection: true,
        manualNumberOfLabels: true,
        palletLocation: true,
        selfCollection: true,
      },
      {
        frostProtectionRequired: true,
        manualNumberOfLabels: 34,
        palletLocationsNumber: 12,
        selfCollection: true,
      },
    ],
    [
      {
        frostProtection: false,
        manualNumberOfLabels: true,
        palletLocation: true,
        selfCollection: true,
      },
      {
        frostProtectionRequired: false,
        manualNumberOfLabels: 34,
        palletLocationsNumber: 12,
        selfCollection: true,
      },
    ],
    [
      {
        frostProtection: false,
        manualNumberOfLabels: false,
        palletLocation: false,
        selfCollection: false,
      },
      {
        frostProtectionRequired: false,
        manualNumberOfLabels: null,
        palletLocationsNumber: null,
        selfCollection: false,
      },
    ],
  ])('resets values in stores that are unavailable for the customer', (settings, expected) => {
    const server = mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: settings,
      },
    });
    withSetup(() => useCustomerSettings());

    vi.waitFor(() => {
      expect(collectionAndDeliveryStore.selfCollection).toBe(expected.selfCollection);
      expect(accountingAdditionalServicesStore.frostProtectionRequired).toBe(
        expected.frostProtectionRequired,
      );
      expect(accountingAdditionalServicesStore.palletLocationsNumber).toBe(
        expected.palletLocationsNumber,
      );
      expect(orderLineStore.manualNumberOfLabels).toBe(expected.manualNumberOfLabels);
    });

    server.shutdown();
  });
});
