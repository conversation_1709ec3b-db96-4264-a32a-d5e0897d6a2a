import { DFE_BOOK_API_URL } from '@/env';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { Countries, CountryCode, OrderProcessResult } from '@dfe/dfe-book-api';
import { OrderSaveAction, OrderStatus, OrderType, Segment } from '@dfe/dfe-book-api';
import type { Server } from 'miragejs';
import { Response } from 'miragejs';
import { storeToRefs } from 'pinia';
import type { Fixtures, Models } from '../server';

function withURL(path: string) {
  return `${DFE_BOOK_API_URL}${path}`;
}

export function useRoutes(
  server: Server,
  fixtures?: Fixtures<Models>,
  environment = 'development',
) {
  // Mock routes in test environment only
  if (environment === 'test') {
    server.get(withURL('/orders/:orderId'), (_, request) => {
      const order =
        +request.params.orderId === 2 ? fixtures?.airExportOrder : fixtures?.roadForwardingOrder;
      return order ?? {};
    });

    server.get(withURL('/v1/ireland/dachserpostalcodes'), (_, request) => {
      const townCounty = request.queryParams.postalCode === 'H12' ? fixtures?.townCounty : [];
      return townCounty ?? {};
    });

    server.put(withURL('/v2/orders/'), (): OrderProcessResult | Response => {
      const formStore = useCreateOrderFormStore();
      const { orderType } = storeToRefs(formStore);
      const order =
        orderType.value === OrderType.RoadForwardingOrder
          ? fixtures?.roadForwardingOrder
          : fixtures?.airExportOrder;
      if (order) {
        order.orderId = 100;
        return {
          order: order,
        };
      }
      return {};
    });
    server.put(
      withURL('/v2/orders/:action'),
      (_, { params, requestBody }): OrderProcessResult | Response => {
        const compileResult = () => {
          switch (params.action) {
            case OrderSaveAction.Validate:
              return {
                validationResult: fixtures?.validationSuccess || fixtures?.validationError || {},
                order: JSON.parse(requestBody),
              };
            case OrderSaveAction.PrintLabels:
              return fixtures?.labels?.[0] ?? {};
            case OrderSaveAction.Submit:
              if (!fixtures?.submitOrderResult) {
                return {};
              }
              return {
                ...fixtures.submitOrderResult,
                validationResult:
                  fixtures?.validationSuccess ||
                  fixtures?.validationError ||
                  fixtures?.submitOrderResult.validationResult ||
                  {},
                order: fixtures.submitOrderResult.order
                  ? {
                      ...fixtures.submitOrderResult.order,
                      status: OrderStatus.SENT,
                    }
                  : undefined,
              };
            default:
              return {};
          }
        };

        const result = compileResult();
        if (result.validationResult?.valid === false) {
          return new Response(400, {}, result);
        }
        return result;
      },
    );

    server.put(withURL('/orders/:orderId/print-labels'), (_, request) => {
      if (request.params.orderId === '999') {
        return new Response(400);
      }
      return { orderLabel: fixtures?.labels?.[0].orderLabel };
    });

    server.get(withURL('/orders/print-labels'), (_, request): string | Response => {
      if (request.queryParams.orderIds?.includes('999')) {
        return new Response(400);
      }
      return fixtures?.labels?.[1].orderLabel ?? '';
    });

    server.get(withURL('/countries'), (_, request) => {
      let countries: Countries = [];

      if (request.queryParams?.customerSegment === Segment.ROAD) {
        countries = fixtures?.countriesRoad || [];
      } else if (request.queryParams?.customerSegment === Segment.AIR) {
        countries = fixtures?.countriesAir || [];
      }

      return countries;
    });

    server.get(withURL('/currencies'), () => {
      return fixtures?.currencies || [];
    });

    server.get(withURL('/customers'), () => {
      return fixtures?.customers || [];
    });

    server.get(withURL('/customers/countries'), () => {
      return fixtures?.countryFavorites || {};
    });

    server.get(withURL('/customers/address'), (_, request) => {
      const { customerNumber } = request.queryParams ?? {
        customerNumber: '00000001',
      };

      return (
        fixtures?.addresses?.find((address) => address.id === parseInt(customerNumber as string)) ||
        {}
      );
    });

    server.get(withURL('/contact-data'), () => {
      return fixtures?.contactData || {};
    });

    server.get(withURL('/customers/container-types'), () => {
      return fixtures?.containerTypesWithLastUsed || {};
    });

    server.get(withURL('/packaging-options'), () => {
      return fixtures?.packagingOptionsWithFavorites || {};
    });

    server.get(withURL('/measurements/proposals'), () => {
      return fixtures?.measurementProposals || {};
    });

    server.get(withURL('/customers/goods-groups'), () => {
      return fixtures?.goodsGroupResponse || [];
    });

    server.get(withURL('/customers/loading-points'), () => {
      return fixtures?.loadingPoints || [];
    });

    server.get(withURL('/customers/further-address-types'), (_, request) => {
      const { customerNumber } = request.queryParams as {
        customerNumber: string;
      };
      return fixtures?.furtherAddressTypes?.[parseInt(customerNumber)] || [];
    });

    server.get(withURL('/third-country-constellation'), (_, { queryParams }) => {
      return {
        result: !!queryParams && queryParams.fromCountryCode !== queryParams.toCountryCode,
      };
    });

    server.get(withURL('/order/content/frequently-used'), () => {
      return fixtures?.orderContent?.slice(0, 5) || [];
    });

    server.get(withURL('/order/content/search'), (_, request) => {
      const { value } = request?.queryParams || {};

      if (value?.length) {
        return (
          fixtures?.orderContent?.filter((item) =>
            item.toLowerCase().includes((value as string).toLowerCase()),
          ) || []
        );
      }

      return [];
    });

    server.get(withURL('/v1/customers/delivery-products-road'), () => {
      return fixtures?.deliveryProducts || [];
    });

    server.get(withURL('/customers/delivery-products-road'), () => {
      return fixtures?.deliveryProducts || [];
    });

    server.get(withURL('/delivery-options'), () => {
      return fixtures?.deliveryOptions || [];
    });

    server.get(withURL('/collection-options'), () => {
      return fixtures?.collectionOptions || [];
    });

    server.get(withURL('/inco-terms'), () => {
      return fixtures?.incoTerms || [];
    });

    server.get(withURL('/customers/collection-time-slots'), (_, request) => {
      const { collectionDate } = request?.queryParams || {};
      const [, , day] = (collectionDate as string).split('-');
      return parseInt(day) % 2 ? fixtures?.collectionTimeSlots || [] : [];
    });

    server.get(withURL('/preferences/creation'), (schema) => {
      return schema.first('creationPreferences');
    });

    server.get(withURL('/collection-interpreter-options'), () => {
      return fixtures?.collectionInterpreterOptions || [];
    });

    server.get(withURL('/transports'), () => {
      return fixtures?.transports || [];
    });

    server.get(withURL('/customers/order-groups'), () => {
      return fixtures?.orderGroups || [];
    });

    server.get(withURL('/document-types'), () => {
      return fixtures?.documentTypes || [];
    });

    server.get(withURL('/nature-of-goods'), () => {
      return fixtures?.natureOfGoods || [];
    });

    server.get(withURL('/documents/recently-used-document-types'), () => {
      return [fixtures?.documentTypes?.[4]];
    });

    server.get(withURL('/extensions'), () => {
      return fixtures?.extensions || [];
    });

    server.post(
      withURL('/documents'),
      (_, request) => {
        const documentName = (request.requestBody as unknown as FormData)
          .get('documentName')
          ?.toString();
        if (documentName?.match(/\.exe$/)) {
          return new Response(
            400,
            {},
            {
              uploadStatus: 'errorInvalidExtension',
              documentId: 1,
              documentName,
            },
          );
        }
        return fixtures?.documentsResponse?.[0] || [];
      },
      { timing: 2 },
    );

    server.put(withURL('/documents/:documentId'), () => {
      return fixtures?.updateDocument || {};
    });

    server.delete(withURL('/documents/:documentId'), () => {
      return [];
    });

    server.get(withURL('/hs-code-options'), () => {
      return fixtures?.hsCodeOptions || [];
    });

    server.get(withURL('/customers/settings'), () => {
      return fixtures?.customerSettings || {};
    });

    server.get(withURL('/ports'), (_, { queryParams }) => {
      if (!fixtures?.ports) {
        return [];
      }
      if (!queryParams?.searchFor) {
        return fixtures.ports;
      }
      const searchPattern = new RegExp(`${queryParams.searchFor}`, 'i');
      return fixtures.ports.filter(
        (port) => (port.name && searchPattern.test(port.name)) || searchPattern.test(port.code),
      );
    });
    server.get(withURL('/freight-terms'), () => {
      return fixtures?.freightTerms || [];
    });

    server.get(withURL('/v1/port-routing'), (_, { queryParams }) => {
      if (!fixtures?.ports) {
        return [];
      }

      if (queryParams.portType) {
        return fixtures?.ports.filter((port) => port.type === queryParams.portType);
      }

      return fixtures?.ports || [];
    });

    server.get(withURL('/v1/documents'), () => {
      return fixtures?.documents || [];
    });

    server.post(withURL('/v1/address/validate'), () => {
      return fixtures?.validateAddressData || {};
    });

    server.get(
      withURL('/v1/validate-postcode'),
      () => {
        const createOrderAddressesStore = useCreateOrderAddressesStore();
        const { formAddress } = storeToRefs(createOrderAddressesStore);

        if (
          (formAddress.value?.postcode && formAddress.value?.postcode?.length !== 5) ||
          (formAddress.value?.countryCode && formAddress.value?.countryCode.length < 2)
        ) {
          return fixtures?.validatePostcodeInvalid || {};
        }

        return fixtures?.validatePostcodeValid || {};
      },
      { timing: 1000 },
    );

    server.get(withURL('/v1/customers/delivery-products-air'), () => {
      return fixtures?.airDeliveryProducts || [];
    });
    server.get(withURL('/v1/embargo'), (_, request) => {
      const { fromCountryCode, toCountryCode } = request.queryParams as Record<string, CountryCode>;

      return !!fixtures?.embargoCountries?.some((country) =>
        [fromCountryCode, toCountryCode].includes(country),
      );
    });

    server.get(withURL('/v1/dangerous-goods/un-search'), (_, { queryParams }) => {
      return (
        fixtures?.unNumberSearchResults?.filter(
          (entry) =>
            entry.unNumber === queryParams.searchFor &&
            queryParams.customerSegment === Segment.ROAD,
        ) || []
      );
    });
  }
}
