<template>
  <div class="mb-6 d-flex">
    <FormNavigation :items="airNavigationItems" class="d-none d-md-block" />
    <div class="flex-grow-1">
      <QuoteToBookBanner
        v-if="isOrderFromQuote"
        :title="t('labels.export_order_from_quote_title.text')"
        :order-is-price-dependent="true"
        :quote-id="quoteInformation?.quoteRequestId"
      />
      <ClonedOrderBanner />
      <GenericErrorBanner class="mb-4" />
      <CreateOrderAir @air-order-navigation-items="setNavigationItems($event)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import CreateOrderAir from '@/views/createOrder/air/AirOrder.vue';
import FormNavigation from '@/components/form/FormNavigation.vue';
import GenericErrorBanner from '@/components/createOrder/sharedComponents/GenericErrorBanner.vue';
import QuoteToBookBanner from '@/components/base/banner/QuoteToBookBanner.vue';
import { Segment } from '@dfe/dfe-book-api';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useI18n } from 'vue-i18n';
import { OrderTypes } from '@/enums';
import ClonedOrderBanner from '@/components/base/banner/ClonedOrderBanner.vue';

const { t } = useI18n();

const createOrderFormStore = useCreateOrderFormStore();
const createOrderDataStore = useCreateOrderDataStore();
const { orderType, isOrderFromQuote, quoteInformation } = storeToRefs(createOrderFormStore);

const airNavigationItems = ref([]);

const setNavigationItems = (items: []) => {
  airNavigationItems.value = items;
};

onMounted(() => {
  if (orderType.value) {
    return;
  }
  orderType.value = OrderTypes.AirExportOrder;
});

onMounted(async () => {
  await createOrderDataStore.fetchCountries(Segment.AIR);
});
</script>
