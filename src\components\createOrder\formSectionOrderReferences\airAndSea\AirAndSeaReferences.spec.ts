import AirAndSeaReferences from '@/components/createOrder/formSectionOrderReferences/airAndSea/AirAndSeaReferences.vue';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import TextField from '@/components/form/TextField.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import MultipleTextFieldsWithCheckboxes from '@/components/createOrder/formSectionOrderReferences/MultipleTextFieldsWithCheckboxes.vue';
import ButtonDropdownMenu from '@/components/base/ButtonDropdownMenu.vue';
import { beforeEach } from 'vitest';
import { storeToRefs } from 'pinia';

describe('Order references - MultipleTextFields component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(AirAndSeaReferences);
  });

  it('should show shippers reference and quotation reference fields for air orders', async () => {
    const textFields = wrapper.findAllComponents(TextField);

    const checkBoxFields = wrapper.findAllComponents(CheckboxField);

    expect(checkBoxFields).toHaveLength(2);
    expect(textFields).toHaveLength(2);

    expect(textFields.at(0)?.props('label')).toBe('labels.shippers_reference.text');
    expect(textFields.at(1)?.props('label')).toBe('labels.quotation_reference.text');

    expect(checkBoxFields.at(0)?.props('label')).toBe('labels.loading_label.text');
    expect(checkBoxFields.at(1)?.props('label')).toBe('labels.unloading_label.text');
  });

  it('should show error when not at least one is selected (loading/unloading) - shippers reference ', async () => {
    const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
    const { shipperReference } = storeToRefs(createOrderOrderReferencesFormStore);
    const checkBoxFields = wrapper.findAllComponents(CheckboxField);

    shipperReference.value.loading = false;
    shipperReference.value.unloading = false;

    await wrapper.vm.$nextTick();

    expect(checkBoxFields).toHaveLength(2);
    expect(checkBoxFields?.at(0)?.props('label')).toBe('labels.loading_label.text');
    expect(checkBoxFields?.at(0)?.props('error')).toBe(true);
    expect(checkBoxFields?.at(1)?.props('label')).toBe('labels.unloading_label.text');
    expect(checkBoxFields?.at(1)?.props('error')).toBe(true);
  });

  it('should not show error when at least one is selected (loading/unloading) - shippers reference ', async () => {
    const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
    const { shipperReference } = storeToRefs(createOrderOrderReferencesFormStore);
    const checkBoxFields = wrapper.findAllComponents(CheckboxField);

    shipperReference.value.loading = false;
    shipperReference.value.unloading = true;

    await wrapper.vm.$nextTick();

    expect(checkBoxFields).toHaveLength(2);
    expect(checkBoxFields?.at(0)?.props('label')).toBe('labels.loading_label.text');
    expect(checkBoxFields?.at(0)?.props('error')).toBe(false);
    expect(checkBoxFields?.at(1)?.props('label')).toBe('labels.unloading_label.text');
    expect(checkBoxFields?.at(1)?.props('error')).toBe(false);
  });


  it('shows components for further references and hides corresponding add button', async () => {
    const store = useCreateOrderOrderReferencesFormStore();

    expect(wrapper.findAll('.add-button')).toHaveLength(4);

    store.furtherReferencesOrder = [OrderReferenceType.PURCHASE_ORDER_NUMBER];
    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(MultipleTextFieldsWithCheckboxes)).toHaveLength(1);
    expect(wrapper.findAll('.add-button')).toHaveLength(3);

    store.furtherReferencesOrder = [
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.INVOICE_NUMBER,
    ];
    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(MultipleTextFieldsWithCheckboxes)).toHaveLength(2);
    expect(wrapper.findAll('.add-button')).toHaveLength(2);

    store.furtherReferencesOrder = [
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
    ];
    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(MultipleTextFieldsWithCheckboxes)).toHaveLength(3);
    expect(wrapper.findAll('.add-button')).toHaveLength(1);

    store.furtherReferencesOrder = [
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
    ];
    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(MultipleTextFieldsWithCheckboxes)).toHaveLength(4);
    expect(wrapper.findAll('.add-button')).toHaveLength(0);

    store.$reset();
  });

  it.each([
    [0, OrderReferenceType.INVOICE_NUMBER],
    [1, OrderReferenceType.PURCHASE_ORDER_NUMBER],
    [2, OrderReferenceType.DELIVERY_NOTE_NUMBER],
    [3, OrderReferenceType.OTHERS],
  ])('calls action on clicking add button -> %s', async (idx, referenceType) => {
    const store = useCreateOrderOrderReferencesFormStore();

    wrapper.findAll('.add-button').at(idx)?.trigger('click');
    await wrapper.vm.$nextTick();

    expect(store.addReference).toHaveBeenCalledTimes(1);
    expect(store.addReference).toHaveBeenCalledWith(referenceType, {
      withLoadingData: true,
    });

    store.$reset();
    vi.clearAllMocks();
  });

  it('shows / hides button dropdown component for even more references', async () => {
    const store = useCreateOrderOrderReferencesFormStore();

    expect(wrapper.findComponent(ButtonDropdownMenu).exists()).toBe(true);

    store.furtherReferencesOrder = [
      OrderReferenceType.MARKS_AND_NUMBERS,
      OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
      OrderReferenceType.PACKAGING_LIST_NUMBER,
      OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
    ];
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(ButtonDropdownMenu).exists()).toBe(false);
  });
});
