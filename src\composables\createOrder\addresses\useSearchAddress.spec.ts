import { useSearchAddress } from '@/composables/createOrder/addresses/useSearchAddress';
import { useCreateAddressDataStore } from '@/store/addressBook/address';

import { initPinia } from '@test/util/init-pinia';
import { mockServer } from '@/mocks/server';
import { expect } from 'vitest';

describe('useSearchAddress composable', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {},
    });
  });

  it('should search addresses in address book', () => {
    const createAddressDataStore = useCreateAddressDataStore();
    const spy = vi.spyOn(createAddressDataStore, 'searchNameInAddressBook');

    useSearchAddress('Test');

    expect(spy).toHaveBeenCalledWith('test');
  });

  it('should NOT search in address book for empty string or less than 3 characters', () => {
    const createAddressDataStore = useCreateAddressDataStore();
    const spy = vi.spyOn(createAddressDataStore, 'searchNameInAddressBook');

    ['', 'te'].map((value) => {
      useSearchAddress(value);
      expect(spy).not.toHaveBeenCalled();
    });
  });
});
