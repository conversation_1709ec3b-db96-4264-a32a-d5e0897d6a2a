<template>
  <SectionCard>
    <template #headline>
      {{ isAirOrder ? t('labels.airport_delivery.text') : t('labels.port_delivery.text') }}
    </template>

    <div class="grid-container-form">
      <VRow>
        <VCol cols="12" md="8" xl="5">
          <h4 class="text-h4 mb-4 d-inline-flex align-center">
            {{
              isAirOrder
                ? t('labels.airport_delivery_date.text')
                : t('labels.port_delivery_date.text')
            }}
            <InfoButtonWithTooltip
              class="ml-1"
              :label="
                isAirOrder ? t('labels.airport_local_date.text') : t('labels.port_local_date.text')
              "
              location="right"
              :max-width="260"
            />
          </h4>
          <!-- Hidden because of ticket DFE-2907 -->
          <SwitchField
            v-if="false"
            v-model="requestArrangement"
            :label="t('labels.request_arrangement.text')"
          />
        </VCol>
        <VCol cols="12" md="8" xl="5">
          <p v-if="requestArrangement" class="request-arrangement-info text-body-2">
            {{
              isAirOrder
                ? t('labels.airport_request_arrangement_info.text')
                : t('labels.port_request_arrangement_info.text')
            }}
          </p>
          <FormCollection v-else />
        </VCol>
      </VRow>
    </div>
  </SectionCard>
</template>

<script setup lang="ts">
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import SectionCard from '@/components/base/SectionCard.vue';
import FormCollection from '@/components/createOrder/formSectionCollectionAndDelivery/FormCollection.vue';
import SwitchField from '@/components/form/SwitchField.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const createOrderFormCollectionAndDeliveryStore = useCreateOrderFormCollectionAndDeliveryStore();

const { requestArrangement } = storeToRefs(createOrderFormCollectionAndDeliveryStore);
const createOrderFormStore = useCreateOrderFormStore();

const { isAirOrder } = storeToRefs(createOrderFormStore);
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as vars;
@use '@/styles/settings';
@use 'sass:map';
:deep(.grid-container-form) {
  grid-auto-rows: auto;
  grid-gap: 0.75em;

  @media #{map.get(settings.$display-breakpoints, 'lg')} {
    grid-template-columns: repeat(3, 1fr);
  }
  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
