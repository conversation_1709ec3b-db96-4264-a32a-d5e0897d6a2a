import { usePortSearch } from '@/composables/createOrder/usePortSearch';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { initPinia } from '../../../test/util/init-pinia';
import { nextTick } from 'vue';
import { PortType } from '@dfe/dfe-book-api';
import { ports } from '@/mocks/fixtures/ports';

vi.useFakeTimers();
describe('usePortSearch', () => {
  let createOrderDataStore: ReturnType<typeof useCreateOrderDataStore>;

  beforeAll(() => {
    initPinia();

    createOrderDataStore = useCreateOrderDataStore();
    createOrderDataStore.searchPort = vi.fn().mockReturnValue(ports);
  });

  it('should debounce the search', async () => {
    const portType: PortType = PortType.AIRPORT;
    const portSearch = usePortSearch({ portType, debounce: 0 });

    portSearch.searchTerm.value = 'abc';
    await nextTick();

    expect(createOrderDataStore.searchPort).not.toHaveBeenCalled();
    vi.advanceTimersByTime(100);
    expect(createOrderDataStore.searchPort).toHaveBeenCalled();
  });
});
