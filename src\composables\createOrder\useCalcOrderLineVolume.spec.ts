import { calculateVolume } from '@/composables/createOrder/useCalcOrderLineVolume';

describe('calculateVolume', () => {
  it.each([
    ['every values given', [3, 120, 80, 116], 3.341],
    ['one value missing', [3, 0, 80, 116], 0],
    ['no values given', [0, 0, 0, 0], 0],
  ])(
    'calculate volume of oder line with %s',
    (_, [quantity, length, width, height], expectedResult) => {
      const result = calculateVolume(quantity, length, width, height);
      expect(result).toBe(expectedResult);
    },
  );
});
