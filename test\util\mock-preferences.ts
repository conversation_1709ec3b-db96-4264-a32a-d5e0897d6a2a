import { DEFAULT_PREFERENCES, Preferences } from '@dfe/dfe-frontend-client';
import { vi } from 'vitest';
import { computed } from 'vue';

export const getComputedPreferences = (preferences?: Partial<Preferences>) => {
  const {
    dateFormat = DEFAULT_PREFERENCES.dateFormat,
    dateFormatPlaceholder = DEFAULT_PREFERENCES.dateFormatPlaceholder,
    firstDayOfWeek = 'Monday',
    locale = DEFAULT_PREFERENCES.locale,
    numberFormat = DEFAULT_PREFERENCES.numberFormat,
    printerHeight = DEFAULT_PREFERENCES.printerHeight,
    printerPresetName = DEFAULT_PREFERENCES.printerPresetName,
    printerWidth = DEFAULT_PREFERENCES.printerWidth,
    timeFormat = DEFAULT_PREFERENCES.timeFormat,
    mobileNumber,
    phoneNumber,
  } = { ...DEFAULT_PREFERENCES, ...preferences };

  return {
    locale: computed(() => locale),
    timeFormat: computed(() => timeFormat),
    numberFormat: computed(() => numberFormat),
    firstDayOfWeek: computed(() => firstDayOfWeek),
    dateFormat: computed(() => dateFormat),
    dateFormatPlaceholder: computed(() => dateFormatPlaceholder),
    phoneNumber: computed(() => phoneNumber),
    mobileNumber: computed(() => mobileNumber),
    printerPresetName: computed(() => printerPresetName),
    printerHeight: computed(() => printerHeight),
    printerWidth: computed(() => printerWidth),
  };
};

export const usePreferencesMock = vi.fn(() => getComputedPreferences());

vi.doMock('@dfe/dfe-frontend-composables', async () => ({
  ...(await vi.importActual('@dfe/dfe-frontend-composables')),
  usePreferences: usePreferencesMock,
  useTimeFormat: vi.fn(() => 'HH:mm'),
}));
