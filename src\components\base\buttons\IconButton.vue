<template>
  <DfeIconButton :tooltip="''" variant="plain" :ripple="false" v-bind="$attrs">
    <MaterialSymbol :size="small ? 16 : 24" :color="color">
      <slot />
    </MaterialSymbol>
  </DfeIconButton>
</template>

<script lang="ts" setup>
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { DfeIconButton } from '@dfe/dfe-frontend-shared-components';

defineProps<{
  color?: string;
  small?: boolean;
}>();
</script>
<style scoped lang="scss">
.v-btn {
  &.v-btn--size-small {
    min-width: 0;
    padding: 0 !important;
  }
}
</style>
