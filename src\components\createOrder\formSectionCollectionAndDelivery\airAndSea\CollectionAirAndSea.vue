<template>
  <SectionCard v-data-test="'section-collection-and-delivery'">
    <template #headline>
      {{ t('labels.collection_title.text') }}
    </template>
    <div class="grid-container-form">
      <VRow>
        <VCol cols="auto">
          <h4 class="text-h4 d-inline-flex align-center">
            {{ t('labels.date_time_label.text') }}
            <InfoButtonWithTooltip
              class="ml-1"
              :label="t('labels.local_date_time_of_collection_point.text')"
              location="right"
              :max-width="260"
            />
          </h4>
          <!-- Hidden because of ticket DFE-2907 -->
          <SwitchField
            v-if="false"
            v-model="requestArrangement"
            :label="t('labels.request_arrangement.text')"
          />
        </VCol>
      </VRow>

      <VRow>
        <VCol cols="12" md="8">
          <p v-if="requestArrangement" class="request-arrangement-info text-body-2">
            {{ t('labels.request_arrangement_info.text') }}
          </p>
          <FormCollection v-else />
        </VCol>
      </VRow>
      <VRow>
        <VCol cols="12">
          <h4 class="text-h4 mb-4">
            {{ t('labels.requirements_label.text') }}
          </h4>
          <CheckboxField
            v-model="tailLiftCollection"
            class="d-inline-flex"
            :label="t('labels.taillift_collection.text')"
          />
        </VCol>
      </VRow>
    </div>
  </SectionCard>
</template>

<script setup lang="ts">
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import SectionCard from '@/components/base/SectionCard.vue';
import FormCollection from '@/components/createOrder/formSectionCollectionAndDelivery/FormCollection.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import SwitchField from '@/components/form/SwitchField.vue';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const createOrderFormCollectionAndDeliveryStore = useCreateOrderFormCollectionAndDeliveryStore();

const { tailLiftCollection, requestArrangement } = storeToRefs(
  createOrderFormCollectionAndDeliveryStore,
);
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';
@use 'sass:map';

:deep(.grid-container-form) {
  grid-auto-rows: auto;
  grid-gap: 0.75em;

  @media #{map.get(settings.$display-breakpoints, 'lg')} {
    grid-template-columns: repeat(3, 1fr);
  }
  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
