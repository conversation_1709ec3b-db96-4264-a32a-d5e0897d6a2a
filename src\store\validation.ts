import { defineStore, storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';

export interface CreateValidationState {
  formValidationSectionsRoad: {
    roadOrderCustomers: boolean;
    roadOrderAddresses: boolean;
    roadOrderFreight: boolean;
    roadOrderCollectionAndDelivery: boolean;
    roadOrderAccountingAndAdditionalServices: boolean;
    roadOrderReferences: boolean;
    roadOrderTexts: boolean;
    roadOrderDocuments: boolean;
    roadOrderIncoterms: boolean;
  };
  formValidationSectionsAir: {
    airOrderCustomers: boolean;
    airOrderAddresses: boolean;
    airOrderFreight: boolean;
    airOrderProducts: boolean;
    airOrderIncoterms: boolean;
    airOrderCollection: boolean;
    airOrderReferences: boolean;
    airOrderDocuments: boolean;
  };
  formValidationSectionsSea: {
    seaOrderCustomers: boolean;
    seaOrderAddresses: boolean;
    seaOrderIncoterms: boolean;
    seaOrderFreight: boolean;
    seaOrderCollection: boolean;
    seaOrderReferences: boolean;
    seaOrderDocuments: boolean;
  };
}

export const useValidationDataStore = defineStore('createValidationData', {
  state: (): CreateValidationState => ({
    formValidationSectionsRoad: {
      roadOrderCustomers: true,
      roadOrderAddresses: true,
      roadOrderFreight: true,
      roadOrderCollectionAndDelivery: true,
      roadOrderAccountingAndAdditionalServices: true,
      roadOrderReferences: true,
      roadOrderTexts: true,
      roadOrderDocuments: true,
      roadOrderIncoterms: true,
    },
    formValidationSectionsAir: {
      airOrderCustomers: true,
      airOrderAddresses: true,
      airOrderFreight: true,
      airOrderProducts: true,
      airOrderIncoterms: true,
      airOrderCollection: true,
      airOrderReferences: true,
      airOrderDocuments: true,
    },
    formValidationSectionsSea: {
      seaOrderCustomers: true,
      seaOrderAddresses: true,
      seaOrderIncoterms: true,
      seaOrderFreight: true,
      seaOrderCollection: true,
      seaOrderReferences: true,
      seaOrderDocuments: true,
    },
  }),
  getters: {
    getFormSectionsValid(): boolean {
      const orderFormStore = useCreateOrderFormStore();
      const { isAirOrder, isSeaOrder } = storeToRefs(orderFormStore);

      let formValidationSections:
        | CreateValidationState['formValidationSectionsRoad']
        | CreateValidationState['formValidationSectionsAir']
        | CreateValidationState['formValidationSectionsSea'] = {
        ...this.formValidationSectionsRoad,
      };

      if (isAirOrder.value) {
        formValidationSections = { ...this.formValidationSectionsAir };
      }

      if (isSeaOrder.value) {
        formValidationSections = { ...this.formValidationSectionsSea };
      }

      return Object.values(formValidationSections).every((isValid) => isValid);
    },
    getForwardingOrderIncoterms(): boolean {
      const orderFormStore = useCreateOrderFormStore();
      const { isAirOrder, isSeaOrder } = storeToRefs(orderFormStore);

      if (isAirOrder.value) {
        return this.formValidationSectionsAir.airOrderIncoterms;
      }

      if (isSeaOrder.value) {
        return this.formValidationSectionsSea.seaOrderIncoterms;
      }

      return this.formValidationSectionsRoad.roadOrderIncoterms;
    },
  },
  actions: {
    scrollToFirstInvalidSection() {
      const btn = document.querySelector<HTMLElement>('.navigation button.has-error');
      btn?.click();
    },
  },
});
