import CardMain from '@/components/createOrder/formSectionIncoTerms/CardMain.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useTermStore } from '@/store/createOrder/formTerms';
import { airExportOrder } from '@/mocks/fixtures/order';
import type { AirExportQuoteInformation, IncoTerm } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import LinkElement from '@/components/base/LinkElement.vue';
import { VRadioGroup } from 'vuetify/components';
import { Segment } from '@dfe/dfe-book-api';
import { HandOverSelection } from '@/types/hand-over';
import { mockServer } from '@/mocks/server';
import { incoTerms } from '@/mocks/fixtures/incoTerms';
import { incotermConfig } from '@/mocks/fixtures/configs';

describe('IncoTerms CardMain component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(CardMain);
    expect(wrapper).toBeDefined();
  });

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        incoTerms,
        incotermConfig,
      },
    });
  });

  it('should not show incoTerms and show info to enter address', () => {
    const disabledMessage = wrapper.find('.disabled-message');
    const incoTerms = wrapper.findComponent(VRadioGroup);
    const link = wrapper.findComponent(LinkElement);

    expect(disabledMessage.exists()).toBeTruthy();
    expect(incoTerms.exists()).toBeFalsy();
    expect(link.exists()).toBeTruthy();
  });

  it('should not show info and should show incoTerms and link', async () => {
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress, consigneeAddress } = storeToRefs(addressStore);

    shipperAddress.value.address.name = 'Test';
    consigneeAddress.value.address.name = 'Test';

    await wrapper.vm.$nextTick();

    const disabledMessage = wrapper.find('.disabled-message');
    const link = wrapper.findComponent(LinkElement);
    const incoTerms = wrapper.findComponent(VRadioGroup);

    expect(disabledMessage.exists()).toBeFalsy();
    expect(incoTerms.exists()).toBeTruthy();
    expect(link.exists()).toBeTruthy();
  });

  it('shows selected incoTerm card if there is an order from Quote (air export or road with daily price)', async () => {
    const termStore = useTermStore();
    const createOrderFormStore = useCreateOrderFormStore();
    const createOrderReferencesStore = useCreateOrderOrderReferencesFormStore();
    const { selectedIncoTerm } = storeToRefs(termStore);

    selectedIncoTerm.value = airExportOrder.incoTerm as IncoTerm;
    createOrderFormStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;
    createOrderFormStore.orderType = OrderTypes.AirExportOrder;
    await wrapper.vm.$nextTick();

    const expectedIncoTermText = `${selectedIncoTerm.value.code} - ${selectedIncoTerm.value.label}`;
    const selectedIncoTermCard = wrapper.findAllComponents({ name: 'v-card' }).at(1);

    expect(selectedIncoTermCard?.exists()).toBe(true);
    expect(wrapper.find('h5').text()).toBe(expectedIncoTermText);
    expect(wrapper.find('div').text()).toContain(selectedIncoTerm.value.description);

    createOrderFormStore.orderType = OrderTypes.RoadForwardingOrder;
    createOrderReferencesStore.dailyPriceReference = '060';
    await wrapper.vm.$nextTick();

    expect(selectedIncoTermCard?.exists()).toBe(true);
    expect(wrapper.find('h5').text()).toBe(expectedIncoTermText);
    expect(wrapper.find('div').text()).toContain(selectedIncoTerm.value.description);
  });

  it('should reset selectedIncoTerm if it is not valid anymore', async () => {
    const termStore = useTermStore();
    const addressStore = useCreateOrderAddressesStore();
    const formStore = useCreateOrderFormStore();
    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);
    const { incoTermsOptions, selectedIncoTerm } = storeToRefs(termStore);

    transportType.value = Segment.SEA;
    orderType.value = OrderTypes.SeaImportOrder;

    selectedIncoTerm.value = {
      id: 19,
      code: 'CFR',
      dachserCode: 'CFR',
      description: '',
      label: 'Cost and Freight',
    };

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 15,
        code: 'FAS',
        dachserCode: 'FAS',
        description: '',
        label: 'Free Alongside Ship ',
      },
      {
        id: 14,
        code: 'FOB',
        dachserCode: 'FOB',
        description: '',
        label: 'Free on Board',
      },
    ];

    await vi.waitFor(() => {
      expect(incoTermsOptions.value).toMatchObject(expectedResult);
      expect(selectedIncoTerm.value).toBeNull();
    });
  });

  it('should preselect incoTerm if only one option is available', async () => {
    const termStore = useTermStore();
    const addressStore = useCreateOrderAddressesStore();
    const formStore = useCreateOrderFormStore();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);
    const { incoTermsOptions, selectedIncoTerm } = storeToRefs(termStore);

    transportType.value = Segment.AIR;
    orderType.value = OrderTypes.AirImportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 4,
        code: 'FCA',
        dachserCode: 'FOA',
        description: '',
        label: 'Free Carrier Departure Airport',
      },
    ];

    await vi.waitFor(() => {
      expect(incoTermsOptions.value).toMatchObject(expectedResult);
      expect(selectedIncoTerm.value).toMatchObject(expectedResult[0]);
    });
  });
});
