import MoreButton from '@/components/createOrder/formSectionFooter/MoreButton.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { mockServer } from '@/mocks/server';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { OrderStatus, Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';
import { mockResizeObserver } from 'jsdom-testing-mocks';

describe('MoreButton component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
    mockServer({
      environment: 'test',
    });
  });

  it('mounts', () => {
    wrapper = mount(MoreButton);
  });

  it('shows save order draft button, if customerNumber is undefined', async () => {
    const formStore = useCreateOrderFormStore();

    await wrapper.find('.v-btn').trigger('click');

    expect(wrapper.find("[data-test='save-as-draft']").exists()).toBe(false);

    formStore.$reset();
  });

  it('hides order draft button, if customerNumber is defined', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.transportType = Segment.AIR;
    await wrapper.find('.v-btn').trigger('click');

    await wrapper.vm.$nextTick();

    expect(formStore.customerNumber).toEqual('00000001');
    expect(wrapper.find("[data-test='book-save-as-draft']").exists()).toBe(true);
  });

  it('disable order draft button, if order is completed', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderData = {
      orderType: OrderTypes.AirExportOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };

    await wrapper.find('.v-btn').trigger('click');
    await wrapper.vm.$nextTick();

    expect(
      wrapper
        .findAllComponents({ name: 'v-list-item' })
        .find((c) => c.attributes('data-test') === 'book-save-as-draft')
        ?.props('disabled'),
    ).toBe(true);

    formStore.orderData = {
      orderType: OrderTypes.RoadForwardingOrder,
      orderStatus: { status: OrderStatus.LABEL_PENDING },
    };

    await wrapper.find('.v-btn').trigger('click');
    await wrapper.vm.$nextTick();

    expect(
      wrapper
        .findAllComponents({ name: 'v-list-item' })
        .find((c) => c.attributes('data-test') === 'book-save-as-draft')
        ?.props('disabled'),
    ).toBe(true);
  });

  it('hides print-labels button, if is not RoadForwardingOrder', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.transportType = Segment.AIR;
    await wrapper.find('.v-btn').trigger('click');

    await wrapper.vm.$nextTick();

    expect(wrapper.find("[data-test='book-print-labels']").exists()).toBe(false);
    formStore.$reset();
  });

  it('shows print-labels button, if isRoadForwardingOrder', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;
    formStore.transportType = Segment.ROAD;
    await wrapper.find('.v-btn').trigger('click');

    await wrapper.vm.$nextTick();

    expect(wrapper.find("[data-test='book-print-labels']").exists()).toBe(true);
  });
});
