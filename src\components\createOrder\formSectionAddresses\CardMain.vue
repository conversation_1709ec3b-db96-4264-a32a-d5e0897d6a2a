<template>
  <SectionCard ref="formContent" v-data-test="'section-addresses'">
    <template #headline>
      {{ t('labels.addresses_title.text') }}
    </template>
    <AddressesTransport>
      <template #shipperAddress>
        <slot name="shipperAddress" />
      </template>
      <template #consigneeAddress>
        <slot name="consigneeAddress" />
      </template>
    </AddressesTransport>
    <slot />
    <div v-if="isAirOrder || isSeaOrder">
      <VDivider class="my-6"></VDivider>
      <slot name="routing" />
    </div>
    <slot name="additionalAddresses" />
  </SectionCard>
</template>

<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import AddressesTransport from '@/components/createOrder/formSectionAddresses/AddressesTransport.vue';
import useLoadingPointsWithLabel from '@/composables/createOrder/useLoadingPointsWithLabel';
import { useLoadingPoints } from '@/composables/data/useLoadingPoints';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

defineProps({
  hasFormValidationError: { type: Boolean, default: false },
});

const createOrderDataStore = useCreateOrderDataStore();
const { data: loadingPoints } = useLoadingPoints();
const createOrderFormStore = useCreateOrderFormStore();
const { customerNumber, transportType, isAirOrder, isSeaOrder } = storeToRefs(createOrderFormStore);

const { setLoadingPoint } = useLoadingPointsWithLabel(loadingPoints);

watch(
  customerNumber,
  (value) => {
    if (value) {
      createOrderDataStore.fetchFurtherAddressTypes(value, transportType.value);
      createOrderDataStore.fetchFavoriteCountries(value, transportType.value);
    }
  },
  { immediate: true },
);

watch(
  () => loadingPoints?.value,
  () => {
    setLoadingPoint();
  },
);
</script>

<style lang="scss" scoped>
.v-card.output-address-card {
  background-color: var(--color-base-grey-50);
  border-color: var(--color-base-grey-400) !important;

  &--has-error {
    border-color: var(--color-base-red-500) !important;
  }
}
</style>
