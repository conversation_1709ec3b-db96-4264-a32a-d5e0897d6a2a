<template>
  <VFooter app :class="{ 'd-none': keyboardVisible }" class="pa-0">
    <VCol class="v-footer__inner d-flex justify-end align-center" cols="12">
      <CheckboxField
        v-if="showCreateAnother"
        v-model="createAnother"
        :label="t('labels.create_another.text')"
        class="mr-6 mt-0 pt-0"
      />
      <VBtn
        v-data-test="'finish-btn'"
        class="mr-4"
        color="primary"
        variant="outlined"
        @click="checkAndSaveOrder(false)"
      >
        {{ t('labels.check_and_save.text') }}
      </VBtn>
      <VBtn v-data-test="'book-btn'" class="mr-2" color="primary" @click="submitSendOrder(false)">
        {{ t('labels.submit_order.text') }}
      </VBtn>
      <div class="more-button">
        <MoreButton
          @save-as-draft="saveDraft"
          @save-print-labels="submitPrintLabels(false)"
          @create-order-validate="createOrderValidate"
        />
      </div>
    </VCol>
    <ConfirmPrompt
      ref="discard-changes-prompt"
      v-model="isCloseOverlayActive"
      :headline="t('labels.close_order_form.text')"
      :cancel-text="t('labels.back_label.text')"
      :confirm-text="t('labels.discard_changes.text')"
      :has-border="false"
      size="sm"
      @confirm="discardChanges"
      @close="isCloseOverlayActive = false"
      @cancel="isCloseOverlayActive = false"
    >
      <h5 class="text-body-2 text-grey-darken-4">
        {{ t('labels.unsaved_address_info.text') }}
      </h5>
    </ConfirmPrompt>
    <ErrorPrompt
      ref="error-prompt"
      v-model="isErrorOverlayActive"
      :headline="t('labels.error_label.text')"
      :cancel-text="t('labels.close_order_form_error.text')"
      :confirm-text="t('labels.ok_label.text')"
      :title="t('labels.sending_not_possible.text')"
      :text="t('messages.id7121.text')"
      size="md"
      @close-order-form="closeOverlay"
      @close="isErrorOverlayActive = false"
    >
    </ErrorPrompt>
  </VFooter>
</template>
<script setup lang="ts">
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import ErrorPrompt from '@/components/base/modal/ErrorPrompt.vue';
import MoreButton from '@/components/createOrder/formSectionFooter/MoreButton.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import {
  CreateOrderValidationEmits,
  useCreateOrderValidation,
} from '@/composables/createOrder/useCreateOrderValidation';
import { showDecreaseSSCCsModal } from '@/composables/createOrder/useShowDecreaseSSCCModal';
import useSubmitOrder from '@/composables/createOrder/useSubmitOrder';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import useValidateOrder from '@/composables/form/useValidateOrder';
import { useBackendLabelKey } from '@/composables/useBackendLabel';
import { useClient } from '@/composables/useClient';
import { useVirtualKeyboard } from '@/composables/useVirtualKeyboard';
import { ErrorCode, Routes } from '@/enums';
import { isErrorHttpResponse, isOrderProcessResult } from '@/services/type-guards';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import linkAllDocumentsToOrder from '@/utils/linkAllDocumentsToOrder';
import {
  GeneralProblem,
  type OrderProcessResult,
  OrderStatus,
  ValidationResult,
} from '@dfe/dfe-book-api';
import { useHeapAnalytics } from '@dfe/dfe-frontend-composables';
import { isEqual } from 'lodash';
import { storeToRefs } from 'pinia';
import { computed, onBeforeUnmount, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { client } = useClient();
const heap = useHeapAnalytics();

type Emits = CreateOrderValidationEmits;

const emit = defineEmits<Emits>();

const isCloseOverlayActive = ref(false);
const isErrorOverlayActive = ref(false);
const errorBannerStore = useErrorBanner();
const createOrderFormStore = useCreateOrderFormStore();
const {
  shipmentNumber,
  createAnother,
  transportType,
  isOrderFromQuote,
  isEditMode,
  customTypeValidationError,
  isValidationTriggered,
  initialOrder,
} = storeToRefs(createOrderFormStore);
const { validateOrder, order } = useValidateOrder();
const { saveOrderData, orderData } = storeToRefs(useCreateOrderFormStore());

const labelPrintService = useLabelStore();

const { submitOrder } = useSubmitOrder(client);
const showCreateAnother = computed(() => !isOrderFromQuote.value && !isEditMode.value);
const { data: customerSettings } = useCustomerSettings();

const { createOrderValidate } = useCreateOrderValidation(emit);

const createOrderDocuments = useCreateOrderDocumentsStore();
const { documents, initialDocuments } = storeToRefs(createOrderDocuments);

const hasDocumentsUnsavedChanges = ref(false);
const hasUnsavedChanges = computed(() => !isEqual(order.value, initialOrder.value));
const preventUnsavedChangesCheck = ref(false);

watch(
  () => documents.value,
  (newValue) => (hasDocumentsUnsavedChanges.value = !isEqual(newValue, initialDocuments.value)),
  { deep: true },
);

const saveDraft = async () => {
  const { saved, problem } = await createOrderFormStore.saveOrder(order.value);

  await linkAllDocumentsToOrder();
  await handleNewOrderAfterSave(saved);

  if (saved) {
    displayToastAfterSave('labels.order_saved.text');
    initialOrder.value = { ...order.value };
    closeOverlay(true);
    heap.track('book-order-draft-saved');
  } else if (problem) {
    errorModalDialog(problem);
  } else {
    client?.toast.error(String(t('labels.save_error.text')));
  }
};

const submitPrintLabels = async (calledByCallback: boolean) => {
  if (showDecreaseSSCCsModal(calledByCallback, customerSettings.value?.manualNumberOfLabels)) {
    client?.events.emit('showDeleteOverflowSSCCs', () => submitPrintLabels(true));
    return;
  }

  await linkAllDocumentsToOrder();

  isValidationTriggered.value = true;
  const isValid = await createOrderValidate();

  if (!isValid) {
    errorBannerStore.addFrontendErrors();
    return;
  }
  const vaildateResponse = await validateOrder();

  const { valid, results } = vaildateResponse as ValidationResult;

  const error = vaildateResponse as GeneralProblem;

  if (valid) {
    customTypeValidationError.value = false;
  } else if (results) {
    errorBannerStore.pushErrors(results);
  } else {
    errorModalDialog(error);
  }

  await handleNewOrderAfterSave();
  let orderProcessResult: OrderProcessResult;

  try {
    orderProcessResult = await labelPrintService.fetchLabels();
    if (!orderProcessResult.orderLabel || !orderProcessResult.order?.orderId) {
      return;
    }
  } catch (errorResult) {
    if (isErrorHttpResponse(errorResult, isOrderProcessResult)) {
      const processResult = errorResult.error;
      const problem = processResult.error as GeneralProblem;
      client?.modal.show('error', {
        headline: String(t(useBackendLabelKey(problem.title))),
        message: String(t(useBackendLabelKey(problem.detail))),
      });
    }
    return;
  }

  client?.events.emit('printLabels', {
    file: orderProcessResult.orderLabel,
    fileName: shipmentNumber.value ? `labels_${shipmentNumber.value}.pdf` : `labels.pdf`,
    modalHeadline: String(t('labels.labels_label.text')),
    orderId: orderProcessResult.order.orderId,
  });
};

const submitSendOrder = async (calledByCallback: boolean) => {
  if (showDecreaseSSCCsModal(calledByCallback, customerSettings.value?.manualNumberOfLabels)) {
    client?.events.emit('showDeleteOverflowSSCCs', () => submitSendOrder(true));
    return;
  }

  isValidationTriggered.value = true;
  const isValid = await createOrderValidate();

  if (!isValid) {
    errorBannerStore.addFrontendErrors();
    return;
  }

  await linkAllDocumentsToOrder();

  const { validationResult, order, orderLabel, error } = await submitOrder();

  if (error) {
    errorModalDialog(error);
  }

  await handleNewOrderAfterSave(validationResult?.valid);

  if (!validationResult?.valid) {
    errorBannerStore.pushErrors(validationResult?.results);
    client?.events.emit('scrollToGenericErrorBanner');
    return;
  }

  if (order?.orderStatus?.status !== OrderStatus.SENT) {
    client?.modal.show('error', {
      headline: String(t('labels.sending_not_possible.text')),
      message: String(t('messages.id6607.text')),
    });
    return;
  }
  closeDialogForSubmitSendOrder();
  client?.events.emit('clearCreateOrderFormData');
  heap.track('book-order-sent');

  if (orderLabel && order.orderId) {
    const fileName = shipmentNumber.value ? `labels_${shipmentNumber.value}.pdf` : `labels.pdf`;

    client?.events.emit('printLabels', {
      file: orderLabel,
      fileName,
      modalHeadline: String(t('labels.labels_label.text')),
      orderId: order.orderId,
    });
  }

  if (validationResult?.avisSent) {
    client?.toast.success(String(t('labels.avis_sent_success.text')));
  }
};

const checkAndSaveOrder = async (calledByCallback: boolean) => {
  if (showDecreaseSSCCsModal(calledByCallback, customerSettings.value?.manualNumberOfLabels)) {
    client?.events.emit('showDeleteOverflowSSCCs', () => checkAndSaveOrder(true));
    return;
  }

  isValidationTriggered.value = true;
  const isValid = await createOrderValidate();

  if (!isValid) {
    errorBannerStore.addFrontendErrors();
    return;
  }

  await linkAllDocumentsToOrder();

  const validateResponse = await validateOrder();
  const { valid, results } =
    'validationResult' in validateResponse
      ? (validateResponse.validationResult as ValidationResult)
      : (validateResponse as ValidationResult);

  await handleNewOrderAfterSave(valid);

  if (valid) {
    customTypeValidationError.value = false;
    displayToastAfterSave('labels.order_saved.text');
    closeDialog(true);
    heap.track('book-order-checked-and-saved');
  } else if (results) {
    errorBannerStore.pushErrors(results);
    client?.events.emit('scrollToGenericErrorBanner');
  } else {
    const error = validateResponse as GeneralProblem;
    errorModalDialog(error);
  }
};

const handleNewOrderAfterSave = async (skipOrderReload?: boolean) => {
  const orderChanged = saveOrderData.value?.orderId !== orderData.value?.orderId;

  if (
    !orderChanged ||
    !orderData.value?.orderId ||
    !saveOrderData.value?.orderId ||
    !saveOrderData.value?.customerNumber
  ) {
    return;
  }

  const isOnOrderDetail = location.pathname.includes(Routes.ORDER);

  if (isOnOrderDetail) {
    preventUnsavedChangesCheck.value = true;
    const targetPath = `${Routes.ORDER}${Routes.BOOK}/${saveOrderData.value.customerNumber}/${saveOrderData.value.orderId}${location.search}`;
    client?.router.remoteNavigate(targetPath, location.pathname);
    // Timeout to allow the router to navigate before continuing - not connected ot waiting on any requests
    await new Promise((resolve) => setTimeout(resolve, 250));

    preventUnsavedChangesCheck.value = false;
  }

  if (skipOrderReload) {
    return;
  }

  client?.events.emit('editOrder', {
    orderId: saveOrderData.value.orderId,
    customerNumber: parseInt(saveOrderData.value.customerNumber, 10),
  });
};

const closeDialogForSubmitSendOrder = () => {
  closeDialog();
  if (!createAnother.value) {
    displayToastAfterSave('labels.order_sent.text');
  }
};

const closeDialog = (updateOrders?: boolean) => {
  const dialogOptions = createAnother.value
    ? {
        reopen: createAnother.value,
        transportType: transportType.value,
        updateOrders: updateOrders ?? false,
      }
    : { reopen: createAnother.value, updateOrders: updateOrders ?? false };
  client?.events.emit('closeBookDialog', dialogOptions);
};

const closeOverlay = (updateOrders?: boolean) => {
  if (isCloseOverlayActive.value) {
    isCloseOverlayActive.value = false;
  }
  if (isErrorOverlayActive.value) {
    isErrorOverlayActive.value = false;
  }
  closeDialog(updateOrders);
};

const discardChanges = () => {
  createAnother.value = false;
  client?.events.emit('clearCreateOrderFormData');
  closeOverlay();
};

const displayToastAfterSave = (toastMessage: string) => {
  const orderId = saveOrderData.value?.orderId;
  const customerNumber = saveOrderData.value?.customerNumber;

  if (orderId && customerNumber) {
    const targetPath = `${Routes.ORDER}${Routes.BOOK}`;
    const orderUrl = `${targetPath}/${customerNumber}/${orderId}`;
    client?.toast.success(
      String(t(toastMessage)),
      {
        text: String(t('labels.order_view.text')),
        handler: () => {
          client?.router.remoteNavigate(orderUrl, location.pathname);
        },
      },
      'create-order-success',
    );
  } else {
    client?.toast.error(String(t('labels.save_error.text')));
  }
};
const handlePreventUnsavedChanges = () => {
  if (preventUnsavedChangesCheck.value) {
    return;
  }

  if (hasUnsavedChanges.value || hasDocumentsUnsavedChanges.value) {
    isCloseOverlayActive.value = true;
  } else if (!isCloseOverlayActive.value) {
    client?.events.emit('closeBookDialog', {});
  }
};

client?.events.on('preventUnsavedChanges', handlePreventUnsavedChanges);

onBeforeUnmount(() => {
  client?.events.off('preventUnsavedChanges', handlePreventUnsavedChanges);
});

const { keyboardVisible } = useVirtualKeyboard();

function errorModalDialog(error: GeneralProblem) {
  if (
    error &&
    (error.errorId === ErrorCode.OrderExpiryErrorCode ||
      error.errorId === ErrorCode.OrderBookingExpiryErrorCode)
  ) {
    isErrorOverlayActive.value = true;
  }
}
</script>
<style lang="scss" scoped>
.v-footer[position='fixed'] {
  z-index: 9;
}

:deep(.v-footer__inner) {
  background: var(--color-base-white);
  border-top: 1px solid var(--color-base-grey-400);
  padding: 14px 32px;
}
</style>
