import { VueQueryPlugin } from '@tanstack/vue-query';
import { config } from '@vue/test-utils';
import vuetify from '@/plugins/vuetify';
import { ClientKey } from '@/types/client';
import { initPinia } from './util/init-pinia';
import { createClientMock } from './util/mock-client';
import { createI18n } from 'vue-i18n';
import { vi } from 'vitest';
import { Access, createUserProfilePlugin, getParsedToken } from '@dfe/dfe-frontend-user-profile';
import { createComposablePlugin } from '@dfe/dfe-frontend-composables';
import './util/mock-preferences';

const client = createClientMock({
  token: getParsedToken({
    quote: Access.Full,
    book: Access.Full,
    customerNumbers: {
      user: [{ number: '00000001', segment: 'ROAD' }],
      company: [],
    },
  }),
});

if (!window.visualViewport) {
  window.visualViewport = {
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => true,
    width: 1024,
    height: 768,
    offsetLeft: 0,
    offsetTop: 0,
    pageTop: 0,
    pageLeft: 0,
    scale: 1,
    onresize: null,
    onscroll: null,
  };
}

config.global.plugins = [
  vuetify,
  createI18n({}),
  initPinia(),
  VueQueryPlugin,
  createUserProfilePlugin({ client }),
  createComposablePlugin({ client }),
];
config.global.provide = {
  [ClientKey as symbol]: client,
};

config.global.directives = {
  'data-test': (el, binding) => {
    el.setAttribute(`data-test`, `book-${binding.value}`);
  },
};

config.global.renderStubDefaultSlot = true;

config.global.mocks = {
  $appRoot: document.body,
};

vi.mock('@/utils/createUuid', () => {
  let id = 0;
  return {
    createUuid: () => id++,
  };
});

vi.mock('@/plugins/i18n', async () => {
  const actualModule = await vi.importActual('@/plugins/i18n');
  return {
    ...actualModule,
    i18n: {
      ...(actualModule.i18n as ReturnType<typeof createI18n>),
      global: {
        ...(actualModule.i18n as ReturnType<typeof createI18n>).global,
        t: (k) => k,
      },
    },
  };
});

vi.mock('vue-i18n', async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import('vue-i18n');
  return {
    ...actual,
    useI18n: () => ({ t: (key: string) => key }),
  };
});

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
