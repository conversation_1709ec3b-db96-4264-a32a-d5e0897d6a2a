<template>
  <AutocompleteField
    v-model="port"
    v-tooltip="{
      text: tooltip,
      location: 'top',
      disabled: !disabled,
      attach: $appRoot,
    }"
    :items="ports"
    item-title="displayName"
    item-value="code"
    :placeholder="isSeaOrder ? t('labels.select_port.text') : t('labels.select_airport.text')"
    :required="true"
    :message="t('labels.validation_select_input_required.text')"
    :return-object="true"
    no-filter
    :loading="isLoading"
    :show-loader-after-ms="1000"
    :disabled="disabled"
    :clearable="true"
    :menu-icon="getMenuIcon()"
    :error-message="hasEmbargo ? t('labels.transport_not_possible.text') : null"
    @update:model-value="updatePortSelection"
    @search-input="onSearchInput"
  />
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import SearchIcon from '@dfe/dfe-frontend-styles/assets/icons/search-16px.svg';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { PortType } from '@dfe/dfe-book-api';
import type { PortItem } from '@/composables/createOrder/usePortSearch';
import { usePortSearch } from '@/composables/createOrder/usePortSearch';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';

const { t } = useI18n();

interface Props {
  disabled?: boolean;
  tooltip?: string;
}

withDefaults(defineProps<Props>(), {
  disabled: false,
  tooltip: '',
});

const port = defineModel<PortItem | undefined>();

const embargoStore = useEmbargoStore();
const { hasEmbargo } = storeToRefs(embargoStore);
const createOrderFormStore = useCreateOrderFormStore();
const { isSeaOrder } = storeToRefs(createOrderFormStore);

const { ports, searchTerm, isLoading } = usePortSearch({
  portType: isSeaOrder.value ? PortType.SEAPORT : PortType.AIRPORT,
  debounce: 350,
});

const updatePortSelection = (value: PortItem | null) => {
  if (!value) {
    return;
  }
  port.value = value;
};

const onSearchInput = (searchString: string | null) => {
  if (searchString && searchString.length > 2) {
    if (port.value?.displayName !== searchString) {
      searchTerm.value = searchString ?? '';
    }
  }
};

const getMenuIcon = () => {
  if (!port.value) {
    return SearchIcon;
  } else {
    return '';
  }
};

onMounted(() => {
  if (port.value) {
    onSearchInput(port.value.code);
  }
});
</script>

<style scoped lang="scss">
:deep(.v-autocomplete) {
  .v-icon {
    transform: none;
  }
}

:deep(.v-field__clearable) {
  .v-icon--clickable {
    background: url('@dfe/dfe-frontend-styles/assets/icons/close-16px.svg') no-repeat;
    top: 2px;
    left: 6px;
  }
}
.cursor-pointer {
  cursor: pointer;
}
</style>
