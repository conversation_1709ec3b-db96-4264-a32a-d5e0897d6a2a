import type { Events } from '@/types/events';
import type {
  KeycloakProfile,
  KeycloakTokenParsed,
  UserInformation,
} from '@dfe/dfe-frontend-client';
import { createClient } from '@dfe/dfe-frontend-client';

export const userProfileMock: KeycloakProfile = {
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  username: 'username',
};

export const userInformationMock: UserInformation = {
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  fullName: 'fullName',
  username: 'username',
};

export const authInitMock = vi.fn();
export const authGetAuthenticatedMock = vi.fn();
export const authLogoutMock = vi.fn();
export const authGetUserInformationMock = vi.fn(() => userInformationMock);
export const logInfoMock = vi.fn();
export const logErrorMock = vi.fn();
export const logWarnMock = vi.fn();
export const logDebugMock = vi.fn();
export const logTraceMock = vi.fn();
export const preferencesUpdateMock = vi.fn();
export const preferencesFetchMock = vi.fn();
export const preferencesOnChangeMock = vi.fn();
export const modalShowMock = vi.fn();

interface Options {
  environment?: string;
  isAdmin?: boolean;
  token?: KeycloakTokenParsed;
}

export const createClientMock = (options?: Options) => {
  const client = createClient<Events>({
    environment: 'test',
    logger: { printLogs: false, baseUrl: '' },
  });

  client.auth.init = authInitMock;
  client.auth.getAuthenticated = authGetAuthenticatedMock;
  client.auth.logout = authLogoutMock;
  client.auth.getTokenParsed = () => options?.token;
  client.auth.getUserInformation = authGetUserInformationMock;
  client.log.info = logInfoMock;
  client.log.error = logErrorMock;
  client.log.warn = logWarnMock;
  client.log.debug = logDebugMock;
  client.log.trace = logTraceMock;
  client.modal.show = modalShowMock;
  client.preferences.update = preferencesUpdateMock;
  client.preferences.fetch = preferencesFetchMock;
  client.preferences.onChange = preferencesOnChangeMock;
  return client;
};
