import { DateFormat, TimeFormat } from '@/types/createOrder';
import { format as formatDate } from 'date-fns';
import isValidDate from '@/utils/isValidDate';
import { toZonedTime } from 'date-fns-tz';

export function useFormattedDate(
  dateValue: Date | string,
  format: TimeFormat | DateFormat,
  timeZone?: 'UTC',
) {
  const date = timeZone === 'UTC' ? toZonedTime(new Date(dateValue), 'UTC') : new Date(dateValue);

  if (!isValidDate(date)) {
    return dateValue as string;
  }

  return formatDate(date, format);
}

export function useUTCTimeFromDate(dateValue: Date | string, timeFormat: TimeFormat) {
  return useFormattedDate(dateValue, timeFormat, 'UTC');
}
