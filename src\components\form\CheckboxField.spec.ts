import CheckboxField from '@/components/form/CheckboxField.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';

const label = 'Label';
const props = {
  value: false,
};

describe('CheckboxField component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(CheckboxField, {
      props,
    });
  });

  it('emits input event on change', async () => {
    await wrapper.findComponent({ name: 'v-checkbox' }).vm.$emit('update:modelValue', true);

    const event = wrapper.emitted('update:modelValue');
    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([true]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').text()).toBe('');

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });
});
