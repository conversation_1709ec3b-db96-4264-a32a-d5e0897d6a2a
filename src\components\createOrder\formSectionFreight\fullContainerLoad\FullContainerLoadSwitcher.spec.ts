import FullContainerLoadSwitcher from '@/components/createOrder/formSectionFreight/fullContainerLoad/FullContainerLoadSwitcher.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { SanitizeKey } from '@/types/sanitize';
import { OrderType } from '@dfe/dfe-book-api';
import { shallowMount, VueWrapper } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import sanitizeHtml from 'sanitize-html';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { nextTick } from 'vue';

const props = { modelValue: true } as const;

describe('FullContainerLoadSwitcher.vue', () => {
  /* eslint-disable */
  let wrapper: VueWrapper<any>;
  const orderLineStore = useCreateOrderOrderLineFormStore();

  beforeEach(() => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    orderType.value = OrderType.SeaImportOrder;
    orderLineStore.addFullContainerLoad(true);
    wrapper = shallowMount(FullContainerLoadSwitcher, {
      props: { ...props },
      global: { stubs: ['v-dialog'] },
      provide: { [SanitizeKey as symbol]: sanitizeHtml },
    });
  });

  afterEach(() => {
    wrapper.unmount();
    orderLineStore.fullContainerLoads = [];
  });

  it('renders radio buttons for FCL and LCL', () => {
    expect(wrapper.find('[data-test="book-full-container-load-lcl-button"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="book-full-container-load-fcl-button"]').exists()).toBe(true);
  });

  it('updates localFullContainerLoad when props.modelValue changes', async () => {
    await wrapper.setProps({ modelValue: false });
    expect(wrapper.vm.localFullContainerLoad).toBe(false);
    await wrapper.setProps({ modelValue: true });
    expect(wrapper.vm.localFullContainerLoad).toBe(true);
  });

  it('does not emit update:modelValue when confirmation is not required (LCL branch)', async () => {
    await wrapper.setProps({ modelValue: false });
    const lclButton = wrapper.find('[data-test="book-full-container-load-lcl-button"]');
    await lclButton.trigger('click');
    expect((wrapper.vm as any).showConfirmSwitchFclLcl).toBe(false);
    expect(wrapper.emitted('update:modelValue')).toBeFalsy();
  });

  it('displays the confirmation prompt when switching between FCL and LCL', async () => {
    await wrapper.setProps({ modelValue: false });
    const lclButton = wrapper.find('[data-test="book-full-container-load-lcl-button"]');
    await lclButton.trigger('click');
    const confirmPrompt = wrapper.findComponent({ name: 'ConfirmPrompt' });
    expect(confirmPrompt.exists()).toBe(true);
  });

  it('emits update:modelValue when the confirmation prompt is confirmed', async () => {
    await wrapper.setProps({ modelValue: false });
    const lclButton = wrapper.find('[data-test="book-full-container-load-lcl-button"]');
    await lclButton.trigger('click');
    const confirmPrompt = wrapper.findComponent({ name: 'ConfirmPrompt' });
    await confirmPrompt.vm.$emit('confirm');
    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
  });

  it('emits switch to FCL', async () => {
    const fclButton = wrapper.find('[data-test="book-full-container-load-fcl-button"]');
    await fclButton.trigger('click');
    const confirmPrompt = wrapper.findComponent({ name: 'ConfirmPrompt' });
    await confirmPrompt.vm.$emit('confirm');
    await vi.waitFor(() => {
      expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    });

    wrapper.unmount();
  });

  it('closes the confirmation prompt and resets the radio button value when cancelled', async () => {
    await wrapper.setProps({ modelValue: false });
    const lclButton = wrapper.find('[data-test="book-full-container-load-lcl-button"]');
    await lclButton.trigger('click');
    const confirmPrompt = wrapper.findComponent({ name: 'ConfirmPrompt' });
    const currentValue = wrapper.vm.localFullContainerLoad;
    await confirmPrompt.vm.$emit('cancel');
    expect(wrapper.vm.localFullContainerLoad).toBe(!currentValue);
    expect((wrapper.vm as any).showConfirmSwitchFclLcl).toBe(false);
  });

  it('calls internal store methods when FCL is selected', async () => {
    await wrapper.setProps({ modelValue: true });
    const removeAllOrderLinesSpy = vi.spyOn(orderLineStore, 'removeAllOrderLines');
    const removeAllFullContainerLoadsSpy = vi.spyOn(orderLineStore, 'removeAllFullContainerLoads');
    const addFullContainerLoadSpy = vi.spyOn(orderLineStore, 'addFullContainerLoad');
    const fclButton = wrapper.find('[data-test="book-full-container-load-fcl-button"]');
    await fclButton.trigger('click');
    const confirmPrompt = wrapper.findComponent({ name: 'ConfirmPrompt' });
    await confirmPrompt.vm.$emit('confirm');
    expect(removeAllOrderLinesSpy).toHaveBeenCalled();
    expect(removeAllFullContainerLoadsSpy).toHaveBeenCalled();
    expect(addFullContainerLoadSpy).toHaveBeenCalled();
    removeAllOrderLinesSpy.mockRestore();
    removeAllFullContainerLoadsSpy.mockRestore();
    addFullContainerLoadSpy.mockRestore();
  });

  it('processes onRadioUpdate correctly LCL', async () => {
    const radioGroup = wrapper.findComponent({ name: 'VRadioGroup' });
    await radioGroup.vm.$emit('update:modelValue', false);
    expect(wrapper.vm.localFullContainerLoad).toBe(false);
    expect(orderLineStore.fullContainerLoads).toHaveLength(0);
    expect(orderLineStore.orderLines).toHaveLength(1);
  });

  it('processes onRadioUpdate correctly FCL', async () => {
    await wrapper.setProps({ modelValue: true });
    const radioGroup = wrapper.findComponent({ name: 'VRadioGroup' });
    (wrapper.vm as any).onRadioUpdate(true);

    await nextTick();

    await radioGroup.vm.$emit('update:modelValue', true);
    expect(wrapper.vm.localFullContainerLoad).toBe(true);
  });
  it('reset the document when the container changes ', async () => {
    const { documents } = storeToRefs(useCreateOrderDocumentsStore());

    documents.value = [
      {
        documentId: 1,
        documentName: 'invoice.xlsx',
        documentType: 'ABC',
        extension: 'xlsx',
        startProcessing: true,
      },
    ];
    expect(documents.value).toHaveLength(1);
    const radioGroup = wrapper.findComponent({ name: 'VRadioGroup' });
    (wrapper.vm as any).onRadioUpdate(true);

    await nextTick();

    documents.value = [];
    expect(documents.value).toHaveLength(0);
  });
});
