<template>
  <div>
    <VDivider v-if="showDivider" class="my-6"></VDivider>

    <div class="d-flex align-center">
      <VBtn
        v-if="showOrderLine"
        v-data-test="'add-order-line-button'"
        :disabled="disableButtons"
        variant="outlined"
        size="small"
        color="primary"
        class="mb-6 mr-2"
        @click="emit('addOrderLine')"
      >
        <MaterialSymbol class="mr-2" left size="16">
          <AddIcon />
        </MaterialSymbol>
        {{ t('labels.add_line.text') }}
      </VBtn>
      <VBtn
        v-if="showPackingPosition"
        v-data-test="'add-packing-line-button'"
        :disabled="disableButtons"
        variant="outlined"
        size="small"
        color="primary"
        class="mb-6"
        @click="emit('addPackingPosition')"
      >
        <MaterialSymbol class="mr-2" left size="16">
          <AddIcon />
        </MaterialSymbol>
        {{ t('labels.add_packing_position.text') }}
      </VBtn>

      <VBtn
        v-if="showFullContainerLoad"
        v-data-test="'add-full-container-load-line-button'"
        :disabled="disableButtons"
        variant="outlined"
        size="small"
        color="primary"
        class="mb-6"
        @click="emit('addFullContainerLoad')"
      >
        <MaterialSymbol class="mr-2" left size="16">
          <AddIcon />
        </MaterialSymbol>
        {{ t('labels.add_full_container_load.text') }}
      </VBtn>
    </div>
  </div>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import AddIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
import { useI18n } from 'vue-i18n';

interface Props {
  disableButtons?: boolean;
  showOrderLine?: boolean;
  showPackingPosition?: boolean;
  showFullContainerLoad?: boolean;
  showDivider?: boolean;
}

withDefaults(defineProps<Props>(), {
  disableButtons: false,
  showOrderLine: false,
  showPackingPosition: false,
  showFullContainerLoad: false,
  showDivider: true,
});
const emit = defineEmits(['addOrderLine', 'addPackingPosition', 'addFullContainerLoad']);

const { t } = useI18n();
</script>
