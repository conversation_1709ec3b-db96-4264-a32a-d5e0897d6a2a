@use "@dfe/dfe-frontend-styles/build/scss/variables" as vars;

table {
  height: 1px;

  table-layout: auto;
  border-collapse: collapse;

  font-size: vars.$font-size-body-2;
  font-weight: vars.$font-weight-body;
  line-height: vars.$size-line-height-sm;

  caption {
    border-bottom: 1px solid vars.$color-base-grey-400;
  }

  .v-checkbox {
    width: 16px !important;
  }

  :not(tfoot) tr {
    th,
    td {
      // border right for first column
      &:first-child::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 1px;
        background-color: vars.$color-base-grey-400;
      }
    }
  }

  thead {
    z-index: 20;

    tr {
      background-color: vars.$color-base-blue-50;

      th {
        background-color: vars.$color-base-blue-50;
        white-space: nowrap;

        &:first-child {
          z-index: 30;
        }

        // border bottom for header (needs to be absolute because of sticky)
        &::before {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 1px;
          background-color: vars.$color-base-grey-400;
        }

        button {
          font-weight: vars.$font-weight-body;
          color: vars.$color-base-grey-700;

          &.sorted {
            color: vars.$color-base-grey-900;

            svg {
              color: vars.$color-base-grey-900;
            }
          }

          &:not(.no-hover):hover {
            color: vars.$color-base-blue-500;

            svg {
              color: vars.$color-base-blue-500;
            }
          }

          &.no-hover:hover {
            cursor: default;
          }
        }
      }
    }
  }

  tbody > tr {
    td {
      position: relative;

      // for firefox, so link is always 100% height
      height: 100%;
      a {
        height: inherit;
      }

      &:not(.no-border)::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background-color: vars.$color-base-grey-400;
      }

      &:first-child {
        z-index: 10;

        background-color: vars.$color-base-white;
      }
    }

    &:hover td {
      background: vars.$color-base-grey-50;
    }

    &:focus-visible {
      position: relative;
      outline-color: var(--color-base-blue-500) !important;

      border-top: 2px solid var(--color-base-blue-500) !important;
      border-bottom: 2px solid var(--color-base-blue-500) !important;
      border-right: 2px solid var(--color-base-blue-500) !important;

      > td:first-child {
        border-top: 2px solid var(--color-base-blue-500) !important;
        border-bottom: 2px solid var(--color-base-blue-500) !important;
      }
    }
  }

  tfoot {
    height: 54px;
    z-index: 20;

    background-color: vars.$color-base-white;

    tr {
      position: relative;

      // border top for footer (needs to be absolute because of sticky)
      &::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 1px;
        background-color: vars.$color-base-grey-400;
      }
    }
  }
}
