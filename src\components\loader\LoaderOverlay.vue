<template>
  <VOverlay
    v-model="modelValue"
    :attach="attach ?? $appRoot"
    :scrim="background"
    :absolute="absolute"
    :contained="contained"
    content-class="d-flex align-center justify-center w-100 h-100"
    persistent
    :class="{ showContentAtTop }"
    no-click-animation
  >
    <ProgressCircular class="spinner" indeterminate size="48" color="primary" />
  </VOverlay>
</template>

<script setup lang="ts">
import ProgressCircular from '@/components/ProgressCircular.vue';

interface Props {
  showContentAtTop?: boolean;
  absolute?: boolean;
  contained?: boolean;
  background?: string;
  attach?: HTMLElement;
}

withDefaults(defineProps<Props>(), {
  absolute: false,
  contained: false,
  background: 'white',
  attach: undefined,
});
const modelValue = defineModel<boolean>({ default: false });
</script>

<style lang="scss" scoped>
:deep(.v-overlay__content) {
  max-height: 100% !important;
}

.showContentAtTop {
  :deep(.v-overlay__content) {
    position: absolute;
    top: 200px;
    left: calc(50vw - 24px);
  }
}
</style>
