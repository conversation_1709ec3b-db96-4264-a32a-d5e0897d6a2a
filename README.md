# DFE Book Frontend

The apps within "DFE Book" are a collection of functionalities that focus on the topic of bookings. They are part of a microfrontend architecture and are used as remote apps. 

---

# Getting started

## Prerequisites

Make sure you have the required tools installed: 

- Node 22.12.0 (Recommendation: Use a version manager like [nvm](https://github.com/nvm-sh/nvm) or [fnm](https://github.com/Schniz/fnm))

## Installation

1. Clone this repository

   ```bash
   git clone https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-book-frontend.git
   ```

2. Run the following setup steps

   ```bash
   cd dfe-book-frontend

   # Load dependencies
   npm install

   ```

## First steps

Simply run `npm run serve` to start the development server. The app will be available at `http://localhost:8080/`.

### Local development with local backend
To run the app with a local backend, you need to add file `.env.development.local` in the folder `src/dev`.
Any environment variables available in `.env.development` can be overridden there and will not be committed to the repository.

On instructions how to run the backend locally, please refer to the respective backend project.

### Local development with platform

To run platform and book local do the following steps:

In project `dfe-platform-frontend` add file `.env.development.local` with following content:

```properties
VITE_APP_DFE_BOOK_FRONTEND_PUBLIC_PATH=http://localhost:3001/
VITE_APP_DFE_QUOTE_FRONTEND_PUBLIC_PATH=https://cdn.platform-dev.dach041.dachser.com/dfe-quote-frontend/dist/
VITE_APP_DFE_TRACKTRACE_FRONTEND_PUBLIC_PATH=https://cdn.platform-dev.dach041.dachser.com/dfe-tracktrace-frontend/dist/
VITE_APP_DFE_ADDRESS_FRONTEND_PUBLIC_PATH=https://cdn.platform-dev.dach041.dachser.com/dfe-address-frontend/dist/
VITE_APP_DFE_PAM_FRONTEND_PUBLIC_PATH=https://cdn.platform-dev.dach041.dachser.com/dfe-pam-frontend/dist/
```

In project `dfe-book-frontend` start the server via `npm run serve:dist`. This will build the project and serve it on port 3001.
After that you can access the app via `http://localhost:8080/`.

---

# Further reading

## Available scripts

- `serve`: Serves in development mode
- `serve:dist`: Runs the `build` script and serves it on port 3001
- `build`: Runs the production build
- `test:watch`: Runs tests in watch mode
- `test:unit`: Runs unit tests
- `lint`: Runs linter

## Release

General information about the release process can be found in [Release Process](https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38866876/Release).
Be aware that it is required to use conventional commit messages to trigger releases.

Be also aware that fast-forward merges might result in a conflict in build conditions and may require an additional commit to run!
To avoid such conflicts merges to master with no-ff option are recommended.

## Microfrontend

DFE is based on a microfrontend architecture with one host app and many remote apps. For more information, see [Confluence](https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38864168/Frontend+Module).

[Vite Plugin Federation plugin](https://github.com/originjs/vite-plugin-federation) is used for the implementation.

### During development

During development, module federation is not used, so apps can be developed as standalone single page applications. To split the individual apps into pages, additional pages can be created under `./src/dev/`. (For example, a page called `overview/index.html` could be viewed at `/overview/`)

### Module federation config

The configuration of the Module Federation plugin is in `vite.config.js`. It expects a `name`, `filename`, which entries are published and which dependencies should be shared.

- `name`: must not contain special characters and will be used in the host app during import.
- `filename`: by convention always "remoteEntry.js".
- `exposes`: key-value pair. The key here is the name (or alias) used in the host app during import. The value is the path to the entry file.
- `shared`: dependencies that should be shared by all apps. Basically, all dependencies can be specified here. Make sure to specify a `requiredVersion`.

```javascript
federation({
  name: "dfeBookFrontend,
  filename: "remoteEntry.js",
  exposes: {
    "./overview": "./src/bootstrap-overview",
  },
  shared: package.dependencies,
});
```

Each entry represents an independent app that can be imported.

```javascript
import BookOverviewApp from 'dfeBookFrontend/overview';
```

### Mount function

For each entry, the host app expects a `mount` function to be able to access. This will render the remote app inside the host. As passing parameters there must be an HTML element and the client.

```javascript
// inside host app
import { mount } from 'dfeBookFrontend/overview';

mount(el, client);
```

Inside the `mount` function, a new Vue instance is created that renders a component. This is mounted into the passed HTML element.

```javascript
// inside remote app entry
import OverviewApp from '@/apps/OverviewApp.vue';

export const mount = async (el, client) => {
  new Vue({
    render: (h) => h(OverviewApp),
  }).$mount(el);
};
```

## Styles

The styles are based on the package [dfe-frontend-styles](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-frontend-styles), which provides general, uniform values for DFE such as colors, typography, spacing, etc.

```scss
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

h1 {
  font-size: vars.$font-size-heading-1;
}
```

Vuetify is used with the theme option turned off. It is customized using SCSS within `variables.scss`. It's important that only variables may be defined in this file and any imported files! Further overwriting of Vuetify styles takes place within `overrides.scss`.

Styles for components are created directly in a scoped styles block of Vue files.

### Vuetify scoped CSS

To prevent styles from different Vuetify apps from overwriting each other, a prefix selector is prepended to the CSS in the Webpack build process. The script for this is located in `webpack/vuetify-prefix`.

A prefix can be passed to the function. This must match the selector in the root element of the app.

```html
<div dfe-book-frontend>
  <v-app></v-app>
</div>
```

```javascript
vuetifyPrefix(config, {
  prefix: `[dfe-book-frontend]`,
});
```

The CSS then looks like this:

```css
[dfe-book-frontend] .v-application {
}
```

## I18n

Internationalization is done with the package [vue-i18n](https://github.com/intlify/vue-i18n). All textual content must be implemented with i18n. The translations are fetched from an api at run-time by the package [@dfe/dfe-dynamiclabel-api](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-dynamiclabel-api).

## API contract

An API contract is the documentation that describes how the API works and how it will be used. [The documentation exists in the form of a yaml file](https://dfe-book-backend-dev-dfe.apps.ocp-dev-02.appl-02.dach041.dachser.com/swagger-ui/index.html). With the help of the documentation, types and services can be generated automatically. It also forms the basis to implement a mock server for early development as well as for unit testing.

### Mock Server

A mock server can be used to mock the API during local development or for unit testing. There are several places that can be customized.

In `mocks/server.ts` is the implementation of the server. Here both the models and the routes can be adapted or extended.

```javascript
routes() {
  this.get("/countries", () => {
    return fixtures?.countries || [];
  });
}
```

The test data is defined in `mocks/fixtures`. The necessary data must be passed as fixtures when the mock server is initialized:

```javascript
mockServer({
  fixtures: {
    countries,
    addresses,
  },
});
```

## Client library

The [client library](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-frontend-client) is used to access DFE-wide methods. These include auth, events, logging and more. An instance of the client is initialized inside the host and passed to all remote apps. This can be used, for example, to query the user token, respond to events from other apps, or post to the logging web service.

In Vue, the client instance is inherited from the parent component to all other components using provide/inject.

```javascript
const client = inject(ClientKey);

client?.events.on('createOrder', (payload) => {
  console.log(payload);
});
```

## Testing

Test files are created in the `tests` folder. To run the tests there are the npm scripts `test:watch` (during development) and `test` (to get coverage report).

The threshold for test coverage in SonarQube is currently set to 80%. This includes both Typescript and Vue files.

## SVG icons

### DFE icons

The main source for icons is [dfe-frontend-styles](https://gitlab.dachser.com/dachser/business-integration/dachser-platform/dfe/dfe-frontend-styles). There are optimized icons in the sizes 16px and 24px. It is important to use the correct size when importing.

```javascript
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import AddIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
```

```html
<MaterialSymbol size="24">
  <DeleteIcon />
</MaterialSymbol>

<MaterialSymbol size="16">
  <AddIcon />
</MaterialSymbol>
```

### SVGs as Vue components

By default, SVGs will be loaded as Vue components.

```javascript
<template>
  <home-icon />
</template>

<script>
import HomeIcon from '@/assets/home.svg';

export default {
  components: {
    HomeIcon,
  },
};
</script>
```

### SVGs inline

If you want to disable this plugin for a specific file, add `?raw` when you import. You will receive the raw HTML markup of the SVG which you can, for example, encode as Base64. x

Input:

```javascript
import HomeIcon from '@/assets/home.svg?raw';
```

Output: 

```html
<svg>...</svg>
```