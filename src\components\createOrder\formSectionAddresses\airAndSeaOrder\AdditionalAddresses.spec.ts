import AdditionalAddresses from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/AdditionalAddresses.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import AddButton from '@/components/createOrder/AddButton.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';

describe('Addresses AirOrder AdditionalAddresses component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(AdditionalAddresses);
  });

  it('shows add buttons for all further address types', async () => {
    expect(wrapper.findAllComponents(AddButton).length).toBe(3);
  });

  it('shows AddressCard component after add buttons are clicked', async () => {
    const buttons = wrapper.findAllComponents(AddButton);
    for (const button of buttons) {
      await button.trigger('click');
    }
    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(AddressCard).length).toBe(3);
  });
});
