import { computed } from 'vue';

interface ValidationItem {
  id: string;
  value: string | null;
}

export const useDuplicateValidation = (items: ValidationItem[]) => {
  const duplicateFlags = computed(() => {
    const valueOccurrences: Record<string, number> = {};

    items.forEach((item) => {
      if (item.value) {
        valueOccurrences[item.value] = (valueOccurrences[item.value] || 0) + 1;
      }
    });

    const flags: Record<string, boolean> = {};
    items.forEach((item) => {
      flags[item.id] = item.value ? valueOccurrences[item.value] > 1 : false;
    });

    return flags;
  });

  const hasDuplicateErrors = computed(() =>
    Object.values(duplicateFlags.value).some((isDuplicate) => isDuplicate),
  );

  const hasDuplicateError = (item: ValidationItem) => duplicateFlags.value[item.id] || false;

  return {
    duplicateFlags,
    hasDuplicateErrors,
    hasDuplicateError,
  };
};
