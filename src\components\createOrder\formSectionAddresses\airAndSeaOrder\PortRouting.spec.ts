import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { Port } from '@dfe/dfe-book-api';
import { PortType } from '@dfe/dfe-book-api';
import { mockServer } from '@/mocks/server';
import { airExportOrder, seaExportOrder } from '@/mocks/fixtures/order';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import PortRouting from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortRouting.vue';
import { OrderTypes } from '@/enums';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { nextTick } from 'vue';

describe('PortRouting component', () => {
  let wrapper: VueWrapper;
  let formStore: ReturnType<typeof useCreateOrderFormStore>;
  let dataStore: ReturnType<typeof useCreateOrderDataStore>;
  let addressStore: ReturnType<typeof useCreateOrderAddressesStore>;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        airExportOrder,
        seaExportOrder,
      },
    });
  });

  beforeEach(() => {
    wrapper = mount(PortRouting);
    formStore = useCreateOrderFormStore();
    dataStore = useCreateOrderDataStore();
    addressStore = useCreateOrderAddressesStore();
  });

  afterEach(() => {
    wrapper.unmount();
    formStore.$reset();
    dataStore.$reset();
  });

  it('should show info text', () => {
    const infoTexts = wrapper.findAll('.info-text');

    expect(infoTexts).toHaveLength(2);
  });

  describe('Routing Air', () => {
    it('should display error banner when routing is invalid', async () => {
      const { hasInvalidRouting } = storeToRefs(dataStore);

      hasInvalidRouting.value = true;
      await wrapper.vm.$nextTick();

      const errorBanner = wrapper.findComponent({ name: 'DfeBanner' });
      expect(errorBanner.exists()).toBe(true);
      expect(errorBanner.props('type')).toBe('error');
      expect(errorBanner.text()).toContain('messages.id7124.text');
    });

    it('should show departure airport (fromIATA)', async () => {
      const { fromIATA } = storeToRefs(addressStore);

      fromIATA.value = <Port>airExportOrder.fromIATA;

      await wrapper.vm.$nextTick();

      const departureAirport = wrapper.find('.departure-port');
      const infoText = wrapper.findAll('.info-text');

      expect(departureAirport.text()).toEqual('MUC - Munich');
      expect(infoText).toHaveLength(1);
    });

    it('should show destination airport (toIATA)', async () => {
      const { toIATA } = storeToRefs(addressStore);

      toIATA.value = <Port>airExportOrder.toIATA;

      await nextTick();

      const destinationAirport = wrapper.find('.destination-port');

      expect(destinationAirport.text()).toEqual('ZRH - Zurich');
    });

    it('should show departure airport (fromIATA) and destination airport (toIATA)', async () => {
      const { fromIATA, toIATA } = storeToRefs(addressStore);

      fromIATA.value = <Port>airExportOrder.fromIATA;
      toIATA.value = <Port>airExportOrder.toIATA;

      await wrapper.vm.$nextTick();

      const departureAirport = wrapper.find('.departure-port');
      const destinationAirport = wrapper.find('.destination-port');

      expect(departureAirport.text()).toEqual('MUC - Munich');
      expect(destinationAirport.text()).toEqual('ZRH - Zurich');
    });

    it('should show dropdown with airports for shipperAddress, if fromPortRouting has multiple airports', async () => {
      const { fromPortRouting } = storeToRefs(dataStore);

      fromPortRouting.value = [
        {
          code: 'GRU',
          name: 'Sao Paulo Guarulhos',
          countryCode: 'BR',
          type: PortType.AIRPORT,
          displayName: 'Sao Paulo Guarulhos (GRU)',
        },
        {
          code: 'VCP',
          name: 'Viracopos Sao Paulo',
          countryCode: 'BR',
          type: PortType.AIRPORT,
          displayName: 'Viracopos Sao Paulo (VCP)',
        },
      ];

      await wrapper.vm.$nextTick();

      const autoComplete = wrapper.findComponent(AutocompleteField);

      expect(autoComplete.exists()).toBeTruthy();
      expect(autoComplete.props('items')).toHaveLength(2);
    });

    it('should show dropdown with airports for consigneeAddress, if toPortRouting has multiple airports', async () => {
      const { toPortRouting } = storeToRefs(dataStore);

      toPortRouting.value = [
        {
          code: 'GRU',
          name: 'Sao Paulo Guarulhos',
          countryCode: 'BR',
          type: PortType.AIRPORT,
          displayName: 'Sao Paulo Guarulhos (GRU)',
        },
        {
          code: 'VCP',
          name: 'Viracopos Sao Paulo',
          countryCode: 'BR',
          type: PortType.AIRPORT,
          displayName: 'Viracopos Sao Paulo (VCP)',
        },
      ];

      await wrapper.vm.$nextTick();

      const autoComplete = wrapper.findComponent(AutocompleteField);

      expect(autoComplete.exists()).toBeTruthy();
      expect(autoComplete.props('items')).toHaveLength(2);
    });
  });

  describe('Routing Sea', () => {
    beforeEach(() => {
      formStore.orderType = OrderTypes.SeaExportOrder;
    });

    it('should show departure port (fromPort)', async () => {
      const { fromPortRouting, toPortRouting } = storeToRefs(dataStore);
      const { fromPort } = storeToRefs(addressStore);

      fromPortRouting.value = [<Port>seaExportOrder.fromPort];
      toPortRouting.value = [];

      fromPort.value = <Port>seaExportOrder.fromPort;

      await wrapper.vm.$nextTick();

      const departureAirport = wrapper.find('.departure-port');
      const infoText = wrapper.findAll('.info-text');

      expect(departureAirport.text()).toEqual('DEHAM - Hamburg');
      expect(infoText).toHaveLength(1);
    });

    it('should show destination port (toPort)', async () => {
      const { toPortRouting } = storeToRefs(dataStore);
      const { toPort } = storeToRefs(addressStore);

      toPortRouting.value = [<Port>seaExportOrder.toPort];

      toPort.value = <Port>seaExportOrder.toPort;

      await wrapper.vm.$nextTick();

      const destinationAirport = wrapper.find('.destination-port');

      expect(destinationAirport.text()).toEqual('DEMUC - Munich');
    });

    it('should show departure port (fromPort) and destination port (toPort)', async () => {
      const { fromPort, toPort } = storeToRefs(addressStore);

      fromPort.value = <Port>seaExportOrder.fromPort;
      toPort.value = <Port>seaExportOrder.toPort;

      await wrapper.vm.$nextTick();

      const departureAirport = wrapper.find('.departure-port');
      const destinationAirport = wrapper.find('.destination-port');

      expect(departureAirport.text()).toEqual('DEHAM - Hamburg');
      expect(destinationAirport.text()).toEqual('DEMUC - Munich');
    });
  });
});
