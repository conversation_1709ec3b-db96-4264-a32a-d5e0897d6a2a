<template>
  <div ref="roadCollectingOrderContainer" tabindex="0">
    <div v-if="collectingOrderNavigationItems.length > 0">
      <FormSection
        v-if="isCustomersAvailable"
        ref="collectingOrderCustomers"
        v-model="valid.roadOrderCustomers"
        class="nav-nav-scroll-section"
      >
        <SectionCustomer />
      </FormSection>
      <FormSection
        ref="collectingOrderAddresses"
        v-model="valid.roadOrderAddresses"
        class="nav-nav-scroll-section"
      >
        <SectionAddresses />
      </FormSection>
      <FormSection
        ref="collectingOrderFreight"
        v-model="valid.roadOrderFreight"
        class="nav-nav-scroll-section"
      >
        <SectionFreight />
      </FormSection>
      <FormSection
        ref="collectingOrderCollectionAndDelivery"
        v-model="valid.roadOrderCollectionAndDelivery"
        class="nav-nav-scroll-section"
      >
        <SectionCollectionAndDelivery />
      </FormSection>
      <FormSection
        ref="collectingOrderAccountingAndAdditionalServices"
        v-model="valid.roadOrderAccountingAndAdditionalServices"
        class="nav-nav-scroll-section"
      >
        <SectionAccountingAndAdditionalServices />
      </FormSection>
      <FormSection
        ref="collectingOrderReferences"
        v-model="valid.roadOrderReferences"
        class="nav-nav-scroll-section"
      >
        <SectionOrderReferences />
      </FormSection>
      <FormSection
        v-if="isAtLeastOneOrderTextVisible"
        ref="collectingOrderTexts"
        v-model="valid.roadOrderTexts"
        class="nav-nav-scroll-section"
      >
        <SectionTexts />
      </FormSection>

      <FormSection
        ref="collectingOrderDocuments"
        v-model="valid.roadOrderDocuments"
        class="nav-scroll-section"
      >
        <SectionDocuments />
      </FormSection>
    </div>

    <MainFooter @create-order-validate="validateForms" />
  </div>
</template>

<script setup lang="ts">
import SectionAccountingAndAdditionalServices from '@/components/createOrder/formSectionAccountingAdditionalServices/collectionOrder/CardMain.vue';
import SectionAddresses from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import SectionCollectionAndDelivery from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import SectionCustomer from '@/components/createOrder/formSectionCustomer/collectingOrder/CardMain.vue';
import MainFooter from '@/components/createOrder/formSectionFooter/MainFooter.vue';
import SectionFreight from '@/components/createOrder/formSectionFreight/CardMain.vue';
import SectionOrderReferences from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import SectionTexts from '@/components/createOrder/formSectionTexts/CardMain.vue';
import SectionDocuments from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import FormSection from '@/components/form/FormSection.vue';
import { ValidationResult } from '@/composables/createOrder/useCreateOrderValidation';
import { useOrderTexts } from '@/composables/createOrder/useOrderTexts';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useValidationDataStore } from '@/store/validation';
import { identity } from 'lodash';
import { storeToRefs } from 'pinia';
import { ComponentPublicInstance, computed, ref, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const emit = defineEmits(['collectingOrderNavigationItems']);
const collectingOrderCustomers = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const collectingOrderAddresses = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const collectingOrderFreight = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const collectingOrderCollectionAndDelivery = ref<ComponentPublicInstance<
  typeof FormSection
> | null>(null);
const collectingOrderAccountingAndAdditionalServices = ref<ComponentPublicInstance<
  typeof FormSection
> | null>(null);
const collectingOrderReferences = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const collectingOrderTexts = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const collectingOrderDocuments = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const createOrderDataStore = useCreateOrderDataStore();
const createOrderDocumentsStore = useCreateOrderDocumentsStore();
const { documentUploadFailed } = storeToRefs(createOrderDocumentsStore);

const { isCustomersAvailable } = storeToRefs(createOrderDataStore);
const { isAtLeastOneOrderTextVisible } = useOrderTexts();
const { hasEmbargo } = storeToRefs(useEmbargoStore());

const { formValidationSectionsRoad: valid, getFormSectionsValid } =
  storeToRefs(useValidationDataStore());

const collectingOrderNavigationItems = computed(() =>
  [
    isCustomersAvailable.value
      ? {
          ref: collectingOrderCustomers,
          text: t('labels.principal_title.text'),
          error: !valid.value.roadOrderCustomers,
        }
      : undefined,
    {
      ref: collectingOrderAddresses,
      text: t('labels.addresses_title.text'),
      error: !valid.value.roadOrderAddresses || hasEmbargo.value,
    },
    {
      ref: collectingOrderFreight,
      text: t('labels.order_line_title.text'),
      error: !valid.value.roadOrderFreight,
    },
    {
      ref: collectingOrderCollectionAndDelivery,
      text: t('labels.collection_delivery_title.text'),
      error: !valid.value.roadOrderCollectionAndDelivery,
    },
    {
      ref: collectingOrderAccountingAndAdditionalServices,
      text: t('labels.accounting.text'),
      error: !valid.value.roadOrderAccountingAndAdditionalServices,
    },
    {
      ref: collectingOrderReferences,
      text: t('labels.order_references.text'),
      error: !valid.value.roadOrderReferences,
    },
    isAtLeastOneOrderTextVisible.value
      ? {
          ref: collectingOrderTexts,
          text: t('labels.order_texts_title.text'),
          error: !valid.value.roadOrderTexts,
        }
      : undefined,
    {
      ref: collectingOrderDocuments,
      text: t('labels.documents_label.text'),
      error: !valid.value.roadOrderDocuments || documentUploadFailed.value,
    },
  ].filter(identity),
);

const validateForms = async (validate: (value: ValidationResult) => void) => {
  await Promise.all(
    collectingOrderNavigationItems.value.map(async (item) => {
      return await item?.ref?.value?.validate();
    }),
  );

  validate(getFormSectionsValid.value);
};

watchEffect(() => {
  emit('collectingOrderNavigationItems', collectingOrderNavigationItems.value);
});
</script>
