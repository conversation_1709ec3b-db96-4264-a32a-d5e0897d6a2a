import { describe, it, expect, beforeEach, vi, afterAll } from 'vitest';
import { mockServer } from '@/mocks/server';
import { unNumberSearchResults } from '@/mocks/fixtures/unNumberSearchResults';
import { withSetup } from '@test/util/with-setup';
import { useUnNumberSearch } from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/useUnNumberSearch';
import { nextTick, ref } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { Segment } from '@dfe/dfe-book-api';

describe('useUnNumberSearch.ts', () => {
  const searchTerm = ref('');

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        unNumberSearchResults,
      },
    });
  });

  beforeEach(() => {
    searchTerm.value = '';
  });

  afterAll(() => {
    vi.resetAllMocks();
  });

  it('returns empty data initially', () => {
    const { data } = withSetup(() => useUnNumberSearch(searchTerm))[0];

    expect(data.value).toEqual([]);
  });

  it('returns data for valid UN number', async () => {
    searchTerm.value = '1203';
    const { data } = withSetup(() => useUnNumberSearch(searchTerm))[0];

    await vi.waitFor(() => {
      expect(data.value).toHaveLength(1);
      expect(data.value?.[0]).toEqual(unNumberSearchResults[0]);
    });
  });

  it('returns different data if the transport type changes', async () => {
    const { data } = withSetup(() => useUnNumberSearch(searchTerm))[0];

    searchTerm.value = '1203';

    await vi.waitFor(() => {
      expect(data.value).toHaveLength(1);
    });

    const formStore = useCreateOrderFormStore();
    const { transportType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;
    await nextTick();

    await vi.waitFor(() => {
      expect(data.value).toHaveLength(0);
    });

    transportType.value = Segment.ROAD;
  });

  it('returns correct table placeholder text', async () => {
    const { tablePlaceholder } = withSetup(() => useUnNumberSearch(searchTerm))[0];
    expect(tablePlaceholder.value).toStrictEqual({
      heading: 'labels.dangerous_good_enter_correct.text',
      text: 'labels.dangerous_good_enter_correct_hint.text',
    });

    searchTerm.value = '1203';

    expect(tablePlaceholder.value).toStrictEqual({
      heading: '',
      text: '',
    });

    await vi.waitFor(() => {
      expect(tablePlaceholder.value).toStrictEqual({
        heading: 'labels.dangerous_good_no_results.text',
        text: 'labels.dangerous_good_no_results_hint.text',
      });
    });
  });

  it.each([[''], ['999'], ['99999'], ['foobar']])(
    'returns empty data for invalid un number %s',
    async (searchInput) => {
      const { data } = withSetup(() => useUnNumberSearch(searchTerm))[0];

      searchTerm.value = '1203';

      await vi.waitFor(() => {
        expect(data.value).toHaveLength(1);
      });

      searchTerm.value = searchInput;
      await vi.waitFor(() => {
        expect(data.value).toHaveLength(0);
      });
    },
  );
});
