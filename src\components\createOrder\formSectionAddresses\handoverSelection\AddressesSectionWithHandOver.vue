<template>
  <div>
    <SectionAddresses>
      <template #shipperAddress>
        <HandOver
          v-data-test="'shipper-hand-over'"
          :model-value="shipperHandOverSelection"
          :headline="t('labels.handover_to_dachser_headline.text')"
          :is-shipper-address="true"
          :default-label="t('labels.handover_to_dachser_shipper_address.text')"
          :alternate-address-label="t('labels.handover_to_dachser_alt_address.text')"
          :airport-selection-label="
            isSeaOrder
              ? t('labels.handover_to_dachser_port.text')
              : t('labels.handover_to_dachser_airport.text')
          "
          :address-card-label="t('labels.pickup_point.text')"
          :default-selection-disabled="shipperDisabled.default"
          :alternate-address-selection-disabled="shipperDisabled.alternateAddress"
          :airport-selection-disabled="shipperDisabled.port"
          :airport-selection-disabled-reason="
            consigneeHandOverSelection.selection === HandOverSelection.port
              ? isSeaOrder
                ? t('labels.port_to_port_not_possible.text')
                : t('labels.airport_to_airport_not_possible.text')
              : ''
          "
          @update:model-value="updateFromData($event)"
          @delete-address="
            addressStore.resetPortRoutingForSelection(
              shipperHandOverSelection,
              'from',
              HandOverSelection.alternateAddress,
            )
          "
          @update:alternate-address="updateShipperAlternateAddress"
          @update:alternate-contact="updateShipperAlternateContact"
        />
      </template>
      <template #routing>
        <PortRouting
          :departure-port-heading="departurePortHeading"
          :departure-port-description="departurePortDescription"
          :destination-port-heading="destinationPortHeading"
          :destination-port-description="destinationPortDescription"
        />
      </template>

      <template #consigneeAddress>
        <HandOver
          v-data-test="'consignee-hand-over'"
          :model-value="consigneeHandOverSelection"
          :headline="t('labels.handover_from_dachser_headline.text')"
          :default-label="t('labels.handover_from_dachser_con_address.text')"
          :alternate-address-label="t('labels.handover_from_dachser_alt_address.text')"
          :airport-selection-label="
            isSeaOrder
              ? t('labels.handover_from_dachser_port.text')
              : t('labels.handover_from_dachser_airport.text')
          "
          :address-card-label="t('labels.delivery_point.text')"
          :default-selection-disabled="consigneeDisabled.default"
          :alternate-address-selection-disabled="consigneeDisabled.alternateAddress"
          :airport-selection-disabled="consigneeDisabled.port"
          :airport-selection-disabled-reason="getAirportDisabledLabel"
          @update:model-value="updateToData($event)"
          @delete-address="
            addressStore.resetPortRoutingForSelection(
              consigneeHandOverSelection,
              'to',
              HandOverSelection.alternateAddress,
            )
          "
          @update:alternate-address="updateConsigneeAlternateAddress"
          @update:alternate-contact="updateConsigneeAlternateContact"
        />
      </template>
      <template #additionalAddresses>
        <AdditionalAddresses with-contacts />
      </template>
    </SectionAddresses>
  </div>
</template>

<script setup lang="ts">
import SectionAddresses from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import AdditionalAddresses from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/AdditionalAddresses.vue';
import PortRouting from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortRouting.vue';
import HandOver from '@/components/createOrder/formSectionAddresses/handoverSelection/HandOver.vue';
import { useHandOverDisabledState } from '@/composables/createOrder/useHandOverDisabledState';
import { PortItem } from '@/composables/createOrder/usePortSearch';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import { ClientKey } from '@/types/client';
import type {
  HandOverAirportSelectionValue,
  HandOverAlternateAddressSelectionValue,
  HandOverSelectionValue,
} from '@/types/hand-over';
import { HandOverSelection } from '@/types/hand-over';
import type { Address, ContactData, OrderAddress } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import type { Ref } from 'vue';
import { computed, inject, nextTick, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const client = inject(ClientKey);
const createOrderDataStore = useCreateOrderDataStore();
const { fromPortRouting, toPortRouting } = storeToRefs(createOrderDataStore);
const addressStore = useCreateOrderAddressesStore();
const {
  shipperHandOverSelection,
  consigneeHandOverSelection,
  consigneeAddress,
  shipperAddress,
  contactDataConsignee,
  contactDataShipper,
  fromIATA,
  toIATA,
  fromPort,
  toPort,
  port,
} = storeToRefs(addressStore);
const { shipper: shipperDisabled, consignee: consigneeDisabled } = useHandOverDisabledState();
const {
  isAirExportOrderFromQuote,
  isAirImportOrderFromQuote,
  isSeaImportOrderFromQuote,
  isSeaExportOrderFromQuote,
  isSeaOrder,
  isAirImportOrder,
  isAirOrder,
  isOrderFromQuote,
} = storeToRefs(useCreateOrderFormStore());
const createAddressDataStore = useCreateAddressDataStore();
const { contactsAlternativePickup, contactsAlternativeDelivery } =
  storeToRefs(createAddressDataStore);
const { transportCountry } = storeToRefs(useCreateOrderFormStore());

const updateAlternateAddress = (
  handOverSelection: Ref<HandOverSelectionValue>,
  addressData: OrderAddress,
) => {
  if (handOverSelection.value.selection === HandOverSelection.alternateAddress) {
    handOverSelection.value = {
      ...handOverSelection.value,
      address: {
        ...addressData,
      },
    };
  }
};

const updateAlternateContact = (
  handOverSelection: Ref<HandOverSelectionValue>,
  contactData: ContactData,
) => {
  if (handOverSelection.value.selection === HandOverSelection.alternateAddress) {
    handOverSelection.value.address.contact = contactData;
  }
};

const handleDefaultSelection = async (
  handOver: HandOverSelectionValue,
  fromOrTo: 'from' | 'to',
  address: Address,
) => {
  await createOrderDataStore.fetchPortRouting(fromOrTo);
  await addressStore.updateRoutingForSelection(
    handOver,
    fromOrTo,
    HandOverSelection.default,
    address,
  );
};

const handleAlternateAddressSelection = async (
  handOverSelection: HandOverAlternateAddressSelectionValue,
  fromOrTo: 'from' | 'to',
) => {
  if (!isOrderFromQuote.value) {
    if (handOverSelection.address.postcode && handOverSelection.address.countryCode) {
      await createOrderDataStore.fetchPortRouting(fromOrTo, handOverSelection.address);
    } else {
      addressStore.resetPortRoutingForSelection(
        handOverSelection,
        fromOrTo,
        HandOverSelection.alternateAddress,
      );
    }
  }
};

const setPort = (isFrom: boolean, portFrom?: PortItem, portTo?: PortItem) => {
  if (isSeaOrder.value && isFrom) {
    fromPort.value = portFrom;
  } else if (isSeaOrder.value && !isFrom) {
    toPort.value = portTo ?? portFrom;
  }
};

const setIATA = (isFrom: boolean, iataFrom?: PortItem, iataTo?: PortItem) => {
  if (isAirOrder.value && isFrom) {
    fromIATA.value = iataFrom;
  } else if (isAirOrder.value && !isFrom) {
    toIATA.value = iataTo ?? iataFrom;
  }
};

const handlePortSelection = (handOverSelection: HandOverAirportSelectionValue, isFrom: boolean) => {
  setPort(isFrom, handOverSelection.port);
  setIATA(isFrom, handOverSelection.port);
};

const handleDefaultCase = (isFrom: boolean) => {
  setPort(isFrom, fromPortRouting.value[0], toPortRouting.value[0]);
  setIATA(isFrom, fromPortRouting.value[0], toPortRouting.value[0]);
};

const updateIATA = async (handOverSelection: HandOverSelectionValue, fromOrTo: 'from' | 'to') => {
  const isFrom = fromOrTo === 'from';
  const handOver = isFrom ? shipperHandOverSelection.value : consigneeHandOverSelection.value;
  const address = isFrom ? shipperAddress.value.address : consigneeAddress.value.address;

  (fromOrTo === 'from' ? shipperHandOverSelection : consigneeHandOverSelection).value.selection =
    handOverSelection.selection;
  if (handOverSelection.selection === HandOverSelection.default) {
    await handleDefaultSelection(handOver, fromOrTo, address);
  } else if (handOverSelection.selection === HandOverSelection.alternateAddress) {
    await handleAlternateAddressSelection(handOverSelection, fromOrTo);
  } else if (handOverSelection.selection === HandOverSelection.port) {
    handlePortSelection(handOverSelection, isFrom);
  } else {
    handleDefaultCase(isFrom);
  }
};

const updateFromData = async (updatedShipperHandOverSelection: HandOverSelectionValue) => {
  switch (updatedShipperHandOverSelection.selection) {
    case HandOverSelection.default:
      if (
        (isAirImportOrderFromQuote.value || isSeaImportOrderFromQuote.value) &&
        shipperHandOverSelection.value.selection === HandOverSelection.alternateAddress
      ) {
        // Move shipper data to alternate address & contact
        const addressToMove = {
          ...shipperHandOverSelection.value.address,
        };
        delete addressToMove.contact;

        // Set shipperAddress first to initial state in order to prevent validation error ...
        shipperAddress.value.address = {
          ...orderAddress(),
        };
        await nextTick();
        // ... now set shipperAddress to the correct address
        shipperAddress.value.address = {
          ...addressToMove,
        };

        if (shipperHandOverSelection.value.address.contact) {
          contactDataShipper.value = shipperHandOverSelection.value.address.contact;
        }
      }
      break;
    case HandOverSelection.alternateAddress:
      if (
        (isAirImportOrderFromQuote.value || isSeaImportOrderFromQuote.value) &&
        shipperHandOverSelection.value.selection === HandOverSelection.default
      ) {
        // Move alternate address & contact to shipper data
        const addressToMove = {
          ...shipperAddress.value.address,
        };

        // Reset shipper address but keep id
        shipperAddress.value.address = {
          ...orderAddress(),
        };

        if (contactDataShipper.value) {
          addressToMove.contact = contactDataShipper.value;
          contactsAlternativePickup.value[0] = contactDataShipper.value;
          contactDataShipper.value = { ...contactData() };
        }
        shipperHandOverSelection.value = {
          selection: HandOverSelection.alternateAddress,
          address: addressToMove,
        };
      }
      break;
  }

  await updateIATA(updatedShipperHandOverSelection, 'from');
};

const updateToData = async (updatedConsigneeHandOverSelection: HandOverSelectionValue) => {
  switch (updatedConsigneeHandOverSelection.selection) {
    case HandOverSelection.default:
      if (
        (isAirExportOrderFromQuote.value || isSeaExportOrderFromQuote.value) &&
        consigneeHandOverSelection.value.selection === HandOverSelection.alternateAddress
      ) {
        // Move consignee data to alternate address & contact
        const addressToMove = {
          ...consigneeHandOverSelection.value.address,
        };
        delete addressToMove.contact;

        // Set consigneeAddress first to initial state in order to prevent validation error ...
        consigneeAddress.value.address = {
          ...orderAddress(),
        };
        await nextTick();
        // ... now set consigneeAddress to the correct address
        consigneeAddress.value.address = {
          ...addressToMove,
        };

        if (consigneeHandOverSelection.value.address.contact) {
          contactDataConsignee.value = consigneeHandOverSelection.value.address.contact;
        }
      }
      break;
    case HandOverSelection.alternateAddress:
      if (
        (isAirExportOrderFromQuote.value || isSeaExportOrderFromQuote.value) &&
        consigneeHandOverSelection.value.selection === HandOverSelection.default
      ) {
        // Move alternate address & contact to consignee data
        const addressToMove = {
          ...consigneeAddress.value.address,
        };

        // Reset consignee address but keep id
        consigneeAddress.value.address = {
          ...orderAddress(),
        };

        if (contactDataConsignee.value) {
          addressToMove.contact = contactDataConsignee.value;
          contactsAlternativeDelivery.value[0] = contactDataConsignee.value;
          contactDataConsignee.value = { ...contactData() };
        }
        consigneeHandOverSelection.value = {
          selection: HandOverSelection.alternateAddress,
          address: addressToMove,
        };
      }
      break;
  }

  await updateIATA(updatedConsigneeHandOverSelection, 'to');
};

const updateShipperAlternateAddress = (addressData: OrderAddress) =>
  updateAlternateAddress(shipperHandOverSelection, addressData);

const updateConsigneeAlternateAddress = (addressData: OrderAddress) =>
  updateAlternateAddress(consigneeHandOverSelection, addressData);

const updateShipperAlternateContact = (contactData: ContactData) => {
  updateAlternateContact(shipperHandOverSelection, contactData);
  contactsAlternativePickup.value[0] = contactData;
};

const updateConsigneeAlternateContact = (contactData: ContactData) => {
  updateAlternateContact(consigneeHandOverSelection, contactData);
  contactsAlternativeDelivery.value[0] = contactData;
};

const getAirportDisabledLabel = computed(() => {
  const selectionIsPort = shipperHandOverSelection.value.selection === HandOverSelection.port;

  if (isAirImportOrder.value && !selectionIsPort) {
    return t('labels.door_to_airport_not_possible.text');
  } else if (isSeaOrder.value && selectionIsPort) {
    return t('labels.port_to_port_not_possible.text');
  } else if (!isSeaOrder.value && selectionIsPort) {
    return t('labels.airport_to_airport_not_possible.text');
  }
  return undefined;
});

const updateFromPortRouting = async () => {
  await createOrderDataStore.fetchPortRouting('from');
  if (
    shipperHandOverSelection.value.selection !== HandOverSelection.port &&
    fromPortRouting &&
    fromPortRouting.value.length > 0 &&
    (!fromIATA.value || !fromPort.value)
  ) {
    if (isSeaOrder.value) {
      fromPort.value = fromPortRouting.value[0];
    } else {
      fromIATA.value = fromPortRouting.value[0];
    }
  }
};

const updateToPortRouting = async () => {
  await createOrderDataStore.fetchPortRouting('to');
  if (
    consigneeHandOverSelection.value.selection !== HandOverSelection.port &&
    toPortRouting &&
    toPortRouting.value.length > 0 &&
    (!toIATA.value || !toPort.value)
  ) {
    if (isSeaOrder.value) {
      toPort.value = toPortRouting.value[0];
    } else {
      toIATA.value = toPortRouting.value[0];
    }
  }
};

const departurePortHeading = computed(() =>
  isSeaOrder.value
    ? t('labels.departure_port_label.text')
    : t('labels.departure_airport_title.text'),
);
const departurePortDescription = computed(() => {
  if (isSeaOrder.value) {
    return t('labels.departure_port_info.text');
  }
  if (fromPortRouting.value.length == 0 && transportCountry.value.fromPostcode == null) {
    return t('labels.departure_airport_info.text');
  }
  return '-';
});

const destinationPortHeading = computed(() =>
  isSeaOrder.value
    ? t('labels.destination_port_label.text')
    : t('labels.destination_airport_title.text'),
);

const destinationPortDescription = computed(() => {
  if (isSeaOrder.value) {
    return t('labels.destination_port_info.text');
  }
  if (toPortRouting.value.length == 0 && transportCountry.value.toPostcode == null) {
    return t('labels.destination_airport_info.text');
  }
  return '-';
});

watch(transportCountry, async ({ fromCountry, toCountry }) => {
  if (fromCountry) {
    await updateFromPortRouting();
  }

  if (toCountry) {
    await updateToPortRouting();
  }
});

watch(port, (newValue, oldValue) => {
  if (oldValue !== newValue && consigneeDisabled.value.port) {
    client?.toast.warning(t('messages.id7178.text').toString());
  }
});
</script>
