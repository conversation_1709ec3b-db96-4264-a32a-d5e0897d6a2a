<template>
  <div>
    <DfeBanner v-if="hasCommercialInvoiceError" type="error" class="mt-0 mb-6">
      <span class="text-h5">{{ t('labels.invoice_missing.text') }}</span>
    </DfeBanner>

    <DfeBanner
      v-if="showCustomDocumentsBanner"
      v-data-test="'custom-documents-banner'"
      type="info"
      class="my-4"
    >
      <span class="text-h5">{{ $t('messages.id6911.description') }}</span>
    </DfeBanner>

    <VList v-if="documents && documents.length" :items="documents">
      <VListItem
        v-for="document in documents"
        :key="document.documentId"
        v-data-test="'document-upload_list-item'"
        class="my-3 pa-0"
      >
        <VCard
          :key="document.progress"
          width="100%"
          :class="[
            'document-card d-flex rounded-lg px-6 py-4',
            { 'virus-detected': isVirusDetected(document.uploadStatus) },
            {
              'upload-error':
                isExtensionNotAllowedError(document.uploadStatus) ||
                isExtensionInvalidError(document.uploadStatus) ||
                isUploadError(document.uploadStatus) ||
                isSizeError(document.uploadStatus),
            },
          ]"
          variant="outlined"
        >
          <VRow class="align-center">
            <VCol
              cols="12"
              sm="6"
              :class="{
                'd-flex align-center': isCriticalError(document.uploadStatus),
              }"
            >
              <MaterialSymbol
                v-if="isCriticalError(document.uploadStatus)"
                :color="
                  isExtensionNotAllowedError(document.uploadStatus) ||
                  isExtensionInvalidError(document.uploadStatus) ||
                  isSizeError(document.uploadStatus)
                    ? 'red'
                    : ''
                "
                size="24"
                class="mr-2"
              >
                <ErrorIcon
                  v-if="
                    isExtensionNotAllowedError(document.uploadStatus) ||
                    isExtensionInvalidError(document.uploadStatus)
                  "
                />
                <warning-icon v-else />
              </MaterialSymbol>
              <div>
                <h5 class="text-h5">
                  {{ document.documentName }}
                </h5>
                <div v-if="!isCriticalError(document.uploadStatus)">
                  <v-progress-linear
                    v-if="!document.uploadStatus"
                    :model-value="document.progress"
                    :color="isUploadError(document.uploadStatus) ? 'red' : 'green'"
                    :indeterminate="document.progress === 0"
                    bg-color="grey lighten-1"
                    class="mt-2 mb-1"
                    rounded
                  />
                  <span
                    v-if="!isUploadError(document.uploadStatus)"
                    class="text-label-3 text-grey-darken-2"
                  >
                    {{ document.size ? useFormatBytes(document.size) : '' }}
                  </span>
                  <span v-else class="d-flex align-center">
                    <MaterialSymbol color="red" class="mr-1">
                      <ErrorIcon />
                    </MaterialSymbol>
                    {{ getErrorMsg(document) }}
                  </span>
                </div>
              </div>
            </VCol>
            <VCol cols="12" sm="5">
              <AutocompleteField
                v-if="!isCriticalError(document.uploadStatus)"
                v-data-test="'upload-list_category'"
                :model-value="getDocumentTypeById(document.documentTypeId)"
                :items="categoryOptions(document)"
                item-title="description"
                item-value="typeId"
                :return-object="true"
                :label="t('labels.category_label.text')"
                :placeholder="t('labels.select_option.text')"
                :required="true"
                :message="t('labels.validation_select_input_required.text')"
                class="mr-3"
                :disabled="document.documentProcessed"
                :menu-icon="ArrowDropDownIcon"
                @update:model-value="onDocumentTypeChange(document, $event)"
              />
              <div v-else>
                <h5 class="text-h5">
                  <span v-data-test="'critical-error-title'">
                    {{ getErrorTitle(document) }}
                  </span>
                </h5>
                <!-- eslint-disable vue/no-v-html -->
                <p class="text-body-2 mb-0">
                  <span v-html="getErrorMsg(document)"></span>
                </p>
                <!--eslint-enable-->
              </div>
            </VCol>

            <VCol cols="12" sm="1" class="d-flex justify-end">
              <VBtn
                v-if="isUploadError(document.uploadStatus)"
                variant="plain"
                class="mr-2"
                @click.prevent="retryUpload(document)"
              >
                <MaterialSymbol size="24" color="grey-darken-2">
                  <redo-icon />
                </MaterialSymbol>
              </VBtn>
              <DfeIconButton
                v-data-test="'delete-document'"
                :tooltip="
                  !isCriticalError(document.uploadStatus) && document.progress === 100
                    ? t('labels.delete_label.text')
                    : t('labels.close_label.text')
                "
                variant="plain"
                class="ml-auto ml-md-0 align-self-end"
                :disabled="document.documentProcessed"
                @click.prevent="removeDocument(document.documentId)"
              >
                <MaterialSymbol
                  size="24"
                  :color="
                    document.documentProcessed
                      ? 'grey'
                      : isVirusDetected(document.uploadStatus)
                        ? 'white'
                        : ''
                  "
                >
                  <DeleteIcon
                    v-if="!isCriticalError(document.uploadStatus) && document.progress === 100"
                  />
                  <CloseIcon v-else />
                </MaterialSymbol>
              </DfeIconButton>
            </VCol>
          </VRow>

          <div v-if="activeDocument === document.documentId">
            <ConfirmPrompt
              :value="isCancelOverlayActive"
              :headline="t('labels.cancel_upload.text') + '?'"
              :cancel-text="t('labels.dfe_continue.text')"
              :confirm-text="t('labels.cancel_upload.text')"
              size="sm"
              @confirm="cancelUpload(document.documentId ?? 0)"
              @close="isCancelOverlayActive = false"
              @cancel="isCancelOverlayActive = false"
            >
              <h5 class="text-body-2 my-6">
                {{ t('messages.id6400.text') }} <br />
                {{ t('messages.id6401.text') }}
              </h5>
            </ConfirmPrompt>
          </div>
        </VCard>
      </VListItem>
    </VList>
  </div>
</template>

<script setup lang="ts">
import { DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import useDocumentCategoryOptions from '@/composables/createOrder/useDocumentCategoryOptions';
import useFormatBytes from '@/composables/createOrder/useFormatBytes';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useDocumentTypes } from '@/composables/data/useDocumentTypes';
import { useRecentlyUsedDocumentTypes } from '@/composables/data/useRecentlyUsedDocumentTypes';
import useValidateOrder from '@/composables/form/useValidateOrder';
import { DocumentTypes, UploadStatus } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { StoreDocument } from '@/store/createOrder/orderDocuments';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import { ClientKey } from '@/types/client';
import type {
  Document,
  DocumentResponse,
  DocumentType,
  Extension,
  Extensions,
} from '@dfe/dfe-book-api';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import redoIcon from '@dfe/dfe-frontend-styles/assets/icons/autorenew-24px.svg';
import CloseIcon from '@dfe/dfe-frontend-styles/assets/icons/close-24px.svg';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import ErrorIcon from '@dfe/dfe-frontend-styles/assets/icons/error-24px.svg';
import WarningIcon from '@dfe/dfe-frontend-styles/assets/icons/warning-24px.svg';
import { storeToRefs } from 'pinia';
import { computed, inject, nextTick, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  documentExtensions: Extensions;
  maxSize: number;
}

const { t } = useI18n();

const props = defineProps<Props>();
const client = inject(ClientKey);
const isCancelOverlayActive = ref(false);
const showCustomDocumentsBanner = ref(false);
const activeDocument = ref<number | undefined>(0);
const customDocumentPrefix = 'CD';

const { data: recentlyUsedDocumentTypes } = useRecentlyUsedDocumentTypes();
const { data: documentTypes } = useDocumentTypes();
const { data: customerSettings } = useCustomerSettings();

const errorBannerStore = useErrorBanner();
const { hasCommercialInvoiceError } = storeToRefs(errorBannerStore);
const createOrderFormStore = useCreateOrderFormStore();
const { isRoadOrder } = storeToRefs(createOrderFormStore);

const createOrderDocuments = useCreateOrderDocumentsStore();
const { documents } = storeToRefs(createOrderDocuments);

const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
const { deliveryNoteNumberRequiredFields } = storeToRefs(createOrderOrderReferencesFormStore);

const { setIsDeliveryNoteNumberFieldRequired } = useValidateOrder();

const getDocumentTypeById = (id: number | undefined) => {
  if (!documentTypes.value) {
    return;
  }
  return id ? documentTypes.value.find((type) => type.typeId === id) : undefined;
};

function setVisibilityOfCustomDocumentsBanner() {
  showCustomDocumentsBanner.value =
    documents.value.some(
      (document) => document.documentType?.startsWith(customDocumentPrefix) ?? false,
    ) && isRoadOrder.value;
}

watch(documents, () => {
  setVisibilityOfCustomDocumentsBanner();
});

function removeDocument(id?: number) {
  activeDocument.value = id;
  createOrderDocuments.removeDocument(id);

  const createOrderFormStore = useCreateOrderFormStore();
  const { customTypeValidationError } = storeToRefs(createOrderFormStore);
  customTypeValidationError.value = false;
}

const cancelUpload = (id: number | undefined) => {
  createOrderDocuments.removeDocument(id);
  isCancelOverlayActive.value = false;
};

const isVirusDetected = (uploadStatus = '') => {
  return uploadStatus === UploadStatus.FileMalicious;
};

const isExtensionNotAllowedError = (uploadStatus = '') => {
  return uploadStatus === UploadStatus.ErrorExtensionNotAllowed;
};

const isExtensionInvalidError = (uploadStatus = '') => {
  return uploadStatus === UploadStatus.ErrorInvalidExtension;
};

const isSizeError = (uploadStatus = '') => {
  return uploadStatus === UploadStatus.ErrorSize;
};

const isUploadError = (uploadStatus = '') => {
  return uploadStatus === UploadStatus.UnspecificError;
};

const isCriticalError = (uploadStatus = '') => {
  return (
    isVirusDetected(uploadStatus) ||
    isExtensionNotAllowedError(uploadStatus) ||
    isExtensionInvalidError(uploadStatus) ||
    isSizeError(uploadStatus)
  );
};

const getErrorTitle = ({ uploadStatus, documentName }: Document & DocumentResponse) => {
  switch (uploadStatus) {
    case UploadStatus.FileMalicious:
      return t('labels.virus_detected.text');
    case UploadStatus.ErrorExtensionNotAllowed:
      return t('messages.id6364.text');
    case UploadStatus.ErrorInvalidExtension:
      return t('labels.invalid_extension.text', {
        0: documentName,
        1: '.pdf',
      });
    case UploadStatus.ErrorSize:
      return t('labels.size_limit.text');
    case UploadStatus.UnspecificError:
      return t('TRANSLATEMElabels.unspecified_error.text');
    default:
      return '';
  }
};

const getErrorMsg = ({ uploadStatus, documentName }: Document & DocumentResponse) => {
  switch (uploadStatus) {
    case UploadStatus.FileMalicious:
      return t('messages.id6363.text');
    case UploadStatus.ErrorExtensionNotAllowed:
      return t('messages.id6365.text') + allowedDocuments.value;
    case UploadStatus.ErrorInvalidExtension:
      return t('labels.invalid_extension.text', {
        0: documentName,
        1: '.pdf',
      });
    case UploadStatus.ErrorSize:
      return `${t('messages.id6484.text')} ${useFormatBytes(props.maxSize)}<br/>${t(
        'messages.id6484.description',
      )}`;
    case UploadStatus.UnspecificError:
      return t('messages.id6366.text');
    default:
      return '';
  }
};

const retryUpload = async (document: Document & DocumentResponse) => {
  await createOrderDocuments.uploadDocument(document);
};

const allowedDocuments = computed(() =>
  props.documentExtensions
    .map((documentExtension: Extension) => documentExtension.label)
    .join(', '),
);

const excludedTypesDG = [
  DocumentTypes.CODDG.toString(),
  DocumentTypes.GGDGA.toString(),
  DocumentTypes.GGDGN.toString(),
  DocumentTypes.GGETA.toString(),
  DocumentTypes.GGETA.toString(),
  DocumentTypes.GGETC.toString(),
];

const categoryOptions = computed(() => {
  return (document: StoreDocument) => {
    let documentTypesFiltered = documentTypes.value ?? [];
    if (!customerSettings.value?.dangerousGoods) {
      documentTypesFiltered = documentTypesFiltered.filter(
        (categoryOption) =>
          categoryOption.type !== undefined && !excludedTypesDG.includes(categoryOption.type),
      );
    }
    return useDocumentCategoryOptions(
      t('labels.frequently_used.text'),
      documentTypesFiltered,
      recentlyUsedDocumentTypes.value ?? [],
      document,
    );
  };
});

const hasDocumentEDN = computed(() =>
  createOrderDocuments.documents.some((document) => document.documentType === DocumentTypes.EDN),
);

const resetDeliveryNoteNumberFieldRequired = () => {
  const { documents } = createOrderDocuments;

  // If there is no document of type EDN
  if (
    documents.every((document) => document.documentType !== DocumentTypes.EDN) &&
    deliveryNoteNumberRequiredFields.value[0]
  ) {
    deliveryNoteNumberRequiredFields.value[0].required = false;
  }
};

const onDocumentTypeChange = (document: StoreDocument, documentType: DocumentType) => {
  document.documentType = documentType.type;
  document.documentTypeId = documentType.typeId;

  setIsDeliveryNoteNumberFieldRequired();
  setVisibilityOfCustomDocumentsBanner();

  if (hasDocumentEDN.value) {
    nextTick(() => {
      client?.events.emit('createOrderValidate');
    });
  }
};

watch(hasDocumentEDN, () => {
  if (hasDocumentEDN.value) {
    setIsDeliveryNoteNumberFieldRequired();
  } else {
    resetDeliveryNoteNumberFieldRequired();
  }
});
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfe-vars;

.v-list-item:first-child {
  margin-top: 0 !important;
}

.v-btn.v-btn--density-default.v-btn--size-default {
  padding: 0;
  min-width: 0;

  :not(:last-child) {
    margin-right: 16px;
  }
}

.v-card {
  border-color: var(--color-base-grey-400);
}

:deep(.v-card.virus-detected) {
  background-color: dfe-vars.$color-base-red-500;
  border-color: dfe-vars.$color-base-red-500;
  color: dfe-vars.$color-base-white;
}

:deep(.v-card.upload-error) {
  background-color: dfe-vars.$color-base-red-100;
  border-color: dfe-vars.$color-base-red-300;
}

:deep(.v-input.v-autocomplete .v-menu__content) {
  max-width: initial;
}
</style>
