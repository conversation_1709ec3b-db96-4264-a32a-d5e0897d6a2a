import FormCollection from '@/components/createOrder/formSectionCollectionAndDelivery/FormCollection.vue';
import DatePicker from '@/components/form/DatePicker.vue';
import TimePicker from '@/components/form/TimePicker.vue';
import { CollectionTimeSlots, OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { AirExportQuoteInformation } from '@dfe/dfe-book-api';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { expect } from 'vitest';
describe('Collection & Delivery - FormCollection component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(FormCollection);
  });

  it('shows collection date picker field', async () => {
    expect(wrapper.findComponent(DatePicker).exists()).toBe(true);
  });

  it('shows time pickers if custom time slot is selected', async () => {
    const orderStore = useCreateOrderFormStore();
    orderStore.orderType = OrderTypes.RoadForwardingOrder;
    const store = useCreateOrderFormCollectionAndDeliveryStore();
    store.collectionTimeSlot = {
      from: CollectionTimeSlots.Custom,
      to: CollectionTimeSlots.Custom,
    };

    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents(TimePicker)).toHaveLength(2);
  });

  it('shows collection interpreter select field only for collection order', async () => {
    const store = useCreateOrderFormStore();

    store.orderType = OrderTypes.RoadForwardingOrder;
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-test="book-collection-interpreter-option"]').exists()).toBe(false);

    store.orderType = OrderTypes.RoadCollectionOrder;
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-test="book-collection-interpreter-option"]').exists()).toBe(true);

    const storeCollDel = useCreateOrderFormCollectionAndDeliveryStore();
    expect(storeCollDel.collectionInterpreterOption).toBe('FIX');
  });

  it('disables date picker input if there is an order from Quote (air export or road with daily price)', async () => {
    expect(wrapper.findComponent(DatePicker).props('disabled')).toBe(false);

    const store = useCreateOrderFormStore();
    const referencesStore = useCreateOrderOrderReferencesFormStore();
    store.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;

    /* NOTE: Disabled as quickfix with DFE-3181 */
    // store.orderType = OrderTypes.AirExportOrder;
    //
    // await wrapper.vm.$nextTick();
    // expect(wrapper.findComponent(DatePicker).props('disabled')).<toBe(true);

    store.orderType = OrderTypes.RoadForwardingOrder;
    referencesStore.dailyPriceReference = '060';

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(DatePicker).props('disabled')).toBe(true);
  });
});
