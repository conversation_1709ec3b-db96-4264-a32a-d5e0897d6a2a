import SeaOrder from '@/views/createOrder/sea/SeaOrder.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes } from '@/enums';
import CustomerSectionCardMain from '@/components/createOrder/formSectionCustomer/collectingOrder/CardMain.vue';
import AddressSectionCardMain from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import FreightSectionCardMain from '@/components/createOrder/formSectionFreight/CardMain.vue';
import IncotermsSectionCardMain from '@/components/createOrder/formSectionIncoTerms/CardMain.vue';
import CollectionAndDeliverySectionCardMain from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import OrderReferencesSectionCardMain from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import OrderDocumentsSection from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import PortRouting from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortRouting.vue';
import { beforeAll } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { VApp } from 'vuetify/components';
import { h } from 'vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import SelectAddress from '@/components/form/SelectAddress.vue';
import { addresses } from '@/mocks/fixtures/addresses';

describe('Sea - SeaOrder view', () => {
  let wrapper: VueWrapper;
  let parent: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    parent = mount(VApp, {
      slots: {
        default: h(SeaOrder),
      },
      global: {
        stubs: {
          VFooter: {
            template: '<div />',
          },
        },
      },
    });
    wrapper = parent.findComponent(SeaOrder);
  });

  it.each([
    ['Customer', CustomerSectionCardMain],
    ['Addresses', AddressSectionCardMain],
    ['Freight', FreightSectionCardMain],
    ['Incoterms', IncotermsSectionCardMain],
    ['Collection and Delivery', CollectionAndDeliverySectionCardMain],
    ['Order References', OrderReferencesSectionCardMain],
    ['Order Documents', OrderDocumentsSection],
  ])('shows %s section', async (sectionName, sectionComponent) => {
    const { customers } = storeToRefs(useCreateOrderDataStore());

    if (customers) {
      customers.value = addresses;
    }

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(sectionComponent).exists()).toBe(true);
  });

  it('shows routings', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(PortRouting).exists()).toBe(true);
  });

  it('should not show principal if not available', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const store = useCreateOrderDataStore();

    orderType.value = OrderTypes.SeaExportOrder;
    store.customers = [];

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(SelectAddress).exists()).toBe(false);
  });
});
