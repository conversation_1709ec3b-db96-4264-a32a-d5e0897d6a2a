import { shallowMount } from '@vue/test-utils';
import { beforeEach } from 'vitest';
import type { TestUtils } from '../../../test/test-utils';
import FormLabel from '@/components/form/FormLabel.vue';
import SpanField from './SpanField.vue';

const label = 'Label';

describe('SpanField component', () => {
  let wrapper: TestUtils.VueWrapper<typeof SpanField>;

  beforeEach(() => {
    wrapper = shallowMount(SpanField);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('displays label depending on prop', async () => {
    await wrapper.setProps({ label });

    const formLabel = wrapper.getComponent(FormLabel);
    expect(formLabel.text()).toEqual(label);
  });

  it('displays text depending on prop', async () => {
    const text = 'Some text';
    await wrapper.setProps({ value: text });

    expect(wrapper.text()).toContain(text);
  });

  it('displays content from slot when provided', async () => {
    wrapper = shallowMount(SpanField, {
      slots: {
        default: 'Slot content',
      },
    });
    const text = 'Some text';
    await wrapper.setProps({ value: text });

    expect(wrapper.text()).not.toContain(text);
    expect(wrapper.text()).toContain('Slot content');
  });
});
