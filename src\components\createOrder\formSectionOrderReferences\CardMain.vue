<template>
  <SectionCard v-data-test="'section-references'">
    <template #headline>
      {{ t('labels.order_references.text') }}
    </template>
    <RoadReferences v-if="isRoadOrder" />
    <AirAndSeaReferences v-else-if="isAirOrder || isSeaOrder" />
  </SectionCard>
</template>

<script setup lang="ts">
import AirAndSeaReferences from '@/components/createOrder/formSectionOrderReferences/airAndSea/AirAndSeaReferences.vue';
import RoadReferences from '@/components/createOrder/formSectionOrderReferences/road/RoadReferences.vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import SectionCard from '@/components/base/SectionCard.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const createOrderFormStore = useCreateOrderFormStore();
const { isRoadOrder, isAirOrder, isSeaOrder } = storeToRefs(createOrderFormStore);
</script>
