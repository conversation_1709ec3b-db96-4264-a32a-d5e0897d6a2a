import { i18n } from '@/plugins/i18n';
import { differenceInDays, format, isSameDay } from 'date-fns';
import {
  hasValue,
  isDate,
  isDateInRange,
  isDecimalWithMaxPlaces,
  isEkear,
  isEmail,
  isGln,
  isInteger,
  isMax,
  isMin,
  isNumber,
  isPhoneNumber,
  isTime,
} from '@dfe/dfe-frontend-client';

export type ValidationRule<T = string> = (v: T) => boolean | string;

export type ValidationRuleBoolean<T = boolean> = (v: T) => boolean | string;

export interface ValidationRulesMap {
  required: ValidationRule;
  requiredCheckbox: ValidationRuleBoolean;
  requiredWithoutText: ValidationRule;
  email: ValidationRule;
  phone: ValidationRule;
  numbers: ValidationRule;
  date: (format: string) => ValidationRule;
  dateRange: (value: Date | string, dateFormat: string, min: string, max: string) => ValidationRule;
  time: (format: string) => ValidationRule;
  gln: ValidationRule;
  ekaer: ValidationRule;
  positiveNumber: ValidationRule;
  integer: ValidationRule;
  min: (n: number | undefined) => ValidationRule;
  max: (n: number | undefined) => ValidationRule;
  maxChars: (n: number) => ValidationRule;
  maxCharsProperty: <T>(n: number, propertyKey: keyof T) => ValidationRule<T>;
  isConditionFulfilled: (v: boolean) => boolean;
  decimalWithMaxPlaces: (n: number) => ValidationRule;
  noWhitespace: ValidationRule;
  regex: (regex: RegExp, message: string, exampleValue: string) => ValidationRule;
}

const useValidationRules: ValidationRulesMap = {
  required: (v: string) => hasValue(v) || i18n.global.t('labels.validation_input_required.text'),
  requiredCheckbox: (v: boolean) => v || i18n.global.t('labels.validation_input_required.text'),
  requiredWithoutText: (v: string) => hasValue(v),
  email: (v: string) => isEmail(v) || i18n.global.t('labels.validation_invalid_email.text'),
  phone: (v: string) => isPhoneNumber(v) || i18n.global.t('labels.validation_invalid_phone.text'),
  numbers: (v: string) => isNumber(v) || i18n.global.t('labels.validation_numbers_only.text'),
  date: (dateFormat: string) => {
    return (v: string) =>
      isDate(v, dateFormat) || i18n.global.t('labels.validation_invalid_date.text');
  },
  dateRange: (date: Date | string, dateFormat: string, min: string, max: string) => {
    return (v: string) =>
      isDateInRange(v, dateFormat, new Date(min), new Date(max)) ||
      i18n.global.t('labels.validation_invalid_date_range.text', [
        differenceInDays(new Date(max), new Date(min)).toLocaleString(),
        spillDate(min, dateFormat),
      ]);
  },
  time: (timeFormat: string) => {
    return (v: string) =>
      isTime(v, timeFormat) || i18n.global.t('labels.validation_invalid_time.text');
  },
  gln: (v: string) => isGln(v) || i18n.global.t('labels.validation_invalid_gln.text'),
  ekaer: (v: string | undefined) =>
    (v !== undefined && isEkear('E' + v)) || i18n.global.t('labels.validation_invalid_ekaer.text'),
  positiveNumber: (v: string) =>
    (isNumber(v) && isMin(0.0001)(v)) || i18n.global.t('labels.validation_number_positive.text'),
  integer: (v: string) => isInteger(v) || i18n.global.t('labels.validation_integer.text'),
  min: (n: number | undefined) => {
    return (v: string) =>
      !hasValue(n) || isMin(n)(v) || i18n.global.t('labels.validation_number_minimum.text', [n]);
  },
  max: (n: number | undefined) => {
    return (v: string) =>
      !hasValue(n) || isMax(n)(v) || i18n.global.t('labels.validation_number_maximum.text', [n]);
  },
  maxChars: (max: number) => {
    return (v: string) =>
      !hasValue(v) ||
      v.toString().length <= max ||
      i18n.global.t('labels.validation_number_maximum.text', [max]);
  },
  maxCharsProperty: <T>(max: number, propertyKey: keyof T) => {
    return (v?: T) => {
      const propertyValue = v?.[propertyKey] as string;
      return (
        !hasValue(propertyValue) ||
        propertyValue.toString().length <= max ||
        i18n.global.t('labels.validation_number_maximum.text', [max])
      );
    };
  },
  isConditionFulfilled: (v: boolean) => v,
  decimalWithMaxPlaces: (n: number) => {
    return (v: string) =>
      isDecimalWithMaxPlaces(v, n) ||
      i18n.global.t('labels.validation_decimal_with_max_places.text', [n]);
  },
  noWhitespace: (v: string) => {
    return v.trim().length > 0 || i18n.global.t('labels.validation_input_required.text');
  },
  regex: (regex: RegExp, message: string, exampleValue: string) => {
    return (v: string) => {
      return !hasValue(v) || regex.test(v) || i18n.global.t(message, [exampleValue]);
    };
  },
} as const;

export type ValidationRules = typeof useValidationRules;

const spillDate = (minimum: string, formatDate: string) => {
  const currentDate = new Date();
  const min = new Date(minimum);
  return isSameDay(currentDate, min)
    ? i18n.global.t('labels.filter_today.text').toLocaleString().toLowerCase()
    : format(min, formatDate);
};

export const withMessage = (rule: ValidationRule, message: string): ValidationRule => {
  return (v: string) => {
    return (typeof rule === 'function' ? rule(v) === true : rule) || message;
  };
};

export const getMessages = (rules: ValidationRule[], value?: string | number | null) => {
  return rules
    .map((rule) => rule(String(value)))
    .filter((value): value is string => typeof value === 'string');
};

export { useValidationRules, hasValue };
