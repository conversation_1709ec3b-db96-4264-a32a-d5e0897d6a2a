[dfe-book-frontend] {
:deep() {
  .v-checkbox {
    &:not(.v-input--disabled) {
      &.v-input--is-label-active:hover {
        .v-icon {
          color: var(--color-base-blue-700) !important;
        }
      }
    }

    .v-selection-control {
      &__wrapper {
        margin-right: 6px;
      }

      &__input::before {
        display: none;
      }

      &--focused {
        .v-icon {
          color: var(--color-base-blue-700) !important;
        }
      }

      &--dirty {
        .v-icon {
          color: var(--color-base-blue-500) !important;
        }

        &:hover,
        &.v-input--focused {
          .v-icon {
            color: var(--color-base-blue-700) !important;
          }
        }
      }

      &--disabled {
        .v-icon {
          color: var(--color-base-grey-400) !important;
        }
      }

      &--error {
        .v-messages__message {
          color: var(--color-base-red-500);
        }

        .text-body-2.grey--text.text--darken-4 {
          color: var(--color-base-red-500) !important;
        }

        svg.icon path {
          fill: var(--color-base-red-500) !important;
        }
      }
    }

    .v-icon {
      color: var(--color-base-grey-600) !important;

      svg {
        width: 20px;
        height: 20px;
      }
    }

    &--disabled {
      :deep(.v-checkbox:hover .v-icon) {
        color: var(--color-base-blue-600) !important;
      }

      :deep(.v-checkbox.v-input--label-active:hover .v-icon) {
        color: var(--color-base-blue-700) !important;
      }
    }

    &.v-input--disabled {
      cursor: not-allowed;
      pointer-events: auto;
    }
  }
}
}