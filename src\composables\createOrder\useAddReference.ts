import type { OrderReferenceType } from '@dfe/dfe-book-api';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { nextTick } from 'vue';

export default async function useAddReference(
  referenceType: OrderReferenceType,
  options?: {
    withLoadingData?: boolean;
    required?: boolean;
    setFocus?: boolean;
  },
) {
  const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();

  const lastNumberId = createOrderOrderReferencesFormStore.addReference(referenceType, options);

  await nextTick();

  if (lastNumberId) {
    const addedElementHtml: HTMLInputElement | null = document.querySelector(`#${lastNumberId}`);

    addedElementHtml?.focus();
  }
}
