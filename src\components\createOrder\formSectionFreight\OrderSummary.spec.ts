import OrderSummary from '@/components/createOrder/formSectionFreight/OrderSummary.vue';
import { shallowMount, VueWrapper } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { AirExportQuoteInformation, OrderType, Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import SelectField from '@/components/form/SelectField.vue';
import NumberField from '@/components/form/NumberField.vue';
import { mockServer } from '@/mocks/server';
import { nextTick } from 'vue';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { storeToRefs } from 'pinia';

const mockCurrencies = {
  countryCode: 'DE',
  currencyCode: 'EUR',
};

describe('OrderSummary component', () => {
  const event = vi.fn();
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        currencies: [mockCurrencies, mockCurrencies],
        customerSettings: {
          manualNumberOfLabels: true,
        },
      },
    });
  });

  beforeEach(() => {
    wrapper = shallowMount(OrderSummary);
  });

  afterEach(() => {
    wrapper.unmount();
    event.mockClear();
  });

  it('shows currencies select-field if there are at least two currencies', async () => {
    await vi.waitFor(() => {
      expect(wrapper.findComponent(SelectField).exists()).toBe(true);
    });
  });

  it.each([
    ['road forwarding', OrderTypes.RoadForwardingOrder, false, false, false],
    ['road collection', OrderTypes.RoadCollectionOrder, false, false, false],
    ['road forwarding Q2B', OrderTypes.RoadForwardingOrder, true, true, true],
    ['road collection Q2B', OrderTypes.RoadCollectionOrder, true, true, true],
    ['road forwarding Q2B no price', OrderTypes.RoadForwardingOrder, true, false, false],
    ['road collection Q2B no price', OrderTypes.RoadCollectionOrder, true, false, false],
  ])(
    'handles state of price related inputs - %s',
    async (text1, orderType, isQ2B, hasDailyPrice, expectedDisabled) => {
      await vi.waitFor(() => {
        expect(wrapper.findComponent(SelectField).props('disabled')).toBe(false);
        expect(wrapper.findComponent(NumberField).props('disabled')).toBe(false);
      });

      const store = useCreateOrderFormStore();
      const referencesStore = useCreateOrderOrderReferencesFormStore();

      store.orderType = orderType as OrderType;

      if (isQ2B) {
        store.quoteInformation = {
          orderType,
          quoteExpiryDate: '2124-12-31',
          customerNumber: '03011149',
          collectionDate: '2024-12-31',
          termCode: 'CFR',
          quoteRequestId: 1,
        } as AirExportQuoteInformation;
      }

      if (isQ2B && hasDailyPrice) {
        referencesStore.dailyPriceReference = '060';
      }

      await nextTick();

      expect(wrapper.findComponent(SelectField).props('disabled')).toBe(expectedDisabled);
      expect(wrapper.findComponent(NumberField).props('disabled')).toBe(expectedDisabled);

      store.$reset();
      referencesStore.$reset();
    },
  );

  it.each([
    ['Air Export', OrderTypes.AirExportOrder],
    ['Air Import', OrderTypes.AirImportOrder],
    ['Sea Export', OrderTypes.SeaExportOrder],
    ['Sea Import', OrderTypes.SeaImportOrder],
  ])('hides value and currency for %s orders', async (text1, orderType) => {
    const store = useCreateOrderFormStore();

    store.orderType = orderType as OrderType;

    await vi.waitFor(() => {
      expect(wrapper.findComponent(SelectField).exists()).toBe(false);
      expect(wrapper.findComponent(NumberField).exists()).toBe(false);
    });

    store.$reset();
  });

  it('Shows number field for manual number of labels according to customer settings', async () => {
    wrapper = shallowMount(OrderSummary);
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;

    await wrapper.vm.$nextTick();

    await vi.waitFor(() => {
      expect(wrapper.find('[data-test="book-order-number-of-labels"]').exists()).toBeTruthy();
    });
  });

  it("hide goodsValueSection if it's not Road Order", async () => {
    expect(wrapper.findComponent({ ref: 'goodsValueSection' }).exists()).toBe(true);

    wrapper = shallowMount(OrderSummary);
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.transportType = Segment.AIR;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ ref: 'goodsValueSection' }).exists()).toBe(false);
  });

  it('shows goodsValueSection if it is a Road Order', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;
    await nextTick();

    expect(wrapper.findComponent({ ref: 'goodsValueSection' }).exists()).toBe(true);
  });

  it('should show total vgm when full container load', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.SeaImportOrder;
    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    const { isFullContainerLoad } = storeToRefs(createOrderOrderLineFormStore);
    if (isFullContainerLoad) {
      isFullContainerLoad.value = true;
    }
    await nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'totalVGMRef' }).exists()).toBe(true);
    });
  });

  it('should show total container quantity when full container load', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.SeaImportOrder;
    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    const { isFullContainerLoad } = storeToRefs(createOrderOrderLineFormStore);
    if (isFullContainerLoad) {
      isFullContainerLoad.value = true;
    }
    await nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'totalContainerQuantityRef' }).exists()).toBe(true);
    });
  });
});
