import type { PackagingOptionsWithFavorites } from '@dfe/dfe-book-api';
import type { Ref } from 'vue';
import type { TranslateResult } from 'vue-i18n';

export const usePackagingOptionsList = (
  favoritesHeader: TranslateResult | string,
  header: TranslateResult | string,
  options: Ref<PackagingOptionsWithFavorites>,
) => {
  const { favorites, packagingOptions = [] } = options.value;

  if (favorites?.length) {
    return [
      { header: favoritesHeader },
      ...favorites,
      { divider: true },
      { header },
      ...packagingOptions,
    ];
  }

  return packagingOptions;
};
