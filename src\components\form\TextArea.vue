<template>
  <div>
    <label v-if="label" :for="textareaId" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VTextarea
      :id="textareaId"
      bg-color="white"
      :model-value="modelValue"
      single-line
      variant="outlined"
      density="compact"
      hide-details="auto"
      :disabled="disabled"
      :rules="rules"
      persistent-counter
      :rows="0"
      :counter="maxLength"
      @update:model-value="onInputChange"
      @paste="onPaste"
    />
  </div>
</template>

<script setup lang="ts">
import { ValidationRule } from '@/composables/form/useValidationRules';
import { createUuid } from '@/utils/createUuid';

interface Props {
  id?: string;
  label?: string;
  required?: boolean;
  maxLength?: number;
  disabled?: boolean;
  rules?: ValidationRule[];
}

const modelValue = defineModel<string>({ default: '' });

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'paste']);

const textareaId = props.id ?? `textarea-${createUuid()}`;

const onInputChange = (value: HTMLInputElement['value']) => {
  emit('update:modelValue', value);
};

const onPaste = (event: ClipboardEvent) => {
  emit('paste', event);
};
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

:deep(.v-textarea) .v-field,
:deep(.v-textarea) .v-field__input {
  min-height: 96px;
}

:deep(.v-text-field--enclosed) .v-text-field__details {
  margin-bottom: 0;
}

:deep(::-webkit-resizer) {
  color: vars.$color-base-grey-700;
  background-image: url('@dfe/dfe-frontend-styles/assets/icons/custom_resize_frame-16px.svg');
}
</style>
