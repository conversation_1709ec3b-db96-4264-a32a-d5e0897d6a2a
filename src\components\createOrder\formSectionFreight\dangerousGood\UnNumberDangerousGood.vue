<template>
  <VRow>
    <VCol class="d-flex justify-space-between align-center py-1">
      <span class="order-line--title | text-body-3 text-grey-darken-2">
        {{ getTitle() }}
      </span>

      <DfeIconButton
        :size="ComponentSize.DEFAULT"
        :color="ColorVariants.NEUTRAL"
        :tooltip="t('labels.delete_label.text')"
        :filled="false"
        @click="deleteUnNumber"
      >
        <MaterialSymbol size="24" color="grey-darken-2">
          <DeleteIcon />
        </MaterialSymbol>
      </DfeIconButton>
    </VCol>
  </VRow>

  <div class="d-flex flex-wrap">
    <div
      class="d-flex flex-wrap flex-md-nowrap align-start"
      :class="{ 'w-100': dangerousGood.dangerousGoodDataItem }"
    >
      <UnNumberSelection
        :un-number-data="dangerousGood.dangerousGoodDataItem"
        :nos="dangerousGood.nos"
        @update:nos="updateValue('nos', $event)"
      />
    </div>

    <div class="d-flex flex-grow-1 flex-wrap flex-md-nowrap align-start">
      <CounterField
        class="size-sm mr-3 mt-4"
        :required="true"
        :label="t('labels.number_of_packages.text')"
        type="number"
        :max="999"
        :min="0"
        :rules="[
          useValidationRules.integer,
          useValidationRules.min(0),
          useValidationRules.max(999),
          useValidationRules.positiveNumber,
        ]"
        :model-value="dangerousGood.noOfPackages"
        append-inner-icon="mdi-plus"
        prepend-inner-icon="mdi-minus"
        :disabled="false"
        @update:model-value="updateValue('noOfPackages', $event)"
      />

      <AutocompleteField
        :model-value="dangerousGood.packaging"
        :items="dangerousGoodsPackagingOptionsList"
        item-title="translationKey"
        item-value="code"
        :label="t('labels.dangerous_good_packaging.text')"
        :placeholder="t('labels.select_option.text')"
        :required="true"
        :multiline-menu="false"
        return-object
        :message="t('labels.validation_select_input_required.text')"
        :menu-icon="ArrowDropDownIcon"
        class="size-lg mr-3 mt-4"
        @update:model-value="updateValue('packaging', $event)"
      />

      <ComboboxMeasurementFields
        :required="true"
        :model-value="dangerousGood.grossMass ?? null"
        :label="t('labels.gross_mass.text')"
        class="size-sm mr-3 mt-4"
        append="kg/L"
        @update:model-value="updateValue('grossMass', $event)"
      />

      <SpanField
        class="mr-3 mt-4"
        :label="t('labels.environmentally_hazardous.text')"
        :required="true"
      >
        <VRadioGroup
          :model-value="dangerousGood.environmentallyHazardous"
          hide-details="auto"
          density="compact"
          direction="horizontal"
          :inline="true"
          :rules="[useValidationRules.required]"
          @update:model-value="updateValue('environmentallyHazardous', $event)"
        >
          <RadioField :value="false" :label="t('labels.no_label.text')" class="mr-3" />
          <RadioField :value="true" :label="t('labels.yes_label.text')" />
        </VRadioGroup>
      </SpanField>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import {
  ADRDangerousGood4Store,
  getEmptyUnNumberDangerousGood,
} from '@/store/createOrder/orderLine';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import { update } from 'lodash';
import { SelectItemKey } from '@/types/vuetify';
import UnNumberSelection from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberSelection.vue';
import CounterField from '@/components/form/CounterField.vue';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { useDangerousGoodsPackagingOptions } from '@/composables/data/useDangerousGoodPackagingOptions';
import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import { useDangerousGoodPackagingOptionsList } from '@/composables/createOrder/useDangerousGoodPackagingOptionsList';
import { computed } from 'vue';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import SpanField from '@/components/form/SpanField.vue';
import RadioField from '@/components/form/RadioField.vue';
import { useValidationRules } from '@/composables/form/useValidationRules';

interface Props {
  computedLineCount: string;
}
const props = defineProps<Props>();
const dangerousGood = defineModel<ADRDangerousGood4Store>({
  default: getEmptyUnNumberDangerousGood(1),
});
const emit = defineEmits(['deleteDangerousGood']);
const { t } = useI18n();
const { data: dangerousGoodsPackagingOptions } = useDangerousGoodsPackagingOptions();

const dangerousGoodsPackagingOptionsList = computed(() => {
  if (!dangerousGoodsPackagingOptions.value) {
    return [];
  }
  return useDangerousGoodPackagingOptionsList(dangerousGoodsPackagingOptions, t);
});

function getTitle() {
  return `${t('labels.dangerous_good_position.text')} ( ${t('labels.order_line_header.text')} ${props.computedLineCount} )`;
}

function deleteUnNumber() {
  emit('deleteDangerousGood');
}

const updateValue = (key: string, value?: string | number | null | SelectItemKey) => {
  dangerousGood.value = update({ ...dangerousGood.value }, key, () => value);
};

const triggerValidation = async () => {
  return (
    dangerousGood.value.grossMass !== undefined &&
    dangerousGood.value.packaging !== undefined &&
    dangerousGood.value.noOfPackages !== undefined
  );
};

defineExpose({ triggerValidation });
</script>
