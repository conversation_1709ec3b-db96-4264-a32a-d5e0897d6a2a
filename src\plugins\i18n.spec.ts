import { mockServer } from '@/mocks/server';
import { i18n, useLocale } from '@/plugins/i18n';
import { ref } from 'vue';

describe('i18n plugin', () => {
  let server: ReturnType<typeof mockServer>;

  beforeAll(() => {
    server = mockServer({
      environment: 'test',
      trackRequests: true,
    });
  });

  it('uses default locale', () => {
    expect(i18n.global.locale.value).toEqual('en');
  });

  it('fetches translations for default locale', async () => {
    await useLocale(ref(i18n.global.locale.value));
    expect(server.pretender.handledRequests).toHaveLength(1);
    expect(server.pretender.handledRequests?.at(0)).toEqual(
      expect.objectContaining({
        queryParams: { language: 'en', appIdList: '158', includeGeneralData: 'true' },
      }),
    );
  });

  it('sets locale that is already loaded and does not fetch translations again', async () => {
    await useLocale(ref('en'));
    expect(server.pretender.handledRequests).toHaveLength(1);
    expect(i18n.global.locale.value).toEqual('en');
  });
});
