import useShowEkaerNumberField from '@/composables/createOrder/useShowEkaerNumberField';
import { CountryCodes, OrderTypes } from '@/enums';
import { ref } from 'vue';

describe('useShowEkaerNumberField composable', () => {
  it('returns true for hungarian shipper address', () => {
    const data = {
      countryCodeShipper: CountryCodes.HU,
      countryCodeConsignee: '0',
      countryCodeSelectedCustomer: '0',
      orderType: OrderTypes.RoadCollectionOrder,
      isCustomer: false,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(true);
  });

  it('returns true for hungarian consignee address', () => {
    const data = {
      countryCodeShipper: '0',
      countryCodeConsignee: CountryCodes.HU,
      countryCodeSelectedCustomer: '0',
      orderType: OrderTypes.RoadCollectionOrder,
      isCustomer: false,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(true);
  });

  it('returns true for hungarian customer in forwarding orders', () => {
    const data = {
      countryCodeShipper: '0',
      countryCodeConsignee: '0',
      countryCodeSelectedCustomer: CountryCodes.HU,
      orderType: OrderTypes.RoadForwardingOrder,
      isCustomer: false,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(true);
  });

  it('returns true for hungarian customer in collecting orders with customer as consignee', () => {
    const data = {
      countryCodeShipper: '0',
      countryCodeConsignee: '0',
      countryCodeSelectedCustomer: CountryCodes.HU,
      orderType: OrderTypes.RoadCollectionOrder,
      isCustomer: true,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(true);
  });

  it('returns false for forwarding order without hungarian addresses', () => {
    const data = {
      countryCodeShipper: '0',
      countryCodeConsignee: '0',
      countryCodeSelectedCustomer: '0',
      orderType: OrderTypes.RoadForwardingOrder,
      isCustomer: false,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(false);
  });

  it('returns false for collecting order without hungarian addresses (customer is not consignee)', () => {
    const data = {
      countryCodeShipper: '0',
      countryCodeConsignee: '0',
      countryCodeSelectedCustomer: '0',
      orderType: OrderTypes.RoadCollectionOrder,
      isCustomer: false,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(false);
  });

  it('returns false for collecting order without hungarian addresses (customer is consignee)', () => {
    const data = {
      countryCodeShipper: '0',
      countryCodeConsignee: '0',
      countryCodeSelectedCustomer: '0',
      orderType: OrderTypes.RoadCollectionOrder,
      isCustomer: true,
    };

    expect(useShowEkaerNumberField(ref(data))).toBe(false);
  });
});
