import type { HttpResponse } from '@dfe/dfe-book-api';
import type { DFEClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';

export const GenericResponseHandler = (client: DFEClient<Events>) => {
  const log = <D, E = unknown>(response: HttpResponse<D, E>) => {
    const { ok, error, status, statusText, url } = response;
    if (ok) {
      return;
    }

    if (status >= 500) {
      console.log(typeof error, error);
      let errorMessage = `UNCAUGHT SERVER ERROR[${status}](${url})`;
      if (statusText) {
        errorMessage += `: ${statusText}`;
      }
      client.log.error(errorMessage, 'dfe-book-frontend', error);
    }
  };

  return {
    log,
  };
};
