@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
[dfe-book-frontend] {
:deep(.v-autocomplete__content.v-menu__content) {
  border-radius: vars.$form-autocomplete-menu-border-radius;
}

.v-autocomplete :deep(.v-input__append-inner) {
  cursor: pointer;
}

:deep(.v-autocomplete.v-input--disabled) {
  cursor: not-allowed;
  pointer-events: auto;

  .v-field__field {
    color: vars.$color-base-grey-500;
  }
}
}
