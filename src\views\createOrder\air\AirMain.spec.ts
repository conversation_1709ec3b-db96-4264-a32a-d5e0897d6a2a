import AirMain from '@/views/createOrder/air/AirMain.vue';
import { customers } from '@/mocks/fixtures/customers';
import { mockServer } from '@/mocks/server';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import FormNavigation from '@/components/form/FormNavigation.vue';
import AirOrder from '@/views/createOrder/air/AirOrder.vue';
import QuoteToBookBanner from '@/components/base/banner/QuoteToBookBanner.vue';
import { OrderTypes } from '@/enums';
import { AirExportQuoteInformation } from '@dfe/dfe-book-api';

describe('AirMain view', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customers,
      },
    });
  });

  it('mounts', () => {
    wrapper = shallowMount(AirMain);
  });

  it('shows form navigation and air-order-view', () => {
    expect(wrapper.findComponent(FormNavigation).exists()).toBe(true);
    expect(wrapper.findComponent(AirOrder).exists()).toBe(true);
  });

  it('shows banner if order was created by quote', async () => {
    expect(wrapper.findComponent(QuoteToBookBanner).exists()).toBe(false);

    const formStore = useCreateOrderFormStore();
    formStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(QuoteToBookBanner).exists()).toBe(true);
  });
});
