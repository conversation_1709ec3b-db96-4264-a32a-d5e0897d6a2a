import CardMain from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount, shallowMount } from '@vue/test-utils';
import { mockServer } from '@/mocks/server';
import { furtherAddressTypes } from '@/mocks/fixtures/furtherAddressTypes';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { storeToRefs } from 'pinia';
import { Segment } from '@dfe/dfe-book-api';
import { airExportOrder } from '@/mocks/fixtures/order';
import { countryFavorites } from '@/mocks/fixtures/countries';

describe('airOrder - CardMain', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        airExportOrder,
        furtherAddressTypes,
        countryFavorites,
      },
    });

    wrapper = mount(CardMain);
  });

  it('renders slots', () => {
    const wrapper = shallowMount(CardMain, {
      slots: {
        additionalAddresses: `<div data-test-id="additional">additional slot</div>`,
      },
    });
    expect(wrapper.find(`[data-test-id="additional"]`).text()).toEqual('additional slot');
  });

  it('mounts', () => {
    expect(wrapper.exists()).toBeTruthy();
  });

  it('should show the title', () => {
    const title = wrapper.find('h2');

    expect(title.exists()).toBeTruthy();
    expect(title.text()).toEqual('labels.addresses_title.text');
  });

  it('should fetchFurtherAddressTypes if customerNumber is changed', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const createOrderDataStore = useCreateOrderDataStore();

    const { customerNumber, transportType } = storeToRefs(createOrderFormStore);

    customerNumber.value = '00000001';
    transportType.value = Segment.AIR;

    await wrapper.vm.$nextTick();

    expect(createOrderDataStore.fetchFurtherAddressTypes).toHaveBeenCalled();
    expect(createOrderDataStore.fetchFurtherAddressTypes).toHaveBeenCalledWith(
      '00000001',
      Segment.AIR,
    );
  });

  it('should favoriteCountries if customerNumber is changed', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const createOrderDataStore = useCreateOrderDataStore();

    const { customerNumber, transportType } = storeToRefs(createOrderFormStore);

    customerNumber.value = '00000001';
    transportType.value = Segment.AIR;

    await wrapper.vm.$nextTick();

    expect(createOrderDataStore.fetchFavoriteCountries).toHaveBeenCalled();
    expect(createOrderDataStore.fetchFavoriteCountries).toHaveBeenCalledWith(
      '00000001',
      Segment.AIR,
    );
  });
});
