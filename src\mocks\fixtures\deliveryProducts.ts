import type { DeliveryProducts, DeliveryProductsWithFavorites } from '@dfe/dfe-book-api';

export const roadDeliveryProducts: DeliveryProductsWithFavorites = {
  favorites: [
    {
      code: 'Code_2',
      description: 'Targofix',
      fixedDeliveryDate: true,
    },

    {
      code: 'Code_4',
      description: 'Ogratfix',
      fixedDeliveryDate: true,
      hint: 'Date-specific delivery, delivery after 11 a.m.',
    },
    {
      code: 'Code_1',
      description: 'Targo',
      fixedDeliveryDate: false,
      hint: 'Date-specific delivery, delivery before 10 a.m.',
    },
  ],
  deliveryProducts: [
    {
      code: 'Code_3',
      description: 'Ograt',
      fixedDeliveryDate: false,
    },
  ],
};

export const airDeliveryProducts: DeliveryProducts = [
  {
    code: 'c',
    description: 'avigoexpress',
    hint: 'Fastest possible service - 1 to 3 days',
  },
  {
    code: 'c',
    description: 'avigospeed',
    hint: 'Fast services but cheaper than avigoexpress - 3 to 5 days',
  },
  { code: 'c', description: 'avigostandard', hint: 'Standard routing - 6 to 10 days' },
  {
    code: 'c',
    description: 'avigoflex',
    hint: 'Cheapest and most flexible service - 11 to 15 days',
  },
];
