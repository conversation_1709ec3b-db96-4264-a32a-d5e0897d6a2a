<template>
  <SectionCard id="documentsSection" v-data-test="'section-documents'">
    <template #headline>
      {{ t('labels.documents_label.text') }}
    </template>
    <p v-if="isAirOrder" class="mb-8 text-body-2 upload-mandatory">
      {{ t('labels.upload_commercial_invoice_mandatory.text')
      }}<span class="label-indicator">*</span>
    </p>
    <document-uploader />
  </SectionCard>
</template>

<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import DocumentUploader from '@/components/createOrder/sharedComponents/formSectionDocuments/documentUploader/DocumentUploader.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const createOrderFormStore = useCreateOrderFormStore();
const { isAirOrder } = storeToRefs(createOrderFormStore);
</script>
