import { ConsigneeAddressType, FurtherAddressTypeKeys, FurtherAddressTypesList } from '@/enums';
import {
  InitialAddressState,
  useCreateOrderAddressesStore,
} from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { contactData, loadingPointAddress, orderAddress } from '@/store/sharedInitialStates';
import {
  Address,
  isAirOrder,
  isRoadCollectionOrder,
  isRoadOrder,
  OrderAddress,
  OrderResponseBody,
} from '@dfe/dfe-book-api';
import { isIrelandCountryCode } from '@dfe/dfe-frontend-composables';
import { pick } from 'lodash';
import { storeToRefs } from 'pinia';

export function getAddressFromOrderAddress(address?: OrderAddress): Address {
  const { townCounty } = storeToRefs(useCreateOrderAddressesStore());
  if (!address) {
    return orderAddress();
  }

  if (isIrelandCountryCode(address.countryCode ?? '')) {
    townCounty.value = {
      label: `${address.city}/${address.supplement}`,
      data: { town: address.city ?? '', county: address.supplement ?? '', dachserPlz: '' },
    };
  }

  const addressKeys = Object.keys(orderAddress()) as (keyof Address)[];
  return pick<OrderAddress, keyof Address>(address, addressKeys);
}

export function getBasicAddressFromOrderAddress(orderAddress?: OrderAddress): Address {
  if (!orderAddress) {
    return loadingPointAddress();
  }

  const addressKeys = Object.keys(loadingPointAddress()) as (keyof Address)[];
  return pick<OrderAddress, keyof Address>(orderAddress, addressKeys);
}

export function useSetAddressData(editOrderData: OrderResponseBody) {
  const {
    shipperAddress,
    consigneeAddress,
    furtherAddresses,
    loadingPoint,
    contactDataShipper,
    contactDataConsignee,
    contactDataCustomer,
    contactDataFurtherAddresses,
    differentConsigneeAddress,
    collectionOption,
    consigneeAddressType,
  } = storeToRefs(useCreateOrderAddressesStore());
  const { contactDataDelivery } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());

  shipperAddress.value.address = Object.assign(
    shipperAddress.value.address,
    getAddressFromOrderAddress(editOrderData.shipperAddress),
  );
  consigneeAddress.value.address = Object.assign(
    consigneeAddress.value.address,
    getAddressFromOrderAddress(editOrderData.consigneeAddress),
  );

  if (editOrderData.furtherAddresses && editOrderData.furtherAddresses.length > 0) {
    const furtherAddressLoadingPoint = editOrderData.furtherAddresses.find(
      ({ addressType }) => addressType === FurtherAddressTypesList.loadingPoint,
    );

    loadingPoint.value = {
      label: `${furtherAddressLoadingPoint?.name}, ${furtherAddressLoadingPoint?.city}`,
      ...getBasicAddressFromOrderAddress(furtherAddressLoadingPoint),
    };

    furtherAddresses.value = editOrderData.furtherAddresses?.reduce<InitialAddressState[]>(
      (formatted, address) => {
        if (address.addressType !== FurtherAddressTypesList.loadingPoint) {
          formatted.push({
            address: getAddressFromOrderAddress(address),
            addressType: address.addressType,
          });
        }
        return formatted;
      },
      [],
    );
  }

  contactDataShipper.value = editOrderData.shipperAddress?.contact ?? contactData();
  contactDataConsignee.value = editOrderData.consigneeAddress?.contact ?? contactData();
  if (isAirOrder(editOrderData)) {
    contactDataCustomer.value = editOrderData.customerContactData ?? contactData();
  }
  if (isRoadOrder(editOrderData)) {
    contactDataDelivery.value = editOrderData.deliveryContact ?? contactData();
  }

  if (isRoadOrder(editOrderData) && isRoadCollectionOrder(editOrderData)) {
    collectionOption.value = editOrderData.collectionOption;
    if (editOrderData.differentConsigneeAddress?.name) {
      consigneeAddressType.value = ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;
      differentConsigneeAddress.value.address = Object.assign(
        differentConsigneeAddress.value,
        getAddressFromOrderAddress(editOrderData.differentConsigneeAddress),
      );
    } else {
      consigneeAddressType.value = ConsigneeAddressType.PRINCIPALS_ADDRESS;
    }
  }

  editOrderData.furtherAddresses?.forEach((address) => {
    const addressType = address.addressType as FurtherAddressTypeKeys;
    contactDataFurtherAddresses.value[addressType] = address.contact ?? contactData();
  });
}
