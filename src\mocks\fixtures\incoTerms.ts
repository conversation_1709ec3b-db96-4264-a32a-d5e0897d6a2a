export interface IncoTerms {
  id?: number;
  code: string;
  dachserCode: string;
  label: string;
  description: string;
}

export const incoTerms: IncoTerms[] = [
  {
    id: 12,
    code: 'CFR',
    dachserCode: 'CFR',
    description: '',
    label: 'Carriage Insurance Paid',
  },
  {
    id: 13,
    code: 'CIF',
    dachserCode: 'CIF',
    description: '',
    label: 'Carriage Insurance Paid',
  },
  {
    id: 6,
    code: 'CIP',
    dachserCode: 'CIP',
    description:
      'Similar to CPT. In that case, seller pays also the insurance for the transportation.',
    label: 'Carriage Insurance Paid',
  },
  {
    id: 5,
    code: 'CPT',
    dachserCode: 'CPT',
    description: 'The seller pays all fees until the destination location.',
    label: 'Carriage Paid To',
  },
  {
    id: 8,
    code: 'DAP',
    dachserCode: 'DAP',
    description:
      'Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading.',
    label: 'Delivered at Place',
  },
  {
    id: 9,
    code: 'DDP',
    dachserCode: 'DD4',
    description:
      'Duties are not part of our quotation. Incoterm refers to the requested service only.',
    label: 'Delivered Duty Paid incl. Duty, excl.Tax',
  },
  {
    id: 10,
    code: 'DDP',
    dachserCode: 'DD5',
    description:
      'Duties and Taxes are not part of our quotation. Incoterm refers to the requested service only.',
    label: 'Delivered Duty Paid incl. Duty, incl.Tax',
  },
  {
    id: 7,
    code: 'DPU',
    dachserCode: 'DPU',
    description: '',
    label: 'Delivered at Place Unloaded',
  },
  {
    id: 1,
    code: 'EXW',
    dachserCode: 'EXW',
    description: '',
    label: 'Ex Works',
  },
  {
    id: 15,
    code: 'FAS',
    dachserCode: 'FAS',
    description: '',
    label: 'Free Alongside Ship ',
  },
  {
    id: 2,
    code: 'FCA',
    dachserCode: 'FC1',
    description: '',
    label: 'Free Carrier Pick up Address',
  },
  {
    id: 4,
    code: 'FCA',
    dachserCode: 'FOA',
    description: '',
    label: 'Free Carrier Departure Airport',
  },
  {
    id: 14,
    code: 'FOB',
    dachserCode: 'FOB',
    description: '',
    label: 'Free on Board',
  },
];
