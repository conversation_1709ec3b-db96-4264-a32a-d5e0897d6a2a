<template>
  <div>
    <template v-for="{ title, info, items } in addresses">
      <div v-if="items.length" :key="title">
        <VDivider class="mt-6" />
        <h4 :class="['text-h4 mt-6 mb-4']">
          {{ title }}
          <InfoButtonWithTooltip v-if="info" :label="info" />
        </h4>
        <VRow class="ma-auto">
          <template v-for="{ code } in items">
            <VCol
              v-if="codeIsAdditionalAddress(code) && showAddress[code]"
              :key="code"
              cols="12"
              sm="6"
              lg="4"
            >
              <AddressCard
                v-if="showAddress[code]"
                v-model="getFurtherAddress(code).address"
                :is-customer="false"
                :header="useFurtherAddressTypeLabel(code)"
                :contact-data="contactDataFurtherAddresses[code]"
                :show-delete="true"
                @update-contact="contactDataFurtherAddresses[code] = $event"
                @delete-address="showAddress[code] = false"
              />
            </VCol>
          </template>
        </VRow>
        <div v-if="useShowAddButtons(items, showAddress)" class="d-flex button-row">
          <div v-for="{ code } in items" :key="code">
            <AddButton
              v-if="codeIsAdditionalAddress(code) && !showAddress[code]"
              :label="useFurtherAddressTypeLabel(code)"
              variant="text"
              @add-new-item="addNewFurtherAddress(code)"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onBeforeUnmount, ref, watch } from 'vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import AddButton from '@/components/createOrder/AddButton.vue';
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import type { FurtherAddressTypes } from '@dfe/dfe-book-api';
import useFurtherAddressTypeLabel from '@/composables/createOrder/addresses/useFurtherAddressTypeLabel';
import {
  useCheckFurtherAddresses,
  useShowAddButtons,
} from '@/composables/createOrder/addresses/useCheckAdditionalAddresses';
import type { FurtherAddressType } from '@/enums';
import { FurtherAddressTypesList } from '@/enums';
import { ClientKey } from '@/types/client';

type AdditionalAddresses =
  | FurtherAddressType['customsAgent']
  | FurtherAddressType['importer']
  | FurtherAddressType['deviatingFreightPayer']
  | FurtherAddressType['DC']
  | FurtherAddressType['AE'];

const codeIsAdditionalAddress = (code: string | undefined): code is AdditionalAddresses =>
  !!(
    code &&
    (code === FurtherAddressTypesList.customsAgent ||
      code === FurtherAddressTypesList.importer ||
      code === FurtherAddressTypesList.deviatingFreightPayer ||
      code === FurtherAddressTypesList.DC ||
      code === FurtherAddressTypesList.AE)
  );

const props = defineProps({
  withContacts: { type: Boolean, required: false, default: false },
});

const client = inject(ClientKey);

const { t } = useI18n();

const showAddress = ref({
  [FurtherAddressTypesList.customsAgent]: false,
  [FurtherAddressTypesList.importer]: false,
  [FurtherAddressTypesList.deviatingFreightPayer]: false,
  [FurtherAddressTypesList.DC]: false,
  [FurtherAddressTypesList.AE]: false,
});

const addressStore = useCreateOrderAddressesStore();
const { isThirdCountryAddress, shipperAddress, consigneeAddress, contactDataFurtherAddresses } =
  storeToRefs(addressStore);
const { getFurtherAddress } = addressStore;
const createOrderDataStore = useCreateOrderDataStore();
const { getAvailableFurtherAddressTypes } = createOrderDataStore;
const createOrderFormStore = useCreateOrderFormStore();
const { isOrderLoaded } = storeToRefs(createOrderFormStore);

const addresses = computed(() => {
  const addresses: Record<
    string,
    {
      title: string;
      info?: TranslateResult;
      items: FurtherAddressTypes;
    }
  > = {};
  if (isThirdCountryAddress.value) {
    addresses.custom = {
      title: t('labels.custom_addresses.text').toString(),
      info: t('messages.id6537.text'),
      items: getAvailableFurtherAddressTypes([
        FurtherAddressTypesList.customsAgent,
        FurtherAddressTypesList.importer,
      ]),
    };
  }
  addresses.additional = {
    title: t('labels.additional_addresses.text').toString(),
    items: getAvailableFurtherAddressTypes([
      FurtherAddressTypesList.deviatingFreightPayer,
      FurtherAddressTypesList.AE,
      FurtherAddressTypesList.DC,
    ]),
  };
  return addresses;
});

const addNewFurtherAddress = (addressType: AdditionalAddresses) => {
  addressStore.addNewFurtherAddress(addressType);
  showAddress.value[addressType] = true;
};

if (!props.withContacts) {
  contactDataFurtherAddresses.value = {};
}

const handleClearCreateOrderFormData = () => {
  useCheckFurtherAddresses(showAddress.value);
};
client?.events.on('clearCreateOrderFormData', handleClearCreateOrderFormData);

onBeforeUnmount(() => {
  client?.events.off('clearCreateOrderFormData', handleClearCreateOrderFormData);
});

watch(
  () => [shipperAddress.value.address?.countryCode, consigneeAddress.value.address?.countryCode],
  async () => {
    await addressStore.updateThirdCountryState();
  },
);

watch(
  () => isOrderLoaded.value,
  async () => useCheckFurtherAddresses(showAddress.value),
);
</script>

<style lang="scss" scoped>
.button-row {
  flex-wrap: wrap;
}
</style>
