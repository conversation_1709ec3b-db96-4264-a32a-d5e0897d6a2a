<template>
  <VRow>
    <VCol class="d-flex justify-space-between align-center">
      <span class="order-line--title | text-body-3 text-grey-darken-2">
        {{ getTitle() }}
      </span>

      <DfeIconButton
        :size="ComponentSize.DEFAULT"
        :color="ColorVariants.NEUTRAL"
        :tooltip="t('labels.delete_label.text')"
        :filled="false"
        @click="deleteLQ"
      >
        <MaterialSymbol size="24" color="grey-darken-2">
          <DeleteIcon />
        </MaterialSymbol>
      </DfeIconButton>
    </VCol>
  </VRow>

  <div class="d-flex flex-wrap">
    <div class="d-flex flex-grow-1 flex-wrap flex-md-nowrap align-start">
      <SpanField
        class="mr-3 mt-4 size-xs"
        :label="t('labels.type_label.text')"
        :value="t('labels.limited_quantities_short.text')"
      />
      <ComboboxMeasurementFields
        :required="true"
        :model-value="dangerousGood.grossMass ?? null"
        :label="t('labels.gross_mass.text')"
        class="size-sm mr-3 mt-4"
        append="kg/L"
        @update:model-value="updateValue('grossMass', $event)"
      />

      <div v-if="!dangerousGood.dangerousGoodDataItem">
        <UnNumberDialog
          button-class="mr-3 mt-10"
          @select-item="updateValue('dangerousGoodDataItem', $event)"
        />
      </div>
      <UnNumberSelection
        v-else
        :un-number-data="dangerousGood.dangerousGoodDataItem"
        :nos="dangerousGood.nos"
        @update:nos="updateValue('nos', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { getEmptyLQDangerousGood, LQDangerousGood4Store } from '@/store/createOrder/orderLine';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import { update } from 'lodash';
import { SelectItemKey } from '@/types/vuetify';
import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import SpanField from '@/components/form/SpanField.vue';
import UnNumberDialog from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberDialog.vue';
import UnNumberSelection from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberSelection.vue';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';

interface Props {
  computedLineCount: string;
}
const props = defineProps<Props>();
const dangerousGood = defineModel<LQDangerousGood4Store>({ default: getEmptyLQDangerousGood(1) });
const emit = defineEmits(['deleteDangerousGood']);
const { t } = useI18n();

function getTitle() {
  return `${t('labels.dangerous_good_position.text')} ( ${t('labels.order_line_header.text')} ${props.computedLineCount} )`;
}

function deleteLQ() {
  emit('deleteDangerousGood');
}

const updateValue = (key: string, value?: string | number | null | SelectItemKey) => {
  dangerousGood.value = update({ ...dangerousGood.value }, key, () => value);
};

const triggerValidation = async () => {
  return dangerousGood.value.grossMass !== undefined && dangerousGood.value.grossMass !== null;
};

defineExpose({ triggerValidation });
</script>
