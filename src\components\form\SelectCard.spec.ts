import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import SelectCard from '@/components/form/SelectCard.vue';

describe('SelectCard', () => {
  let wrapper: VueWrapper;
  beforeEach(() => {
    wrapper = mount(SelectCard, {
      props: {
        name: 'test',
        value: 'test',
        label: 'test',
      },
    });
  });

  it('displays the label', () => {
    expect(wrapper.find('[data-test=book-radio-card-label]').text()).toEqual('test');
  });

  it('Renders no description when not provided', () => {
    expect(wrapper.find('[data-test=book-radio-card-description]').exists()).toBe(false);
  });

  it('Renders the description when provided', async () => {
    await wrapper.setProps({ description: 'test' });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('[data-test=book-radio-card-description]').exists()).toBe(true);
  });

  it('emits event when clicked', () => {
    wrapper.findComponent({ name: 'v-radio' }).vm.$emit('update:modelValue');
    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
  });
});
