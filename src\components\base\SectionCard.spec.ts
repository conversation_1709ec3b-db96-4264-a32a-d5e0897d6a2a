import SectionCard from '@/components/base/SectionCard.vue';
import GridContainer from '@/components/grid/GridContainer.vue';
import { shallowMount } from '@vue/test-utils';

describe('SectionCard', () => {
  it('renders slots correctly', () => {
    const wrapper = shallowMount(SectionCard, {
      slots: {
        headline: 'Headline',
        default: 'Content',
      },
    });
    const headlineElement = wrapper.find('h2');
    const contentElement = wrapper.findComponent(GridContainer);

    expect(headlineElement.text()).toContain('Headline');
    expect(contentElement.text()).toContain('Content');
  });
});
