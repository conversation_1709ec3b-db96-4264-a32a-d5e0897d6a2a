<template>
  <span v-if="disabled" class="link link--disabled">
    <MaterialSymbol
      v-if="icon || $slots.icon"
      :size="iconSize"
      class="link-icon mr-1"
      :class="`link-icon--${iconPosition}`"
    >
      <slot name="icon">
        <ChevronIcon />
      </slot>
    </MaterialSymbol>
    <span>{{ text }}</span>
  </span>
  <VBtn
    v-else
    :href="sanitizeHtml(externalLink)"
    class="opacity-100 pl-0"
    :class="`link--${type}`"
    target="_blank"
    referrerpolicy="no-referrer"
    variant="plain"
    color="primary"
  >
    <template #prepend>
      <MaterialSymbol
        v-if="icon || $slots.icon"
        :size="iconSize"
        class="link-icon mr-1"
        :class="`link-icon--${iconPosition}`"
      >
        <slot name="icon">
          <ChevronIcon />
        </slot>
      </MaterialSymbol>
    </template>
    {{ text }}
  </VBtn>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import ChevronIcon from '@dfe/dfe-frontend-styles/assets/icons/chevron_right-16px.svg';
import sanitizeHtml from 'sanitize-html';
import type { PropType } from 'vue';
import type { TranslateResult } from 'vue-i18n';

type IconPosition = 'start' | 'end';
type IconSize = 16 | 24 | '16' | '24';
type LinkType = 'primary' | 'secondary';

defineProps({
  text: {
    type: [String, Object] as PropType<TranslateResult>,
    required: true,
  },
  externalLink: { type: String, default: '#' },
  icon: Boolean,
  disabled: Boolean,
  type: {
    type: String as PropType<LinkType>,
    default: 'primary',
  },
  iconPosition: {
    type: String as PropType<IconPosition>,
    default: 'start',
  },
  iconSize: {
    type: [String, Number] as PropType<IconSize>,
    default: 16,
  },
});
</script>

<style lang="scss" scoped>
@use '@/styles/settings' as *;
@use 'sass:map';
:deep(.link) {
  font-size: map.get($typography, label-1, size);
  font-weight: map.get($typography, label-1, weight);
  line-height: map.get($typography, label-1, line-height);
  text-decoration: none;
  color: var(--color);
}

:deep(.v-btn__prepend) {
  margin: 0 !important;
}

:not(.v-input--is-disabled) :deep(.link:hover),
:not(.v-input--is-disabled) :deep(.link:focus) {
  color: var(--color-focus);
  outline: none;
}

.link--primary {
  --color: var(--color-base-blue-500);
  --color-focus: var(--color-base-blue-700);
}

.link--primary:hover {
  :deep(.v-btn__content),
  :deep(.v-btn__prepend) {
    color: var(--color-focus);
  }
}

.link--secondary {
  --color: var(--color-base-grey-700);
  --color-focus: var(--color-base-grey-900);
}

.link--disabled {
  --color: var(--color-base-grey-500);
  --color-focus: var(--color-base-grey-500);
}

:deep(.link-icon) {
  margin-bottom: 2px;
  vertical-align: middle;
}

.opacity-100 {
  opacity: 1;
}

:deep(.link-icon--end) {
  order: 2;
}
</style>
