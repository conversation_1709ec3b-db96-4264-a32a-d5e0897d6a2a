import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import PackingPosition from '@/components/createOrder/formSectionFreight/packingPosition/PackingPosition.vue';
import {
  PackingPosition4Store,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import { SanitizeKey } from '@/types/sanitize';
import sanitizeHtml from 'sanitize-html';
import { VBtn } from 'vuetify/components';

describe('PackingPosition.vue', () => {
  let wrapper: VueWrapper;
  const orderLineFromStore = useCreateOrderOrderLineFormStore();

  beforeEach(() => {
    orderLineFromStore.addPackingPosition(true);
    wrapper = mount(PackingPosition, {
      props: {
        localId: orderLineFromStore.packingPositions[0].localId,
        lineCounter: 1,
      },
      global: {
        stubs: ['v-dialog'],
      },
      provide: {
        [SanitizeKey as symbol]: sanitizeHtml,
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
    orderLineFromStore.packingPositions = [];
  });

  it('renders packing position component', () => {
    expect(wrapper.find('[data-test="book-packing-position"]').exists()).toBe(true);
  });

  it('calls removePackingPosition when delete button is clicked', async () => {
    const deleteButton = wrapper
      .find('[data-test="book-packing-position-delete-button"]')
      .findComponent(VBtn);

    expect(orderLineFromStore.packingPositions.length).toBe(1);

    await deleteButton.trigger('click');

    expect(orderLineFromStore.packingPositions.length).toBe(0);
  });

  it('calls removePackingPosition when delete button is clicked with order lines child', async () => {
    orderLineFromStore.addOrderLineToPackingPosition(
      orderLineFromStore.packingPositions[0].localId,
    );

    const deleteButton = wrapper
      .find('[data-test="book-packing-position-delete-button"]')
      .findComponent(VBtn);

    expect(orderLineFromStore.packingPositions.length).toBe(1);
    expect(orderLineFromStore.orderLines.length).toBe(2);

    await deleteButton.trigger('click');

    expect(orderLineFromStore.packingPositions.length).toBe(1);
    expect(orderLineFromStore.orderLines.length).toBe(2);

    const confirmDialog = wrapper.findComponent(ConfirmPrompt);
    const confirmButton = confirmDialog.find('[data-test="book-confirm-btn"]');

    await confirmButton?.trigger('click');

    expect(orderLineFromStore.packingPositions.length).toBe(0);
    expect(orderLineFromStore.orderLines.length).toBe(1);
  });

  it('updates packing position quantity', async () => {
    const incrementQuantityButton = wrapper.find('[data-test="book-counter-field-increment"]');
    await incrementQuantityButton.trigger('click');

    const event = wrapper.emitted('update:modelValue');
    expect(event).toHaveLength(1);
    expect(event?.[0]).toStrictEqual([
      {
        localId: 4,
        id: undefined,
        quantity: 1,
        packagingType: undefined,
      },
    ]);
  });

  it('updates packing position packaging', () => {
    const packingAutocomplete = wrapper.findComponent(AutocompleteField);
    packingAutocomplete.vm.$emit('update:modelValue', { code: 'EU', description: 'Euro Pallet' });

    const event = wrapper.emitted('update:modelValue');
    expect(event).toHaveLength(1);
    expect((event?.[0][0] as PackingPosition4Store).packagingType).toStrictEqual({
      code: 'EU',
      description: 'Euro Pallet',
    });
  });
});
