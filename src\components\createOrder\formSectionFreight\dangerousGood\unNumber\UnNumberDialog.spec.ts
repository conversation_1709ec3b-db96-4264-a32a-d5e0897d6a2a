import { mount, VueWrapper } from '@vue/test-utils';
import { describe, it, expect, beforeEach, vi, afterAll, beforeAll } from 'vitest';
import UnNumberDialog from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberDialog.vue';
import { DangerousGoodDataItem } from '@dfe/dfe-book-api';
import { mockServer } from '@/mocks/server';
import { unNumberSearchResults } from '@/mocks/fixtures/unNumberSearchResults';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { VRadioGroup } from 'vuetify/components';

// Mock components
vi.mock('@/components/base/modal/ModalWrapper.vue', () => ({
  default: {
    name: 'ModalWrapper',
    template: '<div><slot name="trigger" /><slot name="body" /><slot name="footer" /></div>',
    props: ['modelValue', 'headline', 'size'],
  },
}));

const openDangerousGoodsDialog = async (wrapper: VueWrapper, searchFieldValue?: string) => {
  const button = wrapper.find('button');
  await button.trigger('click');
  await wrapper.vm.$nextTick();

  if (searchFieldValue) {
    const searchField = wrapper.findComponent('[data-test="book-dangerous-goods-search-field"]');
    await searchField.setValue(searchFieldValue);
  }
};

const selectTableItem = async (wrapper: VueWrapper) => {
  await vi.waitFor(async () => {
    const tableWrapper = wrapper.findComponent({ name: 'DfeDataTable' });
    const tableRadio = tableWrapper.findComponent(VRadioGroup);

    expect(tableRadio.exists()).toBe(true);
    await tableRadio.setValue([0]);
    await wrapper.vm.$nextTick();
  });
};

describe('UnNumberDialog.vue', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
    mockServer({
      environment: 'test',
      fixtures: {
        unNumberSearchResults,
      },
    });
  });

  beforeEach(() => {
    wrapper = mount(UnNumberDialog);
  });

  afterAll(() => {
    wrapper.unmount();
    vi.resetModules();
    vi.resetAllMocks();
  });

  it('renders trigger button', () => {
    expect(wrapper.text()).toContain('labels.un_number.text');
  });

  it('opens modal on trigger button click', async () => {
    expect(wrapper.text()).not.toContain('labels.select_label.text');
    await openDangerousGoodsDialog(wrapper);
    expect(wrapper.text()).toContain('labels.select_label.text');
  });

  it('disables confirm button when no item is selected', async () => {
    await openDangerousGoodsDialog(wrapper);
    const confirmButton = wrapper
      .findAllComponents({ name: 'VBtn' })
      .find((btn) => btn.text().includes('labels.select_label.text'));

    expect(confirmButton?.attributes('disabled')).toBeDefined();
  });

  it('enables confirm button after item selection', async () => {
    await openDangerousGoodsDialog(wrapper, '1203');
    await selectTableItem(wrapper);

    const confirmButton = wrapper
      .findAllComponents({ name: 'VBtn' })
      .find((btn) => btn.text().includes('labels.select_label.text'));

    expect(confirmButton?.attributes('disabled')).toBeUndefined();
  });

  it('emits selected item on confirm and closes dialog', async () => {
    await openDangerousGoodsDialog(wrapper, '1203');
    await selectTableItem(wrapper);

    const confirmButton = wrapper
      .findAllComponents({ name: 'VBtn' })
      .find((btn) => btn.text().includes('labels.select_label.text'));
    await confirmButton?.trigger('click');

    expect(wrapper.emitted('select-item')).toBeTruthy();
    expect((wrapper.emitted('select-item')![0][0] as DangerousGoodDataItem).unNumber).toBe('1203');
  });

  it('closes modal on cancel button click', async () => {
    const button = wrapper.find('button');
    await button.trigger('click');

    expect(wrapper.text()).toContain('labels.select_label.text');

    const cancelButton = wrapper
      .findAllComponents({ name: 'VBtn' })
      .find((btn) => btn.text().includes('labels.cancel_label.text'));
    await cancelButton?.trigger('click');
    expect(wrapper.text()).not.toContain('labels.select_label.text');
  });
});
