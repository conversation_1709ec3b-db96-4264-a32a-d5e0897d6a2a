<template>
  <SectionCard>
    <template #headline>{{ t('labels.order_texts_title.text') }}</template>
    <VRow dense>
      <VCol
        v-for="textLine in filteredTexts"
        :key="textLine.label"
        cols="12"
        sm="8"
        class="textarea-column-wrapper"
      >
        <CheckboxField
          v-model="orderTextByTypeMapping[textLine.type].active"
          :label="textLine.label"
          @update:model-value="onCheckboxChange(orderTextByTypeMapping[textLine.type])"
        />

        <TextArea
          v-if="orderTextByTypeMapping[textLine.type].active"
          v-model="orderTextByTypeMapping[textLine.type].value"
          :max-length="textLine.maxLength"
          :rules="[useValidationRules.maxChars(textLine.maxLength)]"
          class="mt-2 textarea"
        />
      </VCol>
    </VRow>
  </SectionCard>
</template>

<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import TextArea from '@/components/form/TextArea.vue';
import { useOrderTexts } from '@/composables/createOrder/useOrderTexts';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { TextTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { CreateOrderText, useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const createOrderFormStore = useCreateOrderFormStore();
const { isRoadCollectionOrder } = storeToRefs(createOrderFormStore);
const createOrderTextsStore = useCreateOrderTextsStore();
const { texts: orderTexts } = storeToRefs(createOrderTextsStore);
const { filteredTexts } = useOrderTexts();

const orderTextByTypeMapping = computed(() =>
  orderTexts.value.reduce(
    (acc, text) => {
      if (text.textType === TextTypes.InvoiceText && isRoadCollectionOrder.value) {
        text.value = '';
        text.active = false;
      }
      if (text.textType) {
        acc[text.textType] = text;
      }
      return acc;
    },
    {} as Record<string, CreateOrderText>,
  ),
);

const onCheckboxChange = (orderText: CreateOrderText) => {
  if (!orderText.active) {
    orderText.value = undefined;
  }
};
</script>
<style lang="scss" scoped>
.textarea {
  max-width: 640px;
  margin-left: 22px;

  &-column-wrapper {
    padding: 8px;
  }
}
</style>
