<template>
  <DfeBanner ref="errorBanner" :value="isVisible" type="error">
    <span class="text-h5">{{ validationMessage }}</span>

    <template v-if="deduplicatedErrorDescriptions.length">
      <ul v-if="deduplicatedErrorDescriptions.length > 1" class="pt-2 pl-4">
        <li v-for="error in deduplicatedErrorDescriptions" :key="error" class="text-body-2">
          {{ error }}
        </li>
      </ul>
      <p v-else class="pt-2 text-body-2">
        {{ deduplicatedErrorDescriptions[0] }}
      </p></template
    >
  </DfeBanner>
</template>

<script setup lang="ts">
import { inject, nextTick, onBeforeUnmount, ref, watch } from 'vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import { storeToRefs } from 'pinia';
import { ClientKey } from '@/types/client';

const client = inject(ClientKey);
const errorBanner = ref<InstanceType<typeof DfeBanner>>();

const errorBannerStore = useErrorBanner();
const { isVisible, validationMessage, deduplicatedErrorDescriptions, hasFrontendErrors } =
  storeToRefs(errorBannerStore);

watch(
  () => isVisible.value,
  async (isNowVisible, wasVisible) => {
    if (isNowVisible && !wasVisible && !hasFrontendErrors.value) {
      await nextTick();
      scrollToErrorBanner();
    }
  },
);

const handleScrollToGenericErrorBanner = () => {
  scrollToErrorBanner();
};
client?.events.on('scrollToGenericErrorBanner', handleScrollToGenericErrorBanner);

onBeforeUnmount(() => {
  client?.events.off('scrollToGenericErrorBanner', handleScrollToGenericErrorBanner);
});

const scrollToErrorBanner = () => {
  errorBanner.value?.$el.scrollIntoView({ behavior: 'smooth' });
};
</script>
