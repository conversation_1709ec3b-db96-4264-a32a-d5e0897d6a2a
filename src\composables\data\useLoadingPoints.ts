import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

export const useLoadingPoints = () => {
  const createOrderFormStore = useCreateOrderFormStore();
  const { isRoadForwardingOrder } = storeToRefs(createOrderFormStore);
  const formStore = useCreateOrderFormStore();
  const { customerNumber, transportType } = storeToRefs(formStore);

  const { api } = useInit();
  return useCustomerQuery('loadingPoints', api.book.customers.getCustomerLoadingPoints, {
    enabled: computed(
      () => !!customerNumber.value && !!transportType.value && isRoadForwardingOrder.value,
    ),
    placeholderData: [],
  });
};
