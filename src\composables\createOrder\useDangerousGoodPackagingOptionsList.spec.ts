import { ref } from 'vue';
import { useDangerousGoodPackagingOptionsList } from './useDangerousGoodPackagingOptionsList';

const emptyPackagingOptions = ref([]);

const packagingOptions = ref([
  { code: 'BOX', translationKey: 'generalData.packagingOptionRoad.BOX' },
  { code: 'DRUM', translationKey: 'generalData.packagingOptionRoad.DRUM' },
  { code: 'BAG', translationKey: 'generalData.packagingOptionRoad.BAG' },
]);

const packagingOptionsTranslate = [
  { code: 'BAG', translationKey: 'Bag' },
  { code: 'BOX', translationKey: 'Box' },
  { code: 'DRUM', translationKey: 'Drum' },
];

const mockT = (key: string) => {
  const translations: Record<string, string> = {
    'generalData.packagingOptionRoad.BOX.text': 'Box',
    'generalData.packagingOptionRoad.DRUM.text': 'Drum',
    'generalData.packagingOptionRoad.BAG.text': 'Bag',
  };
  return translations[key] || key;
};

describe('useDangerousGoodPackagingOptionsList composable', () => {
  it('returns an empty array if no packaging options are available', () => {
    expect(useDangerousGoodPackagingOptionsList(emptyPackagingOptions, mockT));
  });

  it('returns packaging options', () => {
    expect(useDangerousGoodPackagingOptionsList(packagingOptions, mockT)).toEqual(
      packagingOptionsTranslate,
    );
  });
});
