import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import SelectField from '@/components/form/SelectField.vue';
import * as uuid from '@/utils/createUuid';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { initPinia } from '@test/util/init-pinia';
import SelectFullContainerLoad from '@/components/createOrder/formSectionFreight/fullContainerLoad/SelectFullContainerLoad.vue';

const uuidSpy = vi.spyOn(uuid, 'createUuid');

describe('SelectFullContainerLoad.vue', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    initPinia();
    wrapper = mount(SelectFullContainerLoad, {
      props: {
        modelValue: null,
        'onUpdate:modelValue': (value: number | null) => wrapper.setProps({ modelValue: value }),
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
    uuidSpy.mockClear();
  });

  it('renders select full container load component', () => {
    expect(wrapper.findComponent(SelectField).exists()).toBe(true);
  });

  it('selects correct option for model value', async () => {
    const orderLineStore = useCreateOrderOrderLineFormStore();
    uuidSpy.mockReturnValueOnce(2);
    orderLineStore.addFullContainerLoad();
    uuidSpy.mockReturnValueOnce(3);
    orderLineStore.addFullContainerLoad();

    await wrapper.setProps({ modelValue: 2 });
    expect(wrapper.findComponent(SelectField).find('.v-select__selection-text').text()).toEqual(
      '1',
    );

    await wrapper.setProps({ modelValue: 3 });
    expect(wrapper.findComponent(SelectField).find('.v-select__selection-text').text()).toEqual(
      '2',
    );
  });
});
