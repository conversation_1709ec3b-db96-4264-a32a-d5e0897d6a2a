import type { PortItem } from '@/composables/createOrder/usePortSearch';
import type { FurtherAddressTypeKeys } from '@/enums';
import { ConsigneeAddressType, FurtherAddressTypesList } from '@/enums';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { contactData, loadingPointAddress, orderAddress } from '@/store/sharedInitialStates';
import type { ContactPreferences } from '@/types/createOrder';
import type { HandOverSelectionValue } from '@/types/hand-over';
import { HandOverSelection } from '@/types/hand-over';
import type { ContactData } from '@dfe/dfe-address-api';
import type { Address, BasicAddressType, OrderAddress, Port } from '@dfe/dfe-book-api';
import { CollectionOption, OrderType } from '@dfe/dfe-book-api';
import { UserInformation } from '@dfe/dfe-frontend-client';
import { isIrelandCountryCode } from '@dfe/dfe-frontend-composables';
import { every } from 'lodash';
import { defineStore, storeToRefs } from 'pinia';
import { computed } from 'vue';

export interface InitialAddressState {
  address: OrderAddress;
  addressType?: string | BasicAddressType; // TODO Remove type string to clarify allowed values
  lockedByQuote?: OrderAddress['lockedByQuote'];
  customerNumber?: string;
}

export interface TownCounty {
  label: string;
  data: {
    town: string;
    county: string;
    dachserPlz: string;
  };
}

export interface AddressesState {
  shipperAddress: InitialAddressState;
  consigneeAddress: InitialAddressState;
  differentConsigneeAddress: InitialAddressState;
  formAddress: OrderAddress;
  furtherAddresses: InitialAddressState[];
  isThirdCountryAddress: boolean;
  shipperHandOverSelection: HandOverSelectionValue;
  consigneeHandOverSelection: HandOverSelectionValue;
  contactDataFurtherAddresses: { [key in FurtherAddressTypeKeys]?: ContactData };
  contactDataCustomer: ContactData;
  contactDataShipper: ContactData;
  contactDataConsignee: ContactData;
  isCustomer: boolean;
  loadingPoint: Address & { label?: string };
  fromIATA: Port | undefined;
  toIATA: Port | undefined;
  fromPort: Port | undefined;
  toPort: Port | undefined;
  port: PortItem | undefined;
  isShipperAddressDisabled: boolean;
  isShipperContactSet: boolean;
  isConsigneeContactSet: boolean;
  hasUnsavedAddressChanges: boolean;
  isUnsavedAddressChangesDialogOpen: boolean;
  townCounty: TownCounty | undefined;
  collectionOption: CollectionOption | undefined;
  consigneeAddressType: ConsigneeAddressType;
}

export const useCreateOrderAddressesStore = defineStore('createOrderAddresses', {
  state: (): AddressesState => ({
    shipperAddress: {
      address: { ...orderAddress() },
      addressType: '',
    },
    consigneeAddress: {
      address: { ...orderAddress() },
      addressType: '',
    },
    differentConsigneeAddress: {
      address: { ...orderAddress() },
      addressType: '',
    },
    formAddress: { ...orderAddress() },
    furtherAddresses: [],
    isThirdCountryAddress: false,
    contactDataFurtherAddresses: Object.values(FurtherAddressTypesList).reduce(
      (prev, current) => ({ ...prev, [current]: { ...contactData() } }),
      {},
    ),
    contactDataCustomer: { ...contactData() },
    contactDataShipper: { ...contactData() },
    contactDataConsignee: { ...contactData() },
    isCustomer: true,
    shipperHandOverSelection: { selection: HandOverSelection.default },
    consigneeHandOverSelection: { selection: HandOverSelection.default },
    loadingPoint: loadingPointAddress(),
    fromIATA: undefined,
    toIATA: undefined,
    fromPort: undefined,
    toPort: undefined,
    port: undefined,
    isShipperAddressDisabled: false,
    isShipperContactSet: true,
    isConsigneeContactSet: true,
    hasUnsavedAddressChanges: false,
    isUnsavedAddressChangesDialogOpen: false,
    townCounty: undefined,
    consigneeAddressType: ConsigneeAddressType.PRINCIPALS_ADDRESS,
    collectionOption: undefined,
  }),
  getters: {
    quoteAirAddressesOrigin(state) {
      const formStore = useCreateOrderFormStore();
      const { isAirExportOrderFromQuote, isAirImportOrderFromQuote } = storeToRefs(formStore);

      if (isAirExportOrderFromQuote.value || isAirImportOrderFromQuote.value) {
        return {
          shipper: state.shipperHandOverSelection.selection,
          consignee: state.consigneeHandOverSelection.selection,
        };
      }

      return { shipper: null, consignee: null };
    },
    isShipperHandOverSelectionPort(state) {
      return state.shipperHandOverSelection.selection === HandOverSelection.port;
    },
    isConsigneeHandOverSelectionPort(state) {
      return state.consigneeHandOverSelection.selection === HandOverSelection.port;
    },
    fromAirportDisplayName(): string {
      return this.fromIATA ? `${this.fromIATA.code} - ${this.fromIATA.name}` : '';
    },
    toAirportDisplayName(): string {
      return this.toIATA ? `${this.toIATA.code} - ${this.toIATA.name}` : '';
    },
    fromSeaportDisplayName(): string {
      return this.fromPort ? `${this.fromPort.code} - ${this.fromPort.name}` : '';
    },
    toSeaportDisplayName(): string {
      return this.toPort ? `${this.toPort.code} - ${this.toPort.name}` : '';
    },
    isShipperContactDataSet(): boolean {
      return every([this.contactDataShipper.name, this.contactDataShipper.email]);
    },
    isConsigneeContactDataSet(): boolean {
      return every([this.contactDataConsignee.name, this.contactDataConsignee.email]);
    },
    isDifferentConsigneeAddress(): boolean {
      return this.consigneeAddressType === ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;
    },
  },
  actions: {
    setDefaultLoadingPoint() {
      this.loadingPoint = {
        label: 'labels.dfe_none.text',
        name: 'labels.dfe_none.text',
      };
    },
    setContactName(userInformation: UserInformation) {
      const firstAndLastname = `${userInformation.firstName} ${userInformation.lastName}`;

      return firstAndLastname.length <= 30 ? firstAndLastname : `${userInformation.lastName}`;
    },
    setDefaultContactDataShipper(
      userInformation: UserInformation,
      preferences: ContactPreferences,
    ) {
      if (this.contactDataShipper.name === '' && this.contactDataShipper.email === '') {
        this.contactDataShipper = {
          ...contactData(),
          name: this.setContactName(userInformation),
          email: userInformation.email,
          telephone: preferences?.phoneNumber,
          mobile: preferences?.mobileNumber,
        };
      }
    },
    setDefaultContactDataConsignee(
      userInformation: UserInformation,
      preferences: ContactPreferences,
    ) {
      if (this.contactDataConsignee.name === '' && this.contactDataConsignee.email === '') {
        this.contactDataConsignee = {
          ...contactData(),
          name: this.setContactName(userInformation),
          email: userInformation.email,
          telephone: preferences?.phoneNumber,
          mobile: preferences?.mobileNumber,
        };
      }
    },
    getFurtherAddress(addressType: FurtherAddressTypeKeys) {
      return (
        this.furtherAddresses.find((address) => address.addressType === addressType) ?? {
          address: orderAddress(),
          addressType,
        }
      );
    },
    // TODO Rename to "setFurtherAddress". Change implementation so that the function upserts an address in the furtherAddresses array
    addNewFurtherAddress(type: string) {
      if (this.furtherAddresses.some((address) => address.addressType === type)) {
        return;
      }
      this.furtherAddresses.push({
        address: orderAddress(),
        addressType: type,
      });
    },
    removeFurtherAddress(type: string) {
      this.furtherAddresses = this.furtherAddresses.filter(
        (address) => address.addressType !== type,
      );
    },
    setTownCountyForIrishPrincipalAddress(address: InitialAddressState) {
      const { townCounty } = storeToRefs(useCreateOrderAddressesStore());
      const isIrelandSelected = computed(() =>
        isIrelandCountryCode(address.address.countryCode ?? ''),
      );

      if (isIrelandSelected.value && address.customerNumber) {
        townCounty.value = {
          label: `${address.address.city}`,
          data: {
            town: address.address.city ?? '',
            county: '',
            dachserPlz: '',
          },
        };
      }
    },
    setCustomerAddressToPrincipal() {
      const { orderType, selectedCustomer } = storeToRefs(useCreateOrderFormStore());
      const { address } = selectedCustomer.value;

      if (!address) return;
      if (
        orderType.value === OrderType.SeaImportOrder ||
        orderType.value === OrderType.AirImportOrder ||
        orderType.value === OrderType.RoadCollectionOrder
      ) {
        if (this.consigneeAddress.address.id) {
          address.id = this.consigneeAddress.address.id;
        }
        this.consigneeAddress.address = address;
        this.consigneeAddress.customerNumber = selectedCustomer.value.customerNumber;
        this.shipperAddress.customerNumber = undefined;
        this.setTownCountyForIrishPrincipalAddress(this.consigneeAddress);
      } else {
        if (this.shipperAddress.address.id) {
          address.id = this.shipperAddress.address.id;
        }
        this.shipperAddress.address = address;
        this.shipperAddress.customerNumber = selectedCustomer.value.customerNumber;
        this.consigneeAddress.customerNumber = undefined;
        this.setTownCountyForIrishPrincipalAddress(this.shipperAddress);
      }

      this.isShipperAddressDisabled = true;
    },
    setCustomerAddressToLoadingPoint() {
      const createOrderFormStore = useCreateOrderFormStore();
      const { selectedCustomer } = storeToRefs(createOrderFormStore);

      if (selectedCustomer.value.address) {
        this.loadingPoint = selectedCustomer.value.address;
      }
    },
    resetLoadingPoint() {
      this.loadingPoint = loadingPointAddress();
    },
    async updateThirdCountryState() {
      if (
        !this.shipperAddress.address?.countryCode ||
        !this.consigneeAddress.address?.countryCode
      ) {
        this.isThirdCountryAddress = false;
        return;
      }

      const apiResponse = await this.api.book.thirdCountryConstellation.isThirdCountryConstellation(
        {
          fromCountryCode: this.shipperAddress.address.countryCode,
          toCountryCode: this.consigneeAddress.address.countryCode,
        },
      );

      this.isThirdCountryAddress = apiResponse.data.result as boolean;
    },
    async updateRoutingForSelection(
      handOverSelection: HandOverSelectionValue,
      fromOrTo: 'from' | 'to',
      selection: (typeof HandOverSelection)[keyof typeof HandOverSelection],
      alternativeAddress?: OrderAddress,
    ) {
      const createOrderFormStore = useCreateOrderFormStore();
      const { isAirOrderFromQuote, isSeaOrderFromQuote, isSeaOrder } =
        storeToRefs(createOrderFormStore);

      if (
        handOverSelection.selection !== selection ||
        isAirOrderFromQuote.value ||
        isSeaOrderFromQuote.value
      ) {
        return;
      }

      const dataStore = useCreateOrderDataStore();
      const { fromPortRouting, toPortRouting } = storeToRefs(dataStore);
      await dataStore.fetchPortRouting(fromOrTo, alternativeAddress);

      if (fromOrTo === 'from') {
        if (isSeaOrder.value) {
          this.fromPort = fromPortRouting.value[0];
        } else {
          this.fromIATA = fromPortRouting.value[0];
        }
      }
      if (fromOrTo === 'to') {
        if (isSeaOrder.value) {
          this.toPort = toPortRouting.value[0];
        } else {
          this.toIATA = toPortRouting.value[0];
        }
      }
    },
    resetPortRoutingForSelection(
      handOverSelection: HandOverSelectionValue,
      fromOrTo: 'from' | 'to',
      selection: (typeof HandOverSelection)[keyof typeof HandOverSelection],
    ) {
      if (handOverSelection.selection === selection) {
        const { isSeaOrder } = storeToRefs(useCreateOrderFormStore());
        const { fromPortRouting, toPortRouting } = storeToRefs(useCreateOrderDataStore());

        if (fromOrTo === 'from') {
          if (isSeaOrder.value) {
            this.fromPort = undefined;
            fromPortRouting.value = [];
          } else {
            this.fromIATA = undefined;
            fromPortRouting.value = [];
          }
        } else if (isSeaOrder.value) {
          this.toPort = undefined;
          toPortRouting.value = [];
        } else {
          this.toIATA = undefined;
          toPortRouting.value = [];
        }
      }
    },
  },
});
