import EditModal from '@/components/base/modal/EditModal.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { VCard } from 'vuetify/components';

const props = {
  headline: 'Headline',
  confirmText: 'Confirm',
  cancelText: 'Cancel',
};

describe('ModalLayout component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(EditModal, {
      props,
      attachTo: document.body,
    });
  });

  it('emits close event', async () => {
    await wrapper.setProps({ modelValue: true });

    const closeBtn = wrapper.findComponent('.cancel-btn');
    await closeBtn.trigger('click');

    const emitted = wrapper.emitted();
    expect(emitted['close']).toBeTruthy();
    expect(emitted['close']).toHaveLength(1);
  });

  it('displays headline', async () => {
    const headline = 'Headline';
    await wrapper.setProps({ modelValue: true, headline });
    expect(wrapper.getComponent(VCard).find('.modal-header h3').text()).toBe(headline);
  });

  it('displays footer slot', async () => {
    const text = 'Footer';

    wrapper = mount(EditModal, {
      props: {
        ...props,
        modelValue: true,
      },
      attachTo: document.body,
      slots: {
        footer: `<div class='footer-text'>${text}</div>`,
      },
    });

    expect(wrapper.getComponent(VCard).find('.modal-footer .footer-text').text()).toBe(text);
  });
});
