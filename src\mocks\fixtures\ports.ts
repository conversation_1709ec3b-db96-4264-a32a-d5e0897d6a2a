import type { Ports } from '@dfe/dfe-book-api';
import { PortType } from '@dfe/dfe-book-api';

export const ports: Ports = [
  {
    countryCode: 'DE',
    name: 'Frankfurt/Main',
    code: 'FRA',
    type: PortType.AIRPORT,
  },
  {
    countryCode: 'DE',
    name: 'Flugha<PERSON>',
    code: 'MUC',
    type: PortType.AIRPORT,
  },
  {
    countryCode: 'US',
    name: 'John F. Kennedy International Airport',
    code: 'JFK',
    type: PortType.AIRPORT,
  },
  {
    countryCode: 'CN',
    name: 'Shanghai Pudong International Airport',
    code: 'PVG',
    type: PortType.AIRPORT,
  },
  {
    countryCode: 'DE',
    name: 'Hamburger Hafen',
    code: 'DEHAM',
    type: PortType.SEAPORT,
  },
  {
    countryCode: 'SG',
    name: 'Port of Singapore',
    code: 'SGSIN',
    type: PortType.SEAPORT,
  },
  {
    countryCode: 'CN',
    name: 'Port of Shenzhen',
    code: 'CNSZ<PERSON>',
    type: PortType.SEAPORT,
  },
];
