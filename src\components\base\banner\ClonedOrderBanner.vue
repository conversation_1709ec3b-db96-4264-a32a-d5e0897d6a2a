<template>
  <DfeBanner type="info" :value="isClonedDraftOrder" class="mb-2">
    <template v-if="isClonedDraftOrder">
      <div class="my-1">
        <div class="text-h5">{{ t('labels.cloned_order_title.text') }}</div>
        <div class="text-body-2 mt-2">
          {{ t('labels.cloned_order_description.text') }}
          <ul class="pt-1 pl-5">
            <li class="text-body-2">{{ t('labels.order_references.text') }}</li>
            <li class="text-body-2">{{ t('labels.documents_label.text') }}</li>
          </ul>
        </div>
      </div>
    </template>
  </DfeBanner>
</template>

<script setup lang="ts">
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useI18n } from 'vue-i18n';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';

const createOrderFormStore = useCreateOrderFormStore();
const { isClonedDraftOrder } = storeToRefs(createOrderFormStore);

const { t } = useI18n();
</script>
<style lang="scss">
.v-banner__prepend {
  margin-top: 4px;
  margin-bottom: 4px;
  margin-inline-end: 10px;
}
</style>
