import { beforeEach, describe, expect, it } from 'vitest';
import { storeToRefs } from 'pinia';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { mount, VueWrapper } from '@vue/test-utils';
import { createClientMock } from '../../../../../test/util/mock-client';
import { ClientKey } from '@/types/client';
import DeleteOverflowSSCCs from '@/components/createOrder/sharedComponents/deleteOverflowSSCCs/DeleteOverflowSSCCs.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { mockServer } from '@/mocks/server';
import type { CustomerSettings } from '@dfe/dfe-book-api';
import { customerSettings } from '@/mocks/fixtures/customerSettings';
import { VCheckbox } from 'vuetify/components';

describe('DeleteOverflowSSCCs.vue', () => {
  const client = createClientMock();
  let wrapper: VueWrapper;

  const customerSettingsMock: CustomerSettings = customerSettings;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: customerSettingsMock,
      },
    });
  });

  beforeEach(() => {
    wrapper = mount(DeleteOverflowSSCCs, {
      attachTo: document.body,
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });
    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    const { generatedSSccs } = storeToRefs(createOrderOrderLineFormStore);
    generatedSSccs.value = ['123456789012345678', '123456789012345679'];
  });

  it('mounts correctly', () => {
    expect(wrapper).toBeDefined();
  });

  it('should call confirm event', async () => {
    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    const removeSSCCsSpy = vi.spyOn(createOrderOrderLineFormStore, 'removeSSccs');

    createOrderOrderLineFormStore.addOrderLine();

    createOrderOrderLineFormStore.orderLines[0].quantity = 1;

    expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    const callbackFunction = vi.fn();
    client.events.emit('showDeleteOverflowSSCCs', callbackFunction);
    await wrapper.vm.$nextTick();
    expect(wrapper.emitted('update:modelValue')).toEqual([[true]]);
    const checkBoxFields = wrapper.findAllComponents(VCheckbox);
    expect(checkBoxFields.length).toBe(2);
    await checkBoxFields[0].setValue({ '123456789012345678': true });
    await wrapper.getComponent('[data-test=book-confirm-btn]').trigger('click');

    expect(callbackFunction).toHaveBeenCalled();
    expect(removeSSCCsSpy).toHaveBeenCalled();
  });

  it('should call cancel ', async () => {
    expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    const callbackFunction = vi.fn();
    client.events.emit('showDeleteOverflowSSCCs', callbackFunction);
    await wrapper.vm.$nextTick();
    expect(wrapper.emitted('update:modelValue')).toEqual([[true]]);
    await wrapper.getComponent('[data-test=book-cancel-btn]').trigger('click');

    expect(callbackFunction).not.toHaveBeenCalled();
  });

  it('displays error message when sscc missmatch is detected ', async () => {
    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    const { manualNumberOfLabelsOnLoad } = storeToRefs(createOrderOrderLineFormStore);

    expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    const callbackFunction = vi.fn();
    client.events.emit('showDeleteOverflowSSCCs', callbackFunction);
    await wrapper.vm.$nextTick();
    expect(wrapper.emitted('update:modelValue')).toEqual([[true]]);
    manualNumberOfLabelsOnLoad.value = 1;
    await wrapper.getComponent('[data-test=book-confirm-btn]').trigger('click');

    expect(callbackFunction).not.toHaveBeenCalled();
    expect(wrapper.getComponent(DfeBanner).isVisible()).toBe(true);
  });
});
