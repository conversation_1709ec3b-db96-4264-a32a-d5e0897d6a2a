@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use 'sass:map';

@mixin withInputElements {
  // Reset width and min-width to initial to avoid conflicts with size classes from CreateOrder.vue
  // We should have unified styles for this in the future, so this is a temporary solution
  width: initial;
  min-width: initial;

  // Add class of the input element to apply the styles to with a comma here
  & > .v-text-field {
    @content;
  }
}

[dfe-book-frontend] {
  .section-freight-sizes {

    .size-sm {
      @include withInputElements {
        width: vars.$form-input-width-sm;
        min-width: vars.$form-input-width-sm;
      };
    }
  }
}