import { useInit } from '@/composables/useInit';
import { useQuery } from '@tanstack/vue-query';
import { computed } from 'vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';

export const useOrderGroups = () => {
  const { api } = useInit();
  const createOrderFormStore = useCreateOrderFormStore();
  const { customerNumber, orderType, transportCountry } = storeToRefs(createOrderFormStore);

  const addressStore = useCreateOrderAddressesStore();

  const { shipperAddress, consigneeAddress } = storeToRefs(addressStore);

  const query = useQuery({
    queryKey: computed(() => [
      'orderGroups',
      customerNumber,
      orderType,
      shipperAddress.value.address.countryCode,
      consigneeAddress.value.address.countryCode,
    ]),
    staleTime: Infinity,
    placeholderData() {
      return [];
    },
    enabled: computed(
      () =>
        !!customerNumber.value &&
        !!orderType.value &&
        !!transportCountry.value.fromCountry &&
        !!transportCountry.value.toCountry,
    ),

    async queryFn() {
      const result = await api.book.customers.getOrderGroupsFiltered({
        customerNumber: customerNumber.value,
        orderType: orderType.value,
        shipperCountryCode: transportCountry.value.fromCountry ?? '',
        consigneeCountryCode: transportCountry.value.toCountry ?? '',
      });
      return result.data;
    },
  });

  return {
    ...query,
  };
};
