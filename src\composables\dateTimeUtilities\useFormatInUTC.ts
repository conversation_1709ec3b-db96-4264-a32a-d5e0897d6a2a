import { TimeFormat } from '@/types/createOrder';

function useFormatInUTC(date: Date | string, locale: string): Date {
  const options = { timeZone: 'UTC' };
  return new Date(date.toLocaleString(locale, options));
}

function useTruncatedDateString(input: string | Date | undefined) {
  if (input && typeof input === 'string') {
    const dateMatch = input.match(/(\d{4}-\d{2}-\d{2})/g);
    if (dateMatch) {
      return dateMatch[0];
    }
  } else if (input instanceof Date) {
    const year = input.getFullYear();
    const month = (input.getMonth() + 1).toString().padStart(2, '0');
    const day = input.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
}

export function useTimeFromDate(dateValue: Date | string, timeFormat: TimeFormat) {
  const date = new Date(dateValue);

  let hours = date.getUTCHours();
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');

  if (timeFormat === 'hh:mm a') {
    const period = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12; // 12-Stunden-Format mit Modulo, um 0 als 12 darzustellen
    return `${hours.toString().padStart(2, '0')}:${minutes} ${period}`;
  } else {
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  }
}

export { useFormatInUTC, useTruncatedDateString };
