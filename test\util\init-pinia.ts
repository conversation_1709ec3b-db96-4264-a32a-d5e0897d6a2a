import { setActivePinia } from 'pinia';
import { setInit } from '@/composables/useInit';
import { createTestingPinia } from '@pinia/testing';
import { useApi } from './use-api';
import { createClientMock } from './mock-client';

export const initPinia = () => {
  const { api } = useApi();
  const client = createClientMock();
  setInit(client, api);
  const testingPinia = createTestingPinia({
    stubActions: false,
    plugins: [
      () => ({
        api,
        createCancelToken: () => 'cancel-token',
        client,
      }),
    ],
  });
  setActivePinia(testingPinia);
  return testingPinia;
};
