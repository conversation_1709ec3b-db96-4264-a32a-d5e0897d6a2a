<template>
  <div ref="roadOrderContainer" tabindex="0">
    <div v-if="forwardingOrderNavigationItems.length > 0">
      <MandatoryFieldsLabel />
      <FormSection
        v-if="isCustomersAvailable"
        ref="forwardingOrderCustomers"
        v-model="valid.roadOrderCustomers"
        class="nav-scroll-section"
      >
        <SectionCustomers class="nav-scroll-section" />
      </FormSection>
      <FormSection
        ref="forwardingOrderAddresses"
        v-model="valid.roadOrderAddresses"
        class="nav-scroll-section"
      >
        <SectionAddresses>
          <template #additionalAddresses>
            <AdditionalAddresses with-contacts />
          </template>
        </SectionAddresses>
      </FormSection>
      <FormSection
        ref="forwardingOrderFreight"
        v-model="valid.roadOrderFreight"
        class="nav-scroll-section"
      >
        <SectionFreight />
      </FormSection>
      <FormSection
        ref="forwardingOrderCollectionAndDelivery"
        v-model="valid.roadOrderCollectionAndDelivery"
        class="nav-scroll-section"
      >
        <SectionCollectionAndDelivery />
      </FormSection>
      <FormSection
        ref="forwardingOrderAccountingAndAdditionalServices"
        v-model="valid.roadOrderAccountingAndAdditionalServices"
        class="nav-scroll-section"
      >
        <SectionAccountingAndAdditionalServices />
      </FormSection>
      <FormSection
        ref="forwardingOrderReferences"
        v-model="valid.roadOrderReferences"
        class="nav-scroll-section"
      >
        <SectionOrderReferences />
      </FormSection>
      <FormSection
        v-if="isAtLeastOneOrderTextVisible"
        ref="forwardingOrderTexts"
        v-model="valid.roadOrderTexts"
        class="nav-scroll-section"
      >
        <SectionTexts />
      </FormSection>
      <FormSection
        ref="forwardingOrderDocuments"
        v-model="valid.roadOrderDocuments"
        class="nav-scroll-section"
      >
        <SectionDocuments />
      </FormSection>
    </div>
    <MainFooter @create-order-validate="validateForms" />
  </div>
</template>

<script setup lang="ts">
import SectionAccountingAndAdditionalServices from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/CardMain.vue';
import SectionAddresses from '@/components/createOrder/formSectionAddresses/CardMain.vue';
import AdditionalAddresses from '@/components/createOrder/formSectionAddresses/forwardingOrder/AdditionalAddresses.vue';
import SectionCollectionAndDelivery from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import SectionCustomers from '@/components/createOrder/formSectionCustomer/forwardingOrder/CardMain.vue';
import MainFooter from '@/components/createOrder/formSectionFooter/MainFooter.vue';
import SectionFreight from '@/components/createOrder/formSectionFreight/CardMain.vue';
import SectionOrderReferences from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import SectionTexts from '@/components/createOrder/formSectionTexts/CardMain.vue';
import SectionDocuments from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import FormSection from '@/components/form/FormSection.vue';
import MandatoryFieldsLabel from '@/components/MandatoryFieldsLabel.vue';
import { ValidationResult } from '@/composables/createOrder/useCreateOrderValidation';
import { useOrderTexts } from '@/composables/createOrder/useOrderTexts';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useValidationDataStore } from '@/store/validation';
import { identity } from 'lodash';
import { storeToRefs } from 'pinia';
import { ComponentPublicInstance, computed, ref, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const emit = defineEmits(['forwardingOrderNavigationItems']);
const forwardingOrderCustomers = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const forwardingOrderAddresses = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const forwardingOrderFreight = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const forwardingOrderCollectionAndDelivery = ref<ComponentPublicInstance<
  typeof FormSection
> | null>(null);
const forwardingOrderAccountingAndAdditionalServices = ref<ComponentPublicInstance<
  typeof FormSection
> | null>(null);
const forwardingOrderReferences = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const forwardingOrderTexts = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const forwardingOrderDocuments = ref<ComponentPublicInstance<typeof FormSection> | null>(null);

const createOrderDataStore = useCreateOrderDataStore();

const createOrderDocumentsStore = useCreateOrderDocumentsStore();
const { documentUploadFailed } = storeToRefs(createOrderDocumentsStore);
const { isCustomersAvailable } = storeToRefs(createOrderDataStore);
const { isAtLeastOneOrderTextVisible } = useOrderTexts();
const { hasEmbargo } = storeToRefs(useEmbargoStore());
const { formValidationSectionsRoad: valid, getFormSectionsValid } =
  storeToRefs(useValidationDataStore());

const forwardingOrderNavigationItems = computed(() =>
  [
    isCustomersAvailable.value
      ? {
          ref: forwardingOrderCustomers,
          text: t('labels.principal_title.text'),
          error: !valid.value.roadOrderCustomers,
        }
      : undefined,
    {
      ref: forwardingOrderAddresses,
      text: t('labels.addresses_title.text'),
      error: !valid.value.roadOrderAddresses || hasEmbargo.value,
    },
    {
      ref: forwardingOrderFreight,
      text: t('labels.order_line_title.text'),
      error: !valid.value.roadOrderFreight,
    },
    {
      ref: forwardingOrderCollectionAndDelivery,
      text: t('labels.collection_delivery_title.text'),
      error: !valid.value.roadOrderCollectionAndDelivery,
    },
    {
      ref: forwardingOrderAccountingAndAdditionalServices,
      text: t('labels.accountingAndAdditionalServices.text'),
      error: !valid.value.roadOrderAccountingAndAdditionalServices,
    },
    {
      ref: forwardingOrderReferences,
      text: t('labels.order_references.text'),
      error: !valid.value.roadOrderReferences,
    },
    isAtLeastOneOrderTextVisible.value
      ? {
          ref: forwardingOrderTexts,
          text: t('labels.order_texts_title.text'),
          error: !valid.value.roadOrderTexts,
        }
      : undefined,
    {
      ref: forwardingOrderDocuments,
      text: t('labels.documents_label.text'),
      error: !valid.value.roadOrderDocuments || documentUploadFailed.value,
    },
  ].filter(identity),
);

const validateForms = async (validate: (value: ValidationResult) => void) => {
  await Promise.all(
    forwardingOrderNavigationItems.value.map(async (item) => {
      return await item?.ref?.value?.validate();
    }),
  );

  validate(getFormSectionsValid.value);
};

watchEffect(() => {
  emit('forwardingOrderNavigationItems', forwardingOrderNavigationItems.value);
});
</script>
