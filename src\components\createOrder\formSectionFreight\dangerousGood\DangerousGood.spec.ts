/* eslint @typescript-eslint/no-explicit-any: 0 */
import { DangerousGoodType } from '@/enums';
import { mockServer } from '@/mocks/server';
import * as uuid from '@/utils/createUuid';
import type { TestUtils } from '@test/test-utils';
import { mount } from '@vue/test-utils';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { Server } from 'miragejs';
import { afterEach, expect } from 'vitest';
import DangerousGood from './DangerousGood.vue';
import {
  getEmptyEQDangerousGood,
  getEmptyLQDangerousGood,
  getEmptyUnNumberDangerousGood,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import { VBtn } from 'vuetify/components';
import EQDangerousGood from './EQDangerousGood.vue';
import LQDangerousGood from './LQDangerousGood.vue';
import UnNumberDangerousGood from './UnNumberDangerousGood.vue';
import { initPinia } from '@test/util/init-pinia';
import CounterField from '@/components/form/CounterField.vue';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import UnNumberDialog from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberDialog.vue';
import { unNumberSearchResults } from '@/mocks/fixtures/unNumberSearchResults';

const uuidSpy = vi.spyOn(uuid, 'createUuid');

const props = {
  dangerousGoods: [],
  computedLineCount: '1',
  localId: 4,
};

describe('DangerousGood component', () => {
  let wrapper: TestUtils.VueWrapper<typeof DangerousGood>;
  let server: Server;
  beforeAll(() => {
    mockResizeObserver();
    initPinia();
    server = mockServer({
      environment: 'test',
      fixtures: {},
    });
  });
  afterAll(() => {
    server.shutdown();
  });
  beforeEach(() => {
    wrapper = mount(DangerousGood, {
      props,
    });
  });

  afterEach(() => {
    uuidSpy.mockClear();
    wrapper.unmount();
  });

  it('renders the title correctly', () => {
    expect(wrapper.find('.text-h4').text()).toBe(
      'labels.dangerous_goods.text ( labels.order_line_header.text 1 )',
    );
  });

  it('add UnNumber shows dialog', async () => {
    const orderLineForm = useCreateOrderOrderLineFormStore();
    expect(wrapper.find('[data-test=book-add-un-number]').exists()).toBe(true);
    const addButton = wrapper.find('[data-test=book-add-un-number]').findComponent(VBtn);
    addButton.trigger('click');
    await wrapper.vm.$nextTick();

    const searchField = wrapper.findComponent('[data-test="book-dangerous-goods-search-field"]');

    expect(searchField.exists()).toBe(true);
    expect(orderLineForm.addDangerousGood).not.toHaveBeenCalled();
  });

  it('add UnNumber from dialog', async () => {
    const orderLineForm = useCreateOrderOrderLineFormStore();
    const unNumberDialog = wrapper.findComponent(UnNumberDialog);

    unNumberDialog.vm.$emit('select-item', unNumberSearchResults[0]);

    expect(orderLineForm.addDangerousGood).toHaveBeenCalledWith(
      DangerousGoodType.UnNumber,
      4,
      unNumberSearchResults[0],
    );
  });

  it('add EQ', () => {
    const orderLineForm = useCreateOrderOrderLineFormStore();
    expect(wrapper.find('[data-test=book-add-excepted-quantitites]').exists()).toBe(true);
    const addButton = wrapper.find('[data-test=book-add-excepted-quantitites]').findComponent(VBtn);
    addButton.trigger('click');

    expect(orderLineForm.addDangerousGood).toHaveBeenCalledWith(DangerousGoodType.EQ, 4, undefined);
  });

  it('add LQ', () => {
    const orderLineForm = useCreateOrderOrderLineFormStore();
    expect(wrapper.find('[data-test=book-add-limited-quantities]').exists()).toBe(true);
    const addButton = wrapper.find('[data-test=book-add-limited-quantities]').findComponent(VBtn);
    addButton.trigger('click');

    expect(orderLineForm.addDangerousGood).toHaveBeenCalledWith(DangerousGoodType.LQ, 4, undefined);
  });

  it('add display LQ , EQ and UnNumber', async () => {
    wrapper.setProps({
      dangerousGoods: [
        getEmptyEQDangerousGood(1),
        getEmptyLQDangerousGood(1),
        getEmptyUnNumberDangerousGood(1),
      ],
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(EQDangerousGood).exists()).toBe(true);
    expect(wrapper.findComponent(LQDangerousGood).exists()).toBe(true);
    expect(wrapper.findComponent(UnNumberDangerousGood).exists()).toBe(true);
  });

  it('delete LQ , EQ and UnNumber', async () => {
    const orderLineForm = useCreateOrderOrderLineFormStore();
    wrapper.setProps({
      dangerousGoods: [
        getEmptyEQDangerousGood(1),
        getEmptyLQDangerousGood(1),
        getEmptyUnNumberDangerousGood(1),
      ],
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(EQDangerousGood).exists()).toBe(true);
    expect(wrapper.findComponent(LQDangerousGood).exists()).toBe(true);
    expect(wrapper.findComponent(UnNumberDangerousGood).exists()).toBe(true);

    wrapper.findComponent(EQDangerousGood).findComponent(VBtn).trigger('click');
    wrapper.findComponent(LQDangerousGood).findComponent(VBtn).trigger('click');
    wrapper.findComponent(UnNumberDangerousGood).findComponent(VBtn).trigger('click');

    await wrapper.vm.$nextTick();

    expect(orderLineForm.deleteDangerousGood).toHaveBeenCalledTimes(3);
  });

  it('detects an error if a child returns false', async () => {
    wrapper.setProps({
      dangerousGoods: [
        getEmptyEQDangerousGood(1),
        getEmptyLQDangerousGood(1),
        getEmptyUnNumberDangerousGood(1),
      ],
    });

    await (wrapper.vm as any).setChildRef(EQDangerousGood, 1);
    await (wrapper.vm as any).validateAll();
    await wrapper.vm.$nextTick();
    expect((wrapper.vm as any).hasError).toBe(true);
  });

  it('update EQ value', async () => {
    const orderLineForm = useCreateOrderOrderLineFormStore();
    wrapper.setProps({
      dangerousGoods: [
        getEmptyEQDangerousGood(1),
        getEmptyLQDangerousGood(1),
        getEmptyUnNumberDangerousGood(1),
      ],
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(EQDangerousGood).exists()).toBe(true);
    wrapper.findComponent(EQDangerousGood).findComponent(CounterField).setValue('10');

    await wrapper.vm.$nextTick();
    expect(orderLineForm.updateDangerousGood).toHaveBeenCalledTimes(1);
  });

  it('delete DG', async () => {
    wrapper.setProps({
      dangerousGoods: [
        getEmptyEQDangerousGood(1),
        getEmptyLQDangerousGood(1),
        getEmptyUnNumberDangerousGood(1),
      ],
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(EQDangerousGood).exists()).toBe(true);
    expect(wrapper.findComponent(LQDangerousGood).exists()).toBe(true);
    expect(wrapper.findComponent(UnNumberDangerousGood).exists()).toBe(true);

    wrapper.findComponent(VBtn).trigger('click');
    await wrapper.vm.$nextTick();

    const confirmDialog = wrapper.findComponent(ConfirmPrompt);
    expect(confirmDialog.exists()).toBe(true);

    confirmDialog.findComponent('[data-test="book-confirm-btn"]').trigger('click');
    await wrapper.vm.$nextTick();

    const emitted = wrapper.emitted();
    expect(emitted['deleteDangerousGoods']).toHaveLength(1);
  });
});
