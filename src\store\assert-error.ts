import { logErrorMock } from '../../test/util/mock-client';

// throwableObject is a class property of an API instance and there doesn't seem to be a way to type it generically, so any is used
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const assertError = async <T extends Record<string, (...args: any[]) => any>>(
  calledMethod: () => unknown,
  throwableObject: T,
  throwableMethod: keyof T,
) => {
  logErrorMock.mockClear();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  vi.spyOn(throwableObject, throwableMethod as any).mockImplementationOnce(() => {
    throw new Error('error');
  });
  await calledMethod();
  expect(logErrorMock).toHaveBeenCalledTimes(1);
  logErrorMock.mockClear();
};
