import useAllowedDate from '@/composables/dateTimeUtilities/useAllowedDate';
import { initPinia } from '@test/util/init-pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { MaxOrderPeriods, OrderTypes } from '@/enums';
import { addDays, formatISO } from 'date-fns/fp';
import { toISODate } from '@/utils/converters/toISODate';

const today = new Date();
const tomorrow = addDays(1, today);

describe('useAllowedDate composable', () => {
  beforeEach(() => {
    initPinia();
  });

  it.each([
    OrderTypes.RoadForwardingOrder,
    OrderTypes.RoadCollectionOrder,
    OrderTypes.AirExportOrder,
    OrderTypes.AirImportOrder,
    OrderTypes.SeaExportOrder,
    OrderTypes.SeaImportOrder,
  ])('should get initial values for %s', (orderType) => {
    const createOrderFormStore = useCreateOrderFormStore();
    createOrderFormStore.orderType = orderType;

    const {
      minAllowedDateCollection,
      maxAllowedDateCollection,
      minAllowedDateDelivery,
      maxAllowedDateDelivery,
    } = useAllowedDate();

    const maxPeriod =
      orderType === OrderTypes.RoadCollectionOrder
        ? MaxOrderPeriods.Collecting
        : MaxOrderPeriods.Forwarding;

    const expectedResults = {
      minAllowedDateCollection: toISODate(today),
      maxAllowedDateCollection: toISODate(addDays(maxPeriod, today)),
      minAllowedDateDelivery: toISODate(tomorrow),
      maxAllowedDateDelivery: toISODate(addDays(maxPeriod, tomorrow)),
    };

    expect(minAllowedDateCollection.value).toEqual(expectedResults.minAllowedDateCollection);
    expect(maxAllowedDateCollection.value).toEqual(expectedResults.maxAllowedDateCollection);
    expect(minAllowedDateDelivery.value).toEqual(expectedResults.minAllowedDateDelivery);
    expect(maxAllowedDateDelivery.value).toEqual(expectedResults.maxAllowedDateDelivery);
  });

  it.each([
    OrderTypes.RoadForwardingOrder,
    OrderTypes.RoadCollectionOrder,
    OrderTypes.AirExportOrder,
    OrderTypes.AirImportOrder,
    OrderTypes.SeaExportOrder,
    OrderTypes.SeaImportOrder,
  ])('should get values for %s with custom date', async (orderType) => {
    const collectionDate = addDays(5, today);
    const collectionDateNextDay = addDays(1, collectionDate);

    const createOrderFormStore = useCreateOrderFormStore();
    createOrderFormStore.orderType = orderType;

    const { customCollectionTimeSlot } = useCreateOrderFormCollectionAndDeliveryStore();
    customCollectionTimeSlot.collectionDate = formatISO(collectionDate);

    const {
      minAllowedDateCollection,
      maxAllowedDateCollection,
      minAllowedDateDelivery,
      maxAllowedDateDelivery,
    } = useAllowedDate();

    const maxPeriod =
      orderType === OrderTypes.RoadCollectionOrder
        ? MaxOrderPeriods.Collecting
        : MaxOrderPeriods.Forwarding;

    const expectedResults = {
      minAllowedDateCollection: toISODate(today),
      maxAllowedDateCollection: toISODate(addDays(maxPeriod, today)),
      minAllowedDateDelivery: toISODate(collectionDateNextDay),
      maxAllowedDateDelivery: toISODate(addDays(maxPeriod, collectionDateNextDay)),
    };

    expect(minAllowedDateCollection.value).toEqual(expectedResults.minAllowedDateCollection);
    expect(maxAllowedDateCollection.value).toEqual(expectedResults.maxAllowedDateCollection);
    expect(minAllowedDateDelivery.value).toEqual(expectedResults.minAllowedDateDelivery);
    expect(maxAllowedDateDelivery.value).toEqual(expectedResults.maxAllowedDateDelivery);
  });
});
