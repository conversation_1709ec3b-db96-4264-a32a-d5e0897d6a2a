import { defineStore, storeToRefs } from 'pinia';
import useSaveOrder from '@/composables/createOrder/useSaveOrder';
import {
  OrderActionWithoutValidation,
  OrderSaveAction,
  PrintLabelStartPosition,
  type OrderProcessResult,
  type OrderLabelString,
} from '@dfe/dfe-book-api';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import { useCreateOrderFormStore } from '@/store/createOrder/form';

export interface LabelStoreState {
  isFetchLabelsLoading: boolean;
}

export const useLabelStore = defineStore('labelStore', {
  state: (): LabelStoreState => ({
    isFetchLabelsLoading: false,
  }),
  getters: {},
  actions: {
    async fetchLabels(): Promise<OrderProcessResult> {
      this.isFetchLabelsLoading = true;

      // since the validate method is called first (see MoreButton.vue), the orderId has to be set in the store so no duplicate order is created
      const { orderId, saveOrderData } = storeToRefs(useCreateOrderFormStore());
      if (saveOrderData.value?.orderId && !orderId.value) {
        orderId.value = saveOrderData.value.orderId;
      }

      try {
        const { data } = await this.api.book.v2.saveOrderV2(
          OrderSaveAction.PrintLabels,
          useSaveOrder(),
        );

        if (data.order) {
          saveOrderData.value = data.order;
        }

        if (data.validationResult) {
          const errorBanner = useErrorBanner();
          errorBanner.importFromValidationResult(data.validationResult);
        }
        return data;
      } finally {
        this.isFetchLabelsLoading = false;
      }
    },
    async fetchLabelsForExistingOrder(
      orderId: number,
      printStartPosition: PrintLabelStartPosition,
    ): Promise<OrderLabelString | undefined> {
      try {
        const { data } = await this.api.book.orders.processActionWithoutBody(
          orderId,
          OrderActionWithoutValidation.PrintLabels,
          {
            startPosition: printStartPosition,
          },
        );
        return data;
      } catch (error) {
        this.client.log.error('Failed to fetchLabels', 'dfe-book-frontend', error);
      }
    },
    async fetchLabelsForBulkOrder(
      orderIds: number[],
      printStartPosition: PrintLabelStartPosition,
    ): Promise<string | undefined> {
      try {
        const { data } = await this.api.book.orders.printLabelsForOrders(
          {
            orderIds,
            startPosition: printStartPosition,
          },
          // Set response format explicitly - since the API returns a text/plain response
          // and the expected response is json there is an internal API error - only visible if
          // the response object is debugged
          {
            format: 'text',
          },
        );
        return data;
      } catch (error) {
        this.client.log.error('Failed to fetchBulkLabels', 'dfe-book-frontend', error);
      }
    },
  },
});
