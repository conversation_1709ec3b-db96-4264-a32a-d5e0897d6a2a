import { MaxLength } from '@/enums';
import { mockServer } from '@/mocks/server';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import type { MultipleReferenceNumber } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { initPinia } from '../../../test/util/init-pinia';
import { i18n } from '@/plugins/i18n';
import { useValidationRules } from '@/composables/form/useValidationRules';

const referenceSamples: MultipleReferenceNumber[] = [
  {
    id: '1',
    value: '11',
    loading: false,
    unloading: false,
  },
  {
    id: '2',
    value: '22',
  },
  {
    id: '3',
    value: '33',
    loading: true,
    unloading: false,
  },
  {
    id: '4',
    value: '44',
    loading: true,
    unloading: true,
  },
];

describe('createOrderOrderReferencesFormStore store', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {},
    });
  });

  afterEach(() => {
    const store = useCreateOrderOrderReferencesFormStore();
    store.$reset();
  });

  it('get true for more than 299 purchase order numbers', () => {
    const store = useCreateOrderOrderReferencesFormStore();

    const purchaseOrderNumbersArray = new Array(300);
    purchaseOrderNumbersArray.fill({
      id: '0',
      value: '00',
    });

    store.purchaseOrderNumbers = purchaseOrderNumbersArray;

    expect(store.disablePurchaseOrderNumberButton).toBe(true);
  });

  it('get false for more less than 300 purchase order numbers', () => {
    const store = useCreateOrderOrderReferencesFormStore();

    const purchaseOrderNumbersArray = new Array(299);
    purchaseOrderNumbersArray.fill({
      id: '0',
      value: '00',
    });

    store.purchaseOrderNumbers = purchaseOrderNumbersArray;

    expect(store.disablePurchaseOrderNumberButton).toBe(false);
  });

  it('returns the stored array for a reference type', () => {
    const store = useCreateOrderOrderReferencesFormStore();

    store.invoiceNumbers = [referenceSamples[0]];
    store.purchaseOrderNumbers = [referenceSamples[1]];
    store.deliveryNoteNumbers = [referenceSamples[2]];
    store.otherNumbers = [referenceSamples[3]];

    expect(store.getStoredReferencesArray(OrderReferenceType.INVOICE_NUMBER)).toEqual(
      store.invoiceNumbers,
    );
    expect(store.getStoredReferencesArray(OrderReferenceType.PURCHASE_ORDER_NUMBER)).toEqual(
      store.purchaseOrderNumbers,
    );
    expect(store.getStoredReferencesArray(OrderReferenceType.DELIVERY_NOTE_NUMBER)).toEqual(
      store.deliveryNoteNumbers,
    );
    expect(store.getStoredReferencesArray(OrderReferenceType.OTHERS)).toEqual(store.otherNumbers);

    // Non-registered reference type
    expect(store.getStoredReferencesArray('unknown' as OrderReferenceType)).toEqual([]);
  });

  it('adds references', () => {
    const store = useCreateOrderOrderReferencesFormStore();

    store.addReference(OrderReferenceType.INVOICE_NUMBER);
    store.addReference(OrderReferenceType.INVOICE_NUMBER);

    expect(store.invoiceNumbers).toHaveLength(2);
    expect(store.furtherReferencesOrder).toEqual([OrderReferenceType.INVOICE_NUMBER]);

    store.addReference(OrderReferenceType.OTHERS);
    store.addReference(OrderReferenceType.OTHERS);
    store.addReference(OrderReferenceType.OTHERS);

    expect(store.otherNumbers).toHaveLength(3);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
    ]);

    store.addReference(OrderReferenceType.DELIVERY_NOTE_NUMBER);

    expect(store.deliveryNoteNumbers).toHaveLength(1);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
    ]);

    store.addReference(OrderReferenceType.PURCHASE_ORDER_NUMBER);
    store.addReference(OrderReferenceType.PURCHASE_ORDER_NUMBER);

    expect(store.purchaseOrderNumbers).toHaveLength(2);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
    ]);

    store.addReference(OrderReferenceType.MARKS_AND_NUMBERS);

    expect(store.markAndNumbers).toHaveLength(1);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.MARKS_AND_NUMBERS,
    ]);

    store.addReference(OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER);
    store.addReference(OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER);

    expect(store.consigneeReferenceNumbers).toHaveLength(2);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.MARKS_AND_NUMBERS,
      OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
    ]);

    store.addReference(OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER);
    store.addReference(OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER);
    store.addReference(OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER);

    expect(store.supplierShipmentNumbers).toHaveLength(3);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.MARKS_AND_NUMBERS,
      OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
    ]);

    store.addReference(OrderReferenceType.PROVIDER_SHIPMENT_NUMBER);

    expect(store.providerShipmentNumbers).toHaveLength(1);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.MARKS_AND_NUMBERS,
      OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
    ]);

    store.addReference(OrderReferenceType.PACKAGING_LIST_NUMBER);
    store.addReference(OrderReferenceType.PACKAGING_LIST_NUMBER);

    expect(store.packingListNumbers).toHaveLength(2);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.MARKS_AND_NUMBERS,
      OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
      OrderReferenceType.PACKAGING_LIST_NUMBER,
    ]);

    store.addReference(OrderReferenceType.COMMERCIAL_INVOICE_NUMBER);
    store.addReference(OrderReferenceType.COMMERCIAL_INVOICE_NUMBER);
    store.addReference(OrderReferenceType.COMMERCIAL_INVOICE_NUMBER);

    expect(store.commercialInvoiceNumbers).toHaveLength(3);
    expect(store.furtherReferencesOrder).toEqual([
      OrderReferenceType.INVOICE_NUMBER,
      OrderReferenceType.OTHERS,
      OrderReferenceType.DELIVERY_NOTE_NUMBER,
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
      OrderReferenceType.MARKS_AND_NUMBERS,
      OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
      OrderReferenceType.PACKAGING_LIST_NUMBER,
      OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
    ]);

    store.addReference(OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT);
    expect(store.identificationCodeTransport).toHaveLength(1);

    store.addReference(OrderReferenceType.BOOKING_REFERENCE);
    expect(store.bookingReference).toHaveLength(1);
  });

  it('returns last reference id of a stored references array', () => {
    const store = useCreateOrderOrderReferencesFormStore();

    // form empty array
    expect(store.getLastReferenceId(OrderReferenceType.INVOICE_NUMBER)).toBe(null);

    // with filled array
    store.invoiceNumbers = [...referenceSamples];

    expect(store.getLastReferenceId(OrderReferenceType.INVOICE_NUMBER)).toBe(
      referenceSamples[referenceSamples.length - 1].id,
    );
  });

  it('deletes reference from a stored references array', () => {
    const store = useCreateOrderOrderReferencesFormStore();

    store.invoiceNumbers = [...referenceSamples];
    store.furtherReferencesOrder = [OrderReferenceType.INVOICE_NUMBER];

    expect(store.invoiceNumbers).toHaveLength(4);
    expect(store.furtherReferencesOrder).toEqual([OrderReferenceType.INVOICE_NUMBER]);

    store.deleteReferenceNumber(OrderReferenceType.INVOICE_NUMBER, referenceSamples[1].id);

    expect(store.invoiceNumbers).toHaveLength(3);
    expect(store.furtherReferencesOrder).toEqual([OrderReferenceType.INVOICE_NUMBER]);

    store.deleteReferenceNumber(OrderReferenceType.INVOICE_NUMBER, referenceSamples[0].id);
    store.deleteReferenceNumber(OrderReferenceType.INVOICE_NUMBER, referenceSamples[2].id);
    store.deleteReferenceNumber(OrderReferenceType.INVOICE_NUMBER, referenceSamples[3].id);

    expect(store.invoiceNumbers).toHaveLength(0);
    expect(store.furtherReferencesOrder).toEqual([]);
  });

  describe('getter: moreReferencesOptions', () => {
    it('returns an array of object of correct structure', () => {
      const store = useCreateOrderOrderReferencesFormStore();

      store.furtherReferencesOrder = [];

      expect(Array.isArray(store.moreReferencesOptions)).toBe(true);
      expect(store.moreReferencesOptions.length).toBeGreaterThan(0);

      store.moreReferencesOptions.forEach((element) => {
        expect(element).toHaveProperty('value');
        expect(element).toHaveProperty('text');
      });
    });

    it('returns a filtered array of options for moreReferences (not already contained in furtherReferencesOrder)', () => {
      const store = useCreateOrderOrderReferencesFormStore();

      store.furtherReferencesOrder = [
        OrderReferenceType.MARKS_AND_NUMBERS,
        OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
      ];

      expect(Array.isArray(store.moreReferencesOptions)).toBe(true);
      expect(store.moreReferencesOptions.length).toBe(4);
    });
  });

  describe('getter: optionalReferencesObjects', () => {
    const { t } = i18n.global;
    // Outputs
    const purchaseOrderNumbersObjectFilledArray = {
      name: OrderReferenceType.PURCHASE_ORDER_NUMBER,
      items: [{ id: '1', value: '11' }],
      label: t('labels.purchase_order_number.text'),
      maxLength: MaxLength.Default,
    };

    const deliveryNoteNumbersObjectEmptyArray = {
      name: OrderReferenceType.DELIVERY_NOTE_NUMBER,
      items: [],
      label: t('labels.delivery_note_number.text'),
      maxLength: MaxLength.Default,
    };

    const invoiceNumbersObjectFilledArray = {
      name: OrderReferenceType.INVOICE_NUMBER,
      items: [{ id: '2', value: '22' }],
      label: t('labels.invoice_number_reference.text'),
      maxLength: MaxLength.Default,
    };

    const otherNumbersObjectEmptyArray = {
      name: OrderReferenceType.OTHERS,
      items: [],
      label: t('labels.other_number.text'),
      maxLength: MaxLength.Default,
    };

    const markAndNumbersObjectFilledArray = {
      name: OrderReferenceType.MARKS_AND_NUMBERS,
      items: [{ id: '3', value: '33' }],
      label: t('labels.marks_label.text'),
      maxLength: MaxLength.Default,
    };

    const consigneeReferenceNumbersObjectEmptyArray = {
      name: OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
      items: [],
      label: t('labels.consignee_reference_number.text'),
      maxLength: MaxLength.Default,
    };

    const supplierShipmentNumbersObjectFilledArray = {
      name: OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
      items: [{ id: '4', value: '44' }],
      label: t('labels.supplier_shipment_number.text'),
      maxLength: MaxLength.Default,
    };

    const providerShipmentNumbersObjectEmptyArray = {
      name: OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
      items: [],
      label: t('labels.provider_shipment_number.text'),
      maxLength: MaxLength.Default,
    };

    const packingListNumbersObjectFilledArray = {
      name: OrderReferenceType.PACKAGING_LIST_NUMBER,
      items: [{ id: '5', value: '55' }],
      label: t('labels.packing_list_number.text'),
      maxLength: MaxLength.Default,
    };

    const commercialInvoiceNumbersObjectEmptyArray = {
      name: OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
      items: [],
      label: t('labels.commercial_invoice_number.text'),
      maxLength: MaxLength.Default,
    };

    const identificationCodeTransportObjectFilledArray = {
      name: OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT,
      items: [{ id: '6', value: '42' }],
      label: t('labels.identification_code_transport.text'),
      maxLength: MaxLength.IdentificationCodeTransport,
      prefix: 'UIT',
      rules: [
        useValidationRules.regex(
          new RegExp('^[a-zA-Z0-9]{16}$'),
          'labels.invalid_identification_code_transport.text',
          '1234AB7890CD3456',
        ),
      ],
    };

    const bookingReferenceObjectFilledArray = {
      name: OrderReferenceType.BOOKING_REFERENCE,
      items: [{ id: '7', value: '666' }],
      label: t('labels.booking_reference_label.text'),
      maxLength: MaxLength.Default,
      rules: [
        useValidationRules.regex(
          new RegExp('^[a-zA-Z\\d\\s:]*$'),
          'labels.invalid_booking_reference_label.text',
          '',
        ),
      ],
    };

    it('returns references objects in correct order', () => {
      const store = useCreateOrderOrderReferencesFormStore();

      // Inputs
      store.$patch({
        furtherReferencesOrder: [
          OrderReferenceType.INVOICE_NUMBER,
          OrderReferenceType.OTHERS,
          OrderReferenceType.DELIVERY_NOTE_NUMBER,
          OrderReferenceType.PURCHASE_ORDER_NUMBER,
          OrderReferenceType.MARKS_AND_NUMBERS,
          OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
          OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
          OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
          OrderReferenceType.PACKAGING_LIST_NUMBER,
          OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
        ],
        purchaseOrderNumbers: [{ id: '1', value: '11' }],
        deliveryNoteNumbers: [],
        invoiceNumbers: [{ id: '2', value: '22' }],
        otherNumbers: [],
        markAndNumbers: [{ id: '3', value: '33' }],
        consigneeReferenceNumbers: [],
        supplierShipmentNumbers: [{ id: '4', value: '44' }],
        providerShipmentNumbers: [],
        packingListNumbers: [{ id: '5', value: '55' }],
        commercialInvoiceNumbers: [],
      });

      expect(store.optionalReferencesObjects).toEqual([
        invoiceNumbersObjectFilledArray,
        otherNumbersObjectEmptyArray,
        deliveryNoteNumbersObjectEmptyArray,
        purchaseOrderNumbersObjectFilledArray,
        markAndNumbersObjectFilledArray,
        consigneeReferenceNumbersObjectEmptyArray,
        supplierShipmentNumbersObjectFilledArray,
        providerShipmentNumbersObjectEmptyArray,
        packingListNumbersObjectFilledArray,
        commercialInvoiceNumbersObjectEmptyArray,
      ]);

      store.$reset();
    });

    it('returns identification code transport', () => {
      const store = useCreateOrderOrderReferencesFormStore();

      // Inputs
      store.$patch({
        furtherReferencesOrder: [OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT],
        identificationCodeTransport: [{ id: '6', value: '42' }],
      });

      expect(store.optionalReferencesObjects.length).toEqual(1);
      expect(store.optionalReferencesObjects.at(0)?.name).toEqual(
        identificationCodeTransportObjectFilledArray.name,
      );
      expect(store.optionalReferencesObjects.at(0)?.items).toEqual(
        identificationCodeTransportObjectFilledArray.items,
      );
      expect(store.optionalReferencesObjects.at(0)?.label).toEqual(
        identificationCodeTransportObjectFilledArray.label,
      );
      expect(store.optionalReferencesObjects.at(0)?.maxLength).toEqual(
        identificationCodeTransportObjectFilledArray.maxLength,
      );
      expect(store.optionalReferencesObjects.at(0)?.prefix).toEqual(
        identificationCodeTransportObjectFilledArray.prefix,
      );
      expect(store.optionalReferencesObjects.at(0)?.rules?.length).toEqual(2);
      expect(typeof store.optionalReferencesObjects.at(0)?.rules?.at(0)).toBe('function');
      expect(typeof store.optionalReferencesObjects.at(0)?.rules?.at(1)).toBe('function');

      store.$reset();
    });

    it('returns booking reference', () => {
      const store = useCreateOrderOrderReferencesFormStore();

      // Inputs
      store.$patch({
        furtherReferencesOrder: [OrderReferenceType.BOOKING_REFERENCE],
        bookingReference: [{ id: '7', value: '666' }],
      });

      expect(store.optionalReferencesObjects.length).toEqual(1);
      expect(store.optionalReferencesObjects.at(0)?.name).toEqual(
        bookingReferenceObjectFilledArray.name,
      );
      expect(store.optionalReferencesObjects.at(0)?.items).toEqual(
        bookingReferenceObjectFilledArray.items,
      );
      expect(store.optionalReferencesObjects.at(0)?.label).toEqual(
        bookingReferenceObjectFilledArray.label,
      );
      expect(store.optionalReferencesObjects.at(0)?.maxLength).toEqual(
        bookingReferenceObjectFilledArray.maxLength,
      );
      expect(store.optionalReferencesObjects.at(0)?.rules?.length).toEqual(1);
      expect(typeof store.optionalReferencesObjects.at(0)?.rules?.at(0)).toBe('function');

      store.$reset();
    });

    it('filters out undefined values', () => {
      const store = useCreateOrderOrderReferencesFormStore();

      store.$patch({
        furtherReferencesOrder: [
          OrderReferenceType.PURCHASE_ORDER_NUMBER,
          'invalid_reference' as OrderReferenceType,
        ],
        purchaseOrderNumbers: [{ id: '1', value: '11' }],
      });

      expect(store.optionalReferencesObjects).toEqual([purchaseOrderNumbersObjectFilledArray]);
    });
  });
});
