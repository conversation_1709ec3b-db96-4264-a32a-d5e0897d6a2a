<template>
  <DfeDetailsLayer
    v-model="showDetails"
    :headline="t('labels.un_number.text') + ': ' + item?.unNumber"
    :attach="attach ?? $appRoot"
  >
    <template #activator="{ props }">
      <DfeChip
        v-bind="props"
        class="mr-1"
        :label="item?.unNumber ?? ''"
        :type="ColorVariants.NEUTRAL"
        @click="showDetails = !showDetails"
      />
    </template>

    <template #default>
      <div class="text-body-2">
        <p v-if="item?.description" class="font-weight-bold mb-6">{{ item?.description }}</p>

        <p class="mb-2">
          {{ t('labels.main_danger.text') }}: {{ displayValueWithFallback(item?.mainDanger) }}
        </p>
        <p class="mb-2">
          {{ t('labels.sub_hazard_one_full.text') }}:
          {{ displayValueWithFallback(item?.subsidiaryHazardOne) }}
        </p>
        <p class="mb-2">
          {{ t('labels.sub_hazard_two_full.text') }}:
          {{ displayValueWithFallback(item?.subsidiaryHazardTwo) }}
        </p>
        <p class="mb-2">
          {{ t('labels.sub_hazard_three_full.text') }}:
          {{ displayValueWithFallback(item?.subsidiaryHazardThree) }}
        </p>
        <p class="mb-2">
          {{ t('labels.packaging_group.text') }}:
          {{ displayValueWithFallback(item?.packingGroup) }}
        </p>
        <p class="mb-2">
          {{ t('labels.classification_code.text') }}:
          {{ displayValueWithFallback(item?.classificationCode) }}
        </p>
        <p class="mb-2">
          {{ t('labels.transport_category.text') }}:
          {{ displayValueWithFallback(item?.transportCategory) }}
        </p>
        <p class="mb-2">
          {{ t('labels.tunnel_code.text') }}:
          {{ displayValueWithFallback(item?.tunnelCode) }}
        </p>
      </div>
    </template>
  </DfeDetailsLayer>
</template>

<script setup lang="ts">
import { ColorVariants, DfeChip, DfeDetailsLayer } from '@dfe/dfe-frontend-shared-components';
import { DangerousGoodDataItem } from '@dfe/dfe-book-api';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { displayValueWithFallback } from '@/utils/form/valueWithFallback';

interface Props {
  item?: DangerousGoodDataItem;
  attach?: HTMLElement;
}

const { item = undefined, attach = undefined } = defineProps<Props>();

const { t } = useI18n();

const showDetails = ref(false);
</script>
