<template>
  <div>
    <div class="grid-container-form | align-start">
      <TextField
        v-model="shipperReference.referenceValue"
        v-data-test="'shipper-reference'"
        :label="t('labels.shippers_reference.text')"
        :required="true"
        :max-length="MaxLength.Default"
        :rules="[useValidationRules.required]"
      />
      <div>
        <FormLabel for="loading-unloading" :required="true">{{
          t('labels.applicable_for.text')
        }}</FormLabel>
        <div class="container__applicable d-flex align-center">
          <CheckboxField
            v-model="shipperReference.loading"
            v-data-test="'air-applicable-for-loading'"
            :data-test-details="'bo-air-applicable-for-loading-' + shipperReference.loading"
            :label="t('labels.loading_label.text')"
            :error="checkIfAtLeastOneSelected"
            class="mr-3"
          />
          <CheckboxField
            v-model="shipperReference.unloading"
            v-data-test="'air-applicable-for-unloading'"
            :data-test-details="'bo-air-applicable-for-unloading-' + shipperReference.unloading"
            :label="t('labels.unloading_label.text')"
            :error="checkIfAtLeastOneSelected"
          />
        </div>
        <span v-if="checkIfAtLeastOneSelected" class="text-body-2 error-loading-unloading">
          {{ $t('labels.validation_at_least_one_selected.text') }}
        </span>
      </div>
    </div>
    <div class="grid-container-form | align-center mt-6">
      <TextField
        v-model="quotationReference.referenceValue"
        :label="t('labels.quotation_reference.text')"
        :max-length="MaxLength.Default"
        :disabled="isAirOrderFromQuote"
      />
    </div>

    <VDivider class="mt-6" />

    <h5 class="text-h5 mt-6">{{ t('labels.further_references.text') }}</h5>

    <MultipleTextFieldsWithCheckboxes
      v-for="(item, index) in optionalReferencesObjects"
      :key="index"
      :reference-type="item.name"
      :items="item.items"
      :label="item.label"
      :max-length="item.maxLength"
    />

    <div class="d-flex flex-wrap align-center mt-2">
      <AddButton
        v-if="!furtherReferencesOrder.includes(OrderReferenceType.INVOICE_NUMBER)"
        v-data-test="'additional-reference-INVOICE-NUMBER'"
        :label="t('labels.invoice_number_reference.text')"
        variant="text"
        class="add-button | mr-1 mt-2"
        @add-new-item="
          useAddReference(OrderReferenceType.INVOICE_NUMBER, {
            withLoadingData: true,
          })
        "
      />
      <AddButton
        v-if="!furtherReferencesOrder.includes(OrderReferenceType.PURCHASE_ORDER_NUMBER)"
        v-data-test="'additional-reference-PURCHASE-ORDER-NUMBER'"
        :label="t('labels.purchase_order_number.text')"
        variant="text"
        class="add-button | mr-1 mt-2"
        @add-new-item="
          useAddReference(OrderReferenceType.PURCHASE_ORDER_NUMBER, {
            withLoadingData: true,
          })
        "
      />
      <AddButton
        v-if="!furtherReferencesOrder.includes(OrderReferenceType.DELIVERY_NOTE_NUMBER)"
        v-data-test="'additional-reference-DELIVERY-NOTE-NUMBER'"
        :label="t('labels.delivery_note_number.text')"
        variant="text"
        class="add-button | mr-1 mt-2"
        @add-new-item="
          useAddReference(OrderReferenceType.DELIVERY_NOTE_NUMBER, {
            withLoadingData: true,
          })
        "
      />
      <AddButton
        v-if="!furtherReferencesOrder.includes(OrderReferenceType.OTHERS)"
        v-data-test="'additional-reference-OTHERS'"
        :label="t('labels.other_number.text')"
        variant="text"
        class="add-button | mt-2"
        @add-new-item="useAddReference(OrderReferenceType.OTHERS, { withLoadingData: true })"
      />
    </div>
    <div class="mt-4">
      <ButtonDropdownMenu
        v-if="moreReferencesOptions.length"
        :items="
          moreReferencesOptions.sort((a, b) => a.text.toString().localeCompare(b.text.toString()))
        "
        @input="selectOption"
        >{{ t('labels.more_references.text') }}
      </ButtonDropdownMenu>
    </div>
  </div>
</template>

<script setup lang="ts">
import ButtonDropdownMenu from '@/components/base/ButtonDropdownMenu.vue';
import AddButton from '@/components/createOrder/AddButton.vue';
import MultipleTextFieldsWithCheckboxes from '@/components/createOrder/formSectionOrderReferences/MultipleTextFieldsWithCheckboxes.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import FormLabel from '@/components/form/FormLabel.vue';
import TextField from '@/components/form/TextField.vue';
import useAddReference from '@/composables/createOrder/useAddReference';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { MaxLength } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
const createOrderFormStore = useCreateOrderFormStore();
const {
  shipperReference,
  quotationReference,
  furtherReferencesOrder,
  moreReferencesOptions,
  optionalReferencesObjects,
} = storeToRefs(createOrderOrderReferencesFormStore);
const { isAirOrderFromQuote } = storeToRefs(createOrderFormStore);

const selectOption = (option: OrderReferenceType) => {
  useAddReference(option, { withLoadingData: true });
};

const checkIfAtLeastOneSelected = computed(() => {
  return !shipperReference.value.loading && !shipperReference.value.unloading;
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables';
@use '@/styles/settings';
@use 'sass:map';
.container__applicable {
  height: 34px; // input height - border width to center align with the input field left of it
}

.error-loading-unloading {
  margin-top: -15px;
  color: var(--color-base-red-500);
}

:deep(.grid-container-form) {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: auto;
  grid-gap: 0.75em;

  @media #{map.get(settings.$display-breakpoints, 'lg')} {
    grid-template-columns: repeat(3, 1fr);
  }
  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
