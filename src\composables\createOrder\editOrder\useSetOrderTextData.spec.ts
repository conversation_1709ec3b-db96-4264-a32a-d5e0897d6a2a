import type { OrderResponseBody, OrderText } from '@dfe/dfe-book-api';
import { CreateOrderText, useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import {
  getUpdatedTexts,
  useSetOrderTextData,
} from '@/composables/createOrder/editOrder/useSetOrderTextData';
import { storeToRefs } from 'pinia';

describe('useSetOrderTextData composable', () => {
  describe('getUpdatedTexts', () => {
    it('should return updated texts', () => {
      const texts: OrderText[] = [
        { textType: 'type1', value: 'value1' },
        { textType: 'type2', value: '' },
        { textType: 'type3', value: 'value3' },
      ];

      const expectedUpdatedTexts: CreateOrderText[] = [
        { id: undefined, textType: 'type1', value: 'value1', active: true },
        { id: undefined, textType: 'type2', value: '', active: false },
        { id: undefined, textType: 'type3', value: 'value3', active: true },
      ];

      expect(getUpdatedTexts(texts)).toEqual(expectedUpdatedTexts);
    });
  });

  describe('useSetOrderTextData', () => {
    it('should update the store when editOrderData contains texts', async () => {
      const orderTexts: OrderText[] = [
        { textType: 'type1', value: 'value1' },
        { textType: 'type2', value: '' },
        { textType: 'type1', value: 'newValue1' },
      ];

      const editOrderData: OrderResponseBody = {
        orderType: 'RoadForwardingOrder',
        texts: orderTexts,
      };

      const store = useCreateOrderTextsStore();
      const { texts } = storeToRefs(store);
      useSetOrderTextData(editOrderData);

      const expectedTexts: CreateOrderText[] = [
        { id: undefined, textType: 'type1', value: 'newValue1', active: true },
        { id: undefined, textType: 'type2', value: '', active: false },
      ];
      const filteredTexts = texts.value.filter((t) =>
        expectedTexts.some(
          (e) => e.textType === t.textType && e.value === t.value && e.active === t.active,
        ),
      );
      expect(filteredTexts).toEqual(expectedTexts);
    });

    it('should not update the store when editOrderData does not contain texts', () => {
      const editOrderData: OrderResponseBody = {} as OrderResponseBody;

      const store = useCreateOrderTextsStore();
      store.texts = [{ id: undefined, textType: 'type1', value: 'initial', active: true }];

      useSetOrderTextData(editOrderData);

      expect(store.texts).toEqual([
        { id: undefined, textType: 'type1', value: 'initial', active: true },
      ]);
    });
  });
});
