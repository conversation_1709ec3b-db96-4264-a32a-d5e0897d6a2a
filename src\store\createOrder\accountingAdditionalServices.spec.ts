import { freightTerms } from '@/mocks/fixtures/freightTerms';
import { mockServer } from '@/mocks/server';
import { i18n } from '@/plugins/i18n';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { initPinia } from '@test/util/init-pinia';

describe('createOrderFormAccountingAdditionalServices store', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {
        freightTerms,
      },
    });
  });

  it('returns computed inco term', async () => {
    const store = useCreateOrderFormAccountingAdditionalServices();
    const dataStore = useCreateOrderDataStore();
    await dataStore.fetchFreightTerms();

    store.selectedFreightTerm = null;
    expect(store.incoTermText).toBe(i18n.global.t('messages.id6349.text'));

    store.selectedFreightTerm = freightTerms[0] ?? '';
    expect(store.incoTermText).toBe(freightTerms[0].incoTermKey);

    store.selectedFreightTerm = freightTerms[1] ?? '';
    expect(store.incoTermText).toBe(i18n.global.t('iCode_2'));
  });
});
