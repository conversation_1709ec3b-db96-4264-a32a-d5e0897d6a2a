import 'pinia';
import type { ApiSecurityDataFn } from './services/api';
import type { Api as BookApi } from '@dfe/dfe-book-api';
import type { Api as AddressApi } from '@dfe/dfe-address-api';
import type { Client } from './types/client';

declare module 'pinia' {
  export interface PiniaCustomProperties {
    api: {
      book: BookApi<ApiSecurityDataFn>;
      address: AddressApi<ApiSecurityDataFn>;
      config: ConfigApi<ApiSecurityDataFn>;
    };
    client: Client;
    createCancelToken: () => symbol | string | number;
  }
}
