<template>
  <VDialog
    v-model="modelValue"
    :attach="attach || $appRoot"
    :persistent="persistent"
    :max-width="maxWidth"
    :class="sizeClass"
    transition="scroll-y-reverse-transition"
    scrollable
  >
    <template #activator="{ props: dialogProps }">
      <div v-if="$slots.trigger" class="modal-trigger">
        <slot name="trigger" v-bind="dialogProps"></slot>
      </div>
    </template>
    <VCard v-data-test="dataTestContent" class="modal" :class="{ 'has-border': hasBorder }">
      <VCardTitle class="modal-header text-h3">
        <slot name="header">
          <h3 v-if="headline" class="text-h3 text-pre-wrap">
            {{ headline }}
          </h3>
        </slot>
        <DfeIconButton
          v-if="!hideCloseButton"
          :tooltip="''"
          class="modal-close-button"
          @click="close"
        >
          <CloseIcon />
        </DfeIconButton>
      </VCardTitle>
      <VCardText class="modal-body text-body-2" :class="{ 'no-padding': noBodyPadding }">
        <slot name="body"></slot>
      </VCardText>
      <VCardActions v-if="$slots.footer" class="modal-footer">
        <slot name="footer"></slot>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<script setup lang="ts">
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import CloseIcon from '@dfe/dfe-frontend-styles/assets/icons/close-24px.svg';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

type Size = 'sm' | 'md' | 'lg' | 'xl';

const MAX_SIZE: Record<Size, number> = {
  sm: 320,
  md: 480,
  lg: 960,
  xl: 1200,
};

type Props = {
  attach?: string | boolean | Element;
  persistent?: boolean;
  headline: string;
  size?: Size;
  id?: string;
  hideCloseButton?: boolean;
  dataTestContent?: string;
  hasBorder?: boolean;
  noBodyPadding?: boolean;
};
const props = withDefaults(defineProps<Props>(), {
  attach: undefined,
  size: 'sm',
  id: undefined,
  persistent: true,
  dataTestContent: undefined,
  hasBorder: true,
  noBodyPadding: false,
});

const modelValue = defineModel<boolean>();

const emit = defineEmits(['close']);

const maxWidth = computed(() => MAX_SIZE[props.size]);
const sizeClass = computed(() => `modal-${props.size}`);

const createOrderAddressesStore = useCreateOrderAddressesStore();
const { hasUnsavedAddressChanges } = storeToRefs(createOrderAddressesStore);

const close = () => {
  if (hasUnsavedAddressChanges.value) {
    emit('close');
  } else {
    modelValue.value = false;
    emit('close');
  }
};
</script>
<style scoped lang="scss">
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings.scss';
@use '@/styles/base' as base;

.v-overlay {
  :deep(.v-overlay__scrim) {
    opacity: 0.75;
  }

  :deep(.v-overlay__content) .modal {
    border-radius: base.space(2);
  }

  .modal {
    .modal-header {
      display: flex;
      align-items: center;
      padding: base.space(6) base.space(6) base.space(4) base.space(6);
      flex-wrap: nowrap;
      word-break: keep-all;
    }

    .modal-body.v-card-text {
      padding: base.space(6);
      line-height: vars.$line-height-body-2;

      &.no-padding {
        padding: 0;
      }
    }

    .modal-footer {
      padding: base.space(4) base.space(6) base.space(6);
    }
  }

  .has-border {
    .modal-header {
      border-bottom: 1px solid settings.$divider-border-color;
    }
    .modal-footer {
      border-top: 1px solid settings.$divider-border-color;
    }
  }

  &.modal-sm .modal {
    .modal-header {
      padding-bottom: 0;
      border-bottom: unset;
    }

    .modal-footer {
      padding-top: 0;
      border-top: unset;
    }
  }

  &.modal-xl {
    & > :deep(.v-overlay__content) {
      height: 100%;
    }
  }
}

.v-card-actions :deep(.v-btn) {
  padding: 0 base.space(3);
}

.v-dialog {
  .v-overlay__content {
    .v-card {
      .v-card-actions {
        justify-content: flex-start;
      }
    }
  }
}

.modal-close-button {
  margin-left: auto;
}

@media #{map.get(settings.$display-breakpoints, 'xs')} {
  .modal-md,
  .modal-lg {
    :deep(.v-overlay__content) {
      padding-top: base.space(6);
      width: 100%;
      min-height: 100%;

      .modal {
        border-radius: base.space(2) base.space(2) 0 0;
      }
    }
  }
}
</style>
