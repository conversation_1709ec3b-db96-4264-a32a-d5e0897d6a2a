import useLocalizedNumber, {
  Options,
  CurrencyOptions,
  UnitOptions,
} from '@/composables/useLocalizedNumber';

describe('useLocalizedNumber composable', () => {
  it.each([
    { value: 1.2, locale: 'en', expected: '1.2' },
    { value: 1.2, locale: 'de', expected: '1,2' },
    {
      value: 1.23,
      options: { maximumFractionDigits: 1 },
      locale: 'en',
      expected: '1.2',
    },
    {
      value: 1.23,
      options: { minimumFractionDigits: 3 },
      locale: 'en',
      expected: '1.230',
    },
    {
      value: 1.2,
      options: { style: <const>'unit', unit: 'kilogram' },
      locale: 'en',
      expected: '1.2 kg',
    },
    {
      value: 1.23,
      options: {
        style: <const>'unit',
        unit: 'kilogram',
        unitDisplay: <const>'long',
        maximumFractionDigits: 1,
      },
      locale: 'de',
      expected: '1,2 Kilogramm',
    },
    {
      value: 1.2,
      options: { style: <const>'currency', currency: 'EUR' },
      locale: 'en',
      expected: 'EUR\xa01.20', // non-breaking space \xa0
    },
    {
      value: 1.2,
      options: {
        style: <const>'currency',
        currency: 'EUR',
        currencyDisplay: 'symbol' as Intl.NumberFormatOptions['currencyDisplay'],
      },
      locale: 'de',
      expected: '1,20\xa0€', // non-breaking space \xa0
    },
  ])(
    'returns localized numbers',
    ({
      value,
      options,
      locale,
      expected,
    }: {
      value: number;
      options?: Options | UnitOptions | CurrencyOptions;
      locale: string;
      expected: string;
    }) => {
      expect(useLocalizedNumber(value, options, locale).value).toEqual(expected);
    },
  );
});
