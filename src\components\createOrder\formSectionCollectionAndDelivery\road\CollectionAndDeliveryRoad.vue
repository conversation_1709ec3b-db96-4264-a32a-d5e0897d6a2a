<template>
  <SectionCard v-data-test="'section-collection-and-delivery'">
    <template #headline>
      {{ t('labels.collection_delivery_title.text') }}
    </template>
    <div class="d-flex flex-wrap">
      <div class="collection-col mb-4">
        <h4 class="text-h4 mb-4 d-inline-flex align-center">
          {{ t('labels.collection_title.text') }}
          <InfoButtonWithTooltip
            class="ml-1"
            :label="t('labels.local_date_time_of_collection_point.text')"
            location="right"
            :max-width="260"
          />
        </h4>
        <FormCollection />
      </div>
      <div class="delivery-col">
        <h4 class="text-h4 mb-4">
          {{ t('labels.transit_time_and_delivery.text') }}
        </h4>
        <FormDelivery />
      </div>
    </div>
  </SectionCard>
</template>

<script setup lang="ts">
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import SectionCard from '@/components/base/SectionCard.vue';
import FormCollection from '@/components/createOrder/formSectionCollectionAndDelivery/FormCollection.vue';
import FormDelivery from '@/components/createOrder/formSectionCollectionAndDelivery/FormDelivery.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
</script>
<style lang="scss" scoped>
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';
@use '@/styles/variables' as projectVars;

.collection-col,
.delivery-col {
  width: 100%;
  max-width: projectVars.$form-cols-width-xl;
}
.collection-col {
  margin-right: 32px !important;
}

@media #{map.get(settings.$display-breakpoints, 'md-and-up')} {
  .collection-col,
  .delivery-col {
    width: 100%;
    max-width: projectVars.$form-cols-width-xl;
  }
  .collection-col {
    margin-right: 32px !important;
  }
}
@media #{map.get(settings.$display-breakpoints, 'lg-and-up')} {
  .collection-col,
  .delivery-col {
    width: 48%;
  }
  .collection-col {
    margin-right: 96px !important;
  }
}
</style>
