<template>
  <div>
    <VSwitch
      :model-value="value"
      inset
      :disabled="disabled"
      hide-details="auto"
      :class="['mt-0 pt-0', { 'v-input--reverse': labelLeft }]"
      @update:model-value="onSwitchChange"
    >
      <template #label>
        <span :class="['text-body-2 grey--text text--darken-4', labelLeft ? 'mr-4' : 'ml-2']"
          ><template v-if="label">{{ label }}</template
          ><slot v-else
        /></span>
      </template>
    </VSwitch>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import type { TranslateResult } from 'vue-i18n';

defineProps({
  value: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
  label: { type: [String, Object] as PropType<TranslateResult>, default: '' },
  labelLeft: { type: Boolean, default: false },
});
const emit = defineEmits(['update:modelValue']);

const onSwitchChange = (value: boolean | null) => {
  emit('update:modelValue', value);
};
</script>

<style lang="scss" scoped>
.v-input--reverse :deep(.v-selection-control) {
  flex-direction: row-reverse;
  justify-content: flex-end;
}

:deep(.v-selection-control__input) {
  margin-right: 0 !important;
}

:deep(.v-switch .v-input__control .v-selection-control) {
  min-height: 0;

  .v-switch__track {
    min-width: auto;
  }
}

:deep(.v-switch) .v-label {
  padding-inline-start: 0;
}
</style>
