@use '@/styles/variables';
@use '@/styles/base' as base;

[dfe-book-frontend] {
:deep {
  .v-radio-group {
    &--column {
      .v-radio:not(:last-child):not(:only-child) {
        margin-bottom: base.space(3);
      }
    }

    .v-selection-control {
      &:not(:last-child):not(:only-child) {
        margin-bottom: 0;
      }

      label {
        color: var(--color-base-grey-900) !important;
      }

      .icon {
        color: var(--color-base-grey-600);

        &--dense svg {
          height: 18px;
        }
      }

      &__input {
        margin-right: base.space(1);
        height: base.space(4);
        width: base.space(4);
      }

      &--dirty:hover .icon {
        color: var(--color-base-blue-700);
      }

      &--dirty .icon {
        color: var(--color-base-blue-500);
      }

      &--disabled {
        cursor: not-allowed;
        pointer-events: auto;

        label {
          cursor: not-allowed;
        }

        input {
          cursor: not-allowed;
        }

        .icon {
          color: var(--color-base-grey-400) !important;
        }
      }
    }

    &.v-input {
      &--disabled {
        label {
          color: var(--color-base-grey-500) !important;
        }

        .icon {
          color: var(--color-base-grey-400) !important;
        }
      }

      &--focused {
        .icon {
          color: var(--color-base-blue-600) !important;
        }

        &.v-selection-control--dirty .icon {
          color: var(--color-base-blue-700) !important;
        }
      }
    }
  }

  :not(.v-input--disabled) :deep(.v-radio-group .v-radio:hover .icon) {
    color: var(--color-base-blue-700) !important;
  }
}
}