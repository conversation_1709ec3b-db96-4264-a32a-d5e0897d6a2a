import type { ApiSecurityDataFn } from '@/services/api';
import { createApi } from '@/services/api';
import { Api as BookApi } from '@dfe/dfe-book-api';
import type { Api as AddressApi } from '@dfe/dfe-address-api';
import createFetchMock from 'vitest-fetch-mock';
import { Api as DynamicLabelApi } from '@dfe/dfe-dynamiclabel-api';
import { expect } from 'vitest';

const fetchMocker = createFetchMock(vi);
fetchMocker.enableMocks();

const fetchMock = vi.spyOn(window, 'fetch');

const params = {
  baseUrl: 'http://localhost',
  path: '/api',
  secure: true,
};
const token = 'token123';
const language = 'en';
const requestOrigin = 'dfe-book-frontend';

describe('api service', () => {
  let apiBook: BookApi<ApiSecurityDataFn>;
  let apiAddress: AddressApi<ApiSecurityDataFn>;
  let apiDynamicLabel: DynamicLabelApi<ApiSecurityDataFn>;

  beforeAll(() => {
    apiBook = createApi('Book', {
      getToken: () => new Promise((resolve) => resolve(token)),
      getLanguage: () => language,
      getRequestOrigin: () => requestOrigin,
    });

    apiAddress = createApi('Address', {
      getToken: () => new Promise((resolve) => resolve(token)),
      getLanguage: () => language,
      getRequestOrigin: () => requestOrigin,
    });

    apiDynamicLabel = createApi('DynamicLabel', {
      getToken: () => new Promise((resolve) => resolve(token)),
      getLanguage: () => language,
    });
  });

  afterEach(() => {
    fetchMock.mockClear();
  });

  it('adds an authorization header to requests', async () => {
    await apiBook.request(params);
    await apiAddress.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      'http://localhost/api',
      expect.objectContaining({
        headers: expect.objectContaining({
          Authorization: `Bearer ${token}`,
        }),
      }),
    );
  });

  it('adds accept-language header to requests', async () => {
    await apiBook.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      'http://localhost/api',
      expect.objectContaining({
        headers: expect.objectContaining({
          'Accept-Language': language,
        }),
      }),
    );

    await apiAddress.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      'http://localhost/api',
      expect.objectContaining({
        headers: expect.objectContaining({
          'Accept-Language': language,
        }),
      }),
    );
  });

  it('adds request_origin header to requests', async () => {
    await apiBook.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      'http://localhost/api',
      expect.objectContaining({
        headers: expect.objectContaining({
          request_origin: requestOrigin,
        }),
      }),
    );

    await apiAddress.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      'http://localhost/api',
      expect.objectContaining({
        headers: expect.objectContaining({
          request_origin: requestOrigin,
        }),
      }),
    );
  });

  it('leaves request_origin header for dynamicLabel requests', async () => {
    await apiDynamicLabel.request(params);

    expect(window.fetch).toHaveBeenCalledWith(
      'http://localhost/api',
      expect.objectContaining({
        headers: expect.not.objectContaining({
          request_origin: requestOrigin,
        }),
      }),
    );
  });
});
