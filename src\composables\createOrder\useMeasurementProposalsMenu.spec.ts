import useMeasurementProposalsMenu from '@/composables/createOrder/useMeasurementProposalsMenu';
import { measurementProposals4ConversionTest } from '@/mocks/fixtures/measurementProposals';
import { ref } from 'vue';

const favoritesHeader = 'Favorites';
const standardHeader = 'Header';

describe('useCountrySelectOptions composable', () => {
  it('execute transform countries', () => {
    const inputExamples = measurementProposals4ConversionTest;
    const expectedResults = [
      [
        {
          header: favoritesHeader,
        },
        {
          length: 110,
          width: 80,
          menuText: `110 cm x <span class="text--light">80 cm</span>`,
          value: '110-80',
        },
        {
          length: 120,
          width: 80,
          menuText: `120 cm x <span class="text--light">80 cm</span>`,
          value: '120-80',
        },
        {
          length: 120,
          width: 100,
          menuText: `120 cm x <span class="text--light">100 cm</span>`,
          value: '120-100',
        },
        {
          length: 80,
          width: 60,
          menuText: `80 cm x <span class="text--light">60 cm</span>`,
          value: '80-60',
        },
        { divider: true },
        { header: standardHeader },
        {
          length: 120,
          width: 80,
          menuText: `120 cm x <span class="text--light">80 cm</span>`,
          value: '120-80--STANDARD',
        },
      ],
      [
        { header: standardHeader },
        {
          length: 120,
          width: 80,
          menuText: `120 cm x <span class="text--light">80 cm</span>`,
          value: '120-80--STANDARD',
        },
      ],
      [
        {
          header: favoritesHeader,
        },
        {
          length: 120,
          width: 100,
          menuText: `120 cm x <span class="text--light">100 cm</span>`,
          value: '120-100',
        },
        {
          length: 80,
          width: 60,
          menuText: `80 cm x <span class="text--light">60 cm</span>`,
          value: '80-60',
        },
      ],
    ];
    for (let i = 0; i < inputExamples.length; i++) {
      expect(
        useMeasurementProposalsMenu(favoritesHeader, standardHeader, ref(inputExamples[i])),
      ).toEqual(expectedResults[i]);
    }
  });
});
