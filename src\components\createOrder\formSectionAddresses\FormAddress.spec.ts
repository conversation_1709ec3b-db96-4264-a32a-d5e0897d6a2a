import FormAddress from '@/components/createOrder/formSectionAddresses/FormAddress.vue';
import { FurtherAddressTypesList, OrderTypes } from '@/enums';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { mockServer } from '@/mocks/server';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { countriesRoad, countryFavorites } from '@/mocks/fixtures/countries';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import TextField from '@/components/form/TextField.vue';
import { nextTick } from 'vue';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import { describe, expect } from 'vitest';
import { validatePostcodeInvalid, validatePostcodeValid } from '@/mocks/fixtures/validatePostcode';
import { useValidatePostcode } from '@/composables/createOrder/useValidatePostcode';
import { withSetup } from '@test/util/with-setup';

const addressType = FurtherAddressTypesList.coverAddressConsignor;

const mockAddress = {
  id: 123,
  name: 'name',
  street: 'street',
  postcode: '12345',
  city: 'city',
  countryCode: 'DE',
};

const props = {
  value: mockAddress,
};

describe('Addresses FormAddress component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        countriesRoad,
        countryFavorites,
        validatePostcodeValid,
        validatePostcodeInvalid,
      },
    });
  });

  beforeEach(() => {
    wrapper = shallowMount(FormAddress, {
      props,
    });
  });

  it('shows headline depending on address type', async () => {
    expect(wrapper.find('h4').exists()).toBe(false);

    await wrapper.setProps({
      addressType,
    });

    const label = 'labels.address_type_' + addressType.toLowerCase() + '.text';

    expect(wrapper.find('h4').exists()).toBe(true);
    expect(wrapper.find('h4').text()).toBe(label);
  });

  it('sets AddressCard props isConsigneeAddress', async () => {
    useCreateOrderDataStore().countries = countriesRoad;
    useCreateOrderDataStore().favoriteCountries = countryFavorites;

    await wrapper.vm.$nextTick();

    const countrySelectField = wrapper.findComponent(AutocompleteField);

    expect(countrySelectField.props('items')).toEqual(countriesRoad);

    await wrapper.setProps({
      smartProposalsEnabled: true,
    });

    expect(wrapper.findComponent(AutocompleteField).props('items')).toMatchSnapshot();
  });

  it('should disable address input fields when isShipperAddress and isShipperAddressDisabled are true (AirOrder)', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { isShipperAddressDisabled } = storeToRefs(createOrderAddressesStore);

    isShipperAddressDisabled.value = true;

    orderType.value = OrderTypes.AirExportOrder;

    await nextTick();
    await wrapper.setProps({
      disableSearch: true,
      isShipperAddress: true,
    });

    const addressInputFields = wrapper.findAllComponents(TextField);
    const countrySelectField = wrapper.findComponent(AutocompleteField);

    // Name
    expect(addressInputFields.at(0)?.props('disabled')).toBe(true);

    // Street
    expect(addressInputFields.at(3)?.props('disabled')).toBe(true);

    // Postcode
    expect(addressInputFields.at(5)?.props('disabled')).toBe(true);

    // City
    expect(addressInputFields.at(6)?.props('disabled')).toBe(true);

    // Country code
    expect(countrySelectField.props('disabled')).toBe(true);
  });

  it('should disable postcode when country code is empty', async () => {
    const { countries, favoriteCountries } = storeToRefs(useCreateOrderDataStore());
    countries.value = countriesRoad;
    favoriteCountries.value = countryFavorites;

    const { orderType } = storeToRefs(useCreateOrderFormStore());
    orderType.value = OrderTypes.RoadForwardingOrder;

    await wrapper.setProps({
      disableSearch: true,
      isShipperAddress: true,
    });

    await nextTick();

    const addressInputFields = wrapper.findAllComponents(TextField);

    expect(addressInputFields.at(3)?.props('disabled')).toBe(true);
  });

  it('should show discard changes dialog, if address have unsaved changes', async () => {
    const { isUnsavedAddressChangesDialogOpen } = storeToRefs(useCreateOrderAddressesStore());
    const discardDialog = wrapper.findComponent(ConfirmPrompt);

    isUnsavedAddressChangesDialogOpen.value = true;

    expect(discardDialog.exists()).toBe(true);
  });

  describe('Postcode validation', () => {
    it('should validate postcode correctly', async () => {
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const { formAddress } = storeToRefs(createOrderAddressesStore);
      const { data: postcodeData, refetch } = withSetup(() => useValidatePostcode())[0];

      formAddress.value = mockAddress;

      await wrapper.vm.$nextTick();

      await refetch();

      expect(postcodeData.value).toEqual(validatePostcodeValid);
    });

    it('should validate postcode with error when postcode ist too short', async () => {
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const { formAddress } = storeToRefs(createOrderAddressesStore);
      const { data: postcodeData, refetch } = withSetup(() => useValidatePostcode())[0];

      formAddress.value = mockAddress;
      formAddress.value.postcode = '123';

      await refetch();

      await wrapper.vm.$nextTick();

      expect(postcodeData.value).toEqual(validatePostcodeInvalid);
    });

    it('should validate postcode with error when postcode ist too long', async () => {
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const { formAddress } = storeToRefs(createOrderAddressesStore);
      const { data: postcodeData, refetch } = withSetup(() => useValidatePostcode())[0];

      formAddress.value = mockAddress;
      formAddress.value.postcode = '123456';

      await refetch();

      await wrapper.vm.$nextTick();

      expect(postcodeData.value).toEqual(validatePostcodeInvalid);
    });
  });

  describe('when selecting ireland as country code ', () => {
    const createOrderAddressesStore = useCreateOrderAddressesStore();
    const { formAddress } = storeToRefs(createOrderAddressesStore);

    beforeEach(() => {
      formAddress.value = mockAddress;
      formAddress.value.countryCode = 'IE';
    });

    it('should show eircode instead of postcode', async () => {
      useCreateOrderDataStore().countries = countriesRoad;

      const addressInputFields = wrapper.findAllComponents(TextField);

      const eircode = addressInputFields.at(4);
      await nextTick();

      expect(eircode?.props('label')).toBe('labels.eircode_label.text');
    });

    it('should not show supplement', async () => {
      useCreateOrderDataStore().countries = countriesRoad;

      const addressInputFields = wrapper.findAllComponents(TextField);

      const supplementField = addressInputFields.find(
        (field) => field.props('label') === 'labels.supplement_label.text',
      );
      expect(supplementField?.exists()).toBe(undefined);
    });

    it('should convert eircode input to uppercase', async () => {
      useCreateOrderDataStore().countries = countriesRoad;

      const addressInputFields = wrapper.findAllComponents(TextField);

      const eircode = addressInputFields.at(4);
      eircode?.setValue('ha');
      eircode?.vm.$emit('input');
      await nextTick();

      expect(eircode?.props('modelValue')).toBe('HA');
    });

    it('should convert eircode input to add space after 3 characters', async () => {
      useCreateOrderDataStore().countries = countriesRoad;

      const addressInputFields = wrapper.findAllComponents(TextField);

      const eircode = addressInputFields.at(4);
      eircode?.setValue('H54YR28');
      eircode?.vm.$emit('input');
      await nextTick();

      expect(eircode?.props('modelValue')).toBe('H54 YR28');
    });

    it('should skip the validation for the eircode, if the principal address is from ireland', async () => {
      await wrapper.setProps({
        customerNumber: '123456',
      });

      const eircodeField = wrapper.findAllComponents(TextField).at(4);

      expect(eircodeField?.vm.rules).toHaveLength(0);
    });

    it('should validate core address with 40 chars when customerNumber is given', async () => {
      await wrapper.setProps({
        customerNumber: '123456',
      });

      const name = wrapper.findAllComponents(TextField).at(0);
      name?.setValue('1234567890123456789012345678901234567890');

      // Use the actual value of the name field for validation
      const result = name?.vm.rules?.[0]?.(name?.props('modelValue') ?? '');
      expect(result).toBe(true);
    });

    it('should fail validation when core address name exceeds 40 chars and customerNumber is given', async () => {
      await wrapper.setProps({
        customerNumber: '123456',
      });

      const name = wrapper.findAllComponents(TextField).at(0);
      await name?.setValue('12345678901234567890123456789012345678901'); // 41 characters
      await nextTick();

      const value = name?.props('modelValue') ?? '';
      const result = name?.vm.rules?.[0]?.(value);
      expect(result).not.toBe(true);
    });

    it('should NOT skip the validation for the eircode for a regular Ireland address (not principal)', async () => {
      await wrapper.setProps({
        customerNumber: undefined,
      });

      const eircodeField = wrapper.findAllComponents(TextField).at(4);

      expect(eircodeField?.vm.rules).toHaveLength(2);
    });

    it('should fail validation when dfe address name exceeds 30 chars and no customerNumber is given', async () => {
      await wrapper.setProps({
        customerNumber: undefined,
      });

      const name = wrapper.findAllComponents(TextField).at(0);
      await name?.setValue('1234567890123456789012345678901'); // 31 characters
      await nextTick();

      const value = name?.props('modelValue') ?? '';
      const result = name?.vm.rules?.[0]?.(value);
      expect(result).not.toBe(true);
    });
  });
});
