import useSaveOrder from '@/composables/createOrder/useSaveOrder';
import { DocumentTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import type { GeneralProblem, ValidationResult } from '@dfe/dfe-book-api';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

function useValidateOrder() {
  const createOrderFormStore = useCreateOrderFormStore();
  const order = computed(() => useSaveOrder());

  const validateOrder = async (): Promise<ValidationResult | GeneralProblem> => {
    const validationResult = await createOrderFormStore.validateOrder(order.value);
    const errorBannerStore = useErrorBanner();
    const generalError = validationResult as GeneralProblem;
    if (generalError) {
      return generalError;
    }

    errorBannerStore.importFromValidationResult(validationResult as ValidationResult);
    return validationResult as ValidationResult;
  };

  const setIsDeliveryNoteNumberFieldRequired = () => {
    const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
    const { deliveryNoteNumbers, deliveryNoteNumberRequiredFields } = storeToRefs(
      createOrderOrderReferencesFormStore,
    );
    const { documents } = useCreateOrderDocumentsStore();

    const hasDocumentEDN = documents.some(
      (document) => document.documentType === DocumentTypes.EDN,
    );

    if (hasDocumentEDN && deliveryNoteNumbers.value.length === 0) {
      createOrderOrderReferencesFormStore.addReference(OrderReferenceType.DELIVERY_NOTE_NUMBER, {
        required: true,
        setFocus: false,
      });
    }

    if (deliveryNoteNumbers.value.length > 0 && deliveryNoteNumberRequiredFields.value.length > 0) {
      deliveryNoteNumberRequiredFields.value[0].required = hasDocumentEDN;
    }
  };

  return {
    order,
    validateOrder,
    setIsDeliveryNoteNumberFieldRequired,
  };
}

export default useValidateOrder;
