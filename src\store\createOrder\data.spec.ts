import { OrderTypes } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { collectionOptions } from '@/mocks/fixtures/collectionOptions';
import { collectionTimeSlots } from '@/mocks/fixtures/collectionTimeSlots';
import { countriesAir, countriesRoad, countryFavorites } from '@/mocks/fixtures/countries';
import { currencies } from '@/mocks/fixtures/currencies';
import { customers, customersWithAddresses } from '@/mocks/fixtures/customers';
import { customerSettings } from '@/mocks/fixtures/customerSettings';
import { deliveryOptions } from '@/mocks/fixtures/deliveryOptions';
import { roadDeliveryProducts } from '@/mocks/fixtures/deliveryProducts';
import { documentTypes } from '@/mocks/fixtures/documentTypes';
import { extensions } from '@/mocks/fixtures/extensions';
import { freightTerms } from '@/mocks/fixtures/freightTerms';
import { furtherAddressTypes } from '@/mocks/fixtures/furtherAddressTypes';
import { goodsGroupResponse } from '@/mocks/fixtures/goodsGroupResponse';
import { incoTerms } from '@/mocks/fixtures/incoTerms';
import { loadingPoints } from '@/mocks/fixtures/loadingPoints';
import { measurementProposals } from '@/mocks/fixtures/measurementProposals';
import { airExportOrder, seaExportOrder } from '@/mocks/fixtures/order';
import { orderContent } from '@/mocks/fixtures/orderContent';
import { orderGroups } from '@/mocks/fixtures/orderGroups';
import { packagingOptionsWithFavorites } from '@/mocks/fixtures/packagingOptions';
import { ports } from '@/mocks/fixtures/ports';
import { isThirdCountryConstellation } from '@/mocks/fixtures/thirdCountryConstellation';
import { transports } from '@/mocks/fixtures/transports';
import { validateAddressData } from '@/mocks/fixtures/validateAddressData';
import { mockServer } from '@/mocks/server';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { AirExportQuoteInformation, OrderAddress } from '@dfe/dfe-book-api';
import { PortType, Segment } from '@dfe/dfe-book-api';
import { initPinia } from '@test/util/init-pinia';
import { logErrorMock, modalShowMock } from '@test/util/mock-client';
import { storeToRefs } from 'pinia';
import { describe } from 'vitest';
import { nextTick } from 'vue';

describe('createOrderData store', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {
        customers,
        addresses,
        countriesRoad,
        countriesAir,
        countryFavorites,
        furtherAddressTypes,
        goodsGroupResponse,
        packagingOptionsWithFavorites,
        measurementProposals,
        isThirdCountryConstellation,
        currencies,
        orderContent,
        deliveryProducts: roadDeliveryProducts,
        deliveryOptions,
        collectionOptions,
        freightTerms,
        collectionTimeSlots,
        orderGroups,
        transports,
        extensions,
        documentTypes,
        ports,
        customerSettings,
        incoTerms,
        loadingPoints,
        airExportOrder,
        validateAddressData,
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });
  it('returns if further address type is available', () => {
    const store = useCreateOrderDataStore();
    store.furtherAddressTypes = [];

    expect(store.isFurtherAddressTypeAvailable('DA')).toBe(false);

    store.furtherAddressTypes = [{ code: 'DA' }];

    expect(store.isFurtherAddressTypeAvailable('DA')).toBe(true);
    expect(store.isFurtherAddressTypeAvailable('IM')).toBe(false);
  });

  it('returns available further address types', () => {
    const store = useCreateOrderDataStore();
    store.furtherAddressTypes = [];

    expect(store.getAvailableFurtherAddressTypes()).toEqual([]);
    expect(store.getAvailableFurtherAddressTypes(['DA', 'IM'])).toEqual([]);

    const mockAddressTypes = [{ code: 'DA' }, { code: 'CB' }];
    store.furtherAddressTypes = mockAddressTypes;

    expect(store.getAvailableFurtherAddressTypes()).toEqual(mockAddressTypes);
    expect(store.getAvailableFurtherAddressTypes(['DA', 'IM'])).toEqual([{ code: 'DA' }]);
  });

  it('fetches customers and their addresses', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchCustomers(Segment.ROAD);

    expect(store.customers).toEqual(customersWithAddresses);
  });

  it('returns an empty array, when fetchCustomers fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.customers.getCustomers = vi.fn().mockRejectedValue(null);
    const customers = await store.fetchCustomers(Segment.ROAD);

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch customers',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(customers).toEqual([]);
  });

  it('fetches countries for ROAD', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchCountries(Segment.ROAD);

    expect(store.countries).toEqual(countriesRoad);
  });

  it('fetches countries for AIR', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchCountries(Segment.AIR);

    expect(store.countries).toEqual(countriesAir);
  });

  it('fetch favorite countries for customer', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchFavoriteCountries('0', Segment.AIR);

    expect(store.favoriteCountries).toEqual(countryFavorites);
  });

  it('returns an empty object when fetchFavoriteCountries fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.customers.getFavoriteCountries = vi.fn().mockRejectedValue(null);
    const result = await store.fetchFavoriteCountries('0', Segment.AIR);

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch customers favorite countries',
      'dfe-book-frontend',
      null,
    );

    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(result).toEqual({});
  });

  it('returns an empty array, when fetchCountries fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.countries.getCountries = vi.fn().mockRejectedValue(null);
    const countries = await store.fetchCountries(Segment.ROAD);

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch countries',
      'dfe-book-frontend',
      null,
    );

    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(countries).toEqual([]);
  });

  it('fetches address types by customer id', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchFurtherAddressTypes('0', Segment.ROAD);

    expect(store.furtherAddressTypes).toEqual(furtherAddressTypes[0]);
  });

  it('returns an empty array, when fetchFurtherAddressTypes fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.customers.getFurtherAddressTypes = vi.fn().mockRejectedValue(null);
    const addressTypes = await store.fetchFurtherAddressTypes('0', Segment.ROAD);

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch further address types',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(addressTypes).toEqual([]);
  });

  it('checks isThirdCountryConstellation', async () => {
    const store = useCreateOrderDataStore();
    await store.checkForThirdCountryConstellation('DE', 'GB');

    expect(store.isThirdCountryConstellation).toEqual(isThirdCountryConstellation.result);
  });

  it('returns undefined, when checkForThirdCountryConstellation fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.thirdCountryConstellation.isThirdCountryConstellation = vi
      .fn()
      .mockRejectedValue(null);
    const isThirdCountryConstellation = await store.checkForThirdCountryConstellation('DE', 'GB');

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to check for third country constellation',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(isThirdCountryConstellation).toEqual([]);
  });

  it('fetches collectionOptions', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchCollectionOptions();

    expect(store.collectionOptions).toEqual(collectionOptions);
  });

  it('returns an empty array, when fetchCollectionOptions fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.collectionOptions.getCollectionOptions = vi.fn().mockRejectedValue(null);
    const collectionOptions = await store.fetchCollectionOptions();

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch collection options',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(collectionOptions).toEqual([]);
  });

  it('fetches freightTerms', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchFreightTerms();

    expect(store.freightTerms).toEqual(freightTerms);
  });

  it('returns an empty array, when fetchFreightTerms fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.freightTerms.getFreightTerms = vi.fn().mockRejectedValue(null);
    const freightTerms = await store.fetchFreightTerms();

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch freight terms',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(freightTerms).toEqual([]);
  });

  it('fetches documentExtensions', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchExtensions();

    expect(store.documentExtensions).toEqual(extensions);
  });

  it('returns an empty array, when fetchExtensions fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.extensions.getExtensions = vi.fn().mockRejectedValue(null);
    const extensions = await store.fetchExtensions();

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch document extensions',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(extensions).toEqual([]);
  });

  it('returns an array of ports when searchPort succeeds', async () => {
    const store = useCreateOrderDataStore();
    const ports = await store.searchPort('FRA', PortType.AIRPORT);

    expect(ports).toMatchSnapshot();
  });

  it('returns an empty array when searchPort fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.ports.getPorts = vi.fn().mockRejectedValue(null);
    const ports = await store.searchPort('FRA', PortType.AIRPORT);

    expect(logErrorMock).toHaveBeenCalledWith('Failed to search ports', 'dfe-book-frontend', null);
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(ports).toEqual([]);
  });

  it('fetches incoTerms without orderType', async () => {
    const store = useCreateOrderDataStore();
    await store.fetchIncoTerms();

    expect(store.incoTerms).toEqual(incoTerms);
  });

  it('returns an empty array, when fetchIncoTerms fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.incoTerms.getIncoterms = vi.fn().mockRejectedValue(null);
    const incoTerms = await store.fetchIncoTerms();

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to fetch inco terms',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(incoTerms).toEqual([]);
  });

  it('fetches fromPortRouting data - AIR', async () => {
    const store = useCreateOrderDataStore();
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = addressStore;
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.AirExportOrder;
    transportType.value = Segment.AIR;
    shipperAddress.address = <OrderAddress>airExportOrder.shipperAddress;

    await store.fetchPortRouting('from');

    const airPorts = ports.filter((port) => port.type === PortType.AIRPORT);

    expect(store.fromPortRouting).toEqual(airPorts);
  });

  it('fetches toPortRouting data - AIR', async () => {
    const store = useCreateOrderDataStore();
    const addressStore = useCreateOrderAddressesStore();
    const { consigneeAddress } = addressStore;
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.AirExportOrder;
    transportType.value = Segment.AIR;

    consigneeAddress.address = <OrderAddress>airExportOrder.consigneeAddress;

    await store.fetchPortRouting('to');
    const airPorts = ports.filter((port) => port.type === PortType.AIRPORT);

    expect(store.toPortRouting).toEqual(airPorts);
  });

  it('fetches fromPortRouting data - SEA', async () => {
    const store = useCreateOrderDataStore();
    const addressStore = useCreateOrderAddressesStore();
    const { shipperAddress } = addressStore;
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.SeaExportOrder;
    transportType.value = Segment.SEA;

    shipperAddress.address = <OrderAddress>seaExportOrder.shipperAddress;

    await store.fetchPortRouting('from');

    const seaPorts = ports.filter((port) => port.type === PortType.SEAPORT);

    expect(store.fromPortRouting).toEqual(seaPorts);
  });

  it('fetches toPortRouting data - SEA', async () => {
    const store = useCreateOrderDataStore();
    const addressStore = useCreateOrderAddressesStore();
    const { consigneeAddress } = addressStore;
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.SeaExportOrder;
    transportType.value = Segment.SEA;

    consigneeAddress.address = <OrderAddress>seaExportOrder.consigneeAddress;

    await store.fetchPortRouting('to');
    const seaPorts = ports.filter((port) => port.type === PortType.SEAPORT);

    expect(store.toPortRouting).toEqual(seaPorts);
  });

  it('returns an empty array, when fetchPortRoutingFromAirport fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.v1.getPortRoutings = vi.fn().mockRejectedValue({
      error: {
        oasDiscriminator: 'GeneralProblem',
        type: 'https://example.com/probs/technical-error',
        errorId: 'errRT-01',
        title: 'Technical Error',
        status: 502,
        detail: 'A technical error occurred.',
        severity: 'Critical',
        timestamp: '2023-10-01T00:00:00Z',
      },
    });
    await store.fetchPortRouting('from');

    expect(modalShowMock).toHaveBeenCalledTimes(1);
  });

  it('returns an empty array, when fetchPortRoutingToAirport fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.v1.getPortRoutings = vi.fn().mockRejectedValue({
      error: {
        oasDiscriminator: 'GeneralProblem',
        type: 'https://example.com/probs/technical-error',
        errorId: 'errRT-01',
        title: 'Technical Error',
        status: 502,
        detail: 'A technical error occurred.',
        severity: 'Critical',
        timestamp: '2023-10-01T00:00:00Z',
      },
    });
    await store.fetchPortRouting('to');

    expect(modalShowMock).toHaveBeenCalledTimes(1);
  });

  it('fetches validationResult data', async () => {
    const store = useCreateOrderDataStore();
    const { validateAddressResult } = storeToRefs(store);

    await store.validateAddress(<OrderAddress>airExportOrder.shipperAddress);

    expect(validateAddressResult.value).toEqual(validateAddressData);
  });

  it('returns an empty object, when validateAddress fails', async () => {
    const store = useCreateOrderDataStore();
    store.api.book.v1.validateAddress = vi.fn().mockRejectedValue(null);
    const validationResult = await store.validateAddress(
      <OrderAddress>airExportOrder.shipperAddress,
    );

    expect(logErrorMock).toHaveBeenCalledWith(
      'Failed to validate address',
      'dfe-book-frontend',
      null,
    );
    expect(logErrorMock).toHaveBeenCalledTimes(1);
    expect(validationResult).toEqual([]);
  });

  describe('Routing', () => {
    beforeAll(() => {
      initPinia();

      mockServer({
        environment: 'test',
        fixtures: {
          customers,
          addresses,
          countriesRoad,
          countriesAir,
          countryFavorites,
          furtherAddressTypes,
          goodsGroupResponse,
          packagingOptionsWithFavorites,
          measurementProposals,
          isThirdCountryConstellation,
          currencies,
          orderContent,
          deliveryProducts: roadDeliveryProducts,
          deliveryOptions,
          collectionOptions,
          freightTerms,
          collectionTimeSlots,
          orderGroups,
          transports,
          extensions,
          documentTypes,
          ports,
          customerSettings,
          incoTerms,
          loadingPoints,
          airExportOrder,
          validateAddressData,
        },
      });
    });

    afterEach(() => {
      vi.clearAllMocks();
    });
    it('fetches fromPortRouting data - AIR', async () => {
      const store = useCreateOrderDataStore();
      const addressStore = useCreateOrderAddressesStore();
      const { shipperAddress } = addressStore;
      const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

      orderType.value = OrderTypes.AirExportOrder;
      transportType.value = Segment.AIR;

      shipperAddress.address = <OrderAddress>airExportOrder.shipperAddress;

      await store.fetchPortRouting('from');

      const airPorts = ports.filter((port) => port.type === PortType.AIRPORT);

      expect(store.fromPortRouting).toEqual(airPorts);
    });

    it('fetches toPortRouting data - AIR', async () => {
      const store = useCreateOrderDataStore();
      const addressStore = useCreateOrderAddressesStore();
      const { consigneeAddress } = addressStore;
      const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

      orderType.value = OrderTypes.AirExportOrder;
      transportType.value = Segment.AIR;

      consigneeAddress.address = <OrderAddress>airExportOrder.consigneeAddress;

      await store.fetchPortRouting('to');
      const airPorts = ports.filter((port) => port.type === PortType.AIRPORT);

      expect(store.toPortRouting).toEqual(airPorts);
    });

    it('fetches fromPortRouting data - SEA', async () => {
      const store = useCreateOrderDataStore();
      const addressStore = useCreateOrderAddressesStore();
      const { shipperAddress } = addressStore;
      const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

      orderType.value = OrderTypes.SeaExportOrder;
      transportType.value = Segment.SEA;

      shipperAddress.address = <OrderAddress>seaExportOrder.shipperAddress;

      await store.fetchPortRouting('from');

      const seaPorts = ports.filter((port) => port.type === PortType.SEAPORT);

      expect(store.fromPortRouting).toEqual(seaPorts);
    });

    it('fetches toPortRouting data - SEA', async () => {
      const store = useCreateOrderDataStore();
      const addressStore = useCreateOrderAddressesStore();
      const { consigneeAddress } = addressStore;
      const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

      orderType.value = OrderTypes.SeaExportOrder;
      transportType.value = Segment.SEA;

      consigneeAddress.address = <OrderAddress>seaExportOrder.consigneeAddress;

      await store.fetchPortRouting('to');
      const seaPorts = ports.filter((port) => port.type === PortType.SEAPORT);

      expect(store.toPortRouting).toEqual(seaPorts);
    });
    it('should set hasInvalidRouting to true when no port routing and user input is present', async () => {
      const dataStore = useCreateOrderDataStore();
      const { shipperAddress, consigneeAddress } = useCreateOrderAddressesStore();
      shipperAddress.address.postcode = '11111';
      consigneeAddress.address.postcode = '22222';
      dataStore.fromPortRouting = [];
      dataStore.toPortRouting = [];

      await nextTick();

      await dataStore.fetchPortRouting('from');

      await nextTick();

      expect(dataStore.hasInvalidRouting).toBe(true);
    });

    it('should set hasInvalidRouting to false when port routing is available', async () => {
      const dataStore = useCreateOrderDataStore();
      const formStore = useCreateOrderFormStore();
      const { transportCountry } = storeToRefs(formStore);

      transportCountry.value.fromPostcode = '12345';
      transportCountry.value.toPostcode = '67890';
      dataStore.fromPortRouting = [{ code: 'PORT1', name: 'Port 1', type: PortType.AIRPORT }];
      dataStore.toPortRouting = [{ code: 'PORT2', name: 'Port 2', type: PortType.AIRPORT }];

      await dataStore.fetchPortRouting('from');

      expect(dataStore.hasInvalidRouting).toBe(false);
    });

    it('should set hasInvalidRouting to false when no user input is present', async () => {
      const dataStore = useCreateOrderDataStore();
      const formStore = useCreateOrderFormStore();
      const { transportCountry } = storeToRefs(formStore);

      transportCountry.value.fromPostcode = null;
      transportCountry.value.toPostcode = null;
      dataStore.fromPortRouting = [];
      dataStore.toPortRouting = [];

      await dataStore.fetchPortRouting('from');

      expect(dataStore.hasInvalidRouting).toBe(false);
    });
    it('fetches no toPortRouting data when there is a Quote ID  - AIR', async () => {
      const dataStore = useCreateOrderDataStore();
      const formStore = useCreateOrderFormStore();
      const { transportType, orderType } = storeToRefs(formStore);
      orderType.value = OrderTypes.AirExportOrder;
      transportType.value = Segment.AIR;

      formStore.quoteInformation = {
        quoteRequestId: 123,
        orderType: OrderTypes.AirExportOrder,
      } as AirExportQuoteInformation;
      await dataStore.fetchPortRouting('to');
      expect(dataStore.toPortRouting).toEqual([]);
    });
  });
});
