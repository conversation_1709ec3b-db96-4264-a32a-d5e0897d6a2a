type NonFalsy<T> = T extends false | 0 | '' | null | undefined | 0n ? never : T;

interface Array<T> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  filter(predicate: BooleanConstructor, thisArg?: any): NonFalsy<T>[];
}

interface ReadonlyArray<T> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  filter(predicate: BooleanConstructor, thisArg?: any): NonFalsy<T>[];
}
