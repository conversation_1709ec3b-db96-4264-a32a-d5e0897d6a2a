import AutocompleteField from '@/components/form/AutocompleteField.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import ProgressCircular from '@/components/ProgressCircular.vue';
import { beforeAll, beforeEach } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';

const props = {
  items: [
    { value: '0', text: 'Select 0' },
    { value: '1', text: 'Select 1' },
    { value: '2', text: 'Select 2' },
  ],
};
const mockInput = props.items[1];
const label = 'Label';
const requiredChar = '*';

describe('AutocompleteField component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(AutocompleteField, {
      props,
      global: {
        stubs: ['v-menu'],
      },
    });
  });

  it('emits input event on change', async () => {
    const autocomplete = wrapper.findComponent({ name: 'v-autocomplete' });
    await autocomplete.vm.$emit('update:modelValue', mockInput);

    const inputEvents = wrapper.emitted('update:modelValue');

    expect(inputEvents).toHaveLength(2);
    expect(inputEvents?.[0]).toEqual([mockInput]);
  });

  it('resets searchText when modelValue is set to undefined', async () => {
    await wrapper.setProps({ modelValue: undefined });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.searchText).toBe('');
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it('shows loader after defined time', async () => {
    await wrapper.setProps({ showLoaderAfterMs: 100 });
    await wrapper.setProps({ showLoader: true });

    const el = wrapper.find('.v-input__control [role="combobox"]');
    await el.trigger('click');
    await wrapper.setProps({ loading: true });

    let loader: VueWrapper;
    loader = wrapper.findComponent(ProgressCircular);
    expect(loader.exists()).toBe(false);

    await new Promise((resolve) => setTimeout(resolve, 100));

    loader = wrapper.findComponent(ProgressCircular);
    expect(loader.exists()).toBe(true);
  });
});
