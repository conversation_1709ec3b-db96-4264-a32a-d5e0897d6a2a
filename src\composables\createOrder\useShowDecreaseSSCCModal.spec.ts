import { describe, expect, it } from 'vitest';
import { createPinia, setActivePinia } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { showDecreaseSSCCsModal } from '@/composables/createOrder/useShowDecreaseSSCCModal';
import { OrderStatus, OrderType } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';

describe('showDecreaseSSCCsModal', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('returns false for non-road orders', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;

    expect(showDecreaseSSCCsModal(false, true)).toBe(false);
  });

  it('returns false when order status is not complete', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.SENT },
    };

    expect(showDecreaseSSCCsModal(false, true)).toBe(false);
  });

  it('returns true when manualNumberOfLabelsActivated is true and manual labels have decreased', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };

    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    createOrderOrderLineFormStore.manualNumberOfLabelsOnLoad = 10;
    createOrderOrderLineFormStore.manualNumberOfLabels = 5;

    expect(showDecreaseSSCCsModal(false, true)).toBe(true);
  });

  it('returns true when manualNumberOfLabelsActivated is false and generated labels have decreased', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };

    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    createOrderOrderLineFormStore.generatedSSccsOnLoad = 10;
    createOrderOrderLineFormStore.generatedSSccs = ['1', '2', '3', '4', '5'];
    expect(showDecreaseSSCCsModal(false, false)).toBe(true);
  });

  it('returns false when called by callback', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };

    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    createOrderOrderLineFormStore.generatedSSccsOnLoad = 5;
    createOrderOrderLineFormStore.generatedSSccs = ['1', '2', '3', '4', '5'];

    expect(showDecreaseSSCCsModal(true, false)).toBe(false);
  });

  it('returns false when manualNumberOfLabelsActivated is true but manual labels have not decreased', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };

    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    createOrderOrderLineFormStore.manualNumberOfLabelsOnLoad = 10;
    createOrderOrderLineFormStore.manualNumberOfLabels = 10;

    expect(showDecreaseSSCCsModal(false, true)).toBe(false);
  });

  it('returns false when manualNumberOfLabelsActivated is false and generated labels have not decreased', () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };

    const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
    createOrderOrderLineFormStore.addOrderLine();
    createOrderOrderLineFormStore.orderLines[0].quantity = 5;
    createOrderOrderLineFormStore.generatedSSccsOnLoad = 5;
    createOrderOrderLineFormStore.generatedSSccs = ['1', '2', '3', '4', '5'];

    expect(showDecreaseSSCCsModal(false, false)).toBe(false);
  });
});
