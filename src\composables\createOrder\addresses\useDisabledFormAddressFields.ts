import { DisabledFormAddressFields } from '@/enums';
import type { OrderAddress } from '@dfe/dfe-book-api';
import { computed, type Ref } from 'vue';

export function useDisabledFormAddressFields(address?: Ref<OrderAddress>) {
  const disabledFields = computed<(keyof OrderAddress)[]>(() => {
    switch (address?.value?.lockedByQuote) {
      case 'partial':
        return DisabledFormAddressFields.partial;
      case 'full':
        return DisabledFormAddressFields.full;
      default:
        return [];
    }
  });

  function isDisabled(field: keyof OrderAddress) {
    return computed(() => disabledFields.value.includes(field));
  }

  return {
    isDisabled,
  };
}
