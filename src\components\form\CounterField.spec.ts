import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import CounterField from '@/components/form/CounterField.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';

const props = {
  modelValue: 1,
  min: 0,
  max: 2,
  disabled: false,
};
const label = 'Label';
const requiredChar = '*';

describe('CounterField component', () => {
  const event = vi.fn();
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(CounterField, {
      props,
    });
  });

  afterEach(() => {
    event.mockClear();
    wrapper.unmount();
  });

  const step = async (dir: 'DOWN' | 'UP') => {
    await wrapper
      .findAllComponents(MaterialSymbol)
      .at(dir === 'DOWN' ? 0 : 1)
      ?.trigger('click');
  };

  it('emits update event with numeric value on input', async () => {
    const textField = wrapper.findComponent({ name: 'v-text-field' });
    await textField.vm.$emit('update:modelValue', '2');

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([2]);
  });

  it('emits update event with decreased value on minus click', async () => {
    await step('DOWN');

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([0]);
  });

  it('emits update event with increased value on plus click', async () => {
    await step('UP');

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([2]);
  });

  it('respects min prop', async () => {
    await step('DOWN');
    await step('DOWN');
    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toStrictEqual([0]);
  });

  it('respects max prop', async () => {
    await step('UP');
    await step('UP');
    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toStrictEqual([2]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it("disables increment button when modelValue is at it's maximum", async () => {
    await wrapper.setProps({ modelValue: 2 });

    const incrementButton = wrapper.findAllComponents(MaterialSymbol).at(1);
    expect(incrementButton?.attributes('disabled')).toBe('true');
  });

  it("disables decrement button when modelValue is at it's minimum", async () => {
    await wrapper.setProps({ modelValue: 0 });

    const decrementButton = wrapper.findAllComponents(MaterialSymbol).at(0);
    expect(decrementButton?.attributes('disabled')).toBe('disabled');
  });
});
