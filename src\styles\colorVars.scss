@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

:root {
  --color-base-black: #{vars.$color-base-black};
  --color-base-white: #{vars.$color-base-white};

  --color-base-grey-50: #{vars.$color-base-grey-50};
  --color-base-grey-100: #{vars.$color-base-grey-100};
  --color-base-grey-200: #{vars.$color-base-grey-200};
  --color-base-grey-300: #{vars.$color-base-grey-300};
  --color-base-grey-400: #{vars.$color-base-grey-400};
  --color-base-grey-500: #{vars.$color-base-grey-500};
  --color-base-grey-600: #{vars.$color-base-grey-600};
  --color-base-grey-700: #{vars.$color-base-grey-700};
  --color-base-grey-800: #{vars.$color-base-grey-800};
  --color-base-grey-900: #{vars.$color-base-grey-900};

  --color-base-blue-50: #{vars.$color-base-blue-50};
  --color-base-blue-100: #{vars.$color-base-blue-100};
  --color-base-blue-200: #{vars.$color-base-blue-200};
  --color-base-blue-300: #{vars.$color-base-blue-300};
  --color-base-blue-400: #{vars.$color-base-blue-400};
  --color-base-blue-500: #{vars.$color-base-blue-500};
  --color-base-blue-600: #{vars.$color-base-blue-600};
  --color-base-blue-700: #{vars.$color-base-blue-700};
  --color-base-blue-800: #{vars.$color-base-blue-800};
  --color-base-blue-900: #{vars.$color-base-blue-900};

  --color-base-green-100: #{vars.$color-base-green-100};
  --color-base-green-200: #{vars.$color-base-green-200};
  --color-base-green-300: #{vars.$color-base-green-300};
  --color-base-green-400: #{vars.$color-base-green-400};
  --color-base-green-500: #{vars.$color-base-green-500};
  --color-base-green-600: #{vars.$color-base-green-600};
  --color-base-green-700: #{vars.$color-base-green-700};
  --color-base-green-800: #{vars.$color-base-green-800};
  --color-base-green-900: #{vars.$color-base-green-900};

  --color-base-orange-100: #{vars.$color-base-orange-100};
  --color-base-orange-200: #{vars.$color-base-orange-200};
  --color-base-orange-300: #{vars.$color-base-orange-300};
  --color-base-orange-400: #{vars.$color-base-orange-400};
  --color-base-orange-500: #{vars.$color-base-orange-500};
  --color-base-orange-600: #{vars.$color-base-orange-600};
  --color-base-orange-700: #{vars.$color-base-orange-700};
  --color-base-orange-800: #{vars.$color-base-orange-800};
  --color-base-orange-900: #{vars.$color-base-orange-900};

  --color-base-red-100: #{vars.$color-base-red-100};
  --color-base-red-200: #{vars.$color-base-red-200};
  --color-base-red-300: #{vars.$color-base-red-300};
  --color-base-red-400: #{vars.$color-base-red-400};
  --color-base-red-500: #{vars.$color-base-red-500};
  --color-base-red-600: #{vars.$color-base-red-600};
  --color-base-red-700: #{vars.$color-base-red-700};
  --color-base-red-800: #{vars.$color-base-red-800};
  --color-base-red-900: #{vars.$color-base-red-900};

  --color-base-yellow-500: #{vars.$color-base-yellow-500};

  --color-text-base: #{vars.$color-text-base};
  --color-text-primary: #{vars.$color-text-primary};
  --color-text-grey: #{vars.$color-text-grey};

  --color-dachser-yellow: #{vars.$color-dachser-yellow};
  --color-dachser-blue: #{vars.$color-dachser-blue};
}
