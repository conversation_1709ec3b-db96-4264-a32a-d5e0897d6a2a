<template>
  <SectionCard v-if="showCustomer" class="card-collecting-order">
    <template #headline>{{ t('labels.principal_title.text') }}</template>
    <VRow>
      <VCol cols="12" sm="6" lg="5" xl="4">
        <SelectAddress
          v-if="showCustomerSelectForm || !showTooltip"
          v-model="customerNumber"
          v-tooltip="{
            text: tooltipText,
            location: 'right',
            disabled: !showTooltip,
            attach: $appRoot,
          }"
          :items="customers ?? []"
          :disabled="isCustomerSectionDisabled"
        />
      </VCol>
    </VRow>
  </SectionCard>
</template>

<script setup lang="ts">
import SectionCard from '@/components/base/SectionCard.vue';
import SelectAddress from '@/components/form/SelectAddress.vue';
import { ContactsType } from '@/enums';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const createOrderDataStore = useCreateOrderDataStore();
const { customers } = storeToRefs(createOrderDataStore);
const createOrderFormStore = useCreateOrderFormStore();
const {
  customerNumber,
  selectedCustomer,
  isAirOrderFromQuote,
  isCompleteRoadOrder,
  isOrderSaved,
  isPrincipalLocked,
} = storeToRefs(createOrderFormStore);

const addressId = computed(() => {
  return selectedCustomer?.value?.address?.id;
});
const createAddressDataStore = useCreateAddressDataStore();
const { contactsCustomer } = storeToRefs(createAddressDataStore);

const getAddressContacts = () => {
  if (addressId?.value) {
    createAddressDataStore.getAddressContacts(addressId.value, ContactsType.Customer);
  } else contactsCustomer.value = [];
};

const showCustomerSelectForm = computed(() => {
  return customers?.value && customers?.value?.length > 1;
});

const showCustomer = computed(() => {
  return customers?.value && !!customers?.value?.length;
});

const showTooltip = computed(() => {
  return isPrincipalLocked.value || isCompleteRoadOrder.value || isOrderSaved.value;
});

const isCustomerSectionDisabled = computed(() => {
  return isAirOrderFromQuote.value || isCompleteRoadOrder.value || isOrderSaved.value;
});

const tooltipText = computed(() => {
  if (isOrderSaved.value && !isPrincipalLocked.value && !isCompleteRoadOrder.value) {
    return t('labels.cannot_change_saved_order.text');
  }

  return isPrincipalLocked.value
    ? t('labels.cannot_edit_order.text')
    : t('labels.status_complete_tooltip.text');
});

onMounted(() => {
  getAddressContacts();
});

watch([addressId], () => {
  getAddressContacts();
});
</script>
