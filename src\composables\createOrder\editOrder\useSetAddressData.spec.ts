import { ConsigneeAddressType } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { loadingPoints } from '@/mocks/fixtures/loadingPoints';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { loadingPointAddress, orderAddress } from '@/store/sharedInitialStates';
import { Address, OrderAddress, OrderResponseBody } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { expect } from 'vitest';
import { nextTick } from 'vue';
import {
  getAddressFromOrderAddress,
  getBasicAddressFromOrderAddress,
  useSetAddressData,
} from './useSetAddressData';

describe('getAddressFromOrderAddress', () => {
  it('returns empty initial address if order address is undefined', () => {
    expect(getAddressFromOrderAddress()).toStrictEqual(orderAddress());
  });

  it('returns values of an order address as type of address', () => {
    const address: Address = addresses[0];

    const orderAddress: OrderAddress = {
      ...address,
      addressType: 'IM',
      neutralizeAddress: true,
      contact: { name: 'name' },
    };

    expect(getAddressFromOrderAddress(orderAddress)).toEqual(address);
  });

  it('returns values of an order address as type of address - ireland', () => {
    const { townCounty } = storeToRefs(useCreateOrderAddressesStore());
    const address: Address = addresses[0];
    address.countryCode = 'IE';
    address.city = 'Dublin';
    address.supplement = 'Supplement';

    const orderAddress: OrderAddress = {
      ...address,
      addressType: 'IM',
      neutralizeAddress: true,
      contact: { name: 'name' },
    };

    expect(getAddressFromOrderAddress(orderAddress)).toEqual(address);
    expect(townCounty.value?.label).toEqual('Dublin/Supplement');
    expect(townCounty.value?.data.dachserPlz).toEqual('');
  });
});

describe('getBasicAddressFromOrderAddress', () => {
  it('returns empty initial basic address if order address is undefined', () => {
    expect(getBasicAddressFromOrderAddress()).toStrictEqual(loadingPointAddress());
  });

  it('returns values of an order address as type of basic address', () => {
    const basicAddressMock: Address = loadingPoints[0];
    const orderAddress: OrderAddress = {
      ...basicAddressMock,
      name2: 'name2',
      supplement: 'supplement',
      addressType: 'IM',
      neutralizeAddress: true,
      contact: { name: 'name' },
    };

    expect(getBasicAddressFromOrderAddress(orderAddress)).toEqual(basicAddressMock);
  });

  it('sets contactDataCustomer for air orders', async () => {
    const editOrderData: OrderResponseBody = {
      orderType: 'AirExportOrder',
      customerContactData: { name: 'Different Name' },
    };

    useSetAddressData(editOrderData);

    await nextTick();
    const addressesStore = useCreateOrderAddressesStore();
    const { contactDataCustomer } = addressesStore;

    expect(contactDataCustomer.name).toEqual('Different Name');
  });

  it('sets differentConsigneeAddress correctly for road orders', async () => {
    const editOrderData: OrderResponseBody = {
      orderType: 'RoadCollectionOrder',
    };

    useSetAddressData(editOrderData);

    await nextTick();
    const addressesStore = useCreateOrderAddressesStore();
    const { consigneeAddressType } = addressesStore;

    expect(consigneeAddressType).toEqual(ConsigneeAddressType.PRINCIPALS_ADDRESS);
  });

  it('sets contactDataDelivery and differentConsigneeAddress correctly for road orders', async () => {
    const editOrderData: OrderResponseBody = {
      orderType: 'RoadCollectionOrder',
      differentConsigneeAddress: { city: 'Different City', name: 'Different Name' },
    };

    useSetAddressData(editOrderData);

    await nextTick();
    const addressesStore = useCreateOrderAddressesStore();
    const { differentConsigneeAddress } = addressesStore;

    expect(differentConsigneeAddress.address.name).toEqual('Different Name');
    expect(differentConsigneeAddress.address.city).toEqual('Different City');
  });
});
