<template>
  <div dfe-book-frontend class="label-print__hook">
    <div v-if="isLocaleLoaded && showStartPositionSection" class="mb-4">
      <h4 class="text-h4">{{ $t('labels.label_start_position.text') }}</h4>
      <div class="text-body-2 mt-4">{{ $t('messages.id6933.text') }}</div>
      <SelectFieldWithMenuIcons
        v-model="selectedItem"
        class="item-select | mt-4 w-240"
        :items="selectItems"
        :label="$t('labels.printing_start_position.text')"
        @update:model-value="recreatePdf"
      ></SelectFieldWithMenuIcons>
    </div>
    <PdfViewer v-if="pdf" :src="pdf" />
    <LoaderOverlay :model-value="loading" absolute contained />
  </div>
</template>

<script setup lang="ts">
import PdfViewer from '@/components/base/pdf/PdfViewer.vue';
import SelectFieldWithMenuIcons from '@/components/form/SelectFieldWithMenuIcons.vue';
import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import { useClient } from '@/composables/useClient';
import { useLocale } from '@/plugins/i18n';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { LabelPrintEvent } from '@/types/events';
import { PrintLabelStartPosition } from '@dfe/dfe-book-api';
import { DEFAULT_PREFERENCES } from '@dfe/dfe-frontend-client';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { client } = useClient();

const { t } = useI18n();
const preferences = usePreferences();
const isLocaleLoaded = ref(false);

onMounted(async () => {
  await useLocale(preferences.locale);
  isLocaleLoaded.value = true;
});

const labelPrintService = useLabelStore();

const showStartPositionSection = computed(
  () => preferences.printerPresetName.value === DEFAULT_PREFERENCES.printerPresetName,
);

type SelectItem = {
  title: string;
  value: string;
  icon: string;
};

const selectItems: SelectItem[] = [
  {
    title: t('labels.position_top_left.text'),
    value: PrintLabelStartPosition.TOP_LEFT,
    icon: '$custom_grid_topleft-16',
  },
  {
    title: t('labels.position_top_right.text'),
    value: PrintLabelStartPosition.TOP_RIGHT,
    icon: '$custom_grid_topright-16',
  },
  {
    title: t('labels.position_bottom_left.text'),
    value: PrintLabelStartPosition.BOTTOM_LEFT,
    icon: '$custom_grid_bottomleft-16',
  },
  {
    title: t('labels.position_bottom_right.text'),
    value: PrintLabelStartPosition.BOTTOM_RIGHT,
    icon: '$custom_grid_bottomright-16',
  },
];
const selectedItem = ref<SelectItem>(selectItems[0]);

const pdf = ref<string>('');
const pdfFileName = ref<string>();
const pdfModalHeadline = ref<string>();
const pdfOrderId = ref<number | number[]>();
const loading = ref(false);

const handleRenderLabelPreview = (event: LabelPrintEvent) => {
  if (event.file) pdf.value = event.file;
  if (event.fileName) pdfFileName.value = event.fileName;
  if (event.modalHeadline) pdfModalHeadline.value = event.modalHeadline;
  if (event.orderId) pdfOrderId.value = event.orderId;
};

client?.events.on('renderLabelPreview', handleRenderLabelPreview);

onBeforeUnmount(() => {
  client?.events.off('renderLabelPreview', handleRenderLabelPreview);
});

const recreatePdf = async () => {
  if (!pdfOrderId.value || (Array.isArray(pdfOrderId.value) && !pdfOrderId.value.length)) {
    return;
  }

  loading.value = true;

  const rawLabelPdf = await getRawLabelPdf(
    pdfOrderId.value,
    selectedItem.value.value as PrintLabelStartPosition,
  );

  if (!rawLabelPdf || !pdfFileName.value) {
    loading.value = false;
    return;
  }

  client?.events.emit('printLabels', {
    file: rawLabelPdf,
    fileName: pdfFileName.value,
    modalHeadline: pdfModalHeadline.value ?? '',
    orderId: pdfOrderId.value,
  });
  loading.value = false;
};

const getRawLabelPdf = async (
  orderId: number | number[],
  selectedStartPosition: PrintLabelStartPosition,
) => {
  if (Array.isArray(orderId)) {
    return await labelPrintService.fetchLabelsForBulkOrder(orderId, selectedStartPosition);
  } else {
    return (await labelPrintService.fetchLabelsForExistingOrder(orderId, selectedStartPosition))
      ?.orderLabel;
  }
};
</script>

<style lang="scss" scoped>
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';
@use '@/styles/overrides.scss' as overrides;

[dfe-book-frontend] {
  overflow: auto;
}
.w-240 {
  width: 240px;
}
</style>
