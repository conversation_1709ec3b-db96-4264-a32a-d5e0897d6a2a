<template>
  <div ref="airOrderContainer" tabindex="0">
    <div v-if="airOrderNavigationItems.length > 0">
      <MandatoryFieldsLabel />
      <FormSection
        v-if="isCustomersAvailable"
        ref="airOrderCustomers"
        v-model="valid.airOrderCustomers"
        class="nav-scroll-section"
      >
        <SectionCustomers />
      </FormSection>
      <FormSection
        ref="airOrderAddresses"
        v-model="valid.airOrderAddresses"
        class="nav-scroll-section"
        @input="updateAddressValidation"
      >
        <AddressesSectionWithHandOver />
      </FormSection>
      <FormSection
        ref="airOrderProducts"
        v-model="valid.airOrderProducts"
        class="nav-scroll-section"
      >
        <ProductsSection />
      </FormSection>
      <FormSection
        ref="airOrderIncoterms"
        v-model="valid.airOrderIncoterms"
        class="nav-scroll-section"
      >
        <SectionIncoterms />
      </FormSection>
      <FormSection ref="airOrderFreight" v-model="valid.airOrderFreight" class="nav-scroll-section">
        <SectionFreight />
      </FormSection>
      <FormSection
        ref="airOrderCollection"
        v-model="valid.airOrderCollection"
        class="nav-scroll-section"
      >
        <SectionCollectionAndDelivery />
      </FormSection>
      <FormSection
        ref="airOrderReferences"
        v-model="valid.airOrderReferences"
        class="nav-scroll-section"
      >
        <SectionOrderReferences />
      </FormSection>
      <FormSection
        ref="airOrderDocuments"
        v-model="valid.airOrderDocuments"
        class="nav-scroll-section"
      >
        <SectionDocuments />
      </FormSection>
    </div>
    <MainFooter @create-order-validate="validateForms" />
  </div>
</template>

<script setup lang="ts">
import AddressesSectionWithHandOver from '@/components/createOrder/formSectionAddresses/handoverSelection/AddressesSectionWithHandOver.vue';
import SectionCollectionAndDelivery from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import SectionCustomers from '@/components/createOrder/formSectionCustomer/collectingOrder/CardMain.vue';
import MainFooter from '@/components/createOrder/formSectionFooter/MainFooter.vue';
import SectionFreight from '@/components/createOrder/formSectionFreight/CardMain.vue';
import SectionIncoterms from '@/components/createOrder/formSectionIncoTerms/CardMain.vue';
import SectionOrderReferences from '@/components/createOrder/formSectionOrderReferences/CardMain.vue';
import ProductsSection from '@/components/createOrder/formSectionProducts/ProductsSection.vue';
import SectionDocuments from '@/components/createOrder/sharedComponents/formSectionDocuments/CardMain.vue';
import FormSection from '@/components/form/FormSection.vue';
import MandatoryFieldsLabel from '@/components/MandatoryFieldsLabel.vue';
import { ValidationResult } from '@/composables/createOrder/useCreateOrderValidation';
import { useAirDeliveryProducts } from '@/composables/data/useAirDeliveryProducts';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import { useValidationDataStore } from '@/store/validation';
import { HandOverSelection } from '@/types/hand-over';
import { identity } from 'lodash';
import { storeToRefs } from 'pinia';
import { ComponentPublicInstance, computed, ref, watchEffect } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const emit = defineEmits(['airOrderNavigationItems']);

const addressStore = useCreateOrderAddressesStore();
const { shipperHandOverSelection } = storeToRefs(addressStore);
const createOrderDataStore = useCreateOrderDataStore();
const { isCustomersAvailable } = storeToRefs(createOrderDataStore);

const airOrderCustomers = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderAddresses = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderFreight = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderDocuments = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderProducts = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderIncoterms = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderReferences = ref<ComponentPublicInstance<typeof FormSection> | null>(null);
const airOrderCollection = ref<ComponentPublicInstance<typeof FormSection> | null>(null);

const validationStore = useValidationDataStore();
const createAddressDataStore = useCreateAddressDataStore();
storeToRefs(createAddressDataStore);

const { formValidationSectionsAir: valid, getFormSectionsValid } =
  storeToRefs(useValidationDataStore());

const { airDeliveryProducts } = useAirDeliveryProducts();
const airOrderNavigationItems = computed(() =>
  [
    isCustomersAvailable.value
      ? {
          ref: airOrderCustomers,
          text: t('labels.principal_title.text'),
          error: !valid.value.airOrderCustomers,
        }
      : undefined,
    {
      ref: airOrderAddresses,
      text: t('labels.addresses_title.text'),
      error: !valid.value.airOrderAddresses || hasEmbargo.value,
    },
    airDeliveryProducts.value?.length
      ? {
          ref: airOrderProducts,
          text: t('labels.products_label.text'),
          error: !valid.value.airOrderProducts,
        }
      : undefined,
    {
      ref: airOrderIncoterms,
      text: t('labels.incoterms_title.text'),
      error: !valid.value.airOrderIncoterms,
    },
    {
      ref: airOrderFreight,
      text: t('labels.order_line_title.text'),
      error: !valid.value.airOrderFreight,
    },
    {
      ref: airOrderCollection,
      text:
        shipperHandOverSelection.value.selection === HandOverSelection.port
          ? t('labels.airport_delivery.text')
          : t('labels.collection_title.text'),
      error: !valid.value.airOrderCollection,
    },
    {
      ref: airOrderReferences,
      text: t('labels.order_references.text'),
      error: !valid.value.airOrderReferences,
    },
    {
      ref: airOrderDocuments,
      text: t('labels.documents_label.text'),
      error: !valid.value.airOrderDocuments || hasCommercialInvoiceError.value,
    },
  ].filter(identity),
);

const validateForms = async (validate: (value: ValidationResult) => void) => {
  await Promise.all(
    airOrderNavigationItems.value.map(async (item) => {
      return await item?.ref?.value?.validate();
    }),
  );

  validate(getFormSectionsValid.value);
};

const { hasEmbargo } = storeToRefs(useEmbargoStore());
const errorBannerStore = useErrorBanner();
const { hasCommercialInvoiceError } = storeToRefs(errorBannerStore);
watchEffect(() => {
  emit('airOrderNavigationItems', airOrderNavigationItems.value);
});

let active = false;
function updateAddressValidation(isValid: boolean) {
  if (!active) {
    active = true;
    return;
  }
  const addressStore = useCreateOrderAddressesStore();
  const createOrderFormStore = useCreateOrderFormStore();
  const { isEditMode } = storeToRefs(createOrderFormStore);
  const { isShipperContactDataSet, isConsigneeContactDataSet } = storeToRefs(addressStore);
  validationStore.formValidationSectionsAir.airOrderAddresses = isEditMode.value
    ? isValid
    : isValid && isShipperContactDataSet.value && isConsigneeContactDataSet.value;
}
</script>
