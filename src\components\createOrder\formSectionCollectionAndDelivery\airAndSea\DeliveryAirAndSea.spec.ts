import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { storeToRefs } from 'pinia';
import DeliveryAirAndSea from '@/components/createOrder/formSectionCollectionAndDelivery/airAndSea/DeliveryAirAndSea.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes } from '@/enums';
import FormCollection from '@/components/createOrder/formSectionCollectionAndDelivery/FormCollection.vue';

describe('Delivery Air and Sea component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(DeliveryAirAndSea);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('should show form-collection by default', async () => {
    expect(wrapper.findComponent(FormCollection).exists()).toBeTruthy();
  });

  it("should show request arrangement info, when user toggles on 'Request arrangement' switch (SEA)", async () => {
    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();
    const orderStore = useCreateOrderFormStore();
    orderStore.orderType = OrderTypes.SeaExportOrder;

    const { requestArrangement } = storeToRefs(createOrderFormCollectionAndDeliveryStore);

    requestArrangement.value = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'form-collection' }).exists()).toBeFalsy();
    expect(wrapper.find('.request-arrangement-info').exists()).toBeTruthy();
    expect(wrapper.find('.request-arrangement-info').text()).toContain('port');
  });

  it("should show request arrangement info, when user toggles on 'Request arrangement' switch (AIR)", async () => {
    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();
    const orderStore = useCreateOrderFormStore();
    orderStore.orderType = OrderTypes.AirExportOrder;

    const { requestArrangement } = storeToRefs(createOrderFormCollectionAndDeliveryStore);

    requestArrangement.value = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'form-collection' }).exists()).toBeFalsy();
    expect(wrapper.find('.request-arrangement-info').exists()).toBeTruthy();
    expect(wrapper.find('.request-arrangement-info').text()).toContain('airport');
  });

  it("should empty customCollectionInfo , when user toggles on 'Request arrangement' switch", async () => {
    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();

    const { requestArrangement } = storeToRefs(createOrderFormCollectionAndDeliveryStore);

    requestArrangement.value = true;

    await wrapper.vm.$nextTick();

    expect(createOrderFormCollectionAndDeliveryStore.customCollectionTimeSlot.collectionDate).toBe(
      '',
    );
    expect(createOrderFormCollectionAndDeliveryStore.customCollectionTimeSlot.to).toBe('');
    expect(createOrderFormCollectionAndDeliveryStore.customCollectionTimeSlot.from).toBe('');
  });
});
