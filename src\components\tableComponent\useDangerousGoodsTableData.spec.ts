import { DfeTableOptions, DfeTableSortDirection } from '@/components/tableComponent/DfeTableTypes';
import { useDangerousGoodsTableData } from '@/components/tableComponent/useDangerousGoodsTableData';

const testTableOptions: DfeTableOptions = {
  totalItems: 0,
  itemsPerPage: 10,
  currentPage: 1,
  totalPages: 0,
  allSelectable: false,
  itemsSelectable: true,
  singleSelect: true,
  firstColumnSticky: false,
  disablePagination: true,
  sortBy: {
    key: 'lastModified',
    order: DfeTableSortDirection.DESC,
  },
};

describe('DfeDataTable', () => {
  it('returns correct table options', () => {
    const { tableOptions } = useDangerousGoodsTableData();

    expect(tableOptions.value).toEqual(testTableOptions);
  });

  it('returns correct table headers', () => {
    const { tableHeaders } = useDangerousGoodsTableData();

    expect(tableHeaders.value.length).toEqual(8);
  });
});
