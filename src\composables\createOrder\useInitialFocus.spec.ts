import { defineComponent, Ref, ref } from 'vue';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createClientMock } from '@test/util/mock-client';
import useInitialFocus from '@/composables/createOrder/useInitialFocus';
import { shallowMount, type VueWrapper } from '@vue/test-utils';
import { ClientKey } from '@/types/client';
import { Segment } from '@dfe/dfe-book-api';

describe('useInitialFocus', () => {
  let wrapper: VueWrapper;
  let initialFocusRef: Ref<{ focus: () => void } | null>;
  const client = createClientMock();

  beforeEach(() => {
    initialFocusRef = ref(null);
    vi.useFakeTimers();

    wrapper = shallowMount(
      defineComponent({
        setup() {
          useInitialFocus(initialFocusRef);
        },
        template: `<div></div>`,
      }),
      {
        global: {
          provide: {
            [ClientKey as symbol]: client,
          },
        },
      },
    );
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.restoreAllMocks();
    wrapper.unmount();
  });

  it('should focus the ref element when the order form is mounted', async () => {
    const mockElement = { focus: vi.fn() };
    initialFocusRef.value = mockElement;

    await wrapper.vm.$nextTick();

    vi.runAllTimers();

    expect(mockElement.focus).toHaveBeenCalledTimes(1);
  });

  it('should focus the ref element when the createOrder event is triggered', async () => {
    const mockElement = { focus: vi.fn() };
    initialFocusRef.value = mockElement;

    await wrapper.vm.$nextTick();

    client.events.emit('createOrder', { transportType: Segment.ROAD });

    vi.runAllTimers();

    expect(mockElement.focus).toHaveBeenCalledTimes(2);
  });

  it('should focus the ref element when the editOrder event is triggered', async () => {
    const mockElement = { focus: vi.fn() };
    initialFocusRef.value = mockElement;

    await wrapper.vm.$nextTick();

    client.events.emit('editOrder', { customerNumber: 1, orderId: 1 });

    vi.runAllTimers();

    expect(mockElement.focus).toHaveBeenCalledTimes(2);
  });

  it('should remove the event listeners on unmount', () => {
    vi.spyOn(client.events, 'off');

    wrapper.unmount();

    expect(client.events.off).toHaveBeenCalledWith('createOrder', expect.any(Function));
    expect(client.events.off).toHaveBeenCalledWith('editOrder', expect.any(Function));
  });
});
