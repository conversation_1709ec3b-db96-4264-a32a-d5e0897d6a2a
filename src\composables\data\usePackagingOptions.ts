import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';

export const usePackagingOptions = () => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { orderType } = storeToRefs(formStore);
  return useCustomerQuery('packagingOptions', api.book.customers.getCustomerPackagingOptions, {
    queryKey: [orderType],
  });
};
