@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

$primary: (
  'base': vars.$color-base-blue-500,
);
$grey: (
  'base': vars.$color-base-grey-500,
  'lighten-1': vars.$color-base-grey-400,
  'lighten-2': vars.$color-base-grey-300,
  'lighten-3': vars.$color-base-grey-200,
  'lighten-4': vars.$color-base-grey-100,
  'lighten-5': vars.$color-base-grey-50,
  'darken-1': vars.$color-base-grey-600,
  'darken-2': vars.$color-base-grey-700,
  'darken-3': vars.$color-base-grey-800,
  'darken-4': vars.$color-base-grey-900,
);

$red: (
  'base': vars.$color-base-red-500,
);

$green: (
  'base': vars.$color-base-green-500,
);

$colors: (
  'primary': $primary,
  'grey': $grey,
);
