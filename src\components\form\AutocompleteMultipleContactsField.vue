<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VAutocomplete
      :id="id"
      v-model:search="searchText"
      v-model="computedValue"
      :hide-no-data="hideNoData"
      :items="contacts"
      :placeholder="placeholder"
      :item-title="itemTextKey"
      :item-value="itemValueKey"
      :single-line="true"
      variant="outlined"
      item-props.color="grey darken-4"
      :menu-props="{ scrollStrategy: 'close', attach: $appRoot, maxWidth: '100%' }"
      bg-color="white"
      :no-filter="staticMenu || showLoader"
      hide-details="auto"
      :return-object="true"
      :rules="rules"
      @focus="onFocus"
      @update:search="onSearchInput"
      @update:model-value="onChange"
      @blur="updateValue"
    >
      <template v-if="contacts.length > 0" #prepend-item>
        <p class="text-caption ml-3 mt-2">
          {{ t('labels.contacts_from_address_book.text') }}
        </p>
      </template>
      <template v-if="!showLoader" #item="{ item, props: slotProps }">
        <VListItem v-bind="slotProps" title="">
          <VListItemTitle v-if="item" class="text-wrap">
            <!-- eslint-disable vue/no-v-html -->
            <div class="flex-container">
              <div
                v-if="item.raw.name"
                class="text-label-1 mb-1"
                v-html="setHighlight(displayName(item.raw))"
              ></div>
              <div v-if="item.raw.isMainContact" class="text-label-1 mb-1 text-grey-darken-2">
                {{ '(' + t('labels.default_contact.text') + ')' }}
              </div>
            </div>
            <div v-if="item.raw.email" class="text-body-3 text-grey-darken-2">
              {{ t('labels.contact_email.text') + ': ' + (item.raw.email ?? '') }}
            </div>
            <div v-if="item.raw.telephone" class="text-body-3 text-grey-darken-2">
              {{ t('labels.phone_number.text') + ': ' + (item.raw.telephone ?? '') }}
            </div>
            <div v-if="item.raw.mobile" class="text-body-3 text-grey-darken-2">
              {{ t('labels.mobile_number.text') + ': ' + (item.raw.mobile ?? '') }}
            </div>
            <!--eslint-enable-->
          </VListItemTitle>
        </VListItem>
      </template>
      <template v-else #item>
        <VListItem class="d-flex justify-center align-center">
          <progress-circular color="primary" size="24" indeterminate />
        </VListItem>
      </template>
      <template #no-data>
        <div v-data-test="'no-data-slot'" class="text-body-2 text-grey-500 px-3 py-2">
          {{ $t('labels.no_matching_results.text') }}
        </div>
      </template>
    </VAutocomplete>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, toRefs, watch } from 'vue';
import { createUuid } from '@/utils/createUuid';
import useHighlight from '@/composables/createOrder/useHighlight';
import { SanitizeFallback, SanitizeKey } from '@/types/sanitize';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import { useDebouncedLoader } from '@/composables/useDebouncedLoader';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import ProgressCircular from '@/components/ProgressCircular.vue';
import { ContactData } from '@dfe/dfe-address-api';
import { useSearchContact } from '@/composables/createOrder/addresses/useSearchContact';
import { isContact } from '@/utils/address/isContact';
import { isObject } from 'lodash';

interface Props {
  items: ContactData[] | undefined;
  itemText?: string;
  itemValue?: string;
  label?: TranslateResult;
  required?: boolean;
  staticMenu?: boolean;
  loading?: boolean;
  showLoaderAfterMs?: number;
  placeholder?: TranslateResult;
  rules?: (ValidationRule<string> | ValidationRule<ContactData>)[];
  hideNoData?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  staticMenu: false,
  loading: false,
  showLoaderAfterMs: 500,
  returnObject: true,
  itemText: '',
  itemValue: '',
  label: '',
  placeholder: '',
  rules: () => [],
});

const contact = defineModel<ContactData>();

const updateValue = () => {
  contact.value = { ...contact.value, name: searchTerm.value };
};

const { searchTerm, contacts, triggerForceSearch } = useSearchContact({ contacts: props.items });

const isSearchPending = ref(false);
const { loading, showLoaderAfterMs } = toRefs(props);
const showLoader = useDebouncedLoader(loading, showLoaderAfterMs);

defineEmits(['focus', 'search-input']);

const { t } = useI18n();

const sanitize = inject(SanitizeKey, SanitizeFallback);
const id = `autocomplete-${createUuid()}`;
const searchText = ref('');

const onChange = (value: ContactData | string | null) => {
  if (isContact(value)) contact.value = value;
};

const onFocus = () => {
  triggerForceSearch();
};

const onSearchInput = (searchString: string | null) => {
  searchTerm.value = searchString ?? '';
};

const itemTextKey = computed(() => props.itemText ?? 'text');
const itemValueKey = computed(() => props.itemValue ?? 'value');
const computedValue = computed(() => {
  if (isObject(contact.value) && contact.value.name === '') {
    return null;
  }
  return contact.value;
});
const setHighlight = (text: string) => {
  return sanitize(useHighlight(text, searchText.value), {
    allowedAttributes: { mark: ['class'] },
  });
};

const displayName = (item: ContactData) => {
  return item.name ?? '';
};

watch(loading, (isLoading) => {
  if (!isLoading) {
    isSearchPending.value = false;
  }
});
</script>

<style lang="scss" scoped>
:deep(.v-autocomplete__menu-icon) {
  display: none !important;
}

.flex-container {
  display: flex;
  align-items: center;
}
</style>
