import UploadList from '@/components/createOrder/sharedComponents/formSectionDocuments/documentUploader/UploadList.vue';
import { extensions } from '@/mocks/fixtures/extensions';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { mockServer } from '@/mocks/server';
import { documentsResponse } from '@/mocks/fixtures/documents';
import type { StoreDocument } from '@/store/createOrder/orderDocuments';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes, UploadStatus } from '@/enums';
import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { expect } from 'vitest';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { ref } from 'vue';

const props = {
  maxSize: 0,
  documentExtensions: extensions,
};

describe('Sections Documents UploadList component', () => {
  let wrapper: VueWrapper;
  let cdinvDocument: StoreDocument;
  let processedDocument: StoreDocument;
  let documentToDelete: StoreDocument;
  let documentWithCriticalError: StoreDocument;
  let documentStore: ReturnType<typeof useCreateOrderDocumentsStore>;

  beforeAll(() => {
    documentStore = useCreateOrderDocumentsStore();
    cdinvDocument = {
      documentId: 111,
      orderId: undefined,
      documentTypeId: 123,
      documentType: 'CDINV',
      documentName: 'invoice.xlsx',
      extension: 'pdf',
      startProcessing: true,
      file: new File([''], 'test.pdf', { type: 'application/pdf' }),
    };

    documentToDelete = {
      documentId: 111,
      orderId: 1,
      documentTypeId: 123,
      documentType: 'edn',
      documentName: 'invoice.xlsx',
      extension: 'pdf',
      file: new File([''], 'test.pdf', { type: 'application/pdf' }),
    };

    processedDocument = {
      documentId: 111,
      orderId: undefined,
      documentTypeId: 123,
      documentType: 'CDINV',
      documentName: 'invoice.xlsx',
      extension: 'pdf',
      documentProcessed: true,
      file: new File([''], 'test.pdf', { type: 'application/pdf' }),
    };

    documentWithCriticalError = {
      documentId: 111,
      orderId: undefined,
      documentTypeId: 123,
      documentType: 'CDINV',
      documentName: 'invoice.xlsx',
      extension: 'pdf',
      documentProcessed: true,
      file: undefined,
      uploadStatus: UploadStatus.FileMalicious,
    };

    mockServer({
      environment: 'test',
      fixtures: {
        documentsResponse,
      },
    });
  });

  beforeEach(() => {
    vi.mock('@/composables/data/useDocumentTypes', () => ({
      useDocumentTypes: () => ({
        data: ref([
          {
            category: 'Order',
            description: 'Electronic Delivery Note',
            supportedExtensions: [
              {
                label: 'pdf',
                description: 'application/pdf',
              },
            ],
            type: 'edn',
            typeId: 59,
          },
          {
            category: 'Dangerous goods documents',
            description: 'Dangerous Goods Note (DGN)',
            supportedExtensions: [
              {
                label: 'pdf',
                description: 'application/pdf',
              },
            ],
            type: 'GGETC',
            typeId: 42,
          },
        ]),
      }),
    }));
    wrapper = shallowMount(UploadList, {
      props,
    });
  });

  it('shows dfeBanner for commercial invoice necessary', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.AirExportOrder;
    const errorBannerStore = useErrorBanner();
    errorBannerStore.validationErrors.push({
      field: 'orderDocuments',
      errorType: 'REQUIRED_FIELD_MISSING',
      description: 'test',
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(DfeBanner).exists()).toBe(true);
  });

  it('shows dfeBanner for custom documents - ROAD', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.RoadCollectionOrder;
    documents.value = [cdinvDocument];

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-custom-documents-banner]').exists()).toBe(true);
  });

  it('should not show dfeBanner for custom documents - AIR', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.AirExportOrder;
    documents.value = [cdinvDocument];

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-custom-documents-banner]').exists()).toBe(false);
  });

  it('should not show dfeBanner for custom documents - SEA', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.SeaExportOrder;
    documents.value = [cdinvDocument];

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-custom-documents-banner]').exists()).toBe(false);
  });

  it('should disable document category autocomplete when document is processed', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.AirExportOrder;
    documents.value = [processedDocument];

    await wrapper.vm.$nextTick();

    expect(wrapper.find("[data-test='book-delete-document']").attributes('disabled')).toBe('true');
  });

  it('should remove document from document list', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.AirExportOrder;
    documents.value = [cdinvDocument, documentToDelete];

    await wrapper.vm.$nextTick();

    await wrapper.find('[data-test=book-delete-document]').trigger('click');

    expect(documents.value).toEqual([]);
  });

  it('should show error banner when document has critical error', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.RoadCollectionOrder;
    documents.value = [documentWithCriticalError];

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-critical-error-title]').exists()).toBe(true);
  });

  it('should reset delivery note required fields if there is no EDN document', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);
    const { deliveryNoteNumberRequiredFields } = storeToRefs(
      useCreateOrderOrderReferencesFormStore(),
    );

    orderType.value = OrderTypes.AirExportOrder;
    documents.value = [documentToDelete];

    await wrapper.vm.$nextTick();

    expect(deliveryNoteNumberRequiredFields.value[0].required).toBe(true);

    await wrapper.find('[data-test=book-delete-document]').trigger('click');

    expect(deliveryNoteNumberRequiredFields.value[0].required).toBe(false);
  });

  it('should remove document from document list', async () => {
    const { orderType, customTypeValidationError } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    customTypeValidationError.value = true;

    orderType.value = OrderTypes.AirExportOrder;
    documents.value = [cdinvDocument, documentToDelete];

    await wrapper.vm.$nextTick();

    await wrapper.find('[data-test=book-delete-document]').trigger('click');

    expect(documents.value).toEqual([]);
    expect(customTypeValidationError.value).toBe(false);
  });

  it('filter documents if we have no dangerous good  - ROAD', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const { documents } = storeToRefs(documentStore);

    orderType.value = OrderTypes.RoadCollectionOrder;
    documents.value = [cdinvDocument];

    await wrapper.vm.$nextTick();

    vi.doMock('@/composables/data/useCustomerSettings', () => ({
      useCustomerSettings: () => ({
        data: ref({
          dangerousGoods: false,
        }),
      }),
    }));

    const categories = wrapper.findComponent(AutocompleteField);
    expect(categories.props('items')).toHaveLength(3);
  });
});
