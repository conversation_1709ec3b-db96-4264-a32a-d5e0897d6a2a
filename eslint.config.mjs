import dataTestPrefix from '@dfe/eslint-plugin-data-test-prefix';
import globals from 'globals';
import js from '@eslint/js';
import pluginVue from 'eslint-plugin-vue';
import pluginVuetify from 'eslint-plugin-vuetify';
import eslintConfigPrettier from 'eslint-config-prettier/flat';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';

export default [
  js.configs.recommended,
  ...defineConfigWithVueTs(pluginVue.configs['flat/recommended'], vueTsConfigs.recommended),
  eslintConfigPrettier,
  ...pluginVuetify.configs['flat/base'],
  {
    languageOptions: {
      ecmaVersion: 2020,
      globals: {
        ...globals.node,
        'vue/setup-compiler-macros': true,
        ScrollOptions: 'readonly',
      },
    },
    rules: {
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'vue/no-required-prop-with-default': 'off',
    },
  },
  {
    files: ['**/*.vue'],
    plugins: { dataTestPrefix },
    rules: {
      'dataTestPrefix/use-directive': 2,
    },
  },
  {
    files: ['**/__tests__/*.{j,t}s?(x)', '**/tests/unit/**/*.spec.{j,t}s?(x)'],
    env: {},
  },
  {
    ignores: ['webpack', 'vue.config.js', 'dist', 'coverage', '!.lintstagedrc.js'],
  },
];
