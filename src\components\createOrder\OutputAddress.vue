<template>
  <div v-if="address" class="output-address pb-4">
    <div class="d-flex justify-space-between text-body-2">
      <div>
        <h5 v-if="header" class="text-h5 mb-4">{{ header }}</h5>
        <div>
          <div v-if="address.individualId" class="text-grey text-grey-darken-1 mb-2">
            {{ address.individualId }}
          </div>
          <div v-if="address.name" :class="{ 'text-label-2': boldTitle }">
            {{ address.name }}
          </div>
          <div v-if="address.name2">{{ address.name2 }}</div>
          <div v-if="address.name3">{{ address.name3 }}</div>
          <div>{{ address.street }}</div>
          <div v-if="address.street2">{{ address.street2 }}</div>
          <div>
            {{ address.countryCode }} {{ address.postcode }}
            <template v-if="address.city">{{ useUpperFirst(address.city) }}</template>
            <template v-if="isIrelandCountryCode(address.countryCode ?? '')"
              >, {{ useUpperFirst(address.supplement) }}</template
            >
          </div>
        </div>
      </div>
      <div>
        <div class="d-flex align-center ga-2">
          <DfeIconButton
            v-if="isEditable"
            :size="ComponentSize.DEFAULT"
            :color="ColorVariants.NEUTRAL"
            :tooltip="t('labels.edit_label.text')"
            :filled="false"
            @click="editAddress"
          >
            <EditIcon />
          </DfeIconButton>
          <DfeIconButton
            v-if="isDeletable"
            :size="ComponentSize.DEFAULT"
            :color="ColorVariants.NEUTRAL"
            :tooltip="t('labels.remove_label.text')"
            :filled="false"
            @click="deleteAddress"
          >
            <DeleteIcon />
          </DfeIconButton>
        </div>
      </div>
    </div>

    <div v-if="contactData && contactData.name && !isRoadForwardingOrder">
      <VDivider class="my-4"></VDivider>
      <ButtonWithMenu :label="contactData.name" :header="t('labels.contact_details.text')">
        <template #chipIcon>
          <MaterialSymbol size="16" color="grey-darken-4" class="chip-icon mr-1">
            <PersonIcon />
          </MaterialSymbol>
        </template>

        <div v-if="contactData.name" class="text-label-2">
          {{ t('labels.contact_name.text') }}
          <span class="d-block text-body-2">
            {{ contactData.name }}
          </span>
        </div>

        <div v-if="contactData.email" class="text-label-2 mt-4">
          {{ t('labels.email_label.text') }}
          <span class="d-block text-body-2">
            {{ contactData.email }}
          </span>
        </div>

        <div v-if="contactData.telephone" class="text-label-2 mt-4">
          {{ t('labels.phone_number.text') }}
          <span class="d-block text-body-2">
            {{ contactData.telephone }}
          </span>
        </div>

        <div v-if="contactData.mobile" class="text-label-2 mt-4">
          {{ t('labels.mobile_number.text') }}
          <span class="d-block text-body-2">
            {{ contactData.mobile }}
          </span>
        </div>
      </ButtonWithMenu>
    </div>

    <div v-if="showEmbargoBanner">
      <VAlert type="error" class="mt-4 mb-0">
        <h5 class="text-h5">{{ t('labels.transport_not_possible.text') }}</h5>
      </VAlert>
    </div>

    <div v-if="isAddressIncomplete && !showEmbargoBanner" class="incomplete-address">
      <VAlert :type="alertType" class="mt-4 mb-0">
        <h5 class="text-h5">
          {{ alertText }}
        </h5>
      </VAlert>
    </div>
  </div>
</template>

<script setup lang="ts">
import ButtonWithMenu from '@/components/base/ButtonWithMenu.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { Address, ContactData } from '@dfe/dfe-address-api';
import { isIrelandCountryCode, useUpperFirst } from '@dfe/dfe-frontend-composables';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import EditIcon from '@dfe/dfe-frontend-styles/assets/icons/edit-24px.svg';
import PersonIcon from '@dfe/dfe-frontend-styles/assets/icons/person-24px.svg';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';

const { t } = useI18n();
const embargoStore = useEmbargoStore();
const { hasEmbargo } = storeToRefs(embargoStore);

interface Props {
  header: string | TranslateResult;
  address: Partial<Address>;
  boldTitle: boolean;
  isEditable: boolean;
  isDeletable: boolean;
  contactData: ContactData;
  isAddressIncomplete: boolean;
  isCurrentlyFinalAddress: boolean | undefined;
}

const props = withDefaults(defineProps<Props>(), {
  header: '',
  address: undefined,
  boldTitle: false,
  isEditable: false,
  isDeletable: false,
  contactData: undefined,
  isAddressIncomplete: false,
  isCurrentlyFinalAddress: false,
});

const emit = defineEmits(['edit-address', 'delete-address']);

const { isValidationTriggered } = storeToRefs(useCreateOrderFormStore());
const createOrderFormStore = useCreateOrderFormStore();
const { isRoadForwardingOrder } = storeToRefs(createOrderFormStore);

const editAddress = () => {
  emit('edit-address');
};
const deleteAddress = () => {
  emit('delete-address');
};

const showEmbargoBanner = computed(() => hasEmbargo?.value && props.isCurrentlyFinalAddress);

const alertType = computed(() => (isValidationTriggered?.value ? 'error' : 'warning'));

const alertText = computed(() =>
  isValidationTriggered?.value
    ? t('labels.address_input_incomplete.text')
    : t('labels.edit_and_complete_address.text'),
);
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

:deep(.v-menu__content) {
  border-radius: 8px;
}
</style>
