<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VSelect
      :id="id"
      v-model="selectedItem"
      class="select"
      bg-color="white"
      :items="items"
      :item-title="itemTitle"
      :item-value="itemValue"
      :placeholder="placeholder"
      :hint="hint"
      :persistent-hint="persistentHint"
      :readonly="readonly"
      single-line
      variant="outlined"
      density="compact"
      hide-details="auto"
      item-props.color="grey darken-4"
      return-object
      :rules="validationRules"
      :disabled="disabled"
      :menu-props="{ scrollStrategy: 'close', attach: $appRoot }"
    >
      <template #item="{ props: itemProps, item }">
        <VListItem
          v-bind="itemProps"
          :prepend-icon="item.raw.icon"
          active-class="bg-blue-50 text-grey-900"
        ></VListItem>
      </template>
      <template #prepend-inner>
        <VIcon :icon="selectedItemIcon"></VIcon>
      </template>
    </VSelect>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { useValidationRules, withMessage } from '@/composables/form/useValidationRules';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import type { SelectItemKey } from '@/types/vuetify';
import { createUuid } from '@/utils/createUuid';

interface Item {
  icon: string;
  [key: string]: unknown;
}

interface Props {
  items: Item[];
  itemValue?: SelectItemKey;
  itemTitle?: SelectItemKey;
  label?: TranslateResult;
  placeholder?: TranslateResult;
  hint?: string;
  persistentHint?: boolean;
  required?: boolean;
  readonly?: boolean;
  disabled?: boolean;
  rules?: ValidationRule[];
}
const props = withDefaults(defineProps<Props>(), {
  itemValue: 'value',
  itemTitle: 'title',
  label: undefined,
  placeholder: undefined,
  hint: undefined,
  rules: undefined,
});

const { t } = useI18n();

const selectedItem = defineModel<Item>();
const selectedItemIcon = computed(() => selectedItem.value?.icon);

const id = `select-field-${createUuid()}`;

const validationRules = computed(() => [
  ...(props.required
    ? [withMessage(useValidationRules.required, t('labels.validation_select_input_required.text'))]
    : []),
  ...(props.rules ?? []),
]);
</script>

<style scoped lang="scss">
:deep(.v-list-item__prepend .v-list-item__spacer) {
  width: 0px !important;
}
:deep(.v-field__prepend-inner > .v-icon) {
  margin-right: 4px !important;
}
</style>
