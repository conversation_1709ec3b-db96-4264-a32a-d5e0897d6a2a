import { shallowMount } from '@vue/test-utils';
import TextField from '@/components/form/TextField.vue';
import { beforeEach } from 'vitest';
import type { TestUtils } from '../../../test/test-utils';
import FormLabel from '@/components/form/FormLabel.vue';

const label = 'Label';

describe('TextField component', () => {
  let wrapper: TestUtils.VueWrapper<typeof TextField>;

  beforeEach(() => {
    wrapper = shallowMount(TextField);
  });

  it('emits input event on input', () => {
    const textField = wrapper.findComponent({ name: 'v-text-field' });
    textField.vm.$emit('update:modelValue', 'foo bar');

    const event = wrapper.emitted('update:modelValue');
    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual(['foo bar']);
  });

  it('displays label depending on prop', async () => {
    await wrapper.setProps({ label });

    const formLabel = wrapper.getComponent(FormLabel);
    expect(formLabel.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.getComponent(FormLabel).props().required).toBe(true);
  });
});
