import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import RadioField from '@/components/form/RadioField.vue';

const label = 'Label';

describe('RadioField component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(RadioField);
  });

  it('shows label depending on props', async () => {
    expect(wrapper.find('label').text()).toHaveLength(0);

    await wrapper.setProps({ label });
    expect(wrapper.find('label').text()).toEqual(label);
  });

  it('shows tooltip on mouseover if available', async () => {
    await wrapper.trigger('mouseover');
    expect(wrapper.findComponent({ name: 'v-tooltip' }).exists()).toBe(false);

    await wrapper.setProps({ tooltip: 'foo bar' });
    await wrapper.trigger('mouseover');
    expect(wrapper.findComponent({ name: 'v-tooltip' }).exists()).toBe(true);
  });
});
