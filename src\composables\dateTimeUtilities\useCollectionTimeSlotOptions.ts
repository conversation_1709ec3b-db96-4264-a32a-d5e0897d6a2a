import type { Ref } from 'vue';
import { CollectionTimeSlots as TimeSlots } from '@/enums';
import type { CollectionTimeSlots } from '@dfe/dfe-book-api';
import type { TimeFormat } from '@/types/createOrder';
import { useUTCTimeFromDate } from './useTimeFromDate';
import { i18n } from '@/plugins/i18n';

function useCollectionTimeSlotOptions(
  timeSlots: Ref<CollectionTimeSlots | undefined>,
  timeFormat: TimeFormat,
) {
  const { t } = i18n.global;
  return [
    ...(timeSlots.value?.map((dates) => ({
      ...dates,
      text:
        dates.from && dates.to
          ? [
              useUTCTimeFromDate(dates.from, timeFormat),
              useUTCTimeFromDate(dates.to, timeFormat),
            ].join(' - ')
          : '',
    })) ?? []),
    {
      from: TimeSlots.Custom,
      to: TimeSlots.Custom,
      text: t('labels.other_label.text'),
    },
  ];
}

export default useCollectionTimeSlotOptions;
