import type { ComponentPublicInstance, Ref } from 'vue';
import { unref } from 'vue';

type Item = Ref<ComponentPublicInstance<HTMLElement> | HTMLElement | null>;
type Items = Item[];

interface Options {
  top: boolean;
  bottom: boolean;
  offset?: {
    top?: number;
    bottom?: number;
  };
  threshold?: number;
}

function getElementBounding(item: Item) {
  const element: HTMLElement | ComponentPublicInstance | null = unref(item);
  const target = element instanceof HTMLElement ? element : element?.$el;

  return target?.getBoundingClientRect();
}

function getActiveIndex(items: Items, offset = 0, threshold = 0) {
  const map = items.map((item) => {
    return (getElementBounding(item)?.bottom ?? 0) - offset >= threshold;
  });
  return map.findIndex((value) => value);
}

function useFormNavigationActiveIndex(items?: Items, options?: Options) {
  if (options?.top || !items?.length) {
    return 0;
  } else if (options?.bottom) {
    return items.length - 1;
  }
  return getActiveIndex(items, options?.offset?.top, options?.threshold) ?? 0;
}

export default useFormNavigationActiveIndex;
