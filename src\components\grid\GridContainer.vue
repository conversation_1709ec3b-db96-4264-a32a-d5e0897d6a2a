<template>
  <VContainer fluid class="grid-container">
    <slot />
  </VContainer>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';

.grid-container.container {
  @media #{map.get(settings.$display-breakpoints, 'sm-and-up')} {
    padding: vars.$spacing-grid-container-sm;
  }
  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    padding: vars.$spacing-grid-container-xl;
  }

  .row {
    @media #{map.get(settings.$display-breakpoints, 'sm-and-up')} {
      margin: -#{calc(map.get(settings.$grid-gutters, 'sm') / 2)};

      > div {
        padding: calc(map.get(settings.$grid-gutters, 'sm') / 2);
      }
    }

    @media #{map.get(settings.$display-breakpoints, 'xl')} {
      margin: -#{calc(map.get(settings.$grid-gutters, 'xl') / 2)};

      > div {
        padding: calc(map.get(settings.$grid-gutters, 'xl') / 2);
      }
    }

    &--dense {
      @media #{map.get(settings.$display-breakpoints, 'sm-and-up')} {
        margin: -#{calc(map.get(settings.$grid-gutters, 'sm') / 4)};

        > div {
          padding: calc(map.get(settings.$grid-gutters, 'sm') / 4);
        }
      }

      @media #{map.get(settings.$display-breakpoints, 'xl')} {
        margin: -#{calc(map.get(settings.$grid-gutters, 'xl') / 4)};

        > div {
          padding: calc(map.get(settings.$grid-gutters, 'xl') / 4);
        }
      }
    }
  }
}
</style>
