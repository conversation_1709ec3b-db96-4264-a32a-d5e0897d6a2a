@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

$ripple-animation-visible-opacity: vars.$button-animation-ripple;
$btn-transition: vars.$button-animation-transition;

$btn-text-transform: vars.$button-text-transform;
$btn-letter-spacing: vars.$button-text-letter-spacing;

$btn-border-radius: vars.$button-border-radius;

$btn-sizes: (
  'default': vars.$button-size-base,
  'small': vars.$button-size-small,
);

$btn-font-sizes: (
  'default': vars.$button-text-size-base,
  'small': vars.$button-text-size-small,
);

$btn-toggle-btn-height: #{vars.$button-toggle-size-base}px;
$btn-toggle-btn-opacity: vars.$button-toggle-opacity;
