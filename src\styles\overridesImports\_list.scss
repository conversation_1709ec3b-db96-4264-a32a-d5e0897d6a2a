@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
[dfe-book-frontend] {
:deep() {
  .v-list {
    padding: 0;

    &-item {
      &:hover,
      &:focus {
        background-color: var(--color-base-blue-50);
      }

      &--density-default {
        &.v-list-item--one-line {
          min-height: 0;
        }

        .v-list-item-title {
          font-weight: vars.$font-weight-body;
          font-size: vars.$font-size-body-2;
          line-height: vars.$line-height-body-3;
        }

        .v-subheader {
          font-size: vars.$font-size-caption;
          line-height: vars.$line-height-caption;
          height: auto;
          padding: 12px 12px 8px;
        }
      }

      &--link {
        &:before {
          content: none;
        }

        .v-list-item--active {
          background-color: var(--color-base-blue-50);
        }
      }

      &__mask {
        background-color: var(--color-base-blue-100);
        color: var(--color-base-grey-900);
      }

      &__overlay {
        display: none;
      }
    }
  }

  :not(.v-input--is-disabled) :deep(.v-list-item--link:hover),
  :not(.v-input--is-disabled) :deep(.v-list-item--link:focus),
  :not(.v-input--is-disabled) :deep(.v-list-item--link.v-list-item--highlighted) {
    background-color: var(--color-base-grey-100);
  }
}
}