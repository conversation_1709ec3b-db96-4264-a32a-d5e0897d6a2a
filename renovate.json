{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "updateLockFiles": true, "packageRules": [{"description": "Allow all updates for devDependencies", "matchDepTypes": ["devDependencies"], "allowedVersions": "*"}, {"groupName": "Major Updates", "description": "Group major updates together", "matchUpdateTypes": ["major"]}, {"groupName": "Minor Updates", "description": "Group minor updates together", "matchUpdateTypes": ["minor"]}, {"groupName": "Patch Updates", "description": "Group patch updates together", "matchUpdateTypes": ["patch"]}, {"description": "lock vue-i18n version to 9, pending dfe-shared-components update", "matchPackageNames": ["vue-i18n"], "allowedVersions": "9.x"}, {"description": "lock vue-query version to 4, update pending", "matchPackageNames": ["@tanstack/vue-query"], "allowedVersions": "4.x"}, {"description": "group for vuetify, allow patch updates from version x.x.2 and above", "matchPackageNames": ["vuetify"], "labels": ["vuetify"], "groupName": "vuetify"}, {"description": "group for linting packages", "matchPackagePatterns": ["eslint", "@eslint/js", "@typescript-eslint/eslint-plugin", "@typescript-eslint/parser", "@vue/eslint-config-typescript", "eslint-config-prettier", "eslint-plugin-n", "eslint-plugin-promise", "eslint-plugin-vue", "eslint-plugin-vuetify", "globals", "prettier"], "labels": ["linting"], "groupName": "linting"}, {"description": "group for build util packages", "matchPackagePatterns": ["husky", "is-ci", "lint-staged", "commitlint", "concurrently"], "labels": ["buildUtils"], "groupName": "buildUtils"}], "reviewers": ["@kevin.petiteau", "@moritz.vogt-external", "@peycho.peychev-external", "@philipp.ciftci-external", "@vijayaraj.mahendran"]}