import type { QueryKey, UseQueryOptions } from '@tanstack/vue-query';
import { useQuery } from '@tanstack/vue-query';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { GeneralProblem, HttpResponse, RequestParams, Segment } from '@dfe/dfe-book-api';
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import type { MaybeRef } from '@vueuse/core';
import { get } from '@vueuse/core';

type ApiFn<T> = (
  query: {
    customerNumber: string;
    customerSegment: Segment;
  },
  params: RequestParams,
  options?: UseQueryOptions<T>,
) => Promise<HttpResponse<T, void | GeneralProblem>>;
export const useCustomerQuery = <T>(id: string, apiFn: ApiFn<T>, opts?: UseQueryOptions<T>) => {
  const formStore = useCreateOrderFormStore();
  const { customerNumber, transportType } = storeToRefs(formStore);

  const queryKey: unknown[] = [id, customerNumber, transportType];

  if (opts?.queryKey) {
    queryKey.push(...get(opts.queryKey as MaybeRef<QueryKey>));
  }

  const { data, isLoading } = useQuery({
    enabled: computed(() => {
      return !!customerNumber.value && !!transportType.value
    }),
    queryFn: (params) =>
      apiFn(
        {
          customerNumber: customerNumber.value,
          customerSegment: transportType.value,
        },
        params,
        opts,
      ).then(({ data }) => data),
    staleTime: Infinity,
    ...opts,
    queryKey: queryKey as QueryKey,
  });

  return {
    data,
    isLoading,
  };
};
