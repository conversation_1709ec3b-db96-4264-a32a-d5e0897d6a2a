import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { ContainerType, Option } from '@dfe/dfe-book-api';
import { Options } from '@dfe/dfe-book-api';
import { computed } from 'vue';

export interface ContainerTypesAsOptions {
  containerTypes?: Options;
  lastUsed?: Options;
}

export const useContainerTypes = () => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { orderType } = storeToRefs(formStore);

  const query = useCustomerQuery('containerTypes', api.book.customers.getContainerTypes, {
    queryKey: [orderType],
  });

  const containerTypesAsOptions = computed(
    (): { containerTypes: Options | undefined; lastUsed: Options | undefined } => {
      return {
        containerTypes: mapContainerTypes(query.data.value?.containerTypes),
        lastUsed: mapContainerTypes(query.data.value?.lastUsed),
      };
    },
  );

  return { query, containerTypesAsOptions };
};

function mapContainerTypes(containerTypes: ContainerType[] | undefined) {
  return (
    containerTypes?.map(
      (containerType): Option => ({
        description: `${containerType.size} ${containerType.description}`,
        code: containerType.key,
      }),
    ) ?? []
  );
}
