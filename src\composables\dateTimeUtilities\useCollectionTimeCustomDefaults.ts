import { TimeFormat } from '@/types/createOrder';
import { CollectionTimeSlots } from '@/enums';
import { useUTCTimeFromDate } from '@/composables/dateTimeUtilities/useTimeFromDate';

interface Defaults {
  collectionDate?: string;
  from: string;
  to: string;
}

function useCollectionTimeCustomDefaults(timeFormat: TimeFormat) {
  const baseDate = new Date();
  const defaults: Defaults = {
    from: '',
    to: '',
  };

  baseDate.setUTCMinutes(0);

  baseDate.setUTCHours(CollectionTimeSlots.CustomDefaultFromHours);
  defaults.from = useUTCTimeFromDate(baseDate, timeFormat);

  baseDate.setUTCHours(CollectionTimeSlots.CustomDefaultToHours);
  defaults.to = useUTCTimeFromDate(baseDate, timeFormat);

  return defaults;
}

export default useCollectionTimeCustomDefaults;
