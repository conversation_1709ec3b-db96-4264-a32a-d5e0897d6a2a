import { useErrorBanner } from '@/store/createOrder/validationErrorBanner';
import GenericErrorBanner from '@/components/createOrder/sharedComponents/GenericErrorBanner.vue';
import { nextTick } from 'vue';
import ErrorBanner from '@/components/base/banner/DfeBanner.vue';
import { createClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';
import type { mount } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import type { Mock } from 'vitest';
import { storeToRefs } from 'pinia';
import flushPromises from 'flush-promises';

describe('GenericErrorBanner', () => {
  let wrapper: ReturnType<typeof mount>;
  let scrollIntoViewMock: Mock;
  const client = createClient<Events>({});
  const errorBannerStore = useErrorBanner();

  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = scrollIntoViewMock = vi.fn();
  });

  beforeEach(() => {
    wrapper = shallowMount(GenericErrorBanner);
  });

  afterEach(() => {
    wrapper.unmount();
    scrollIntoViewMock.mockReset();
    errorBannerStore.$reset();
  });

  it('should be visible when there are validation errors', async () => {
    const { validationErrors } = storeToRefs(errorBannerStore);
    validationErrors.value = [{ description: 'error1' }, { description: 'error2' }];
    await nextTick();
    expect(wrapper.findComponent(ErrorBanner).props('value')).toBe(true);
  });

  it('should not be visible when there are no validation errors', async () => {
    expect(wrapper.findComponent(ErrorBanner).props('value')).toBe(false);
  });

  it('should scroll the banner element into view', async () => {
    const { validationErrors } = storeToRefs(errorBannerStore);
    validationErrors.value = [{ description: 'error1' }, { description: 'error2' }];

    await flushPromises();

    expect(scrollIntoViewMock).toHaveBeenCalled();
  });

  it('should scroll the banner element into view when event is emited', async () => {
    const { validationErrors } = storeToRefs(errorBannerStore);
    validationErrors.value = [{ description: 'error1' }, { description: 'error2' }];

    client.events.emit('scrollToGenericErrorBanner');

    await flushPromises();

    expect(scrollIntoViewMock).toHaveBeenCalled();
  });
});
