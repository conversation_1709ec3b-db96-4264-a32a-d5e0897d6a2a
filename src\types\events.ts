import type { UseScrollReturn } from '@vueuse/core';
import type { Segment } from '@dfe/dfe-book-api';

export type CreateOrder = {
  transportType: Segment;
};

export interface DialogScroll {
  scroll: UseScrollReturn;
  offset: {
    top: number;
    bottom: number;
  };
}

interface CloseBookDialog {
  reopen?: boolean;
  transportType?: Segment;
  updateOrders?: boolean;
}

/**
 * @param file Either a base64 encoded string or an ArrayBuffer
 * @param fileName (optional) Filename for the downloaded pdf. default: 'labels'
 * @param modalHeadline (optional)
 */
export type LabelPrintEvent = {
  file: string;
  fileName?: string;
  modalHeadline: string;
  orderId: number | number[];
};

export type EditBookOrderEvent = {
  customerNumber: number;
  orderId: number;
  scrollToDocuments?: boolean;
};

type CurrentOrderStatus = { status?: string };

export type Events = {
  createOrder: CreateOrder;
  createOrderDialogScroll: DialogScroll;
  closeBookDialog: CloseBookDialog;
  createOrderValidate: void;
  printLabels: LabelPrintEvent;
  renderLabelPreview: Pick<LabelPrintEvent, 'file' | 'fileName' | 'modalHeadline' | 'orderId'>;
  clearCreateOrderFormData: void;
  doPrintLabels: void;
  preventUnsavedChanges: void;
  editOrder: EditBookOrderEvent;
  currentOrderStatus: CurrentOrderStatus;
  scrollToGenericErrorBanner: void;
  formLoaded: void;
  showDeleteOverflowSSCCs: () => void;
};
