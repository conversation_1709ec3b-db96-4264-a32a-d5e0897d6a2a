import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import OutputAddress from '@/components/createOrder/OutputAddress.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes } from '@/enums';
import { afterEach } from 'vitest';

const props = {
  address: {
    name: 'Name',
    name2: 'Second',
    name3: 'Third',
    countryCode: 'DE',
    street: 'MYStreet 1',
    postcode: '12345',
    individualId: '123456789',
    city: 'City',
  },
  boldTitle: false,
  editable: false,
};

describe('OutputAddress component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(OutputAddress, {
      props,
    });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('shows address values', () => {
    Object.values(props.address).forEach((value) => {
      expect(wrapper.text()).toContain(value);
    });
  });

  it("doesn't output address if empty", async () => {
    await wrapper.setProps({ address: undefined });

    expect(wrapper.html()).not.toContain('.output-address');
  });

  it("doesn't output contact if road order", async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    expect(wrapper.html()).not.toContain('v-chip');
  });

  it('shows contact values', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.SeaExportOrder;

    await wrapper.setProps({ contactData: { name: 'Test' } });

    const divEl = wrapper.find('.v-chip');

    expect(divEl.exists()).toBe(true);
  });

  it('has modifiable icon buttons', async () => {
    wrapper = mount(OutputAddress, {
      props,
    });

    await wrapper.setProps({ isEditable: true });
    await wrapper.setProps({ isDeletable: true });

    expect(wrapper.html()).toContain('svg');

    await wrapper.setProps({ isEditable: false });
    await wrapper.setProps({ isDeletable: false });

    expect(wrapper.html()).not.toContain('svg');
  });

  it('shows boldTitle', async () => {
    wrapper = mount(OutputAddress, {
      props,
    });

    await wrapper.setProps({ boldTitle: true });

    expect(wrapper.html()).toContain('text-label-2');
  });

  it('shows incomplete address alert depending on store variable isValidationTriggered', async () => {
    const formStore = useCreateOrderFormStore();

    expect(wrapper.find('.incomplete-address').exists()).toBe(false);

    await wrapper.setProps({ isAddressIncomplete: true });

    const incompleteAddressElement = wrapper.find('.incomplete-address');

    const vAlert = incompleteAddressElement.findComponent({ name: 'v-alert' });

    expect(incompleteAddressElement.exists()).toBe(true);
    expect(vAlert.props().type).toBe('warning');
    expect(incompleteAddressElement.find('h5').text()).toBe(
      'labels.edit_and_complete_address.text',
    );

    formStore.isValidationTriggered = true;
    await wrapper.vm.$nextTick();

    expect(vAlert.props().type).toBe('error');
    expect(incompleteAddressElement.find('h5').text()).toBe('labels.address_input_incomplete.text');
  });
});
