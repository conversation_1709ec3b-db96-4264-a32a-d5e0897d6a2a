import {
  isDocumentResponse,
  isErrorHttpResponse,
  isHttpResponse,
  isOrderProcessResult,
} from '@/services/type-guards';
import { documentsResponse } from '@/mocks/fixtures/documents';

describe('Type Guards', () => {
  describe('isHttpResponse', () => {
    it('should return true if response is of type HttpResponse', () => {
      // Arrange
      const response = {
        data: documentsResponse[0],
        error: null,
      };
      // Act
      const result = isHttpResponse(response, isDocumentResponse);
      // Assert
      expect(result).toBe(true);
    });

    it('should return false if response is not of type HttpResponse', () => {
      // Arrange
      const response = {
        data: documentsResponse[0],
      };
      // Act
      const result = isHttpResponse(response, isDocumentResponse);
      // Assert
      expect(result).toBe(false);
    });
  });

  describe('isErrorHttpResponse', () => {
    it('should return true if response is of type HttpResponse', () => {
      // Arrange
      const response = {
        data: null,
        error: documentsResponse[0],
      };
      // Act
      const result = isErrorHttpResponse(response, isDocumentResponse);
      // Assert
      expect(result).toBe(true);
    });

    it('should return false if response is not of type HttpResponse', () => {
      // Arrange
      const response = {
        data: documentsResponse[0],
      };
      // Act
      const result = isErrorHttpResponse(response, isDocumentResponse);
      // Assert
      expect(result).toBe(false);
    });
  });

  describe.each([
    ['isDocumentResponse', isDocumentResponse, [documentsResponse[0]], [{ documentId: 0 }]],
    [
      'isOrderProcessResult',
      isOrderProcessResult,
      [{ order: {}, orderLabel: {}, validationResult: {} }],
      [{ documentId: 0 }, { order: {}, validationResult: {} }],
    ],
  ] as [string, (content: unknown) => boolean, unknown[], unknown[]][])(
    '%s',
    (_, typeGuardFn, positives, negatives) => {
      it.each(positives)('should return true', (positive) => {
        expect(typeGuardFn(positive)).toBe(true);
      });

      it.each(negatives)('should return false', (negative) => {
        expect(typeGuardFn(negative)).toBe(false);
      });
    },
  );
});
