<template>
  <div v-if="addressModel" v-data-test="'address-card'" v-bind="$attrs">
    <div v-if="showShortForm">
      <VCard
        ref="addressVueCard"
        tabindex="0"
        :class="['output-address-card pa-4 pb-0', hasErrorClass]"
        variant="outlined"
      >
        <TooltipWithSlot
          :tooltip-props="{
            text: tooltip,
            location: 'right',
            disabled: !tooltip,
            attach: $appRoot,
          }"
        >
          <template #content>
            <OutputAddress
              :address="addressModel"
              :header="header"
              :contact-data="contactData"
              :bold-title="true"
              :is-editable="isEditable"
              :is-deletable="isDeletable"
              :is-address-incomplete="isAddressIncomplete"
              :is-currently-final-address="isCurrentlyFinalAddress"
              :tooltip="t('labels.edit_address.text')"
              @delete-address="deleteAddress"
              @edit-address="editAddress"
            />
            <slot name="neutralize"></slot>
          </template>
        </TooltipWithSlot>
      </VCard>
      <VInput
        type="hidden"
        :model-value="!isAddressIncomplete"
        :rules="[useValidationRules.isConditionFulfilled]"
        hide-details
      ></VInput>
    </div>
    <div v-else>
      <div class="d-flex justify-space-between align-center">
        <SearchAddressField
          :header="hideSearchFieldLabel ? undefined : header"
          :model-value="addressModel"
          :is-customer="isCustomer"
          :class="['flex-grow-1']"
          :rules="required ? [useValidationRules.required] : []"
          :is-required="required"
          @update:model-value="updateAddress($event)"
        />
        <DfeIconButton
          v-if="showDelete"
          class="ml-2 mt-6 align-self-auto"
          :size="ComponentSize.DEFAULT"
          :color="ColorVariants.NEUTRAL"
          :tooltip="t('labels.remove_label.text')"
          :filled="false"
          @click="deleteAddress"
        >
          <MaterialSymbol size="24">
            <DeleteIcon />
          </MaterialSymbol>
        </DfeIconButton>
      </div>

      <AddButton
        v-data-test="'add-address-button'"
        :label="t('labels.add_address.text')"
        :without-icon="true"
        variant="outlined"
        class="mt-2"
        @add-new-item="addNewAddress"
      />
    </div>
    <EditModal
      v-model="showAddressEditor"
      :headline="
        addressEditorEditMode ? t('labels.edit_address.text') : t('labels.new_address.text')
      "
      :cancel-text="t('labels.dfe_cancel.text')"
      :confirm-text="t('labels.apply_label.text')"
      :is-shipper-address="isShipperAddress"
      @confirm="confirmAddressEditor"
      @close="closeAddressEditor"
    >
      <DfeBanner :value="showCountryBanner" type="error" class="my-4">
        <span class="text-h5">{{ t('messages.id6643.text1') }}</span>
        <div class="text-body-2 mt-2">{{ t('messages.id6643.text2') }}</div>
      </DfeBanner>

      <DfeBanner :value="showAddressBookErrorBanner" type="error" class="my-4">
        <h5 class="text-h5">{{ $t('labels.invalid_input.text') }}</h5>
        <ul class="pt-2 pl-4 text-body-2">
          <li v-for="item in errorMessageAddressBook" :key="item.field">
            {{ $t(item.field) }}: {{ item.description }}
          </li>
        </ul>
      </DfeBanner>
      <DfeBanner :value="showDuplicateEntryBookErrorBanner" type="error" class="my-4">
        <h5 class="text-h5">{{ $t('labels.address_duplicate.text') }}</h5>
        <div class="text-body-2 mt-2">{{ t('messages.id6914.text') }}</div>
      </DfeBanner>
      <VRow>
        <VCol
          sm="12"
          :md="isContactAllowed ? '5' : '12'"
          :lg="isContactAllowed ? '5' : '12'"
          cols="12"
        >
          <LoaderOverlay v-if="showLoadingSpinner" :model-value="showLoadingSpinner" />
          <VForm
            v-if="showAddressEditor"
            ref="addressForm"
            validate-on="submit lazy"
            @submit.prevent
          >
            <FormAddress
              v-data-test="'address-card-address'"
              :disable-search="addressEditorEditMode"
              :smart-proposals-enabled="smartProposalsEnabled"
              :is-shipper-address="isShipperAddress"
              :customer-number="props.customerNumber"
              @update-contact="updateContact($event)"
              @input="closeAddressEditor"
            />
          </VForm>
        </VCol>

        <VCol
          v-if="isContactAllowed"
          v-data-test="'contact-form-col'"
          sm="12"
          md="5"
          lg="5"
          offset-md="1"
          offset-lg="1"
          cols="12"
        >
          <VForm v-if="showAddressEditor" ref="contactForm" @submit.prevent>
            <FormContactPerson
              v-if="contactDataFormValue"
              v-model="contactDataFormValue"
              v-data-test="'address-card-contact'"
              :default-country="formAddress.countryCode"
              :address-id="addressId"
              :contacts="fetchedContactsForAddress"
              :required-fields="isNameAndEmailRequired ? ['name', 'email'] : []"
            />
          </VForm>
        </VCol>
      </VRow>
      <template #footer>
        <VSelectionControlGroup v-if="showSaveToAddressBookCheckbox" v-model="saveToAddressBook">
          <CheckboxField :label="t('labels.save_to_address_book.text')" class-styles="pt-0 mt-0" />
        </VSelectionControlGroup>
      </template>
    </EditModal>
  </div>
</template>

<script setup lang="ts">
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import EditModal from '@/components/base/modal/EditModal.vue';
import TooltipWithSlot from '@/components/base/tooltips/TooltipWithSlot.vue';
import AddButton from '@/components/createOrder/AddButton.vue';
import FormAddress from '@/components/createOrder/formSectionAddresses/FormAddress.vue';
import SearchAddressField from '@/components/createOrder/formSectionAddresses/SearchAddressField.vue';
import OutputAddress from '@/components/createOrder/OutputAddress.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import FormContactPerson from '@/components/form/FormContactPerson.vue';
import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { useApplyOrderAddressPresets } from '@/composables/createOrder/addresses/useApplyOrderAddressPresets';
import { useValidatePostcode } from '@/composables/createOrder/useValidatePostcode';
import { useValidateAddress } from '@/composables/data/useValidateAddress';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { AddressCardType, ContactsType } from '@/enums';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { contactData as contactDataBlueprint, orderAddress } from '@/store/sharedInitialStates';
import { useValidationDataStore } from '@/store/validation';
import { ClientKey } from '@/types/client';
import type { AddressValidationError } from '@/types/createOrder';
import { getAddressValidationErrors } from '@/utils/address/getAddressValidationErrors';
import type { Address, ContactData } from '@dfe/dfe-address-api';
import {
  isDuplicateEntryProblem,
  isHttpResponse,
  isInvalidAddressProblem,
} from '@dfe/dfe-address-api';
import { GeneralProblem, OrderAddress } from '@dfe/dfe-book-api';
import { isIrelandCountryCode, usePreferences, useUpperFirst } from '@dfe/dfe-frontend-composables';
import { ColorVariants, ComponentSize, DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-16px.svg';
import { every, isEmpty } from 'lodash';
import { storeToRefs } from 'pinia';
import type { PropType } from 'vue';
import { computed, inject, nextTick, ref, toRefs, watch, watchEffect } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';
import { VCard, VForm } from 'vuetify/components';

const { t } = useI18n();

const props = defineProps({
  header: {
    type: [String, Object] as PropType<TranslateResult>,
    required: false,
    default: '',
  },
  contactData: {
    type: Object as PropType<ContactData>,
    required: false,
    default: contactDataBlueprint,
  },
  hideSearchFieldLabel: { type: Boolean, default: false },
  smartProposalsEnabled: { type: Boolean, default: false },
  showDelete: { type: Boolean, default: false },
  isEditable: { type: Boolean, default: true },
  isDeletable: { type: Boolean, default: true },
  isShipperAddress: { type: Boolean, default: false },
  isConsigneeAddress: { type: Boolean, default: false },
  isCustomer: { type: Boolean, default: false },
  required: { type: Boolean, default: false },
  tooltip: { type: String, default: '' },
  isCurrentlyFinalAddress: { type: Boolean, default: false },
  applyPresets: { type: Boolean, default: false },
  isContactRequired: { type: Boolean, default: false },
  addressCardType: { type: Number as PropType<AddressCardType>, default: 0 },
  customerNumber: { type: String, default: undefined },
});

const emit = defineEmits(['update-contact', 'add-new-address', 'delete-address']);

const addressModel = defineModel<OrderAddress>({ default: { ...orderAddress() } });

const client = inject(ClientKey);
const { mobileNumber, phoneNumber } = usePreferences();

const {
  isShipperAddressDisabled,
  hasUnsavedAddressChanges,
  isUnsavedAddressChangesDialogOpen,
  townCounty,
  formAddress,
  isDifferentConsigneeAddress,
  collectionOption,
} = storeToRefs(useCreateOrderAddressesStore());
const createOrderDataStore = useCreateOrderDataStore();
const embargoStore = useEmbargoStore();
const createAddressDataStore = useCreateAddressDataStore();
const { contactsCollection } = storeToRefs(createAddressDataStore);
const { contactDataDelivery } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());
const {
  isAirOrder,
  isRoadOrder,
  isSeaOrder,
  isAirAndSeaOrder,
  isRoadForwardingOrder,
  isRoadCollectionOrder,
  transportCountry,
} = storeToRefs(useCreateOrderFormStore());
const { countries } = storeToRefs(useCreateOrderDataStore());
const { formValidationSectionsAir, formValidationSectionsSea } =
  storeToRefs(useValidationDataStore());

const { refetch, fetchStatus, data: validatePostcodeData } = useValidatePostcode();
const { mutate, data: validationResult } = useValidateAddress();

const { contactData } = toRefs(props);
const addressForm = ref<VForm | null>(null);
const contactForm = ref<VForm | null>(null);
const addressVueCard = ref<VCard | null>();
const contactDataFormValue = ref<ContactData>({ ...props.contactData });
const showAddressEditor = ref(false);
const addressEditorEditMode = ref(false);
const saveToAddressBook = ref(false);
const addressIsValid = ref(false);
const contactIsValid = ref(false);
const fetchedContactsForAddress = ref<ContactData[] | undefined>();
const addressFormValueInitial = ref<OrderAddress>({ ...formAddress.value });
const showAddressBookErrorBanner = ref(false);
const showDuplicateEntryBookErrorBanner = ref(false);

const errorMessageAddressBook = ref<AddressValidationError[]>([]);

const showShortForm = computed(() => !every(addressModel.value, isEmpty));
const addressId = computed(() => addressModel.value?.id ?? undefined);
const showCountryBanner = computed(() => countries && instanceOfGeneralProblem(countries.value));

const showLoadingSpinner = computed(() => {
  return fetchStatus.value === 'fetching';
});

const showSaveToAddressBookCheckbox = computed(() => {
  // Check if the address comes from the address book
  const addressFromAddressBook = addressEditorEditMode.value;

  return !addressFromAddressBook;
});

const hasErrorClass = computed(() => {
  if (isRoadOrder.value) return false;

  return {
    'output-address-card--has-error':
      (isSeaOrder && !formValidationSectionsSea.value.seaOrderAddresses && !hasContactData.value) ||
      (isAirOrder && !formValidationSectionsAir.value.airOrderAddresses && !hasContactData.value),
  };
});

const hasContactData = computed(() => {
  return contactData.value?.name && contactData.value?.email;
});

const isContactAllowed = computed(() => {
  return (
    (!isRoadForwardingOrder.value && !isDifferentConsigneeAddress.value) || props.isShipperAddress
  );
});

const isNameAndEmailRequired = computed(() => {
  const isAdressCardTypeConsignee = props.addressCardType === AddressCardType.CONSIGNEE;
  const isCollectionOptionOrAddressCardTypeConsignee =
    collectionOption.value || isAdressCardTypeConsignee;

  return (
    (isRoadCollectionOrder.value && isCollectionOptionOrAddressCardTypeConsignee) ||
    !isRoadOrder.value
  );
});

const isAddressIncomplete = computed(() => {
  const isValid = validationResult.value !== undefined ? validationResult.value?.valid : true;
  const isCollectionOptionSetAndNoContact =
    props.isShipperAddress && !!collectionOption.value && !hasContactData.value;
  return !isValid || isCollectionOptionSetAndNoContact;
});

watch(transportCountry, async ({ fromCountry, toCountry }) => {
  if (fromCountry && toCountry) {
    await embargoStore.fetchHasEmbargo(fromCountry, toCountry);
  } else {
    embargoStore.resetHasEmbargo();
  }
});

const updateHasUnsavedChanges = () => {
  hasUnsavedAddressChanges.value =
    JSON.stringify(formAddress.value) !== JSON.stringify(addressFormValueInitial.value);
};

const closeAddressEditor = () => {
  if (hasUnsavedAddressChanges.value) {
    isUnsavedAddressChangesDialogOpen.value = true;
    return;
  }

  formAddress.value = { ...orderAddress() };
  showAddressEditor.value = false;
  addressEditorEditMode.value = false;
  saveToAddressBook.value = false;
  showAddressBookErrorBanner.value = false;
  showDuplicateEntryBookErrorBanner.value = false;
  isUnsavedAddressChangesDialogOpen.value = false;
};

const validateAddressForm = async () => {
  if (addressForm.value) {
    const isValid = await addressForm.value?.validate();
    addressIsValid.value = isValid.valid;
  }
};

const validateContactForm = async () => {
  if (!isRoadForwardingOrder.value && contactForm.value) {
    const isValid = await contactForm.value?.validate();
    contactIsValid.value = isValid?.valid ?? false;
  }
};

const updateContact = (contacts: ContactData[]) => {
  emit('update-contact', contacts[0]);
};

const resetValidation = () => {
  addressForm.value?.resetValidation();
};

const isPostcodeRequired = computed(
  () =>
    !!formAddress.value.countryCode &&
    !createOrderDataStore.isPostcodeOptional(formAddress.value.countryCode),
);

const confirmAddressEditor = async () => {
  if (isPostcodeRequired.value) {
    await refetch();
  }

  await validateAddressForm();
  await validateContactForm();

  if (!addressIsValid.value) {
    return;
  }

  if (isIrelandCountryCode(formAddress.value.countryCode ?? '') && townCounty?.value) {
    formAddress.value.city = townCounty?.value.data.town;
    formAddress.value.supplement = townCounty?.value.data.county;
  }

  const address = saveToAddressBook.value ? await saveAddressToAddressBook() : formAddress.value;

  const isNotRoadForwardingOrderOrDifferentConsigneeAddress =
    !isRoadForwardingOrder.value || !isDifferentConsigneeAddress.value;

  if (
    isNotRoadForwardingOrderOrDifferentConsigneeAddress &&
    !contactIsValid.value &&
    props.isContactRequired
  ) {
    return;
  }

  const isPostcodeDataValid = validatePostcodeData.value?.valid ?? !isPostcodeRequired.value;

  if (address && isPostcodeDataValid) {
    addressModel.value = formAddress.value;
    emit('update-contact', contactDataFormValue.value);
    hasUnsavedAddressChanges.value = false;
    closeAddressEditor();
    resetValidation();
  }
};

const saveAddressToAddressBook = async () => {
  try {
    if (isAirAndSeaOrder.value && !contactIsValid.value) {
      return;
    }
    const address = await createAddressDataStore.upsertAddress(formAddress.value);
    if (isAirAndSeaOrder.value) {
      if (!address.id) {
        throw new Error('Cannot set contact to address without id');
      }
      await saveContact(address.id);
    }
    client?.toast.success(t('labels.add_address_to_address_book_success.text').toString());
    return {
      ...address,
      originAddressId: address?.id,
      id: addressId.value,
    };
  } catch (e) {
    if (isHttpResponse(e) && isInvalidAddressProblem(e.error)) {
      errorMessageAddressBook.value = getAddressValidationErrors(e.error);
      showAddressBookErrorBanner.value = true;
    } else if (isHttpResponse(e) && isDuplicateEntryProblem(e.error)) {
      showDuplicateEntryBookErrorBanner.value = true;
    } else {
      client?.toast.error(t('labels.save_error.text').toString());
    }
  }
};

/** V1 ONLY : in AIR, an address can only hold a single contact */
/** therefore, contact editing in AIR will always update the first contact */
const saveContact = async (originAddressId: number) => {
  if (contactIsValid.value && formAddress.value) {
    if (
      (isAirOrder.value && contactsCollection.value.length === 0) ||
      contactsCollection.value.length <= 20
    ) {
      addAddressContacts(originAddressId);
    } else {
      await updateAddressContacts(originAddressId, contactDataFormValue.value.id);
    }
  }
};

function getBookAddressFromAddress(address: Partial<Address>): OrderAddress {
  return {
    ...address,
    id: addressId.value,
    originAddressId: address.id,
  };
}

function setFocusOnAddressCard(): void {
  nextTick(() => {
    addressVueCard.value?.$el.focus();
  });
}

const updateAddress = async (address: Partial<Address>, contact?: ContactData) => {
  const bookAddress = getBookAddressFromAddress(address);

  setFocusOnAddressCard();
  addressModel.value = bookAddress;

  setIrelandTownCounty(bookAddress);
  const mainContact = await maybeFetchContacts(bookAddress, contact);

  emitContact(contact);
  applyPresetsIfAvailable(address, mainContact);
};

const setIrelandTownCounty = (bookAddress: OrderAddress) => {
  const { townCounty } = storeToRefs(useCreateOrderAddressesStore());

  if (!isIrelandCountryCode(bookAddress.countryCode ?? '')) return;
  townCounty.value = {
    label: `${useUpperFirst(bookAddress.city)}/${useUpperFirst(bookAddress.supplement)}`,
    data: {
      town: useUpperFirst(bookAddress.city),
      county: useUpperFirst(bookAddress.supplement),
      dachserPlz: '',
    },
  };
};

const maybeFetchContacts = async (bookAddress: OrderAddress, contact?: ContactData) => {
  if (contact || !bookAddress.originAddressId) return undefined;

  fetchedContactsForAddress.value = await createAddressDataStore.getAddressContacts(
    bookAddress.originAddressId,
    ContactsType.Customer,
  );

  const mainContact = fetchedContactsForAddress.value?.find((value) => value?.isMainContact);
  if (!mainContact) return undefined;

  if (!isRoadForwardingOrder.value) {
    contact = mainContact;
  } else if (props.isConsigneeAddress) {
    contactDataDelivery.value = mainContact;
  }

  return mainContact;
};

const emitContact = (contact: ContactData | undefined) => {
  if (!contact && props.isCustomer) {
    const keycloakProfile = client?.auth.getUserInformation();
    emit('update-contact', {
      ...contactDataBlueprint(),
      name: `${keycloakProfile?.firstName} ${keycloakProfile?.lastName}`,
      email: keycloakProfile?.email,
      telephone: phoneNumber.value,
      mobile: mobileNumber.value,
    });
  } else {
    emit('update-contact', contact);
  }
};

const applyPresetsIfAvailable = (address: Partial<Address>, mainContact?: ContactData) => {
  if (isEmpty(address.addressPresets) || !props.applyPresets) return;
  const atLeastOneApplied = useApplyOrderAddressPresets(address.addressPresets, mainContact);

  if (atLeastOneApplied) {
    client?.toast.info(t('labels.address_presets_applied.text').toString());
  }
};

const addNewAddress = () => {
  formAddress.value = { ...orderAddress() };
  townCounty.value = undefined;
  contactDataFormValue.value = contactDataBlueprint();
  showAddressEditor.value = true;
  addressEditorEditMode.value = false;
  isShipperAddressDisabled.value = false;
  isUnsavedAddressChangesDialogOpen.value = false;
  nextTick(() => {
    addressVueCard.value?.$el.focus();
  });
  updateHasUnsavedChanges();
  emit('add-new-address');
};

const editAddress = async () => {
  formAddress.value = { ...addressModel.value };
  contactDataFormValue.value = { ...props.contactData };
  addressEditorEditMode.value = true;
  showAddressEditor.value = true;
  isUnsavedAddressChangesDialogOpen.value = false;
  updateHasUnsavedChanges();

  await nextTick();

  await validateAddressForm();
  await validateContactForm();

  const { selectedCustomer } = storeToRefs(useCreateOrderFormStore());
  const { address } = selectedCustomer.value;

  isShipperAddressDisabled.value = formAddress.value.name === address?.name;
};

const deleteAddress = () => {
  emit('delete-address');
  emit('update-contact', contactDataBlueprint());
  addressModel.value = { ...orderAddress() };
};

const instanceOfGeneralProblem = (object: object): object is GeneralProblem => {
  return Object.hasOwn(object, 'errorId');
};

const getAddressContacts = () => {
  if (formAddress.value?.id) {
    createAddressDataStore.getAddressContacts(formAddress.value.id, ContactsType.Collection);
  } else {
    contactsCollection.value = [];
  }
};

const addAddressContacts = (id: number | undefined) => {
  if (!id) {
    console.warn('Could not save contact details, address id was undefined');
    return;
  }
  createAddressDataStore.addContactToAddress(id, contactDataFormValue.value);
};

const updateAddressContacts = async (
  addressId: number | undefined,
  contactId: number | undefined,
) => {
  if (!addressId || !contactId) {
    console.warn('Could not update contact details, missing Ids');
    return;
  }
  await createAddressDataStore.updateAddressContact(
    addressId,
    contactId,
    contactDataFormValue.value,
  );
};

watchEffect(() => {
  if (!addressModel.value) return;

  mutate({
    address: addressModel.value,
    contact: contactData?.value,
  });
});

watch(
  () => props.contactData,
  (newContactData) => {
    contactDataFormValue.value = newContactData;
  },
);

watch(
  () => formAddress.value?.id,
  () => {
    getAddressContacts();
  },
);

watch(
  () => showAddressEditor.value,
  (newShowAddressEditor) => {
    if (!newShowAddressEditor) {
      nextTick(() => addressVueCard.value?.$el.focus());
    }
  },
);
</script>

<style lang="scss">
.edit-modal-footer-slot .v-checkbox .v-selection-control {
  min-height: 0;
}
</style>
