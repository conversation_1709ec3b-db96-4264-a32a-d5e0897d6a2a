import useCollectionTimeSlotOptions from '@/composables/dateTimeUtilities/useCollectionTimeSlotOptions';
import { CollectionTimeSlots } from '@/enums';
import { ref } from 'vue';
import { withSetup } from '../../../test/util/with-setup';

describe('useCollectionTimeSlotOptions composable', () => {
  it('returns only custom time slot if input array is empty', () => {
    const options = withSetup(() => useCollectionTimeSlotOptions(ref([]), 'HH:mm'))[0];

    expect(options).toHaveLength(1);
    expect(options[0]).toHaveProperty('text');
    expect(options[0]).toEqual(
      expect.objectContaining({
        from: CollectionTimeSlots.Custom,
        to: CollectionTimeSlots.Custom,
      }),
    );
  });

  it('returns time slot options', () => {
    const dates = [
      { from: '2022-09-19T10:00:00Z', to: '2022-09-19T11:00:00Z' },
      { from: '2022-09-19T12:00:00Z', to: '2022-09-19T13:00:00Z' },
    ];
    const options = withSetup(() => useCollectionTimeSlotOptions(ref(dates), 'HH:mm'))[0];

    expect(options).toHaveLength(3);
    expect(options[0]).toEqual({
      ...dates[0],
      text: '10:00 - 11:00',
    });
    expect(options[1]).toEqual({
      ...dates[1],
      text: '12:00 - 13:00',
    });
  });

  it('returns time empty slot options if there are no dates', () => {
    const dates = [{ from: '', to: '' }];

    const options = withSetup(() => useCollectionTimeSlotOptions(ref(dates), 'HH:mm'))[0];

    expect(options).toHaveLength(2);
    expect(options[0]).toEqual({
      ...dates[0],
      text: '',
    });
  });
});
