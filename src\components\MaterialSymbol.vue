<template>
  <span class="material-symbol" :class="className" :style="{ fontSize }" v-bind="$attrs">
    <slot />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  size?: number | string;
  color?: string;
  left?: boolean;
  right?: boolean;
}

const props = defineProps<Props>();

const applyColors = (values: string) => {
  const colors = values.split(' ').map((value) => {
    if (value.search(/^(darken|lighten|accent)/) >= 0) {
      return `text--${value}`;
    }
    return `text-${value}`;
  });

  return colors.join(' ');
};

const className = computed(() => [
  ...(props?.color ? [applyColors(props.color)] : []),
  ...(props?.left ? ['left'] : []),
  ...(props?.right ? ['right'] : []),
]);

const fontSize = computed(() => {
  return props?.size ? `${props.size}px` : undefined;
});
</script>

<style lang="scss" scoped>
.material-symbol {
  display: inline-flex;
  font-size: 24px;

  :deep(.icon) {
    height: 1em;
    width: 1em;

    &,
    path {
      fill: currentColor;
    }

    mask {
      visibility: visible;
    }
  }
}
</style>
