import type { Port } from '@dfe/dfe-book-api';
import { CustomsType } from '@dfe/dfe-book-api';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { LoadOrderOptions, useEditOrder } from '@/composables/createOrder/editOrder/useEditOrder';
import { airExportOrder, roadCollectionOrder, roadForwardingOrder } from '@/mocks/fixtures/order';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import useResetCreateOrderFormData from '@/composables/createOrder/useResetCreateOrderFormData';
import { useFormatInUTC } from '@/composables/dateTimeUtilities/useFormatInUTC';
import { mockServer } from '@/mocks/server';
import { hsCodeOptions } from '@/mocks/fixtures/hsCodeOptions';
import type { MockAirExportOrderQuote2BookKey } from '@/mocks/fixtures/orderQuote2Book';
import { getAirExportOrderQuote2Book } from '@/mocks/fixtures/orderQuote2Book';
import { storeToRefs } from 'pinia';
import { HandOverSelection } from '@/types/hand-over';
import { withSetup } from '@test/util/with-setup';
import { ports } from '@/mocks/fixtures/ports';
import { nextTick } from 'vue';

describe('useEditOrder composable', () => {
  let server: ReturnType<typeof mockServer>;

  const loadOrderOptions: LoadOrderOptions = {
    timeFormat: 'HH:mm',
    locale: 'en',
  };

  beforeAll(() => {
    server = mockServer({
      environment: 'test',
      fixtures: {
        airExportOrder,
        hsCodeOptions,
        ports,
      },
    });
  });

  afterEach(() => {
    withSetup(() => useResetCreateOrderFormData().clearFormData());
    vi.clearAllMocks();
  });

  afterAll(() => {
    server.shutdown();
  });

  describe('getDateFromString', () => {
    it('should return date from string', () => {
      const dateString = '2021-01-01';
      const expectedDate = new Date('2021-01-01T00:00:00.000Z');
      expect(useFormatInUTC(dateString, 'en')).toEqual(expectedDate);
    });
  });

  describe('editOrder', () => {
    beforeAll(() => {
      vi.mock('@/composables/useUuid', () => ({
        useUuid: () => 1,
      }));
    });

    it('should set order data for Road Order', async () => {
      await withSetup(() =>
        useEditOrder().loadOrder(roadForwardingOrder, { ...loadOrderOptions, locale: 'de' }),
      )[0];

      const createOrderFormStore = useCreateOrderFormStore();
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
      createOrderOrderLineFormStore.manualNumberOfLabels = 123;
      const createOrderTextsStore = useCreateOrderTextsStore();
      const createOrderFormCollectionAndDeliveryStore =
        useCreateOrderFormCollectionAndDeliveryStore();
      const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
      const createOrderFormAccountingAdditionalServices =
        useCreateOrderFormAccountingAdditionalServices();
      const createOrderDataStore = useCreateOrderDataStore();
      [
        createOrderFormStore,
        createOrderAddressesStore,
        createOrderOrderLineFormStore,
        createOrderTextsStore,
        createOrderFormCollectionAndDeliveryStore,
        createOrderOrderReferencesFormStore,
        createOrderFormAccountingAdditionalServices,
        createOrderDataStore,
      ].forEach((store) => {
        expect(store.$state).toMatchSnapshot();
      });
    });

    it('should set order data for Road Order - null', async () => {
      roadForwardingOrder.product = undefined;
      roadForwardingOrder.orderGroup = undefined;
      roadForwardingOrder.tailLiftDelivery = undefined;
      roadForwardingOrder.selfCollection = undefined;
      roadForwardingOrder.fixDate = '2022-10-01T00:00:00.000Z';

      await withSetup(() =>
        useEditOrder().loadOrder(roadForwardingOrder, { ...loadOrderOptions, locale: 'de' }),
      )[0];

      const createOrderFormStore = useCreateOrderFormStore();
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
      createOrderOrderLineFormStore.manualNumberOfLabels = 123;
      const createOrderTextsStore = useCreateOrderTextsStore();
      const createOrderFormCollectionAndDeliveryStore =
        useCreateOrderFormCollectionAndDeliveryStore();
      const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
      const createOrderFormAccountingAdditionalServices =
        useCreateOrderFormAccountingAdditionalServices();
      const createOrderDataStore = useCreateOrderDataStore();
      [
        createOrderFormStore,
        createOrderAddressesStore,
        createOrderOrderLineFormStore,
        createOrderTextsStore,
        createOrderFormCollectionAndDeliveryStore,
        createOrderOrderReferencesFormStore,
        createOrderFormAccountingAdditionalServices,
        createOrderDataStore,
      ].forEach((store) => {
        expect(store.$state).toMatchSnapshot();
      });
    });

    it('should set order data for Road Order - Collection', async () => {
      await withSetup(() =>
        useEditOrder().loadOrder(roadCollectionOrder, { ...loadOrderOptions, locale: 'de' }),
      )[0];

      const createOrderFormStore = useCreateOrderFormStore();
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
      createOrderOrderLineFormStore.manualNumberOfLabels = 123;
      const createOrderTextsStore = useCreateOrderTextsStore();
      const createOrderFormCollectionAndDeliveryStore =
        useCreateOrderFormCollectionAndDeliveryStore();
      const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
      const createOrderFormAccountingAdditionalServices =
        useCreateOrderFormAccountingAdditionalServices();
      const createOrderDataStore = useCreateOrderDataStore();
      [
        createOrderFormStore,
        createOrderAddressesStore,
        createOrderOrderLineFormStore,
        createOrderTextsStore,
        createOrderFormCollectionAndDeliveryStore,
        createOrderOrderReferencesFormStore,
        createOrderFormAccountingAdditionalServices,
        createOrderDataStore,
      ].forEach((store) => {
        expect(store.$state).toMatchSnapshot();
      });
    });

    it('should set order data for Road Order - Collection - null', async () => {
      roadCollectionOrder.tailLiftCollection = undefined;
      roadCollectionOrder.interpreter = undefined;
      roadCollectionOrder.deliveryOption = undefined;

      await withSetup(() =>
        useEditOrder().loadOrder(roadCollectionOrder, { ...loadOrderOptions, locale: 'de' }),
      )[0];

      const createOrderFormStore = useCreateOrderFormStore();
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
      createOrderOrderLineFormStore.manualNumberOfLabels = 123;
      const createOrderTextsStore = useCreateOrderTextsStore();
      const createOrderFormCollectionAndDeliveryStore =
        useCreateOrderFormCollectionAndDeliveryStore();
      const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
      const createOrderFormAccountingAdditionalServices =
        useCreateOrderFormAccountingAdditionalServices();
      const createOrderDataStore = useCreateOrderDataStore();
      [
        createOrderFormStore,
        createOrderAddressesStore,
        createOrderOrderLineFormStore,
        createOrderTextsStore,
        createOrderFormCollectionAndDeliveryStore,
        createOrderOrderReferencesFormStore,
        createOrderFormAccountingAdditionalServices,
        createOrderDataStore,
      ].forEach((store) => {
        expect(store.$state).toMatchSnapshot();
      });
    });

    it('should set order data for Air Order', async () => {
      await withSetup(() =>
        useEditOrder().loadOrder(airExportOrder, { ...loadOrderOptions, locale: 'de' }),
      )[0];

      const createOrderFormStore = useCreateOrderFormStore();
      const createOrderAddressesStore = useCreateOrderAddressesStore();
      const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
      const createOrderTextsStore = useCreateOrderTextsStore();
      const createOrderFormCollectionAndDeliveryStore =
        useCreateOrderFormCollectionAndDeliveryStore();
      const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
      const createOrderFormAccountingAdditionalServices =
        useCreateOrderFormAccountingAdditionalServices();
      const createOrderDataStore = useCreateOrderDataStore();

      createOrderOrderLineFormStore.orderLines[0].hsCodes = [
        { hsCode: '160555', goods: 'Test' },
        { hsCode: '071232', goods: 'Test 2 ' },
      ];

      [
        createOrderFormStore,
        createOrderAddressesStore,
        createOrderOrderLineFormStore,
        createOrderTextsStore,
        createOrderFormCollectionAndDeliveryStore,
        createOrderOrderReferencesFormStore,
        createOrderFormAccountingAdditionalServices,
        createOrderDataStore,
      ].forEach((store) => {
        expect(store.$state).toMatchSnapshot();
      });
    });

    it('should set fromIATA, if deliverToAirport is true', async () => {
      const { shipperHandOverSelection, fromIATA } = storeToRefs(useCreateOrderAddressesStore());

      shipperHandOverSelection.value.selection = HandOverSelection.port;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      await nextTick();

      expect(fromIATA.value).toStrictEqual(airExportOrder.fromIATA);
    });

    it('should set toIATA, if collectFromAirport is true', async () => {
      const { consigneeHandOverSelection, toIATA } = storeToRefs(useCreateOrderAddressesStore());

      consigneeHandOverSelection.value.selection = HandOverSelection.port;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      expect(toIATA.value).toStrictEqual(airExportOrder.toIATA);
    });

    it('should set the displayName for airport selection', async () => {
      const { port } = storeToRefs(useCreateOrderAddressesStore());

      port.value = <Port>airExportOrder.fromIATA;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      expect(port.value.displayName).toBe(
        `${airExportOrder.fromIATA?.name} (${airExportOrder.fromIATA?.code})`,
      );
    });

    it('should set shipperHandOverSelection airport', async () => {
      const { shipperHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());
      const { port } = storeToRefs(useCreateOrderAddressesStore());

      port.value = <Port>airExportOrder.fromIATA;

      shipperHandOverSelection.value.selection = HandOverSelection.port;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      if (shipperHandOverSelection.value.selection === HandOverSelection.port) {
        expect(shipperHandOverSelection.value.port).toBe(port.value);
      }
    });

    it('should set consigneeHandOverSelection airport', async () => {
      const { consigneeHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());
      const { port } = storeToRefs(useCreateOrderAddressesStore());

      port.value = <Port>airExportOrder.toIATA;

      consigneeHandOverSelection.value.selection = HandOverSelection.port;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      if (consigneeHandOverSelection.value.selection === HandOverSelection.port) {
        expect(consigneeHandOverSelection.value.port).toBe(port.value);
      }
    });

    it('should set pickupAddress', async () => {
      const { shipperHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());

      shipperHandOverSelection.value.selection = HandOverSelection.alternateAddress;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      if (shipperHandOverSelection.value.selection === HandOverSelection.alternateAddress) {
        expect(shipperHandOverSelection.value.address).toStrictEqual(airExportOrder.pickupAddress);
      }
    });

    it('should set deliveryAddress', async () => {
      const { consigneeHandOverSelection } = storeToRefs(useCreateOrderAddressesStore());

      consigneeHandOverSelection.value.selection = HandOverSelection.alternateAddress;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      if (consigneeHandOverSelection.value.selection === HandOverSelection.alternateAddress) {
        expect(consigneeHandOverSelection.value.address).toStrictEqual(
          airExportOrder.deliveryAddress,
        );
      }
    });

    it('should set airport value to fromIATA', async () => {
      const { port } = storeToRefs(useCreateOrderAddressesStore());

      airExportOrder.deliverToAirport = true;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      airExportOrder.deliverToAirport = false;

      expect(port.value).toEqual(airExportOrder.fromIATA);
    });

    it('should set airport value to toIATA', async () => {
      const { port } = storeToRefs(useCreateOrderAddressesStore());

      airExportOrder.collectFromAirport = true;

      await withSetup(() => useEditOrder().loadOrder(airExportOrder, loadOrderOptions))[0];

      expect(port.value).toEqual(airExportOrder.toIATA);

      airExportOrder.collectFromAirport = false;
    });

    it('should set default value for customsType if undefined', async () => {
      const { customsDeclarationExecutor } = storeToRefs(
        useCreateOrderFormAccountingAdditionalServices(),
      );

      roadForwardingOrder.customsType = undefined;
      await withSetup(() => useEditOrder().loadOrder(roadForwardingOrder, loadOrderOptions))[0];

      expect(customsDeclarationExecutor.value).toEqual(CustomsType.CUSTOMER);
    });

    it('should set manual number labels value if present', async () => {
      const { manualNumberOfLabels } = storeToRefs(useCreateOrderOrderLineFormStore());

      await withSetup(() => useEditOrder().loadOrder(roadForwardingOrder, loadOrderOptions))[0];

      expect(manualNumberOfLabels.value).toEqual(123);
    });
  });

  describe('editOrder Quote2Book', () => {
    it.each<MockAirExportOrderQuote2BookKey[]>([
      ['customer', 'address'],
      ['address', 'address'],
      ['addressBook', 'address'],
      ['partialAddress', 'address'],
      ['port', 'address'],
      ['customer', 'addressBook'],
      ['customer', 'partialAddress'],
      ['customer', 'port'],
    ])('should set addresses for Air Order from Quote -> %s to %s', async (from, to) => {
      const addressStore = useCreateOrderAddressesStore();

      await withSetup(() =>
        useEditOrder().loadOrder(getAirExportOrderQuote2Book(from, to), loadOrderOptions),
      )[0];

      expect({
        shipperAddress: addressStore.shipperAddress,
        consigneeAddress: addressStore.consigneeAddress,
        shipperHandOverSelection: addressStore.shipperHandOverSelection,
        consigneeHandOverSelection: addressStore.consigneeHandOverSelection,
        contactDataCustomer: addressStore.contactDataCustomer,
        contactDataShipper: addressStore.contactDataShipper,
        contactDataConsignee: addressStore.contactDataConsignee,
      }).toMatchSnapshot();
    });
  });
});
