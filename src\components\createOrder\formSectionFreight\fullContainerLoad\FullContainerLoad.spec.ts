import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { mount, shallowMount } from '@vue/test-utils';
import {
  FullContainerLoad4Store,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import { SanitizeKey } from '@/types/sanitize';
import sanitizeHtml from 'sanitize-html';
import FullContainerLoad from '@/components/createOrder/formSectionFreight/fullContainerLoad/FullContainerLoad.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import flushPromises from 'flush-promises';
import TextField from '@/components/form/TextField.vue';
import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import type { TestUtils } from '@test/test-utils';
import { useValidationDataStore } from '@/store/validation';
import { storeToRefs } from 'pinia';
import MainFooter from '@/components/createOrder/formSectionFooter/MainFooter.vue';
import { ClientKey } from '@/types/client';
import { createClientMock } from '@test/util/mock-client';
import { VBtn } from 'vuetify/components';

describe('FullContainerLoad.vue', () => {
  let wrapper: TestUtils.VueWrapper<typeof FullContainerLoad>;
  const orderLineFormStore = useCreateOrderOrderLineFormStore();

  beforeEach(() => {
    orderLineFormStore.addFullContainerLoad(true);
    wrapper = mount(FullContainerLoad, {
      props: {
        localId: orderLineFormStore.fullContainerLoads[0].localId,
        lineCounter: 1,
      },
      global: {
        stubs: ['v-dialog'],
      },
      provide: {
        [SanitizeKey as symbol]: sanitizeHtml,
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
    orderLineFormStore.fullContainerLoads = [];
  });

  it('renders full container load component', () => {
    expect(wrapper.find('[data-test="book-full-container-load"]').exists()).toBe(true);
  });

  it('calls removeFullContainerLoad when delete button is clicked', async () => {
    const deleteButton = wrapper.find('[data-test="book-full-container-load-delete-button"]');

    expect(orderLineFormStore.fullContainerLoads.length).toBe(1);

    await deleteButton.trigger('click');

    expect(orderLineFormStore.fullContainerLoads.length).toBe(1);
  });

  it('calls removeFullContainerLoad when delete button is clicked with order lines child', async () => {
    orderLineFormStore.addOrderLineToFullContainerLoad(
      orderLineFormStore.fullContainerLoads[0].localId,
    );

    const deleteButton = wrapper
      .find('[data-test="book-full-container-load-delete-button"]')
      .findComponent(VBtn);

    expect(orderLineFormStore.fullContainerLoads.length).toBe(1);
    expect(orderLineFormStore.orderLines.length).toBe(2);

    await deleteButton.trigger('click');

    expect(orderLineFormStore.fullContainerLoads.length).toBe(1);
    expect(orderLineFormStore.orderLines.length).toBe(1);

    const confirmDialog = wrapper.findComponent(ConfirmPrompt);
    const confirmButton = confirmDialog.find('[data-test="book-confirm-btn"]');

    await confirmButton?.trigger('click');

    expect(orderLineFormStore.orderLines.length).toBe(2);
  });

  it('updates fullContainerLoad type', async () => {
    const fclAutocomplete = wrapper.findComponent(AutocompleteField);
    fclAutocomplete.vm.$emit('update:modelValue', { code: 'GP-20', description: '20 Standard' });

    const event = wrapper.emitted('update:modelValue');

    expect(event).toHaveLength(1);
    expect((event?.[0][0] as FullContainerLoad4Store).containerType).toStrictEqual({
      code: 'GP-20',
      description: '20 Standard',
    });

    await flushPromises();

    const props = wrapper.props() as { modelValue: FullContainerLoad4Store };
    expect(props.modelValue.containerType).toStrictEqual({
      code: 'GP-20',
      description: '20 Standard',
    });
  });

  it('updates fullContainerLoad number with correct value', async () => {
    const textField = wrapper.findComponent(TextField);

    await textField.setValue('AAAU1111111');

    await wrapper.vm.$nextTick();

    const event = wrapper.emitted('update:modelValue');
    expect(event).toHaveLength(1);
    expect((event?.[0][0] as FullContainerLoad4Store).containerNumber).toEqual('AAAU1111111');
  });

  it('updates fullContainerLoad verified gross mass', async () => {
    const vgmField = wrapper.findComponent(ComboboxMeasurementFields);

    await vgmField.setValue(999);

    await wrapper.vm.$nextTick();

    const event = wrapper.emitted('update:modelValue');
    expect(event).toHaveLength(1);
    expect((event?.[0][0] as FullContainerLoad4Store).verifiedGrossMass).toEqual(999);
  });

  it('collapses the FCL container if expanded', async () => {
    const fullContainerLoadHeader = wrapper.find('[data-test="book-full-container-load__header"]');
    const fullContainerLoadBody = wrapper.find('[data-test="book-full-container-load__body"]');

    expect(fullContainerLoadHeader.classes('collapsed')).exist.equals(false);
    expect(fullContainerLoadBody.classes('not-visible')).exist.equals(false);

    await wrapper.find('[data-test="book-full-container-load-collapse-button"]').trigger('click');

    expect(fullContainerLoadHeader.classes('collapsed')).exist.equals(true);
    expect(fullContainerLoadBody.classes('not-visible')).exist.equals(true);
  });

  it('expands the FCL container if collapsed', async () => {
    const fullContainerLoadHeader = wrapper.find('[data-test="book-full-container-load__header"]');
    const fullContainerLoadBody = wrapper.find('[data-test="book-full-container-load__body"]');

    await wrapper.find('[data-test="book-full-container-load-collapse-button"]').trigger('click');

    expect(fullContainerLoadHeader.classes('collapsed')).exist.equals(true);
    expect(fullContainerLoadBody.classes('not-visible')).exist.equals(true);

    await wrapper.find('[data-test="book-full-container-load-expand-button"]').trigger('click');

    expect(fullContainerLoadHeader.classes('collapsed')).exist.equals(false);
    expect(fullContainerLoadBody.classes('not-visible')).exist.equals(false);
  });

  it('expands all FCL containers if an error is existing', async () => {
    const fullContainerLoadHeader = wrapper.find('[data-test="book-full-container-load__header"]');
    const fullContainerLoadBody = wrapper.find('[data-test="book-full-container-load__body"]');

    const client = createClientMock();

    const footer = shallowMount(MainFooter, {
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });

    const checkAndSaveOrderBtn = footer.find('[data-test="book-finish-btn"]');

    const validationStore = useValidationDataStore();
    const { formValidationSectionsSea } = storeToRefs(validationStore);

    await wrapper.find('[data-test="book-full-container-load-collapse-button"]').trigger('click');

    formValidationSectionsSea.value.seaOrderFreight = false;

    await wrapper.vm.$nextTick();

    await checkAndSaveOrderBtn.trigger('click');

    expect(fullContainerLoadHeader.classes('collapsed')).exist.equals(false);
    expect(fullContainerLoadBody.classes('not-visible')).exist.equals(false);
  });
});
