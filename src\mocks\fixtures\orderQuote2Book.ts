import type {
  AirExportOrder,
  AirExportQuoteInformation,
  OrderAddress,
  Port,
  RoadForwardingOrder,
  RoadForwardingQuoteInformation,
} from '@dfe/dfe-book-api';
import { OrderReferenceType, PortType } from '@dfe/dfe-book-api';
import { airExportOrder, roadForwardingOrder } from './order';
import { OrderTypes } from '@/enums';

const keys = {
  shipper: {
    default: 'shipperAddress',
    alternative: 'pickupAddress',
    port: 'fromIATA',
  },
  consignee: {
    default: 'consigneeAddress',
    alternative: 'deliveryAddress',
    port: 'toIATA',
  },
};

const addressValues: OrderAddress = {
  name: 'Ray Sono AG',
  street: 'Tumblinger Str. 32',
  postcode: '80337',
  city: 'Munich',
  countryCode: 'DE',
  lockedByQuote: 'full',
};

const addressBookValues: OrderAddress = {
  ...addressValues,
  id: 123,
  contact: {
    name: 'Ray Sono AG',
    telephone: '123456789',
  },
};

const partialAddressValues: Partial<OrderAddress> = {
  postcode: '80337',
  countryCode: 'DE',
  lockedByQuote: 'partial',
};

const airportValues: Port = {
  code: 'MUC',
  name: 'Munich',
  countryCode: 'DE',
  type: PortType.AIRPORT,
};

const get = {
  customer(type: keyof typeof keys) {
    return {
      [keys[type].default]: addressValues,
      [keys[type].alternative]: undefined,
      [keys[type].port]: undefined,
    };
  },

  address(type: keyof typeof keys) {
    return {
      [keys[type].default]: type === 'consignee' ? addressValues : undefined,
      [keys[type].alternative]: type === 'consignee' ? undefined : addressValues,
      [keys[type].port]: undefined,
    };
  },

  addressBook(type: keyof typeof keys) {
    return {
      [keys[type].default]: type === 'consignee' ? addressBookValues : undefined,
      [keys[type].alternative]: type === 'consignee' ? undefined : addressBookValues,
      [keys[type].port]: undefined,
    };
  },

  partialAddress(type: keyof typeof keys) {
    return {
      [keys[type].default]: type === 'consignee' ? partialAddressValues : undefined,
      [keys[type].alternative]: type === 'consignee' ? undefined : partialAddressValues,
      [keys[type].port]: undefined,
    };
  },

  port(type: keyof typeof keys) {
    return {
      [keys[type].default]: undefined,
      [keys[type].alternative]: undefined,
      [keys[type].port]: airportValues,
    };
  },
};

export type MockAirExportOrderQuote2BookKey = keyof typeof get;

export function getAirExportOrderQuote2Book(
  from: MockAirExportOrderQuote2BookKey,
  to: MockAirExportOrderQuote2BookKey,
): AirExportOrder {
  const order = { ...airExportOrder };
  delete order.furtherAddresses;

  return {
    ...order,
    quoteInformation: {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation,
    ...get[from]('shipper'),
    ...get[to]('consignee'),
  };
}

export const roadForwardingOrderWithDailyPrice: RoadForwardingOrder = {
  ...roadForwardingOrder,
  quoteInformation: {
    orderType: OrderTypes.RoadForwardingOrder,
    quoteExpiryDate: '2124-12-31',
    customerNumber: '03011149',
    collectionDate: '2024-12-31',
    termCode: 'CFR',
    quoteRequestId: 123,
  } as RoadForwardingQuoteInformation,
  customerNumber: '03011149',
  consigneeAddress: {
    postcode: '80337',
    countryCode: 'DE',
    lockedByQuote: 'partial',
  },
  references: [
    ...(roadForwardingOrder.references ?? []),
    {
      referenceType: OrderReferenceType.DAILY_PRICE_REFERENCE,
      referenceValue: '060',
    },
  ],
};
