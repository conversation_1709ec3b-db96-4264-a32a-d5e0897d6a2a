import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import type { GoodsGroupResponseItem } from '@dfe/dfe-book-api';

export const useGoodsGroups = () => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { orderType } = storeToRefs(formStore);

  const query = useCustomerQuery(
    'goodsGroups',
    (query) =>
      api.book.customers.getCustomerGoodsGroups({
        ...query,
        orderType: orderType.value,
      }),
    {
      placeholderData: [],
      queryKey: [orderType],
    },
  );

  const isGoodsGroupQuantityActivated = (code: GoodsGroupResponseItem['code']) => {
    return query.data.value?.find((item) => item.code === code)?.quantityActivated ?? false;
  };

  return {
    ...query,
    isGoodsGroupQuantityActivated,
  };
};
