<template>
  <VTooltip v-if="showTooltipComputed" v-bind="{ attach: $appRoot, ...tooltipProps }">
    <template #activator="{ props: activatorProps }">
      <component :is="wrapperTag" v-bind="activatorProps">
        <slot name="content"></slot>
      </component>
    </template>
    <slot name="tooltipContent">{{ tooltipProps?.text }}</slot>
  </VTooltip>

  <component :is="wrapperTag" v-else>
    <slot name="content"></slot>
  </component>
</template>

<script setup lang="ts">
import { computed, useSlots } from 'vue';
import { VTooltip } from 'vuetify/components/VTooltip';

type Props = {
  wrapperTag?: 'span' | 'div';
  showTooltip?: boolean;
  tooltipProps?: Partial<VTooltip>;
};

const props = withDefaults(defineProps<Props>(), {
  wrapperTag: 'div',
  showTooltip: true,
  tooltipProps: () => ({}) as Partial<VTooltip>,
});

const slots = useSlots();

const showTooltipComputed = computed(() => {
  return props.showTooltip && (!!props.tooltipProps.text || !!slots.tooltipContent);
});
</script>
