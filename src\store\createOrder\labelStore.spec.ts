import { labels } from '@/mocks/fixtures/labels';
import { airExportOrder, roadForwardingOrder } from '@/mocks/fixtures/order';
import { mockServer } from '@/mocks/server';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { PrintLabelStartPosition } from '@dfe/dfe-book-api';
import type { Server } from 'miragejs';
import { initPinia } from '../../../test/util/init-pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { expect } from 'vitest';

describe('Label Store', () => {
  beforeEach(() => {
    initPinia();
  });

  it('should set initial state', () => {
    const labelStore = useLabelStore();

    expect(labelStore.$state).toEqual({
      isFetchLabelsLoading: false,
    });
  });

  describe('Succeeding to save order', () => {
    let server: Server;

    beforeAll(() => {
      server = mockServer({
        environment: 'test',
        fixtures: {
          labels,
          roadForwardingOrder,
        },
      });
    });

    it('fetches labels successful', async () => {
      const labelStore = useLabelStore();

      await expect(labelStore.fetchLabels()).resolves.toEqual(labels[0]);
    });

    it('(re)fetches labels successfully - without save action', async () => {
      const labelStore = useLabelStore();

      await expect(
        labelStore.fetchLabelsForExistingOrder(101, PrintLabelStartPosition.BOTTOM_RIGHT),
      ).resolves.toEqual({ orderLabel: labels[0].orderLabel });
    });

    it('logs error on print label request error', async () => {
      const labelStore = useLabelStore();

      await labelStore.fetchLabelsForExistingOrder(999, PrintLabelStartPosition.BOTTOM_RIGHT);
      expect(labelStore.client.log.error).toHaveBeenCalled();
    });

    it('(re)fetches bulk print labels successfully - without save action', async () => {
      const labelStore = useLabelStore();

      await expect(
        labelStore.fetchLabelsForBulkOrder([101, 102], PrintLabelStartPosition.BOTTOM_RIGHT),
      ).resolves.toEqual(labels[1].orderLabel);
    });

    it('logs error on bulk print label request error', async () => {
      const labelStore = useLabelStore();

      await labelStore.fetchLabelsForBulkOrder([999], PrintLabelStartPosition.BOTTOM_RIGHT);
      expect(labelStore.client.log.error).toHaveBeenCalled();
    });

    it('sets orderId when fetching labels', async () => {
      const labelStore = useLabelStore();
      const orderStore = useCreateOrderFormStore();

      expect(orderStore.saveOrderData).toBe(null);

      await labelStore.fetchLabels();

      expect(orderStore.orderId).toBe(null);

      orderStore.saveOrderData = airExportOrder;

      await labelStore.fetchLabels();

      expect(orderStore.orderId).toBe(1);

      orderStore.$reset();
      labelStore.$reset();
    });

    it('sets saveOrderData when fetching labels', async () => {
      const labelStore = useLabelStore();
      const orderStore = useCreateOrderFormStore();

      expect(orderStore.saveOrderData).toBe(null);

      await labelStore.fetchLabels();

      expect(orderStore.saveOrderData).toEqual(airExportOrder);
      orderStore.$reset();
      labelStore.$reset();
    });

    afterAll(() => {
      server.shutdown();
    });
  });
});
