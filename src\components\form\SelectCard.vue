<template>
  <div class="select-card position-relative">
    <VRadio
      :id="radioId"
      v-model="selection"
      v-data-test="'select-card'"
      :name="name"
      :true-value="value"
      :disabled
      class="position-absolute h-100 ml-4"
    />
    <VLabel :for="radioId" class="select-card__label d-block pl-12">
      <h3 v-data-test="'radio-card-label'" class="text-h5" :class="{ 'mb-1': !!description }">
        {{ label }}
      </h3>
      <p
        v-if="description"
        v-data-test="'radio-card-description'"
        class="description text-body-3 mb-0"
      >
        {{ description }}
      </p>
    </VLabel>
  </div>
</template>
<script setup lang="ts">
import { createUuid } from '@/utils/createUuid';
import { computed } from 'vue';

type ValueType = string | object | number | boolean;

interface Props {
  value?: ValueType;
  name: string;
  label: string;
  description?: string;
  id?: string;
  disabled?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  value: true,
  description: undefined,
  id: undefined,
  disabled: false,
});

const selection = defineModel<ValueType | undefined>();

const radioId = computed(() => props.id ?? `select-card__${createUuid()}`);
</script>

<style scoped lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables';

.select-card {
  .VRadio {
    :deep(.v-selection-control__wrapper) {
      &,
      & * {
        display: block;
        margin: 0;
        height: 16px;
        width: 16px;
      }
    }
  }

  .v-label {
    cursor: pointer;
    border: 1px solid var(--color-base-grey-200);
    border-radius: 8px;
    padding: 16px;

    p {
      text-wrap: wrap;
    }
  }

  .v-selection-control--dirty {
    + .v-label {
      border-color: var(--color-base-grey-400);
    }
  }

  &:hover {
    :deep(.v-radio.v-selection-control .icon) {
      color: var(--color-base-blue-700);
    }
    .v-label {
      border-color: var(--color-base-blue-700);
    }
  }

  .v-selection-control--focused + .v-label {
    border-color: transparent;
    outline: 2px solid var(--color-base-blue-500);
  }

  .v-radio.v-selection-control.v-selection-control--disabled {
    :deep(.v-icon .icon) {
      color: var(--color-base-grey-400);
    }

    + .v-label {
      cursor: default;
      border-color: var(--color-base-grey-200);

      p,
      h3 {
        color: var(--color-base-grey-400);
      }
    }

    &.v-selection-control--dirty + .v-label {
      border-color: var(--color-base-grey-400);
    }
  }
}

:deep(.v-selection-control__wrapper) {
  --v-selection-control-size: 16px;
}
</style>
