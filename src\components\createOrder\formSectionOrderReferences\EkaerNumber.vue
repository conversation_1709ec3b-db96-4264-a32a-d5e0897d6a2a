<template>
  <div>
    <label :for="textFieldId" class="d-flex text-label-3 mt-4">
      {{ t('labels.ekaer_number.text') }}
      <span v-if="!ekaerNumberNotRequired" class="label-indicator">*</span>
      <InfoButtonWithTooltip :label="t('messages.id6339.text')" class="ml-2" />
    </label>
    <div class="grid-container-form | align-start mt-1">
      <v-form ref="formRef">
        <TextField
          :id="textFieldId"
          v-model="ekaerNumber"
          :required="!ekaerNumberNotRequired"
          :disabled="ekaerNumberNotRequired"
          prepend="E"
          :max-length="ekaerMaxLength"
          :rules="
            !ekaerNumberNotRequired && ekaerNumber != undefined ? [useValidationRules.ekaer] : []
          "
          @paste="onPasteEkaer"
        />
        <CheckboxField
          v-model="ekaerNumberNotRequired"
          :label="t('labels.ekaer_number_not_required.text')"
          class="mt-6"
          @change="triggerValidation"
        />
      </v-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import TextField from '@/components/form/TextField.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { createUuid } from '@/utils/createUuid';
import { MaxLength } from '@/enums';
import { useI18n } from 'vue-i18n';
import { onUnmounted, ref, watch } from 'vue';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { VForm } from 'vuetify/components';

const { t } = useI18n();

const textFieldId = `text-field-${createUuid()}`;

const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();
const { ekaerNumber, ekaerNumberNotRequired } = storeToRefs(createOrderOrderReferencesFormStore);

const ekaerMaxLength = MaxLength.EkaerNo; // incl. leading "E"
const formRef = ref<InstanceType<typeof VForm> | null>(null);

const onPasteEkaer = (event: ClipboardEvent) => {
  const pastedText = event.clipboardData?.getData('text');

  if (
    pastedText &&
    pastedText.length >= ekaerMaxLength &&
    pastedText.toUpperCase().startsWith('E')
  ) {
    event.preventDefault();
    ekaerNumber.value = pastedText.slice(1, ekaerMaxLength + 1);
  }
};

watch(ekaerNumberNotRequired, (newVal) => {
  if (newVal) ekaerNumber.value = undefined;
  triggerValidation();
});

onUnmounted(() => {
  ekaerNumber.value = '';
});

const triggerValidation = () => {
  formRef.value?.validate();
};
</script>

<style lang="scss" scoped>
.tooltip-small {
  max-width: 160px;
}
</style>
