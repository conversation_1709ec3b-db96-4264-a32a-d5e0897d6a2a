import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import type { MeasurementProposalsMenu } from '@/types/createOrder';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import * as validation from '@/composables/form/useValidationRules';

const props = {
  items: [
    {
      length: 12,
      width: 34,
      value: '12-34',
      menuText: '<strong>12 cm x</strong> 34 cm',
    },
    {
      length: 56,
      width: 78,
      value: '56-78',
      menuText: '<strong>56 cm x</strong> 78 cm',
    },
  ] satisfies MeasurementProposalsMenu,
  modelValue: 12,
};
const label = 'Label';
const requiredChar = '*';

describe('ComboboxMeasurementFields component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(ComboboxMeasurementFields, {
      props,
    });
  });

  it('emits input event with numeric value on change', async () => {
    const combobox = wrapper.getComponent({ name: 'v-combobox' });

    await combobox.vm.$emit('update:modelValue', '12');

    const event = wrapper.emitted();

    expect(event['update:modelValue']).toHaveLength(1);
    expect(event['update:modelValue']?.[0]).toEqual([12]);
  });

  it('emits input event with not sanitized numeric value', async () => {
    const combobox = wrapper.getComponent({ name: 'v-combobox' });

    await combobox.vm.$emit('update:modelValue', '1.2');

    const event = wrapper.emitted();

    expect(event['update:modelValue']).toHaveLength(1);
    expect(event['update:modelValue']?.[0]).toEqual([1.2]);
  });

  it.skip('emits multiple-input event with object value on change', async () => {
    await wrapper
      .findComponent({ name: 'v-combobox' })
      .vm.$emit('update:modelValue', { length: 12, width: 34 });

    const event = wrapper.emitted('inputMultiple');
    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([{ length: 12, width: 34 }]);
  });

  it('shows html text in dropdown if specified', async () => {
    await wrapper.setProps({ itemHtmlText: 'html' });

    const el = wrapper.get('.v-field__input [type="number"]');
    await el.trigger('focus');

    const options = wrapper.findAll('.v-list-item__content span');
    for (let i = 0; i < options.length; i++) {
      expect(options.at(i)?.html()).toEqual(`<span>${props.items[i].menuText}</span>`);
    }
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it('shows manual error message based on input validity', async () => {
    const mockInputValidity = {
      valid: true,
      badInput: false,
      customError: false,
      patternMismatch: false,
      rangeOverflow: false,
      rangeUnderflow: false,
      stepMismatch: false,
      tooLong: false,
      tooShort: false,
      typeMismatch: false,
      valueMissing: false,
    };
    const spyOnGetMessages = vi
      .spyOn(validation, 'getMessages' as never)
      .mockReturnValue('error message');
    const inputSpy = vi.spyOn(window.HTMLInputElement.prototype, 'validity', 'get');

    const comboboxTextField = wrapper.findComponent({ name: 'v-text-field' });
    const input = comboboxTextField.find('input');
    await wrapper.setProps({ required: false });

    expect(wrapper.find('.v-input__details').exists()).toBe(false);

    inputSpy.mockReturnValueOnce({ ...mockInputValidity, valid: false });
    comboboxTextField.vm.$emit('update:modelValue', 'e');
    await comboboxTextField.vm.$emit('update:focused', false);
    await wrapper.vm.$nextTick();
    expect(spyOnGetMessages).toHaveBeenCalled();
    expect(comboboxTextField.html()).toContain('error message');
    expect(comboboxTextField.find('.v-input__details').exists()).toBe(true);

    await comboboxTextField.vm.$emit('update:focused', true);
    await wrapper.vm.$nextTick();
    expect(comboboxTextField.find('.v-input__details').exists()).toBe(false);

    inputSpy.mockReturnValueOnce({ ...mockInputValidity, valid: true });
    comboboxTextField.vm.$emit('update:modelValue', '123');
    await input.trigger('blur');
    await comboboxTextField.vm.$emit('update:focused', false);
    expect(comboboxTextField.find('.v-input__details').exists()).toBe(false);

    vi.restoreAllMocks();
  });
});
