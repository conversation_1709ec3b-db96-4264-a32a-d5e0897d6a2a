import { useInit } from '@/composables/useInit';
import { useQuery } from '@tanstack/vue-query';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import type { Ref } from 'vue';
import { computed } from 'vue';
import type { OrderType } from '@dfe/dfe-book-api';

export const useDocumentTypes = () => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { orderType } = storeToRefs(formStore);
  return useQuery({
    queryKey: ['documentTypes', orderType] as [string, Ref<OrderType>],
    enabled: computed(() => !!orderType.value),
    queryFn: () =>
      api.book.documents
        .getOrderTypeDocumentTypes({ orderType: orderType.value })
        .then((res) => res.data ?? []),
    placeholderData: [],
    staleTime: Infinity,
  });
};
