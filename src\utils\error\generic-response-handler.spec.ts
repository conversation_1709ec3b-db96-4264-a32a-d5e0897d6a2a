import { GenericResponseHandler } from '@/utils/error/generic-response-handler';
import type { DFEClient } from '@dfe/dfe-frontend-client';
import { createClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';

describe('GenericResponseHandler', () => {
  const client = createClient<Events>({}) as DFEClient<Events>;

  it('should log error', () => {
    vi.spyOn(client.log, 'error');

    expect(GenericResponseHandler(client)).toBeDefined();
  });
});
