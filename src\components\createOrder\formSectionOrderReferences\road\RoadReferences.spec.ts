import RoadReferences from '@/components/createOrder/formSectionOrderReferences/road/RoadReferences.vue';
import { OrderTypes } from '@/enums';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { addresses } from '@/mocks/fixtures/addresses';
import { customers } from '@/mocks/fixtures/customers';
import { mockServer } from '@/mocks/server';
import { OrderReferenceType, OrderStatus } from '@dfe/dfe-book-api';
import { nextTick } from 'vue';
import EkaerNumber from '@/components/createOrder/formSectionOrderReferences/EkaerNumber.vue';
import MultipleTextFields from '@/components/createOrder/formSectionOrderReferences/MultipleTextFields.vue';
import AddButton from '@/components/createOrder/AddButton.vue';

describe('Order references - RoadReferences component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customers,
        addresses,
      },
    });
  });

  beforeEach(() => {
    wrapper = mount(RoadReferences);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  // EKAER number
  it('hides ekaer number input (default)', async () => {
    expect(wrapper.findComponent({ name: 'ekaer-number' }).exists()).toBe(false);
  });

  it('shows ekaer number input if shipper address is HU', async () => {
    const store = useCreateOrderAddressesStore();
    store.shipperAddress.address.countryCode = 'HU';

    await nextTick();

    expect(wrapper.findComponent(EkaerNumber).exists()).toBe(true);
    store.$reset();
  });

  it('shows ekaer number input if selected customer is from HU in forwarding orders', async () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.RoadForwardingOrder;
    store.customerNumber = '00000001';
    const store2 = useCreateOrderDataStore();

    store2.customers = [
      {
        customerNumber: '00000001',
        label: '3',
        address: {
          id: 4,
          name: '5',
          street: '6',
          postcode: '7',
          city: '8',
          countryCode: 'HU',
        },
      },
    ];

    await nextTick();

    expect(wrapper.findComponent(EkaerNumber).exists()).toBe(true);
    store.$reset();
    store2.$reset();
  });

  it('shows identification code transport input if selected customer is from RO in forwarding orders', async () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.RoadForwardingOrder;
    store.customerNumber = '00000001';
    const store2 = useCreateOrderDataStore();

    store2.customers = [
      {
        customerNumber: '00000001',
        label: '3',
        address: {
          id: 4,
          name: '5',
          street: '6',
          postcode: '7',
          city: '8',
          countryCode: 'RO',
        },
      },
    ];

    await nextTick();
    const input = wrapper.find('[data-test-id="identification-code-transport"]');
    expect(input.exists()).toBe(true);
    store.$reset();
    store2.$reset();
  });

  // Purchase order numbers and delivery note numbers
  it('hides purchase order numbers and delivery note numbers inputs', async () => {
    expect(wrapper.findComponent(MultipleTextFields).exists()).toBe(false);
  });

  // Add buttons for for purchase order and delivery note numbers
  it.skip('shows add buttons for purchase order and delivery note numbers and booking reference', async () => {
    const addButtons = wrapper.findAllComponents(AddButton);
    expect(addButtons).toHaveLength(2);
  });

  it('focus just added purchase order number', async () => {
    wrapper.unmount();

    wrapper = mount(RoadReferences, {
      attachTo: document.body,
    });

    const store = useCreateOrderOrderReferencesFormStore();

    // Click purchase order button
    const addButtons = wrapper.findAllComponents(AddButton);
    await addButtons.at(0)?.trigger('click');

    expect(wrapper.findComponent(MultipleTextFields).findAll('input')).toHaveLength(1);

    let lastAddedInputElementId = store.getLastReferenceId(
      OrderReferenceType.PURCHASE_ORDER_NUMBER,
    );

    const input = wrapper.find(`input#${lastAddedInputElementId}`);
    expect(input.exists()).toBe(true);

    // Second click
    await addButtons.at(0)?.trigger('click');

    expect(wrapper.findComponent(MultipleTextFields).findAll('input')).toHaveLength(2);

    lastAddedInputElementId = store.getLastReferenceId(OrderReferenceType.PURCHASE_ORDER_NUMBER);

    const input2 = wrapper.find(`input#${lastAddedInputElementId}`);
    expect(input2.exists()).toBe(true);
  });

  it('dont disable order number when order is complete and order number is not set', async () => {
    const store = useCreateOrderFormStore();
    store.orderData = {
      orderType: OrderTypes.RoadForwardingOrder,
      orderStatus: {
        status: OrderStatus.COMPLETE,
      },
    };

    await nextTick();

    expect(wrapper.findComponent({ name: 'v-text-field' }).props('disabled')).toBe(false);
  });

  it('disable order number when order is complete and order number is set', async () => {
    const store = useCreateOrderFormStore();
    store.orderNumber = '123';
    store.orderData = {
      orderType: OrderTypes.RoadForwardingOrder,
      orderStatus: {
        status: OrderStatus.COMPLETE,
      },
      references: [
        {
          id: 1,
          referenceType: OrderReferenceType.ORDER_NUMBER,
          referenceValue: '123',
        },
      ],
    };

    await nextTick();

    expect(wrapper.findComponent({ name: 'v-text-field' }).props('disabled')).toBe(true);
  });
});
