import useTimeOptions from '@/composables/dateTimeUtilities/useTimeOptions';

describe('useTimeOptions composable', () => {
  it('returns a list of formatted times (24h)', () => {
    const options = useTimeOptions('HH:mm');

    expect(options).toHaveLength(48);
    expect(options[0]).toEqual('00:00');
    expect(options[options.length / 2]).toEqual('12:00');
    expect(options[options.length - 1]).toEqual('23:30');
  });

  it('returns a list of formatted times (12h)', () => {
    const options = useTimeOptions('hh:mm a');

    expect(options).toHaveLength(48);
    expect(options[0]).toEqual('12:00 AM');
    expect(options[options.length / 2]).toEqual('12:00 PM');
    expect(options[options.length - 1]).toEqual('11:30 PM');
  });
});
