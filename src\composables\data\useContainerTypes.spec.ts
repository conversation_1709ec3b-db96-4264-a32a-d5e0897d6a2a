import { describe, expect, it, beforeAll } from 'vitest';
import { nextTick } from 'vue';
import { mockServer } from '@/mocks/server';
import { withSetup } from '@test/util/with-setup';
import { containerTypesWithLastUsed } from '@/mocks/fixtures/containerTypes';
import { containerTypesAsOptions as mappedContainerTypes } from '@/mocks/fixtures/containerTypes';
import { useContainerTypes } from '@/composables/data/useContainerTypes';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { Segment } from '@dfe/dfe-book-api';

describe('useCollectionInterpreterOptions', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        containerTypesWithLastUsed,
      },
    });
  });

  it('returns formatted container types', async () => {
    const formStore = useCreateOrderFormStore();
    const { transportType, customerNumber } = storeToRefs(formStore);
    transportType.value = Segment.ROAD;
    customerNumber.value = '00000001';

    const { containerTypesAsOptions } = withSetup(() => useContainerTypes())[0];

    await nextTick();

    await vi.waitFor(() => {
      expect(containerTypesAsOptions.value).toEqual(mappedContainerTypes);
    });
  });
});
