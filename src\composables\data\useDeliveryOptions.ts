import { useQuery } from '@tanstack/vue-query';
import { useInit } from '@/composables/useInit';
import { computed } from 'vue';
import { Options } from '@dfe/dfe-book-api';

export const useDeliveryOptions = () => {
  const { api } = useInit();
  const query = useQuery({
    queryKey: ['deliveryOptions'],
    async queryFn() {
      return api.book.deliveryOptions.getDeliveryOptions().then((d) => d.data);
    },
  });

  const selectableDeliveryOptions = computed<Options>(() => [
    ...(query.data?.value ? query.data.value : []),
  ]);

  return {
    selectableDeliveryOptions,
    ...query,
  };
};
