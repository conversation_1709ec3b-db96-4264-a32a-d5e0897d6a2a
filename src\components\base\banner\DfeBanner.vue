<template>
  <VBanner
    v-show="value"
    class="banner"
    :class="`banner--${type}`"
    variant="outlined"
    @input="emit('input', $event)"
  >
    <template #prepend>
      <MaterialSymbol size="24" :color="bannerType.color">
        <component :is="bannerType.icon" />
      </MaterialSymbol>
    </template>
    <span class="error-banner__message"><slot /></span>
  </VBanner>
</template>
<script setup lang="ts">
import type { BannerType } from '@/components/base/banner/banner.types';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import ErrorIcon from '@dfe/dfe-frontend-styles/assets/icons/error-24px.svg';
import InfoIcon from '@dfe/dfe-frontend-styles/assets/icons/info-24px.svg';
import WarningIcon from '@dfe/dfe-frontend-styles/assets/icons/warning-24px.svg';
import { computed, shallowRef } from 'vue';

interface Props {
  value?: boolean;
  type: BannerType;
}

const props = withDefaults(defineProps<Props>(), {
  value: true,
  type: 'info',
});

const emit = defineEmits(['input']);

const bannerConfig = shallowRef({
  error: {
    color: 'red',
    icon: ErrorIcon,
  },
  info: {
    color: 'blue',
    icon: InfoIcon,
  },
  warning: {
    color: 'orange',
    icon: WarningIcon,
  },
});

const bannerType = computed(() => {
  return bannerConfig.value[props.type] ?? bannerConfig.value.info;
});
</script>

<style scoped lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables.scss' as dfevars;
@use 'vuetify/lib/styles/settings/variables' as vuevars;
@use '@/styles/variables' as vars;
@use '@/styles/base' as base;

.v-banner {
  border-radius: base.space(2);
  padding: base.space(4);
  border: none;

  display: flex;
  align-items: center;

  /**
  loop through banner types and create a class for each
  setting its colors properly
   */
  @mixin bannerType($name, $color) {
    &.banner--#{$name} {
      background-color: var(--color-base-#{$color}-100);

      :deep(.material-symbol.#{$color}--text) {
        color: var(--color-base-#{$color}-500) !important;
      }
    }
  }

  @include bannerType('error', 'red');
  @include bannerType('info', 'blue');
  @include bannerType('warning', 'orange');

  .v-banner__wrapper {
    padding: 0;
    border-bottom: none;
  }

  .v-banner__icon {
    align-self: flex-start !important;
    height: auto !important;
    width: auto !important;
    min-width: auto !important;
    margin-right: base.space(2);
  }
}

:deep(.v-banner__prepend) {
  display: flex;
  margin-top: 0;
  margin-bottom: 0;
  margin-right: base.space(2);
}
</style>
