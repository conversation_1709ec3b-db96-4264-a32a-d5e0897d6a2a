import { OrderTypes } from '@/enums';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import type { PreferredCurrency } from '@/types/createOrder';
import { defineStore, storeToRefs } from 'pinia';

import { i18n } from '@/plugins/i18n';
import { fullContainerLoadStorage } from '@/store/createOrder/useFullContainerLoad';
import { HandOverSelection } from '@/types/hand-over';
import {
  Address,
  BasicQuoteInformation,
  Division,
  isBadGatewayProblem,
  OrderReferenceType,
  OrderRequestBody,
  OrderResponseBody,
  OrderSaveAction,
  OrderStatus,
  OrderSubmitResult,
  OrderType,
  SeaOrder,
  Segment,
  ValidationResult,
} from '@dfe/dfe-book-api';
import { useCreateOrderOrderReferencesFormStore } from './formOrderReferences';

interface GeneralProblem {
  oasDiscriminator: string;
  type: string;
  errorId: string;
  title: string;
  status: number;
  detail: string;
  severity: 'Critical' | 'High' | 'Moderate' | 'Low';
  traceId?: string;
  timestamp: string;
}

export interface CreateOrderFormState {
  orderId: number | null;
  transportType: Segment;
  orderType: OrderType;
  preferredOrderType: OrderType | null;
  preferredCurrency?: PreferredCurrency;
  customerNumber: string;
  orderNumber: string;
  quoteInformation: BasicQuoteInformation | null;
  orderData: OrderResponseBody | null;
  createAnother: boolean;
  validateData: ValidationResult | null;
  saveOrderData: OrderResponseBody | null;
  submitResultData: OrderSubmitResult | null;
  isSubmitLoading: boolean;
  isSaveLoading: boolean;
  isValidateLoading: boolean;
  customTypeValidationError: boolean;
  isEditMode: boolean;
  isOrderLoaded: boolean;
  isValidationTriggered: boolean;
  initialOrder: OrderRequestBody | null;
}

export const useCreateOrderFormStore = defineStore('createOrderForm', {
  state: (): CreateOrderFormState => ({
    orderId: null,
    transportType: Segment.ROAD,
    orderType: OrderTypes.RoadForwardingOrder,
    preferredOrderType: null,
    preferredCurrency: undefined,
    customerNumber: '',
    orderNumber: '',
    quoteInformation: null,
    orderData: null,
    createAnother: false,
    validateData: null,
    saveOrderData: null,
    submitResultData: null,
    isSaveLoading: false,
    isValidateLoading: false,
    isSubmitLoading: false,
    customTypeValidationError: false,
    isEditMode: false,
    isOrderLoaded: false,
    isValidationTriggered: false,
    initialOrder: null,
  }),
  getters: {
    selectedCustomer({ customerNumber }) {
      const createOrderDataStore = useCreateOrderDataStore();
      const { customers } = createOrderDataStore;
      return customers?.find((customer) => customer.customerNumber === customerNumber) ?? {};
    },
    isCashOnDelivery(): boolean {
      return this.selectedCustomer.cashOnDelivery ?? false;
    },
    isFoodLogistics(): boolean {
      return Division.FOOD_LOGISTICS === this.selectedCustomer.division;
    },
    isEuropeanLogistics(): boolean {
      return Division.EUROPEAN_LOGISTICS === this.selectedCustomer.division;
    },
    isRoadOrder(): boolean {
      return this.isRoadForwardingOrder || this.isRoadCollectionOrder;
    },
    isAirOrder(): boolean {
      return this.isAirExportOrder || this.isAirImportOrder;
    },
    isSeaOrder(): boolean {
      return this.isSeaExportOrder || this.isSeaImportOrder;
    },
    isRoadForwardingOrder(): boolean {
      return this.orderType === OrderTypes.RoadForwardingOrder;
    },
    isRoadCollectionOrder(): boolean {
      return this.orderType === OrderTypes.RoadCollectionOrder;
    },
    isAirAndSeaOrder(): boolean {
      return (
        this.isAirExportOrder ||
        this.isAirImportOrder ||
        this.isSeaExportOrder ||
        this.isSeaImportOrder
      );
    },
    isAirExportOrder(): boolean {
      return this.orderType === OrderTypes.AirExportOrder;
    },
    isAirImportOrder(): boolean {
      return this.orderType === OrderTypes.AirImportOrder;
    },
    isSeaExportOrder(): boolean {
      return this.orderType === OrderTypes.SeaExportOrder;
    },
    isSeaImportOrder(): boolean {
      return this.orderType === OrderTypes.SeaImportOrder;
    },
    isImportOrder(): boolean {
      return this.isAirImportOrder || this.isSeaImportOrder;
    },
    isExportOrder(): boolean {
      return this.isAirExportOrder || this.isSeaExportOrder;
    },
    isOrderFromQuote(): boolean {
      return !!this?.quoteInformation?.quoteRequestId;
    },
    isRoadOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isRoadOrder;
    },
    isRoadForwardingOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isRoadForwardingOrder;
    },
    isRoadOrderFromQuoteWithDailyPrice(): boolean {
      const { dailyPriceReference } = storeToRefs(useCreateOrderOrderReferencesFormStore());
      return this.isRoadOrderFromQuote && !!dailyPriceReference.value;
    },
    isAirExportOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isAirExportOrder;
    },
    isAirImportOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isAirImportOrder;
    },
    isAirOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isAirOrder;
    },
    isSeaOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isSeaOrder;
    },
    isAslOrderFromQuote(): boolean {
      return this.isSeaOrderFromQuote || this.isAirOrderFromQuote;
    },
    isSeaImportOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isSeaImportOrder;
    },
    isSeaExportOrderFromQuote(): boolean {
      return this.isOrderFromQuote && this.isSeaExportOrder;
    },
    isDisabledForPriceRelevantChanges(): boolean {
      return this.isAirOrderFromQuote || this.isRoadOrderFromQuoteWithDailyPrice;
    },
    getTransportCountryFromAddress(): Address | null {
      const { shipperAddress } = useCreateOrderAddressesStore();
      if (this.isRoadForwardingOrder && this.selectedCustomer.address)
        return this.selectedCustomer.address;
      return shipperAddress.address;
    },
    getFromAddress(): Address | null {
      const { shipperHandOverSelection } = useCreateOrderAddressesStore();

      switch (shipperHandOverSelection.selection) {
        case HandOverSelection.alternateAddress:
          return shipperHandOverSelection.address;
        case HandOverSelection.port:
          return { countryCode: shipperHandOverSelection.port?.countryCode };
        default:
          return this.getTransportCountryFromAddress;
      }
    },
    getToAddress(): Address | null {
      const { consigneeHandOverSelection, consigneeAddress } = useCreateOrderAddressesStore();

      switch (consigneeHandOverSelection.selection) {
        case HandOverSelection.alternateAddress:
          return consigneeHandOverSelection.address;
        case HandOverSelection.port:
          return { countryCode: consigneeHandOverSelection.port?.countryCode };
        default:
          return consigneeAddress.address;
      }
    },
    transportCountry(): {
      fromCountry: string | null;
      toCountry: string | null;
      fromPostcode: string | null;
      toPostcode: string | null;
    } {
      const fromAddress = this.getFromAddress;
      const toAddress = this.getToAddress;

      return {
        fromCountry: fromAddress?.countryCode ?? null,
        toCountry: toAddress?.countryCode ?? null,
        fromPostcode: fromAddress?.postcode || null,
        toPostcode: toAddress?.postcode || null,
      };
    },
    shipmentNumber(): number | undefined {
      if (this.saveOrderData) {
        return this.saveOrderData.shipmentNumber;
      }
    },
    initialOrderNumber(): string | undefined {
      if (this.orderData?.references) {
        const orderNumber = this.orderData.references.filter(
          (item) => item.referenceType === OrderReferenceType.ORDER_NUMBER,
        );

        if (orderNumber.length > 0) {
          return orderNumber[0].referenceValue;
        }
      }

      return undefined;
    },
    orderInformation({ customerNumber, transportType }): {
      customerNumber: string;
      customerSegment: Segment;
    } {
      return {
        customerNumber,
        customerSegment: transportType,
      };
    },
    isPrincipalLocked(): boolean {
      return this.orderData?.principalLocked ?? false;
    },
    isClonedOrder(): boolean {
      return this.orderData?.clonedOrder ?? false;
    },
    isClonedDraftOrder(): boolean {
      return this.isClonedOrder && this.currentOrderStatus === OrderStatus.DRAFT;
    },
    isCompleteRoadOrder(): boolean {
      return this.currentOrderStatus === OrderStatus.COMPLETE && this.isRoadOrder;
    },
    isDraftOrder(): boolean {
      return this.currentOrderStatus === OrderStatus.DRAFT || this.orderData === null;
    },
    isOrderSaved(): boolean {
      return !!this.saveOrderData?.orderId || !!this.orderData?.orderId;
    },
    currentOrderStatus(): OrderStatus | undefined {
      return this.saveOrderData?.orderStatus?.status ?? this.orderData?.orderStatus?.status;
    },
  },
  actions: {
    async fetchCreationPreferences() {
      try {
        const { data } = await this.api.book.preferences.getCreationPreferences();
        this.preferredOrderType = data.orderType ?? null;
        this.preferredCurrency = data.preferredCurrency;
      } catch (error) {
        this.client?.log.error('Failed to fetch creation preferences', 'dfe-book-frontend', error);
      }
    },
    async saveOrder(orderData: OrderRequestBody) {
      this.isSaveLoading = true;

      let saved = false;
      let problem: GeneralProblem | null = null;

      try {
        const { data } = await this.api.book.v2.saveDraftOrderV2(orderData);
        if (!data.order) {
          throw new Error('saving the order as draft returned no order data');
        }

        this.saveOrderData = data.order;
        saved = true;
        problem = null;
        this.postSaveAction(this.saveOrderData);
        return { saved, problem };
      } catch (error) {
        this.client?.log.error('Failed to save order', 'dfe-book-frontend', error);

        return { saved: false, problem: (error as { error?: GeneralProblem }).error };
      } finally {
        this.isSaveLoading = false;
      }
    },
    async getOrder(orderId: number) {
      try {
        const { data } = await this.api.book.orders.getOrderDetails(orderId);
        this.orderData = data;
      } catch (error) {
        this.client?.log.error('Failed to get order', 'dfe-book-frontend', error);
      }
    },
    async validateOrder(order: OrderRequestBody) {
      this.isValidateLoading = true;
      try {
        const { data } = await this.api.book.v2.saveOrderV2(OrderSaveAction.Validate, order);
        if (!(data.order && data.validationResult)) {
          throw new Error('validation returned no order data or validation result');
        }
        this.saveOrderData = data.order;
        this.validateData = data.validationResult;
        this.postSaveAction(this.saveOrderData);
        return data.validationResult;
      } catch (error) {
        this.client?.log.error('Failed to validate order', 'dfe-book-frontend', error);

        this.checkForTechnicalError(error);

        const validationError = (
          error as {
            error: {
              validationResult: ValidationResult;
            };
          }
        ).error;

        if (validationError.validationResult) {
          return validationError.validationResult;
        }

        return validationError;
      } finally {
        this.isValidateLoading = false;
      }
    },
    checkForTechnicalError(error: unknown) {
      const generalProblem = (error as { error?: GeneralProblem }).error;
      if (isBadGatewayProblem(generalProblem) && generalProblem?.errorId === 'errVA-01') {
        const { t } = i18n.global;
        this.client?.modal.show('error', {
          headline: t(generalProblem.title),
          message: t(generalProblem.detail),
        });
      }
    },
    async submitOrder(order: OrderRequestBody) {
      this.isSubmitLoading = true;

      try {
        const { data } = await this.api.book.v2.saveOrderV2(OrderSaveAction.Submit, order);
        if (data.order) {
          this.saveOrderData = data.order;
          this.postSaveAction(this.saveOrderData);
        }
        this.submitResultData = data;
        return data;
      } finally {
        this.isSubmitLoading = false;
      }
    },
    postSaveAction(order: OrderResponseBody) {
      this.postSeaSaveActions(order);
    },
    postSeaSaveActions(order: OrderResponseBody) {
      if (
        order.orderType == OrderTypes.SeaExportOrder ||
        order.orderType == OrderTypes.SeaImportOrder
      ) {
        const seaOrder = order as SeaOrder;
        fullContainerLoadStorage.value = seaOrder.fullContainerLoad;
      }
    },
    setInitialOrder(orderData: OrderRequestBody) {
      this.initialOrder = orderData;
    },
  },
});
