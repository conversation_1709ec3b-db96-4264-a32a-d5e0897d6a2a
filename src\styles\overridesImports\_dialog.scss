@use 'vuetify/lib/styles/settings/variables' as vuetify-variables;
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfe-variables;

//:deep(.v-dialog) {
//  background-color: var(--color-base-white);
//  box-shadow: $box-shadow-m;
//}
[dfe-book-frontend] {
:deep(.v-card.v-sheet.theme--light) {
  box-shadow: none !important;
}

:deep(.theme--light.v-card .v-card__subtitle),
:deep(.theme--light.v-card > .v-card__text) {
  color: var(--color-base-grey-900) !important;
}
}
