import { useCheckEmptyValuesWithIgnoredKeys } from '@/composables/useCheckEmptyValuesWithIgnoredKeys';

const emptyValue = {
  deliveryInstructions: '',
  goodsDescription: '',
  invoiceText: '',
  commonInstructions: '',
  otherInformation: '',
  id: 1,
  automaticDeliveryNotification: '',
};

const notEmptyValue = {
  deliveryInstructions: 'text',
  goodsDescription: 'text',
  invoiceText: '',
  commonInstructions: '',
  otherInformation: 'text',
  id: 1,
  automaticDeliveryNotification: null,
};

describe('useCheckEmptyValuesWithIgnoredKeys composable', () => {
  it('should return the correct title', () => {
    const resultOne = useCheckEmptyValuesWithIgnoredKeys(emptyValue, ['id']);
    const resultTwo = useCheckEmptyValuesWithIgnoredKeys(notEmptyValue, ['id']);

    expect(resultOne).toEqual(false);
    expect(resultTwo).toEqual(notEmptyValue);
  });
});
