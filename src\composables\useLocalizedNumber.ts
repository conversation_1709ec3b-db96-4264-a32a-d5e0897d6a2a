import type { Ref } from 'vue';
import { computed, unref } from 'vue';

export interface Options
  extends Pick<Intl.NumberFormatOptions, 'minimumFractionDigits' | 'maximumFractionDigits'> {
  style?: 'decimal' | 'percent';
}

export interface UnitOptions extends Omit<Options, 'style'> {
  style: 'unit';
  unit: NonNullable<Intl.NumberFormatOptions['unit']>;
  unitDisplay?: Intl.NumberFormatOptions['unitDisplay'];
}

export interface CurrencyOptions extends Omit<Options, 'style'> {
  style: 'currency';
  currency: NonNullable<Intl.NumberFormatOptions['currency']>;
  currencyDisplay?: Intl.NumberFormatOptions['currencyDisplay'];
}

function useLocalizedNumber(
  value: Ref<number> | number,
  options?: Options | UnitOptions | CurrencyOptions,
  locale?: Ref<string> | string,
) {
  switch (options?.style) {
    case 'unit':
      return computed(() =>
        Intl.NumberFormat(unref(locale), {
          ...options,
          unitDisplay: options.unitDisplay ?? 'short',
        }).format(unref(value)),
      );
    case 'currency':
      return computed(() =>
        Intl.NumberFormat(unref(locale), {
          ...options,
          currencyDisplay: options.currencyDisplay ?? 'code',
        }).format(unref(value)),
      );
    default:
      return computed(() => Intl.NumberFormat(unref(locale), options).format(unref(value)));
  }
}

export default useLocalizedNumber;
