import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import OrderLine from '@/components/createOrder/formSectionFreight/OrderLine.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import * as uuid from '@/utils/createUuid';
import { OrderTypes } from '@/enums';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import ComboboxField from '@/components/form/ComboboxField.vue';
import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import NumberField from '@/components/form/NumberField.vue';
import CounterField from '@/components/form/CounterField.vue';
import type { OrderLine4Store } from '@/store/createOrder/orderLine';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { expect } from 'vitest';
import type { TestUtils } from '@test/test-utils';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import { storeToRefs } from 'pinia';
import { AirExportQuoteInformation } from '@dfe/dfe-book-api';

const uuidSpy = vi.spyOn(uuid, 'createUuid');
uuidSpy.mockReturnValue(0);

const props = () =>
  ({
    localId: 0,
    lineCounter: 0,
    modelValue: {
      localId: 0,
      id: 0,
      number: 999,
      quantity: 1,
      packaging: {
        code: 'string',
        description: 'string',
      },
      content: 'string',
      weight: 99999,
      length: 100,
      width: 100,
      height: 100,
      volume: 1,
      loadingMeter: 0,
      goodsClassifications: [
        {
          hsCode: {
            code: 'test',
            description: 'test',
          },
          goods: 'string',
        },
      ],
      goodsGroup: {
        code: 'string',
        quantity: 999,
      },
      dangerousGoods: [],
    },
  }) satisfies { localId: number; lineCounter: number; modelValue: OrderLine4Store };

const mockValueWithNewPackagingCode = { ...props().modelValue };
mockValueWithNewPackagingCode.packaging = {
  code: 'string',
  description: 'string',
};

describe('OrderLine component', () => {
  let wrapper: TestUtils.VueWrapper<typeof OrderLine>;

  beforeEach(() => {
    wrapper = mount(OrderLine, {
      props: props(),
    });
  });

  it('shows textfields for content, if there are no contentOptions', async () => {
    await wrapper.vm.$nextTick();

    const contentCombobox = wrapper.findComponent(ComboboxField);
    const contentTextField = wrapper.findComponent({
      ref: 'ContentTextField',
    });
    expect(contentTextField.exists()).toBe(true);
    expect(contentCombobox.exists()).toBe(false);
  });

  it.each([
    ['quantity', '[data-test=book-order-line-quantity] input', 1],
    ['length', '[data-test=book-order-line-length] input', 100],
    ['width', '[data-test=book-order-line-width] input', 100],
    ['height', '[data-test=book-order-line-height] input', 100],
  ])('calculates volume on %s change', async (dimension, selector, value) => {
    wrapper.find(selector).setValue(value);
    await nextTick();
    expect(wrapper.props('modelValue')?.volume).toBe(1);
  });

  it("it don't launch a error if weight is missing", async () => {
    const weightField = wrapper.find('[data-test=book-order-line-weight]');

    const textField = weightField.findComponent({
      name: 'v-text-field',
    });

    const input = textField.find('input');

    expect(wrapper.find('.v-input__details').exists()).toBe(false);

    textField.vm.$emit('update:modelValue', null);
    await input.trigger('blur');
    await textField.vm.$emit('update:focused', false);

    await wrapper.vm.$nextTick();

    expect(textField.find('.v-input__details').exists()).toBe(false);
  });

  it('emits update after changing length', async () => {
    const measurementField = wrapper.findComponent(ComboboxMeasurementFields);

    measurementField.vm.$emit('update:modelValue', 100);

    await nextTick();

    expect(measurementField.vm.$props.modelValue).toBe(100);
  });

  it('emits update after changing width', async () => {
    const widthField = wrapper.findAllComponents(NumberField).at(0);
    widthField?.vm.$emit('update:modelValue', 100);
    expect(widthField?.vm.$props.modelValue).toBe(100);
  });

  it('emits update after changing height', async () => {
    const heightField = wrapper.findAllComponents(NumberField).at(1);
    heightField?.vm.$emit('update:modelValue', 100);
    expect(heightField?.vm.$props.modelValue).toBe(100);
  });

  it('disables all price related inputs if there is an order from Quote (air export or road with daily price)', async () => {
    const quantityInput = wrapper.findComponent(CounterField);
    const packagingInput = wrapper.findAllComponents(AutocompleteField).at(0);

    const dimensionsInput = wrapper.findAllComponents(ComboboxMeasurementFields);
    const numberInputs = wrapper.findAllComponents(NumberField);

    const priceRelatedInputs = [
      quantityInput,
      packagingInput,
      dimensionsInput.at(0),
      dimensionsInput.at(1),
      numberInputs.at(0),
      numberInputs.at(1),
      numberInputs.at(2),
    ];

    priceRelatedInputs.forEach((input) => {
      expect(input?.exists()).toEqual(true);
      expect(input?.props().disabled).toEqual(false);
    });

    const store = useCreateOrderFormStore();
    const referencesStore = useCreateOrderOrderReferencesFormStore();
    const orderLineStore = useCreateOrderOrderLineFormStore();
    store.orderType = OrderTypes.AirExportOrder;
    store.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;
    orderLineStore.originalPackaging = {
      code: 'string',
      description: 'string',
    };

    await wrapper.vm.$nextTick();

    priceRelatedInputs.forEach((input) => {
      expect(input?.props().disabled).toEqual(true);
    });

    store.orderType = OrderTypes.RoadForwardingOrder;
    referencesStore.dailyPriceReference = '060';
    await wrapper.vm.$nextTick();

    priceRelatedInputs.forEach((input) => {
      expect(input?.props().disabled).toEqual(true);
    });
  });

  it('should show search icon for hsCode autocomplete field', async () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.AirImportOrder;

    await wrapper.setProps({
      modelValue: {
        ...props().modelValue,
        goodsClassifications: [
          {
            hsCode: undefined,
            goods: '',
          },
        ],
      },
    });
    await wrapper.vm.$nextTick();
    const searchIcon = wrapper.findComponent({ ref: 'searchIcon' });
    expect(searchIcon.exists()).toBe(true);
  });

  it('should show delete icon for hsCode autocomplete field', async () => {
    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.AirImportOrder;
    await wrapper.setProps({
      modelValue: {
        ...props().modelValue,
        goodsClassifications: [
          {
            hsCode: {
              code: 'test',
              description: 'test',
            },
            goods: '',
          },
        ],
      },
    });
    await wrapper.vm.$nextTick();
    const searchIcon = wrapper.findComponent({ ref: 'searchIcon' });
    const deleteIcon = wrapper.findComponent({ ref: 'closeIcon' });
    expect(searchIcon.exists()).toBe(false);
    expect(deleteIcon.exists()).toBe(true);
  });

  it('should validate volume directly (not on blur), when length, width and height are set', async () => {
    const volumeTextField = wrapper.findComponent({
      ref: 'volumeField',
    });

    await wrapper.setProps({
      modelValue: {
        ...props().modelValue,
        length: null,
        width: null,
        height: null,
      },
    });

    await wrapper.vm.$nextTick();

    expect(volumeTextField.props().validateOnBlur).toBe(true);

    await wrapper.setProps({
      modelValue: {
        ...props().modelValue,
        length: 10,
        width: 10,
        height: 10,
      },
    });

    await wrapper.vm.$nextTick();

    expect(volumeTextField.props().validateOnBlur).toBe(false);
  });

  it('should NOT show packaging changed banner, if order is NOT from quote', async () => {
    const store = useCreateOrderFormStore();
    store.quoteInformation = null;

    await wrapper.vm.$nextTick();

    const packagingChangedBanner = wrapper.findComponent(DfeBanner);

    expect(packagingChangedBanner.exists()).toBe(false);
  });

  it('should NOT show packaging changed banner, if order is from quote and packaging has NOT changed', async () => {
    const store = useCreateOrderFormStore();
    store.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;

    await wrapper.vm.$nextTick();

    const packagingChangedBanner = wrapper.findComponent(DfeBanner);

    expect(packagingChangedBanner.exists()).toBe(false);
  });

  it('should show packaging changed banner, if order is from quote and packaging has changed', async () => {
    await wrapper.setProps({
      modelValue: {
        ...props().modelValue,
        packaging: {
          code: 'newString',
          description: 'newString',
        },
      },
    });

    const store = useCreateOrderFormStore();

    const { orderType, quoteInformation } = storeToRefs(store);

    orderType.value = OrderTypes.AirExportOrder;
    quoteInformation.value = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;

    await wrapper.vm.$nextTick();

    const packagingChangedBanner = wrapper.findComponent(DfeBanner);

    expect(packagingChangedBanner.exists()).toBe(true);
  });

  it('should allow max 99.9 for loading meter', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    orderType.value = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    const loadingMeterField = wrapper.findComponent({
      ref: 'loadingMeterField',
    });

    const textField = loadingMeterField.findComponent({
      name: 'v-text-field',
    });

    const input = textField.find('input');

    expect(wrapper.find('.v-input__details').exists()).toBe(false);

    textField.vm.$emit('update:modelValue', '100');
    await input.trigger('blur');
    await textField.vm.$emit('update:focused', false);

    await wrapper.vm.$nextTick();

    expect(textField.find('.v-input__details').exists()).toBe(true);

    await textField.vm.$emit('update:focused', true);
    await wrapper.vm.$nextTick();

    textField.vm.$emit('update:modelValue', '99.9');

    await input.trigger('blur');
    await textField.vm.$emit('update:focused', false);

    await vi.waitFor(() => {
      expect(textField.find('.v-input__details').exists()).toBe(false);
    });
  });

  it('should not display button to add DangerousGood', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    orderType.value = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    const addDangerousGoods = wrapper.find('[data-test=book-add-dangerous-goods]');
    expect(addDangerousGoods.exists()).toBe(false);
  });
});
