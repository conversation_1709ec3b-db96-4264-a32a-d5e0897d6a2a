import type { OrderText } from '@dfe/dfe-book-api';
import { OrderResponseBody } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import type { CreateOrderText } from '@/store/createOrder/formTexts';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';

export function useSetOrderTextData(editOrderData: OrderResponseBody) {
  if (editOrderData.texts) {
    updateTexts(editOrderData.texts);
  }
}

function updateTexts(orderTexts: OrderText[] | undefined) {
  const createOrderTextsStore = useCreateOrderTextsStore();
  const { texts } = storeToRefs(createOrderTextsStore);
  const updatedTexts = getUpdatedTexts(orderTexts ?? []);

  texts.value = Array.from(
    new Map([...texts.value, ...updatedTexts].map((item) => [item.textType, item])).values(),
  );
}

export function getUpdatedTexts(texts: OrderText[]): CreateOrderText[] {
  const updatedTexts: CreateOrderText[] = [];

  texts.forEach((text) => {
    const updatedText: CreateOrderText = {
      id: text.id,
      textType: text.textType,
      value: text.value,
      active: !!text.value,
    };

    updatedTexts.push(updatedText);
  });

  return updatedTexts;
}
