import { Component } from 'vue';

export interface DfeTableHeader {
  key: string;
  label: string;
  visible: boolean;
  sortable?: boolean;
  sorted?: DfeTableSortDirection;
  minWidth?: number;
  fixedWidth?: number;
  noLink?: boolean;
  component?: Component;
  justification?: DfeColumnAlignment;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formatting?: (value?: any) => string;
}

export interface DfeColumnSorting {
  key: string;
  order?: DfeTableSortDirection;
}

export interface DfeTableOptions {
  totalItems: number;
  itemsPerPage: number;
  /**
   * Page number starts at 1
   */
  currentPage: number;
  totalPages: number;
  allSelectable: boolean;
  itemsSelectable: boolean;
  singleSelect?: boolean;
  firstColumnSticky: boolean;
  disablePagination?: boolean;
  sortBy?: DfeColumnSorting;
}

export enum DfeTableSortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum DfeColumnAlignment {
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center',
}

export enum DfeSelectPage {
  FIRST = 'first',
  PREV = 'prev',
  NEXT = 'next',
  LAST = 'last',
}

export interface DfeDataTableExpose {
  onClearSelection: () => void;
}
