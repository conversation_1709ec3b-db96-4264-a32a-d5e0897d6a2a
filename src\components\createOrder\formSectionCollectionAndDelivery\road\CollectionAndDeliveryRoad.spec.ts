import CollectionAndDeliveryRoad from '@/components/createOrder/formSectionCollectionAndDelivery/road/CollectionAndDeliveryRoad.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';

describe('Collection & Delivery Road component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(CollectionAndDeliveryRoad);

    expect(wrapper.exists()).toBeTruthy();
  });
});
