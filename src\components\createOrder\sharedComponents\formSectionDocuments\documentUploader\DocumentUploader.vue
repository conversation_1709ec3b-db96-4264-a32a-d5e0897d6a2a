<template>
  <div>
    <UploadList :document-extensions="documentExtensions" :max-size="MAX_SIZE_DOCUMENT_UPLOAD" />
    <VCard
      v-data-test="'document-upload_dropzone'"
      min-height="200"
      class="dropzone d-flex align-center text-center justify-center"
      elevation="0"
      tabindex="0"
      @keydown.enter="triggerFileInput"
    >
      <div>
        <MaterialSymbol size="24" class="text-grey-darken-2">
          <UploadFileIcon />
        </MaterialSymbol>
        <div class="text-body-2">
          {{ t('labels.drop_files_to_upload.text') }} {{ t('labels.or_label.text') }}
          <label for="file" class="text-primary"> {{ t('labels.browse_label.text') }} </label>
        </div>
        <div class="text-body-3 text-grey-darken-2">
          ({{ t('labels.max_label.text') }} {{ useFormatBytes(MAX_SIZE_DOCUMENT_UPLOAD) }})
        </div>
      </div>
    </VCard>
    <VOverlay
      v-if="upload?.dropActive"
      :dark="false"
      scrim="black"
      z-index="15"
      :class="['text-center', { active: upload?.dropActive }]"
    >
      <MaterialSymbol color="white" size="24"> <UploadFileIcon /> </MaterialSymbol>
      <div class="text-h2 text-white">{{ t('labels.drop_files_to_upload.text') }}</div>
    </VOverlay>
    <FileUpload
      ref="upload"
      :accept="allowedExtensions"
      :multiple="true"
      :add-index="true"
      :drop="true"
      :drop-directory="true"
      :max-size="MAX_SIZE_DOCUMENT_UPLOAD"
      @input-file="onFileUpload"
    />
  </div>
</template>
<script setup lang="ts">
import UploadList from '@/components/createOrder/sharedComponents/formSectionDocuments/documentUploader/UploadList.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import useFormatBytes from '@/composables/createOrder/useFormatBytes';
import { UploadStatus } from '@/enums';
import { MAX_SIZE_DOCUMENT_UPLOAD } from '@/env';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import type { VueUploadDocument } from '@/types/createOrder';
import { createUuid } from '@/utils/createUuid';
import type { Document } from '@dfe/dfe-book-api';
import UploadFileIcon from '@dfe/dfe-frontend-styles/assets/icons/upload_file-24px.svg';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import FileUpload from 'vue-upload-component';

const { t } = useI18n();
const upload = ref<typeof FileUpload>();
const createOrderDocuments = useCreateOrderDocumentsStore();
const { documents } = storeToRefs(createOrderDocuments);
const createOrderFormStore = useCreateOrderFormStore();
const { customerNumber } = storeToRefs(createOrderFormStore);
const createOrderDataStore = useCreateOrderDataStore();
const { documentExtensions } = storeToRefs(createOrderDataStore);
const allowedExtensions = computed(() => {
  return documentExtensions.value.map((ext) => ext.description).join(', ');
});
const isSizeValid = (size: number) => size < MAX_SIZE_DOCUMENT_UPLOAD;
const isErrorExtensionNotAllowed = (extension: string) =>
  allowedExtensions.value.includes(extension);
const isExtensionInvalidError = (extension: string) => allowedExtensions.value.includes(extension);
const addDocument = (document: Document, status: UploadStatus) => {
  documents.value.push({ ...document, documentId: createUuid(), uploadStatus: `${status}` });
};
const onFileUpload = async (document: VueUploadDocument) => {
  const newDocument: Document & { size?: number } = {
    documentName: document.name,
    file: document.file,
    mimeType: document.type,
    size: document.size,
  };
  if (customerNumber?.value && document) {
    if (document.size && !isSizeValid(document.size)) {
      addDocument(newDocument, UploadStatus.ErrorSize);
      return;
    }
    if (document.type && !isErrorExtensionNotAllowed(document.type)) {
      addDocument(newDocument, UploadStatus.ErrorExtensionNotAllowed);
      return;
    }
    if (document.type && !isExtensionInvalidError(document.type)) {
      addDocument(newDocument, UploadStatus.ErrorInvalidExtension);
      return;
    }
    await createOrderDocuments.addDocument(newDocument);
  }
};
const triggerFileInput = () => {
  const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
  fileInput?.click();
};
onMounted(() => {
  createOrderDataStore.fetchExtensions();
});
</script>
<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

.text-primary:hover {
  color: vars.$color-base-blue-700 !important;
}

:deep(.file-uploads.file-uploads-html4 label),
:deep(.file-uploads.file-uploads-html5 input) {
  position: static;
}

:deep(.v-overlay.active)::after {
  content: '';
  position: absolute;
  inset: 0;
  margin: 14px;
  border-radius: 4px;
  border: 1px dashed vars.$color-base-white;
}

:deep() {
  .v-card {
    &.dropzone {
      background-color: vars.$color-base-grey-50;
      border: 1px dashed vars.$color-base-grey-400;

      label {
        cursor: pointer;
      }
    }

    &:focus-visible {
      outline: 1px dashed vars.$color-base-blue-700 !important;
    }
  }
}
</style>
