<template>
  <SelectField
    v-model="selectedFullContainerLoadId"
    :items="fullContainerLoadOptions"
    item-value="localId"
    item-text="name"
    :label="t('labels.full_container_load_header.text')"
  />
</template>

<script setup lang="ts">
import SelectField from '@/components/form/SelectField.vue';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';

const selectedFullContainerLoadId = defineModel<number | null>({ default: null });

const { t } = useI18n();

const { fullContainerLoads } = storeToRefs(useCreateOrderOrderLineFormStore());

const fullContainerLoadOptions = computed(() => {
  return [
    ...fullContainerLoads.value.map((fullContainerLoad, index) => {
      return { name: index + 1, ...fullContainerLoad };
    }),
  ];
});
</script>
