// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`useEditOrder composable > editOrder > should set order data for Air Order 1`] = `
{
  "createAnother": false,
  "customTypeValidationError": false,
  "customerNumber": "********",
  "initialOrder": null,
  "isEditMode": false,
  "isOrderLoaded": false,
  "isSaveLoading": false,
  "isSubmitLoading": false,
  "isValidateLoading": false,
  "isValidationTriggered": false,
  "orderData": null,
  "orderId": 1,
  "orderNumber": "*********",
  "orderType": "AirExportOrder",
  "preferredCurrency": "EUR",
  "preferredOrderType": null,
  "quoteInformation": null,
  "saveOrderData": null,
  "submitResultData": null,
  "transportType": "ROAD",
  "validateData": null,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 2`] = `
{
  "collectionOption": undefined,
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeAddressType": 0,
  "consigneeHandOverSelection": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "gln": "4313920192301",
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "supplement": "Tel. 0815",
    },
    "selection": "alternateAddressSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataFurtherAddresses": {
    "AE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CT": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CX": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "EV": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "FW": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "IM": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "LP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N1": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N2": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OS": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OY": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "PJ": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "TO": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UK": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "WB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
  },
  "contactDataShipper": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "differentConsigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "formAddress": {
    "city": "",
    "contact": undefined,
    "countryCode": undefined,
    "dropOfLocation": undefined,
    "gln": undefined,
    "id": undefined,
    "lockedByQuote": undefined,
    "name": "",
    "name2": "",
    "name3": "",
    "neutralizeAddress": undefined,
    "originAddressId": undefined,
    "postcode": "",
    "street": "",
    "street2": "",
    "supplement": "",
    "taxID": undefined,
  },
  "fromIATA": {
    "code": "MUC",
    "countryCode": "DE",
    "name": "Munich",
    "type": "AIRPORT",
  },
  "fromPort": undefined,
  "furtherAddresses": [
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Ray Sono AG",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DA",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "RE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "RE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "CB",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "CB",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DP",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DP",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "AE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "AE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DC",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DC",
    },
    {
      "address": {
        "city": "ImporterMunich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Importer",
        "name2": "Importer rear annex",
        "name3": "Importer string",
        "postcode": "80337",
        "street": "Importer Str. 32",
        "supplement": "Importer Tel. 0815",
      },
      "addressType": "IM",
    },
  ],
  "hasUnsavedAddressChanges": false,
  "isConsigneeContactSet": true,
  "isCustomer": true,
  "isShipperAddressDisabled": false,
  "isShipperContactSet": true,
  "isThirdCountryAddress": false,
  "isUnsavedAddressChangesDialogOpen": false,
  "loadingPoint": {
    "city": "Munich",
    "countryCode": "DE",
    "label": "LP, Munich",
    "name": "LP",
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
  },
  "port": undefined,
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "gln": "4313920192301",
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "supplement": "Tel. 0815",
    },
    "selection": "alternateAddressSelection",
  },
  "toIATA": {
    "code": "ZRH",
    "countryCode": "CH",
    "name": "Zurich",
    "type": "AIRPORT",
  },
  "toPort": undefined,
  "townCounty": undefined,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 3`] = `
{
  "fullContainerLoads": [],
  "generatedSSccs": [],
  "generatedSSccsOnLoad": 0,
  "isFullContainerLoad": false,
  "isFullContainerLoadAllowed": false,
  "manualNumberOfLabels": undefined,
  "manualNumberOfLabelsOnLoad": 0,
  "orderLines": [
    {
      "content": undefined,
      "dangerousGoods": [],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "Test",
          "hsCode": {
            "code": "070190",
          },
          "id": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "",
        "quantity": undefined,
      },
      "height": 999,
      "hsCodes": [
        {
          "goods": "Test",
          "hsCode": "160555",
        },
        {
          "goods": "Test 2 ",
          "hsCode": "071232",
        },
      ],
      "id": 1,
      "length": 999,
      "loadingMeter": undefined,
      "localId": 21,
      "markAndNumbers": undefined,
      "number": 999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": undefined,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
  ],
  "originalPackaging": undefined,
  "packagingAidPosition": false,
  "packingPositions": [],
  "shockSensitive": false,
  "stackable": false,
  "totalValue": 999,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 4`] = `
{
  "texts": [
    {
      "active": false,
      "id": undefined,
      "textType": "A",
      "value": undefined,
    },
    {
      "active": true,
      "id": undefined,
      "textType": "ZU",
      "value": "Additional Text",
    },
    {
      "active": false,
      "id": undefined,
      "textType": "WA",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "RE",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "SI",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "GS",
      "value": undefined,
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 5`] = `
{
  "cashOnDelivery": false,
  "cashOnDeliveryAmount": null,
  "collectionInterpreterOption": "FIX",
  "collectionOption": "",
  "collectionTimeSlot": {
    "collectionDate": "2022-11-25",
    "from": "2022-11-25T15:15:10.478Z",
    "text": "15:15 - 15:15",
    "to": "2022-11-25T15:15:10.478Z",
  },
  "contactDataCollection": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataDelivery": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {},
  "customCollectionTimeSlot": {
    "collectionDate": "2022-11-25T00:00:00.000Z",
    "from": "15:15",
    "to": "15:15",
  },
  "date": 2022-11-25T00:00:00.000Z,
  "dateDelivery": "",
  "deliveryOption": "st",
  "deliveryProduct": null,
  "productOption": "",
  "requestArrangement": false,
  "selectedAirDeliveryProduct": {
    "code": "c",
    "description": "avigoexpress",
    "hint": "Fastest possible service - 1 to 3 days",
  },
  "selfCollection": false,
  "tailLiftCollection": false,
  "tailLiftDelivery": false,
  "timeSlotOptions": [
    {
      "collectionDate": "2022-11-25",
      "from": "2022-11-25T15:15:10.478Z",
      "text": "15:15 - 15:15",
      "to": "2022-11-25T15:15:10.478Z",
    },
    {
      "from": "Custom",
      "text": "labels.other_label.text",
      "to": "Custom",
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 6`] = `
{
  "bookingReference": [],
  "commercialInvoiceNumbers": [
    {
      "id": "10",
      "loading": false,
      "unloading": true,
      "value": "111",
    },
  ],
  "consigneeReferenceNumbers": [
    {
      "id": "6",
      "loading": false,
      "unloading": true,
      "value": "66",
    },
  ],
  "dailyPriceReference": "",
  "deliveryNoteNumberRequiredFields": [],
  "deliveryNoteNumbers": [
    {
      "id": "3",
      "loading": false,
      "unloading": true,
      "value": "33",
    },
  ],
  "ekaerNumber": undefined,
  "ekaerNumberNotRequired": false,
  "furtherReferencesOrder": [
    "INVOICE_NUMBER",
    "PURCHASE_ORDER_NUMBER",
    "DELIVERY_NOTE_NUMBER",
    "OTHERS",
    "MARKS_AND_NUMBERS",
    "CONSIGNEE_REFERENCE_NUMBER",
    "SUPPLIER_SHIPMENT_NUMBER",
    "PROVIDER_SHIPMENT_NUMBER",
    "PACKAGING_LIST_NUMBER",
    "COMMERCIAL_INVOICE_NUMBER",
  ],
  "identificationCodeTransport": [],
  "invoiceNumbers": [
    {
      "id": "2",
      "loading": true,
      "unloading": true,
      "value": "22",
    },
  ],
  "markAndNumbers": [
    {
      "id": "5",
      "loading": true,
      "unloading": true,
      "value": "55",
    },
  ],
  "otherNumbers": [
    {
      "id": "4",
      "loading": false,
      "unloading": false,
      "value": "44",
    },
  ],
  "packingListNumbers": [
    {
      "id": "9",
      "loading": true,
      "unloading": true,
      "value": "99",
    },
  ],
  "providerShipmentNumbers": [
    {
      "id": "8",
      "loading": false,
      "unloading": false,
      "value": "88",
    },
  ],
  "purchaseOrderNumbers": [
    {
      "id": "1",
      "loading": true,
      "unloading": false,
      "value": "11",
    },
  ],
  "quotationReference": {
    "loading": false,
    "referenceType": "QUOTATION_REFERENCE",
    "referenceValue": "987654321",
    "unloading": false,
  },
  "shipperReference": {
    "loading": true,
    "referenceValue": "",
    "unloading": true,
  },
  "supplierShipmentNumbers": [
    {
      "id": "7",
      "loading": true,
      "unloading": false,
      "value": "77",
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 7`] = `
{
  "customsDeclarationExecutor": "DACHSER",
  "customsDeclarationRequired": true,
  "freightPayer": "Principal",
  "frostProtectionRequired": false,
  "orderGroup": "",
  "palletLocationsNumber": undefined,
  "selectedFreightTerm": null,
  "transport": "",
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Air Order 8`] = `
{
  "collectionOptions": [],
  "countries": [],
  "customers": [],
  "deliveryOptions": [],
  "documentExtensions": [],
  "favoriteCountries": {
    "consigneeCountries": [],
    "shipperCountries": [],
  },
  "freightTerms": [],
  "fromPortRouting": [
    {
      "code": "DEHAM",
      "countryCode": "DE",
      "name": "Hamburger Hafen",
      "type": "SEAPORT",
    },
    {
      "code": "SGSIN",
      "countryCode": "SG",
      "name": "Port of Singapore",
      "type": "SEAPORT",
    },
    {
      "code": "CNSZX",
      "countryCode": "CN",
      "name": "Port of Shenzhen",
      "type": "SEAPORT",
    },
  ],
  "furtherAddressTypes": [],
  "hasInvalidRouting": false,
  "incoTerms": [],
  "isThirdCountryConstellation": false,
  "loadingPoints": [],
  "toPortRouting": [
    {
      "code": "DEHAM",
      "countryCode": "DE",
      "name": "Hamburger Hafen",
      "type": "SEAPORT",
    },
    {
      "code": "SGSIN",
      "countryCode": "SG",
      "name": "Port of Singapore",
      "type": "SEAPORT",
    },
    {
      "code": "CNSZX",
      "countryCode": "CN",
      "name": "Port of Shenzhen",
      "type": "SEAPORT",
    },
  ],
  "validateAddressResult": {
    "results": [],
    "valid": false,
  },
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 1`] = `
{
  "createAnother": false,
  "customTypeValidationError": false,
  "customerNumber": "********",
  "initialOrder": null,
  "isEditMode": false,
  "isOrderLoaded": false,
  "isSaveLoading": false,
  "isSubmitLoading": false,
  "isValidateLoading": false,
  "isValidationTriggered": false,
  "orderData": null,
  "orderId": 1,
  "orderNumber": "*********",
  "orderType": "RoadCollectionOrder",
  "preferredCurrency": "EUR",
  "preferredOrderType": null,
  "quoteInformation": null,
  "saveOrderData": null,
  "submitResultData": null,
  "transportType": "ROAD",
  "validateData": null,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 2`] = `
{
  "collectionOption": "NOTIFICATION",
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeAddressType": 1,
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {
    "AE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CT": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CX": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "EV": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "FW": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "IM": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "LP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N1": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N2": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OS": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OY": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "PJ": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "TO": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UK": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "WB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
  },
  "contactDataShipper": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "differentConsigneeAddress": {
    "address": [Circular],
    "addressType": "",
    "city": "Munich",
    "countryCode": "DE",
    "gln": "",
    "name": "Different Consignee",
    "name2": "",
    "name3": "",
    "neutralizeAddress": true,
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
    "supplement": "",
  },
  "formAddress": {
    "city": "",
    "contact": undefined,
    "countryCode": undefined,
    "dropOfLocation": undefined,
    "gln": undefined,
    "id": undefined,
    "lockedByQuote": undefined,
    "name": "",
    "name2": "",
    "name3": "",
    "neutralizeAddress": undefined,
    "originAddressId": undefined,
    "postcode": "",
    "street": "",
    "street2": "",
    "supplement": "",
    "taxID": undefined,
  },
  "fromIATA": undefined,
  "fromPort": undefined,
  "furtherAddresses": [
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Ray Sono AG",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DA",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "RE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "RE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "CB",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "CB",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DP",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DP",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "AE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "AE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DC",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DC",
    },
    {
      "address": {
        "city": "ImporterMunich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Importer",
        "name2": "Importer rear annex",
        "name3": "Importer string",
        "postcode": "80337",
        "street": "Importer Str. 32",
        "supplement": "Importer Tel. 0815",
      },
      "addressType": "IM",
    },
  ],
  "hasUnsavedAddressChanges": false,
  "isConsigneeContactSet": true,
  "isCustomer": true,
  "isShipperAddressDisabled": false,
  "isShipperContactSet": true,
  "isThirdCountryAddress": false,
  "isUnsavedAddressChangesDialogOpen": false,
  "loadingPoint": {
    "city": "Munich",
    "countryCode": "DE",
    "label": "LP, Munich",
    "name": "LP",
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
  },
  "port": undefined,
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
  "toIATA": undefined,
  "toPort": undefined,
  "townCounty": undefined,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 3`] = `
{
  "fullContainerLoads": [],
  "generatedSSccs": [],
  "generatedSSccsOnLoad": 0,
  "isFullContainerLoad": false,
  "isFullContainerLoadAllowed": false,
  "manualNumberOfLabels": 123,
  "manualNumberOfLabelsOnLoad": 0,
  "orderLines": [
    {
      "content": "string",
      "dangerousGoods": [
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 1,
          "localId": 18,
          "noOfPackages": 3,
          "packaging": {
            "code": "S",
            "translationKey": "generalData.packagingOptionRoad.S.text",
          },
          "sortingPosition": 1,
        },
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 2,
          "localId": 19,
          "noOfPackages": 4,
          "sortingPosition": 2,
        },
      ],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 1,
      "length": 999,
      "loadingMeter": 0,
      "localId": 16,
      "markAndNumbers": undefined,
      "number": 999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": undefined,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
    {
      "content": "string",
      "dangerousGoods": [],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 2,
      "length": 999,
      "loadingMeter": 0,
      "localId": 17,
      "markAndNumbers": undefined,
      "number": 9999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": 20,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
  ],
  "originalPackaging": undefined,
  "packagingAidPosition": false,
  "packingPositions": [
    {
      "id": 1,
      "lines": undefined,
      "localId": 20,
      "packagingType": {
        "code": "string",
        "description": "string",
      },
      "quantity": 999,
    },
  ],
  "shockSensitive": false,
  "stackable": false,
  "totalValue": 999,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 4`] = `
{
  "texts": [
    {
      "active": false,
      "id": undefined,
      "textType": "A",
      "value": undefined,
    },
    {
      "active": true,
      "id": undefined,
      "textType": "ZU",
      "value": "Additional Text",
    },
    {
      "active": false,
      "id": undefined,
      "textType": "WA",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "RE",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "SI",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "GS",
      "value": undefined,
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 5`] = `
{
  "cashOnDelivery": false,
  "cashOnDeliveryAmount": null,
  "collectionInterpreterOption": "",
  "collectionOption": "",
  "collectionTimeSlot": {
    "collectionDate": "2022-11-25",
    "from": "2022-11-25T15:15:10.478Z",
    "text": "15:15 - 15:15",
    "to": "2022-11-25T15:15:10.478Z",
  },
  "contactDataCollection": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataDelivery": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {},
  "customCollectionTimeSlot": {
    "collectionDate": "2022-11-25T00:00:00.000Z",
    "from": "15:15",
    "to": "15:15",
  },
  "date": 2022-11-25T00:00:00.000Z,
  "dateDelivery": "",
  "deliveryOption": "",
  "deliveryProduct": "E",
  "productOption": "",
  "requestArrangement": false,
  "selectedAirDeliveryProduct": undefined,
  "selfCollection": false,
  "tailLiftCollection": false,
  "tailLiftDelivery": true,
  "timeSlotOptions": [
    {
      "collectionDate": "2022-11-25",
      "from": "2022-11-25T15:15:10.478Z",
      "text": "15:15 - 15:15",
      "to": "2022-11-25T15:15:10.478Z",
    },
    {
      "from": "Custom",
      "text": "labels.other_label.text",
      "to": "Custom",
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 6`] = `
{
  "bookingReference": [],
  "commercialInvoiceNumbers": [],
  "consigneeReferenceNumbers": [],
  "dailyPriceReference": "",
  "deliveryNoteNumberRequiredFields": [],
  "deliveryNoteNumbers": [
    {
      "id": "2",
      "value": "222",
    },
  ],
  "ekaerNumber": undefined,
  "ekaerNumberNotRequired": false,
  "furtherReferencesOrder": [
    "PURCHASE_ORDER_NUMBER",
    "DELIVERY_NOTE_NUMBER",
  ],
  "identificationCodeTransport": [],
  "invoiceNumbers": [],
  "markAndNumbers": [],
  "otherNumbers": [],
  "packingListNumbers": [],
  "providerShipmentNumbers": [],
  "purchaseOrderNumbers": [
    {
      "id": "1",
      "value": "111",
    },
  ],
  "quotationReference": {
    "referenceValue": "",
  },
  "shipperReference": {
    "loading": true,
    "referenceValue": "",
    "unloading": true,
  },
  "supplierShipmentNumbers": [],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 7`] = `
{
  "customsDeclarationExecutor": "DACHSER",
  "customsDeclarationRequired": true,
  "freightPayer": "Principal",
  "frostProtectionRequired": false,
  "orderGroup": "",
  "palletLocationsNumber": undefined,
  "selectedFreightTerm": null,
  "transport": "",
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection - null 8`] = `
{
  "collectionOptions": [],
  "countries": [],
  "customers": [],
  "deliveryOptions": [],
  "documentExtensions": [],
  "favoriteCountries": {
    "consigneeCountries": [],
    "shipperCountries": [],
  },
  "freightTerms": [],
  "fromPortRouting": [],
  "furtherAddressTypes": [],
  "hasInvalidRouting": false,
  "incoTerms": [],
  "isThirdCountryConstellation": false,
  "loadingPoints": [],
  "toPortRouting": [],
  "validateAddressResult": {
    "results": [],
    "valid": false,
  },
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 1`] = `
{
  "createAnother": false,
  "customTypeValidationError": false,
  "customerNumber": "********",
  "initialOrder": null,
  "isEditMode": false,
  "isOrderLoaded": false,
  "isSaveLoading": false,
  "isSubmitLoading": false,
  "isValidateLoading": false,
  "isValidationTriggered": false,
  "orderData": null,
  "orderId": 1,
  "orderNumber": "*********",
  "orderType": "RoadCollectionOrder",
  "preferredCurrency": "EUR",
  "preferredOrderType": null,
  "quoteInformation": null,
  "saveOrderData": null,
  "submitResultData": null,
  "transportType": "ROAD",
  "validateData": null,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 2`] = `
{
  "collectionOption": "NOTIFICATION",
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeAddressType": 1,
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {
    "AE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CT": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CX": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "EV": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "FW": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "IM": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "LP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N1": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N2": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OS": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OY": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "PJ": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "TO": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UK": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "WB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
  },
  "contactDataShipper": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "differentConsigneeAddress": {
    "address": [Circular],
    "addressType": "",
    "city": "Munich",
    "countryCode": "DE",
    "gln": "",
    "name": "Different Consignee",
    "name2": "",
    "name3": "",
    "neutralizeAddress": true,
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
    "supplement": "",
  },
  "formAddress": {
    "city": "",
    "contact": undefined,
    "countryCode": undefined,
    "dropOfLocation": undefined,
    "gln": undefined,
    "id": undefined,
    "lockedByQuote": undefined,
    "name": "",
    "name2": "",
    "name3": "",
    "neutralizeAddress": undefined,
    "originAddressId": undefined,
    "postcode": "",
    "street": "",
    "street2": "",
    "supplement": "",
    "taxID": undefined,
  },
  "fromIATA": undefined,
  "fromPort": undefined,
  "furtherAddresses": [
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Ray Sono AG",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DA",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "RE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "RE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "CB",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "CB",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DP",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DP",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "AE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "AE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DC",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DC",
    },
    {
      "address": {
        "city": "ImporterMunich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Importer",
        "name2": "Importer rear annex",
        "name3": "Importer string",
        "postcode": "80337",
        "street": "Importer Str. 32",
        "supplement": "Importer Tel. 0815",
      },
      "addressType": "IM",
    },
  ],
  "hasUnsavedAddressChanges": false,
  "isConsigneeContactSet": true,
  "isCustomer": true,
  "isShipperAddressDisabled": false,
  "isShipperContactSet": true,
  "isThirdCountryAddress": false,
  "isUnsavedAddressChangesDialogOpen": false,
  "loadingPoint": {
    "city": "Munich",
    "countryCode": "DE",
    "label": "LP, Munich",
    "name": "LP",
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
  },
  "port": undefined,
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
  "toIATA": undefined,
  "toPort": undefined,
  "townCounty": undefined,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 3`] = `
{
  "fullContainerLoads": [],
  "generatedSSccs": [],
  "generatedSSccsOnLoad": 0,
  "isFullContainerLoad": false,
  "isFullContainerLoadAllowed": false,
  "manualNumberOfLabels": 123,
  "manualNumberOfLabelsOnLoad": 0,
  "orderLines": [
    {
      "content": "string",
      "dangerousGoods": [
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 1,
          "localId": 13,
          "noOfPackages": 3,
          "packaging": {
            "code": "S",
            "translationKey": "generalData.packagingOptionRoad.S.text",
          },
          "sortingPosition": 1,
        },
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 2,
          "localId": 14,
          "noOfPackages": 4,
          "sortingPosition": 2,
        },
      ],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 1,
      "length": 999,
      "loadingMeter": 0,
      "localId": 11,
      "markAndNumbers": undefined,
      "number": 999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": undefined,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
    {
      "content": "string",
      "dangerousGoods": [],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 2,
      "length": 999,
      "loadingMeter": 0,
      "localId": 12,
      "markAndNumbers": undefined,
      "number": 9999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": 15,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
  ],
  "originalPackaging": undefined,
  "packagingAidPosition": false,
  "packingPositions": [
    {
      "id": 1,
      "lines": undefined,
      "localId": 15,
      "packagingType": {
        "code": "string",
        "description": "string",
      },
      "quantity": 999,
    },
  ],
  "shockSensitive": false,
  "stackable": false,
  "totalValue": 999,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 4`] = `
{
  "texts": [
    {
      "active": false,
      "id": undefined,
      "textType": "A",
      "value": undefined,
    },
    {
      "active": true,
      "id": undefined,
      "textType": "ZU",
      "value": "Additional Text",
    },
    {
      "active": false,
      "id": undefined,
      "textType": "WA",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "RE",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "SI",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "GS",
      "value": undefined,
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 5`] = `
{
  "cashOnDelivery": false,
  "cashOnDeliveryAmount": null,
  "collectionInterpreterOption": "COLLECTION_NOT_BEFORE",
  "collectionOption": "",
  "collectionTimeSlot": {
    "collectionDate": "2022-11-25",
    "from": "2022-11-25T15:15:10.478Z",
    "text": "15:15 - 15:15",
    "to": "2022-11-25T15:15:10.478Z",
  },
  "contactDataCollection": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataDelivery": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {},
  "customCollectionTimeSlot": {
    "collectionDate": "2022-11-25T00:00:00.000Z",
    "from": "15:15",
    "to": "15:15",
  },
  "date": 2022-11-25T00:00:00.000Z,
  "dateDelivery": "",
  "deliveryOption": "st",
  "deliveryProduct": "E",
  "productOption": "",
  "requestArrangement": false,
  "selectedAirDeliveryProduct": undefined,
  "selfCollection": false,
  "tailLiftCollection": true,
  "tailLiftDelivery": true,
  "timeSlotOptions": [
    {
      "collectionDate": "2022-11-25",
      "from": "2022-11-25T15:15:10.478Z",
      "text": "15:15 - 15:15",
      "to": "2022-11-25T15:15:10.478Z",
    },
    {
      "from": "Custom",
      "text": "labels.other_label.text",
      "to": "Custom",
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 6`] = `
{
  "bookingReference": [],
  "commercialInvoiceNumbers": [],
  "consigneeReferenceNumbers": [],
  "dailyPriceReference": "",
  "deliveryNoteNumberRequiredFields": [],
  "deliveryNoteNumbers": [
    {
      "id": "2",
      "value": "222",
    },
  ],
  "ekaerNumber": undefined,
  "ekaerNumberNotRequired": false,
  "furtherReferencesOrder": [
    "PURCHASE_ORDER_NUMBER",
    "DELIVERY_NOTE_NUMBER",
  ],
  "identificationCodeTransport": [],
  "invoiceNumbers": [],
  "markAndNumbers": [],
  "otherNumbers": [],
  "packingListNumbers": [],
  "providerShipmentNumbers": [],
  "purchaseOrderNumbers": [
    {
      "id": "1",
      "value": "111",
    },
  ],
  "quotationReference": {
    "referenceValue": "",
  },
  "shipperReference": {
    "loading": true,
    "referenceValue": "",
    "unloading": true,
  },
  "supplierShipmentNumbers": [],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 7`] = `
{
  "customsDeclarationExecutor": "DACHSER",
  "customsDeclarationRequired": true,
  "freightPayer": "Principal",
  "frostProtectionRequired": false,
  "orderGroup": "",
  "palletLocationsNumber": undefined,
  "selectedFreightTerm": null,
  "transport": "",
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - Collection 8`] = `
{
  "collectionOptions": [],
  "countries": [],
  "customers": [],
  "deliveryOptions": [],
  "documentExtensions": [],
  "favoriteCountries": {
    "consigneeCountries": [],
    "shipperCountries": [],
  },
  "freightTerms": [],
  "fromPortRouting": [],
  "furtherAddressTypes": [],
  "hasInvalidRouting": false,
  "incoTerms": [],
  "isThirdCountryConstellation": false,
  "loadingPoints": [],
  "toPortRouting": [],
  "validateAddressResult": {
    "results": [],
    "valid": false,
  },
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 1`] = `
{
  "createAnother": false,
  "customTypeValidationError": false,
  "customerNumber": "********",
  "initialOrder": null,
  "isEditMode": false,
  "isOrderLoaded": false,
  "isSaveLoading": false,
  "isSubmitLoading": false,
  "isValidateLoading": false,
  "isValidationTriggered": false,
  "orderData": null,
  "orderId": 1,
  "orderNumber": "*********",
  "orderType": "RoadForwardingOrder",
  "preferredCurrency": "EUR",
  "preferredOrderType": null,
  "quoteInformation": null,
  "saveOrderData": null,
  "submitResultData": null,
  "transportType": "ROAD",
  "validateData": null,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 2`] = `
{
  "collectionOption": undefined,
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeAddressType": 0,
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {
    "AE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CT": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CX": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "EV": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "FW": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "IM": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "LP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N1": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N2": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OS": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OY": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "PJ": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "TO": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UK": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "WB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
  },
  "contactDataShipper": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "differentConsigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "formAddress": {
    "city": "",
    "contact": undefined,
    "countryCode": undefined,
    "dropOfLocation": undefined,
    "gln": undefined,
    "id": undefined,
    "lockedByQuote": undefined,
    "name": "",
    "name2": "",
    "name3": "",
    "neutralizeAddress": undefined,
    "originAddressId": undefined,
    "postcode": "",
    "street": "",
    "street2": "",
    "supplement": "",
    "taxID": undefined,
  },
  "fromIATA": undefined,
  "fromPort": undefined,
  "furtherAddresses": [
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Ray Sono AG",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DA",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "RE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "RE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "CB",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "CB",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DP",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DP",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "AE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "AE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DC",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DC",
    },
    {
      "address": {
        "city": "ImporterMunich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Importer",
        "name2": "Importer rear annex",
        "name3": "Importer string",
        "postcode": "80337",
        "street": "Importer Str. 32",
        "supplement": "Importer Tel. 0815",
      },
      "addressType": "IM",
    },
  ],
  "hasUnsavedAddressChanges": false,
  "isConsigneeContactSet": true,
  "isCustomer": true,
  "isShipperAddressDisabled": false,
  "isShipperContactSet": true,
  "isThirdCountryAddress": false,
  "isUnsavedAddressChangesDialogOpen": false,
  "loadingPoint": {
    "city": "Munich",
    "countryCode": "DE",
    "label": "LP, Munich",
    "name": "LP",
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
  },
  "port": undefined,
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
  "toIATA": undefined,
  "toPort": undefined,
  "townCounty": undefined,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 3`] = `
{
  "fullContainerLoads": [],
  "generatedSSccs": [],
  "generatedSSccsOnLoad": 0,
  "isFullContainerLoad": false,
  "isFullContainerLoadAllowed": false,
  "manualNumberOfLabels": 123,
  "manualNumberOfLabelsOnLoad": 0,
  "orderLines": [
    {
      "content": "string",
      "dangerousGoods": [
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 1,
          "localId": 8,
          "noOfPackages": 3,
          "packaging": {
            "code": "S",
            "translationKey": "generalData.packagingOptionRoad.S.text",
          },
          "sortingPosition": 1,
        },
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 2,
          "localId": 9,
          "noOfPackages": 4,
          "sortingPosition": 2,
        },
      ],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 1,
      "length": 999,
      "loadingMeter": 0,
      "localId": 6,
      "markAndNumbers": undefined,
      "number": 999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": undefined,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
    {
      "content": "string",
      "dangerousGoods": [],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 2,
      "length": 999,
      "loadingMeter": 0,
      "localId": 7,
      "markAndNumbers": undefined,
      "number": 9999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": 10,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
  ],
  "originalPackaging": undefined,
  "packagingAidPosition": false,
  "packingPositions": [
    {
      "id": 1,
      "lines": undefined,
      "localId": 10,
      "packagingType": {
        "code": "string",
        "description": "string",
      },
      "quantity": 999,
    },
  ],
  "shockSensitive": false,
  "stackable": false,
  "totalValue": 999,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 4`] = `
{
  "texts": [
    {
      "active": false,
      "id": undefined,
      "textType": "A",
      "value": undefined,
    },
    {
      "active": true,
      "id": undefined,
      "textType": "ZU",
      "value": "Additional Text",
    },
    {
      "active": false,
      "id": undefined,
      "textType": "WA",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "RE",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "SI",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "GS",
      "value": undefined,
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 5`] = `
{
  "cashOnDelivery": false,
  "cashOnDeliveryAmount": 0,
  "collectionInterpreterOption": "FIX",
  "collectionOption": "",
  "collectionTimeSlot": {
    "collectionDate": "2022-11-25",
    "from": "2022-11-25T15:15:10.478Z",
    "text": "15:15 - 15:15",
    "to": "2022-11-25T15:15:10.478Z",
  },
  "contactDataCollection": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataDelivery": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {},
  "customCollectionTimeSlot": {
    "collectionDate": "2022-11-25T00:00:00.000Z",
    "from": "15:15",
    "to": "15:15",
  },
  "date": 2022-11-25T00:00:00.000Z,
  "dateDelivery": 2022-10-01T00:00:00.000Z,
  "deliveryOption": "st",
  "deliveryProduct": "",
  "productOption": "",
  "requestArrangement": false,
  "selectedAirDeliveryProduct": undefined,
  "selfCollection": false,
  "tailLiftCollection": false,
  "tailLiftDelivery": false,
  "timeSlotOptions": [
    {
      "collectionDate": "2022-11-25",
      "from": "2022-11-25T15:15:10.478Z",
      "text": "15:15 - 15:15",
      "to": "2022-11-25T15:15:10.478Z",
    },
    {
      "from": "Custom",
      "text": "labels.other_label.text",
      "to": "Custom",
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 6`] = `
{
  "bookingReference": [],
  "commercialInvoiceNumbers": [],
  "consigneeReferenceNumbers": [],
  "dailyPriceReference": "",
  "deliveryNoteNumberRequiredFields": [],
  "deliveryNoteNumbers": [
    {
      "id": "2",
      "value": "222",
    },
  ],
  "ekaerNumber": "987654321",
  "ekaerNumberNotRequired": false,
  "furtherReferencesOrder": [
    "PURCHASE_ORDER_NUMBER",
    "DELIVERY_NOTE_NUMBER",
  ],
  "identificationCodeTransport": [],
  "invoiceNumbers": [],
  "markAndNumbers": [],
  "otherNumbers": [],
  "packingListNumbers": [],
  "providerShipmentNumbers": [],
  "purchaseOrderNumbers": [
    {
      "id": "1",
      "value": "111",
    },
  ],
  "quotationReference": {
    "referenceValue": "",
  },
  "shipperReference": {
    "loading": true,
    "referenceValue": "",
    "unloading": true,
  },
  "supplierShipmentNumbers": [],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 7`] = `
{
  "customsDeclarationExecutor": "DACHSER",
  "customsDeclarationRequired": true,
  "freightPayer": "Principal",
  "frostProtectionRequired": false,
  "orderGroup": "",
  "palletLocationsNumber": 2,
  "selectedFreightTerm": {
    "dachserTermKey": "Code_1",
    "headline": "Free delivered",
    "incoTermKey": "iCode_1",
  },
  "transport": "",
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order - null 8`] = `
{
  "collectionOptions": [],
  "countries": [],
  "customers": [],
  "deliveryOptions": [],
  "documentExtensions": [],
  "favoriteCountries": {
    "consigneeCountries": [],
    "shipperCountries": [],
  },
  "freightTerms": [],
  "fromPortRouting": [],
  "furtherAddressTypes": [],
  "hasInvalidRouting": false,
  "incoTerms": [],
  "isThirdCountryConstellation": false,
  "loadingPoints": [],
  "toPortRouting": [],
  "validateAddressResult": {
    "results": [],
    "valid": false,
  },
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 1`] = `
{
  "createAnother": false,
  "customTypeValidationError": false,
  "customerNumber": "********",
  "initialOrder": null,
  "isEditMode": false,
  "isOrderLoaded": false,
  "isSaveLoading": false,
  "isSubmitLoading": false,
  "isValidateLoading": false,
  "isValidationTriggered": false,
  "orderData": null,
  "orderId": 1,
  "orderNumber": "*********",
  "orderType": "RoadForwardingOrder",
  "preferredCurrency": "EUR",
  "preferredOrderType": null,
  "quoteInformation": null,
  "saveOrderData": null,
  "submitResultData": null,
  "transportType": "ROAD",
  "validateData": null,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 2`] = `
{
  "collectionOption": undefined,
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeAddressType": 0,
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {
    "AE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CT": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "CX": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DC": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "DP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "EV": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "FW": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "IM": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "LP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N1": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "N2": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OS": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "OY": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "PJ": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RA": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "RE": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "TO": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UK": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "UP": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
    "WB": {
      "email": "",
      "id": 0,
      "mobile": "",
      "name": "",
      "telephone": "",
    },
  },
  "contactDataShipper": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "differentConsigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "formAddress": {
    "city": "",
    "contact": undefined,
    "countryCode": undefined,
    "dropOfLocation": undefined,
    "gln": undefined,
    "id": undefined,
    "lockedByQuote": undefined,
    "name": "",
    "name2": "",
    "name3": "",
    "neutralizeAddress": undefined,
    "originAddressId": undefined,
    "postcode": "",
    "street": "",
    "street2": "",
    "supplement": "",
    "taxID": undefined,
  },
  "fromIATA": undefined,
  "fromPort": undefined,
  "furtherAddresses": [
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Ray Sono AG",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DA",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "RE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "RE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "CB",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "CB",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DP",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DP",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "AE",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "AE",
    },
    {
      "address": {
        "city": "Munich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "DC",
        "name2": "rear annex",
        "name3": "string",
        "postcode": "80337",
        "street": "Tumblinger Str. 32",
        "supplement": "Tel. 0815",
      },
      "addressType": "DC",
    },
    {
      "address": {
        "city": "ImporterMunich",
        "countryCode": "DE",
        "gln": "4313920192301",
        "name": "Importer",
        "name2": "Importer rear annex",
        "name3": "Importer string",
        "postcode": "80337",
        "street": "Importer Str. 32",
        "supplement": "Importer Tel. 0815",
      },
      "addressType": "IM",
    },
  ],
  "hasUnsavedAddressChanges": false,
  "isConsigneeContactSet": true,
  "isCustomer": true,
  "isShipperAddressDisabled": false,
  "isShipperContactSet": true,
  "isThirdCountryAddress": false,
  "isUnsavedAddressChangesDialogOpen": false,
  "loadingPoint": {
    "city": "Munich",
    "countryCode": "DE",
    "label": "LP, Munich",
    "name": "LP",
    "postcode": "80337",
    "street": "Tumblinger Str. 32",
  },
  "port": undefined,
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": {
        "email": "string",
        "mobile": "string",
        "name": "string",
        "telephone": "string",
      },
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": "4313920192301",
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "Ray Sono AG",
      "name2": "rear annex",
      "name3": "string",
      "neutralizeAddress": true,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "Tel. 0815",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
  "toIATA": undefined,
  "toPort": undefined,
  "townCounty": undefined,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 3`] = `
{
  "fullContainerLoads": [],
  "generatedSSccs": [],
  "generatedSSccsOnLoad": 0,
  "isFullContainerLoad": false,
  "isFullContainerLoadAllowed": false,
  "manualNumberOfLabels": 123,
  "manualNumberOfLabelsOnLoad": 0,
  "orderLines": [
    {
      "content": "string",
      "dangerousGoods": [
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 1,
          "localId": 3,
          "noOfPackages": 3,
          "packaging": {
            "code": "S",
            "translationKey": "generalData.packagingOptionRoad.S.text",
          },
          "sortingPosition": 1,
        },
        {
          "dangerousGoodType": "EQDangerousGood",
          "id": 2,
          "localId": 4,
          "noOfPackages": 4,
          "sortingPosition": 2,
        },
      ],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 1,
      "length": 999,
      "loadingMeter": 0,
      "localId": 1,
      "markAndNumbers": undefined,
      "number": 999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": undefined,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
    {
      "content": "string",
      "dangerousGoods": [],
      "fullContainerLoadId": undefined,
      "goodsClassifications": [
        {
          "goods": "",
          "hsCode": undefined,
          "loading": false,
          "search": [],
        },
      ],
      "goodsGroup": {
        "code": "string",
        "quantity": 99999,
      },
      "height": 999,
      "hsCodes": [],
      "id": 2,
      "length": 999,
      "loadingMeter": 0,
      "localId": 2,
      "markAndNumbers": undefined,
      "number": 9999,
      "packaging": {
        "code": "string",
        "description": "string",
      },
      "packingPositionId": 5,
      "quantity": 99999,
      "volume": 0,
      "weight": 99999,
      "width": 999,
    },
  ],
  "originalPackaging": undefined,
  "packagingAidPosition": false,
  "packingPositions": [
    {
      "id": 1,
      "lines": undefined,
      "localId": 5,
      "packagingType": {
        "code": "string",
        "description": "string",
      },
      "quantity": 999,
    },
  ],
  "shockSensitive": false,
  "stackable": false,
  "totalValue": 999,
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 4`] = `
{
  "texts": [
    {
      "active": false,
      "id": undefined,
      "textType": "A",
      "value": undefined,
    },
    {
      "active": true,
      "id": undefined,
      "textType": "ZU",
      "value": "Additional Text",
    },
    {
      "active": false,
      "id": undefined,
      "textType": "WA",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "RE",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "SI",
      "value": undefined,
    },
    {
      "active": false,
      "id": undefined,
      "textType": "GS",
      "value": undefined,
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 5`] = `
{
  "cashOnDelivery": false,
  "cashOnDeliveryAmount": 0,
  "collectionInterpreterOption": "FIX",
  "collectionOption": "",
  "collectionTimeSlot": {
    "collectionDate": "2022-11-25",
    "from": "2022-11-25T15:15:10.478Z",
    "text": "15:15 - 15:15",
    "to": "2022-11-25T15:15:10.478Z",
  },
  "contactDataCollection": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataDelivery": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataFurtherAddresses": {},
  "customCollectionTimeSlot": {
    "collectionDate": "2022-11-25T00:00:00.000Z",
    "from": "15:15",
    "to": "15:15",
  },
  "date": 2022-11-25T00:00:00.000Z,
  "dateDelivery": "",
  "deliveryOption": "st",
  "deliveryProduct": "E",
  "productOption": "",
  "requestArrangement": false,
  "selectedAirDeliveryProduct": undefined,
  "selfCollection": false,
  "tailLiftCollection": false,
  "tailLiftDelivery": true,
  "timeSlotOptions": [
    {
      "collectionDate": "2022-11-25",
      "from": "2022-11-25T15:15:10.478Z",
      "text": "15:15 - 15:15",
      "to": "2022-11-25T15:15:10.478Z",
    },
    {
      "from": "Custom",
      "text": "labels.other_label.text",
      "to": "Custom",
    },
  ],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 6`] = `
{
  "bookingReference": [],
  "commercialInvoiceNumbers": [],
  "consigneeReferenceNumbers": [],
  "dailyPriceReference": "",
  "deliveryNoteNumberRequiredFields": [],
  "deliveryNoteNumbers": [
    {
      "id": "2",
      "value": "222",
    },
  ],
  "ekaerNumber": "987654321",
  "ekaerNumberNotRequired": false,
  "furtherReferencesOrder": [
    "PURCHASE_ORDER_NUMBER",
    "DELIVERY_NOTE_NUMBER",
  ],
  "identificationCodeTransport": [],
  "invoiceNumbers": [],
  "markAndNumbers": [],
  "otherNumbers": [],
  "packingListNumbers": [],
  "providerShipmentNumbers": [],
  "purchaseOrderNumbers": [
    {
      "id": "1",
      "value": "111",
    },
  ],
  "quotationReference": {
    "referenceValue": "",
  },
  "shipperReference": {
    "loading": true,
    "referenceValue": "",
    "unloading": true,
  },
  "supplierShipmentNumbers": [],
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 7`] = `
{
  "customsDeclarationExecutor": "DACHSER",
  "customsDeclarationRequired": true,
  "freightPayer": "Principal",
  "frostProtectionRequired": false,
  "orderGroup": "",
  "palletLocationsNumber": 2,
  "selectedFreightTerm": {
    "dachserTermKey": "Code_1",
    "headline": "Free delivered",
    "incoTermKey": "iCode_1",
  },
  "transport": "",
}
`;

exports[`useEditOrder composable > editOrder > should set order data for Road Order 8`] = `
{
  "collectionOptions": [],
  "countries": [],
  "customers": [],
  "deliveryOptions": [],
  "documentExtensions": [],
  "favoriteCountries": {
    "consigneeCountries": [],
    "shipperCountries": [],
  },
  "freightTerms": [],
  "fromPortRouting": [],
  "furtherAddressTypes": [],
  "hasInvalidRouting": false,
  "incoTerms": [],
  "isThirdCountryConstellation": false,
  "loadingPoints": [],
  "toPortRouting": [],
  "validateAddressResult": {
    "results": [],
    "valid": false,
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> address to address 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "address": {
      "city": "Munich",
      "countryCode": "DE",
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
    },
    "selection": "alternateAddressSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> addressBook to address 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "address": {
      "city": "Munich",
      "contact": {
        "name": "Ray Sono AG",
        "telephone": "*********",
      },
      "countryCode": "DE",
      "id": 123,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
    },
    "selection": "alternateAddressSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> customer to address 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> customer to addressBook 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": {
        "name": "Ray Sono AG",
        "telephone": "*********",
      },
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": 123,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "name": "Ray Sono AG",
    "telephone": "*********",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> customer to partialAddress 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "partial",
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> customer to port 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> partialAddress to address 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "address": {
      "countryCode": "DE",
      "lockedByQuote": "partial",
      "postcode": "80337",
    },
    "selection": "alternateAddressSelection",
  },
}
`;

exports[`useEditOrder composable > editOrder Quote2Book > should set addresses for Air Order from Quote -> port to address 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "Munich",
      "contact": undefined,
      "countryCode": "DE",
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": "full",
      "name": "Ray Sono AG",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "80337",
      "street": "Tumblinger Str. 32",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataCustomer": {
    "email": "string",
    "mobile": "string",
    "name": "string",
    "telephone": "string",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "shipperAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "selection": "defaultSelection",
  },
}
`;
