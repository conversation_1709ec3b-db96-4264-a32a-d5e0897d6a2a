import type { Countries, FavoriteCountries } from '@dfe/dfe-book-api';

export const countriesRoad: Countries = [
  { countryCode: 'AL', label: 'Albania', isPostCodeMandatory: true },
  { countryCode: 'DZ', label: 'Algeria', isPostCodeMandatory: true },
  { countryCode: 'AD', label: 'Andorra', isPostCodeMandatory: true },
  { countryCode: 'AT', label: 'Austria', isPostCodeMandatory: true },
  { countryCode: 'BE', label: 'Belgium', isPostCodeMandatory: true },
  { countryCode: 'BA', label: 'Bosnia and Herzegovina', isPostCodeMandatory: true },
  { countryCode: 'BG', label: 'Bulgaria', isPostCodeMandatory: true },
  { countryCode: 'HR', label: 'Croatia', isPostCodeMandatory: true },
  { countryCode: 'CY', label: 'Cyprus', isPostCodeMandatory: true },
  { countryCode: 'CZ', label: 'Czech Republic', isPostCodeMandatory: true },
  { countryCode: 'DK', label: 'Denmark', isPostCodeMandatory: true },
  { countryCode: 'EE', label: 'Estonia', isPostCodeMandatory: true },
  { countryCode: 'FO', label: 'Faroe Islands', isPostCodeMandatory: true },
  { countryCode: 'FI', label: 'Finland', isPostCodeMandatory: true },
  { countryCode: 'FR', label: 'France', isPostCodeMandatory: true },
  { countryCode: 'DE', label: 'Germany', isPostCodeMandatory: true },
  { countryCode: 'GR', label: 'Greece', isPostCodeMandatory: true },
  { countryCode: 'GL', label: 'Greenland', isPostCodeMandatory: true },
  { countryCode: 'HU', label: 'Hungary', isPostCodeMandatory: true },
  { countryCode: 'IS', label: 'Iceland', isPostCodeMandatory: true },
  { countryCode: 'IE', label: 'Ireland', isPostCodeMandatory: true },
  { countryCode: 'IT', label: 'Italy', isPostCodeMandatory: true },
  { countryCode: 'XK', label: 'Kosovo', isPostCodeMandatory: true },
  { countryCode: 'LV', label: 'Latvia', isPostCodeMandatory: true },
  { countryCode: 'LI', label: 'Liechtenstein', isPostCodeMandatory: true },
  { countryCode: 'LT', label: 'Lithuania', isPostCodeMandatory: true },
  { countryCode: 'LU', label: 'Luxembourg', isPostCodeMandatory: true },
  { countryCode: 'MK', label: 'Macedonia', isPostCodeMandatory: true },
  { countryCode: 'MD', label: 'Moldova', isPostCodeMandatory: true },
  { countryCode: 'ME', label: 'Montenegro', isPostCodeMandatory: true },
  { countryCode: 'MA', label: 'Morocco', isPostCodeMandatory: true },
  { countryCode: 'NL', label: 'Netherlands', isPostCodeMandatory: true },
  { countryCode: 'NO', label: 'Norway', isPostCodeMandatory: true },
  { countryCode: 'PL', label: 'Poland', isPostCodeMandatory: true },
  { countryCode: 'PT', label: 'Portugal', isPostCodeMandatory: true },
  { countryCode: 'RS', label: 'Republik of Serbia', isPostCodeMandatory: true },
  { countryCode: 'RO', label: 'Romania', isPostCodeMandatory: true },
  { countryCode: 'SM', label: 'San Marino', isPostCodeMandatory: true },
  { countryCode: 'SK', label: 'Slovakia', isPostCodeMandatory: true },
  { countryCode: 'SI', label: 'Slovenia', isPostCodeMandatory: true },
  { countryCode: 'ES', label: 'Spain', isPostCodeMandatory: true },
  { countryCode: 'SE', label: 'Sweden', isPostCodeMandatory: true },
  { countryCode: 'CH', label: 'Switzerland', isPostCodeMandatory: true },
  { countryCode: 'TN', label: 'Tunisia', isPostCodeMandatory: true },
  { countryCode: 'TR', label: 'Turkey', isPostCodeMandatory: true },
  { countryCode: 'GB', label: 'United Kingdom', isPostCodeMandatory: true },
  { countryCode: 'VA', label: 'Vatican City', isPostCodeMandatory: true },
];

export const countriesAir: Countries = [
  { countryCode: 'AF', label: 'Afghanistan', isPostCodeMandatory: true },
  { countryCode: 'AX', label: 'Aland Islands', isPostCodeMandatory: true },
  { countryCode: 'AL', label: 'Albania', isPostCodeMandatory: true },
  { countryCode: 'DZ', label: 'Algeria', isPostCodeMandatory: true },
  { countryCode: 'UM', label: 'American Minor Outlying Islands', isPostCodeMandatory: true },
  { countryCode: 'AS', label: 'American Samoa', isPostCodeMandatory: true },
  { countryCode: 'VI', label: 'American Virgin Islands', isPostCodeMandatory: true },
  { countryCode: 'AD', label: 'Andorra', isPostCodeMandatory: true },
  { countryCode: 'AO', label: 'Angola', isPostCodeMandatory: true },
  { countryCode: 'AI', label: 'Anguilla', isPostCodeMandatory: true },
  { countryCode: 'AQ', label: 'Antarctica', isPostCodeMandatory: true },
  { countryCode: 'AG', label: 'Antigua and Barbuda', isPostCodeMandatory: true },
  { countryCode: 'AR', label: 'Argentina', isPostCodeMandatory: true },
  { countryCode: 'AM', label: 'Armenia', isPostCodeMandatory: true },
  { countryCode: 'AW', label: 'Aruba', isPostCodeMandatory: true },
  { countryCode: 'AU', label: 'Australia', isPostCodeMandatory: true },
  { countryCode: 'AT', label: 'Austria', isPostCodeMandatory: true },
  { countryCode: 'AZ', label: 'Azerbaijan', isPostCodeMandatory: true },
  { countryCode: 'BS', label: 'Bahamas', isPostCodeMandatory: true },
  { countryCode: 'BH', label: 'Bahrain', isPostCodeMandatory: true },
  { countryCode: 'BD', label: 'Bangladesh', isPostCodeMandatory: true },
  { countryCode: 'BB', label: 'Barbados', isPostCodeMandatory: true },
  { countryCode: 'BY', label: 'Belarus', isPostCodeMandatory: true },
  { countryCode: 'BE', label: 'Belgium', isPostCodeMandatory: true },
  { countryCode: 'BZ', label: 'Belize', isPostCodeMandatory: true },
  { countryCode: 'BJ', label: 'Benin', isPostCodeMandatory: true },
  { countryCode: 'BM', label: 'Bermuda', isPostCodeMandatory: true },
  { countryCode: 'BT', label: 'Bhutan', isPostCodeMandatory: true },
  { countryCode: 'BO', label: 'Bolivia', isPostCodeMandatory: true },
  { countryCode: 'BQ', label: 'Bonaire, Saint Eustatius and Saba', isPostCodeMandatory: true },
  { countryCode: 'BA', label: 'Bosnia and Herzegovina', isPostCodeMandatory: true },
  { countryCode: 'BW', label: 'Botswana', isPostCodeMandatory: true },
  { countryCode: 'BV', label: 'Bouvet Islands', isPostCodeMandatory: true },
  { countryCode: 'BR', label: 'Brazil', isPostCodeMandatory: true },
  { countryCode: 'IO', label: 'British Indian Ocean Territory', isPostCodeMandatory: true },
  { countryCode: 'VG', label: 'British Virgin Islands', isPostCodeMandatory: true },
  { countryCode: 'BN', label: 'Brunei Darussalam', isPostCodeMandatory: true },
  { countryCode: 'BG', label: 'Bulgaria', isPostCodeMandatory: true },
  { countryCode: 'BF', label: 'Burkina Faso', isPostCodeMandatory: true },
  { countryCode: 'MM', label: 'Burma', isPostCodeMandatory: true },
  { countryCode: 'BI', label: 'Burundi', isPostCodeMandatory: true },
  { countryCode: 'KH', label: 'Cambodia', isPostCodeMandatory: true },
  { countryCode: 'CM', label: 'Cameroon', isPostCodeMandatory: true },
  { countryCode: 'CA', label: 'Canada', isPostCodeMandatory: true },
  { countryCode: 'CV', label: 'Cape Verde', isPostCodeMandatory: true },
  { countryCode: 'KY', label: 'Cayman Islands', isPostCodeMandatory: true },
  { countryCode: 'CF', label: 'Central African Republic', isPostCodeMandatory: true },
  { countryCode: 'TD', label: 'Chad', isPostCodeMandatory: true },
  { countryCode: 'CL', label: 'Chile', isPostCodeMandatory: true },
  { countryCode: 'CN', label: 'China', isPostCodeMandatory: true },
  { countryCode: 'CX', label: 'Christmas Islnd', isPostCodeMandatory: true },
  { countryCode: 'CC', label: 'Coconut Islands', isPostCodeMandatory: true },
  { countryCode: 'CO', label: 'Colombia', isPostCodeMandatory: true },
  { countryCode: 'KM', label: 'Comoros', isPostCodeMandatory: true },
  { countryCode: 'CK', label: 'Cook Islands', isPostCodeMandatory: true },
  { countryCode: 'CR', label: 'Costa Rica', isPostCodeMandatory: true },
  { countryCode: 'CI', label: "Cote d'Ivoire", isPostCodeMandatory: true },
  { countryCode: 'HR', label: 'Croatia', isPostCodeMandatory: true },
  { countryCode: 'CU', label: 'Cuba', isPostCodeMandatory: true },
  { countryCode: 'CW', label: 'Curacao', isPostCodeMandatory: true },
  { countryCode: 'CY', label: 'Cyprus', isPostCodeMandatory: true },
  { countryCode: 'CZ', label: 'Czech Republic', isPostCodeMandatory: true },
  { countryCode: 'CD', label: 'Democratic Republic of the Congo', isPostCodeMandatory: true },
  { countryCode: 'DK', label: 'Denmark', isPostCodeMandatory: true },
  { countryCode: 'DJ', label: 'Djibouti', isPostCodeMandatory: true },
  { countryCode: 'DM', label: 'Dominica', isPostCodeMandatory: true },
  { countryCode: 'DO', label: 'Dominican Republic', isPostCodeMandatory: true },
  { countryCode: 'AN', label: 'Dutch Antilles', isPostCodeMandatory: true },
  { countryCode: 'TL', label: 'East Timor', isPostCodeMandatory: true },
  { countryCode: 'EC', label: 'Ecuador', isPostCodeMandatory: true },
  { countryCode: 'EG', label: 'Egypt', isPostCodeMandatory: true },
  { countryCode: 'SV', label: 'El Salvador', isPostCodeMandatory: true },
  { countryCode: 'GQ', label: 'Equatorial Guinea', isPostCodeMandatory: true },
  { countryCode: 'ER', label: 'Eritrea', isPostCodeMandatory: true },
  { countryCode: 'EE', label: 'Estonia', isPostCodeMandatory: true },
  { countryCode: 'ET', label: 'Ethiopia', isPostCodeMandatory: true },
  { countryCode: 'FK', label: 'Falkland Islnds', isPostCodeMandatory: true },
  { countryCode: 'FO', label: 'Faroe Islands', isPostCodeMandatory: true },
  { countryCode: 'FJ', label: 'Fiji', isPostCodeMandatory: true },
  { countryCode: 'FI', label: 'Finland', isPostCodeMandatory: true },
  { countryCode: 'FR', label: 'France', isPostCodeMandatory: true },
  { countryCode: 'GF', label: 'French Guyana', isPostCodeMandatory: true },
  { countryCode: 'PF', label: 'French Polynesia', isPostCodeMandatory: true },
  { countryCode: 'TF', label: 'French S.Territ', isPostCodeMandatory: true },
  { countryCode: 'GA', label: 'Gabon', isPostCodeMandatory: true },
  { countryCode: 'GM', label: 'Gambia', isPostCodeMandatory: true },
  { countryCode: 'GE', label: 'Georgia', isPostCodeMandatory: true },
  { countryCode: 'DE', label: 'Germany', isPostCodeMandatory: true },
  { countryCode: 'GH', label: 'Ghana', isPostCodeMandatory: true },
  { countryCode: 'GI', label: 'Gibraltar', isPostCodeMandatory: true },
  { countryCode: 'GR', label: 'Greece', isPostCodeMandatory: true },
  { countryCode: 'GL', label: 'Greenland', isPostCodeMandatory: true },
  { countryCode: 'GD', label: 'Grenada', isPostCodeMandatory: true },
  { countryCode: 'GP', label: 'Guadeloupe', isPostCodeMandatory: true },
  { countryCode: 'GU', label: 'Guam', isPostCodeMandatory: true },
  { countryCode: 'GT', label: 'Guatemala', isPostCodeMandatory: true },
  { countryCode: 'GG', label: 'Guernsey, Channel Islands', isPostCodeMandatory: true },
  { countryCode: 'GN', label: 'Guinea', isPostCodeMandatory: true },
  { countryCode: 'GW', label: 'Guinea-Bissau', isPostCodeMandatory: true },
  { countryCode: 'GY', label: 'Guyana', isPostCodeMandatory: true },
  { countryCode: 'HT', label: 'Haiti', isPostCodeMandatory: true },
  { countryCode: 'HM', label: 'Heard and McDonald Islands', isPostCodeMandatory: true },
  { countryCode: 'HN', label: 'Honduras', isPostCodeMandatory: true },
  { countryCode: 'HK', label: 'Hong Kong', isPostCodeMandatory: true },
  { countryCode: 'HU', label: 'Hungary', isPostCodeMandatory: true },
  { countryCode: 'IS', label: 'Iceland', isPostCodeMandatory: true },
  { countryCode: 'IN', label: 'India', isPostCodeMandatory: true },
  { countryCode: 'ID', label: 'Indonesia', isPostCodeMandatory: true },
  { countryCode: 'IR', label: 'Iran', isPostCodeMandatory: true },
  { countryCode: 'IQ', label: 'Iraq', isPostCodeMandatory: true },
  { countryCode: 'IE', label: 'Ireland', isPostCodeMandatory: true },
  { countryCode: 'IM', label: 'Isle of Man', isPostCodeMandatory: true },
  { countryCode: 'IL', label: 'Israel', isPostCodeMandatory: true },
  { countryCode: 'IT', label: 'Italy', isPostCodeMandatory: true },
  { countryCode: 'JM', label: 'Jamaica', isPostCodeMandatory: true },
  { countryCode: 'JP', label: 'Japan', isPostCodeMandatory: true },
  { countryCode: 'JE', label: 'Jersey, Channel Islands', isPostCodeMandatory: true },
  { countryCode: 'JO', label: 'Jordan', isPostCodeMandatory: true },
  { countryCode: 'KZ', label: 'Kazakhstan', isPostCodeMandatory: true },
  { countryCode: 'KE', label: 'Kenya', isPostCodeMandatory: true },
  { countryCode: 'KI', label: 'Kiribati', isPostCodeMandatory: true },
  { countryCode: 'XK', label: 'Kosovo', isPostCodeMandatory: true },
  { countryCode: 'KW', label: 'Kuwait', isPostCodeMandatory: true },
  { countryCode: 'KG', label: 'Kyrgyzstan', isPostCodeMandatory: true },
  { countryCode: 'LA', label: 'Laos', isPostCodeMandatory: true },
  { countryCode: 'LV', label: 'Latvia', isPostCodeMandatory: true },
  { countryCode: 'LB', label: 'Lebanon', isPostCodeMandatory: true },
  { countryCode: 'LS', label: 'Lesotho', isPostCodeMandatory: true },
  { countryCode: 'LR', label: 'Liberia', isPostCodeMandatory: true },
  { countryCode: 'LY', label: 'Libya', isPostCodeMandatory: true },
  { countryCode: 'LI', label: 'Liechtenstein', isPostCodeMandatory: true },
  { countryCode: 'LT', label: 'Lithuania', isPostCodeMandatory: true },
  { countryCode: 'LU', label: 'Luxembourg', isPostCodeMandatory: true },
  { countryCode: 'MO', label: 'Macau', isPostCodeMandatory: true },
  { countryCode: 'MK', label: 'Macedonia', isPostCodeMandatory: true },
  { countryCode: 'MG', label: 'Madagascar', isPostCodeMandatory: true },
  { countryCode: 'MW', label: 'Malawi', isPostCodeMandatory: true },
  { countryCode: 'MY', label: 'Malaysia', isPostCodeMandatory: true },
  { countryCode: 'MV', label: 'Maldives', isPostCodeMandatory: true },
  { countryCode: 'ML', label: 'Mali', isPostCodeMandatory: true },
  { countryCode: 'MT', label: 'Malta', isPostCodeMandatory: true },
  { countryCode: 'MH', label: 'Marshall Islands', isPostCodeMandatory: true },
  { countryCode: 'MQ', label: 'Martinique', isPostCodeMandatory: true },
  { countryCode: 'MR', label: 'Mauretania', isPostCodeMandatory: true },
  { countryCode: 'MU', label: 'Mauritius', isPostCodeMandatory: true },
  { countryCode: 'YT', label: 'Mayotte', isPostCodeMandatory: true },
  { countryCode: 'MX', label: 'Mexico', isPostCodeMandatory: true },
  { countryCode: 'FM', label: 'Micronesia', isPostCodeMandatory: true },
  { countryCode: 'MD', label: 'Moldova', isPostCodeMandatory: true },
  { countryCode: 'MN', label: 'Mongolia', isPostCodeMandatory: true },
  { countryCode: 'ME', label: 'Montenegro', isPostCodeMandatory: true },
  { countryCode: 'MS', label: 'Montserrat', isPostCodeMandatory: true },
  { countryCode: 'MA', label: 'Morocco', isPostCodeMandatory: true },
  { countryCode: 'MZ', label: 'Mozambique', isPostCodeMandatory: true },
  { countryCode: 'NA', label: 'Namibia', isPostCodeMandatory: true },
  { countryCode: 'NR', label: 'Nauru', isPostCodeMandatory: true },
  { countryCode: 'NP', label: 'Nepal', isPostCodeMandatory: true },
  { countryCode: 'NL', label: 'Netherlands', isPostCodeMandatory: true },
  { countryCode: 'NC', label: 'New Caledonia', isPostCodeMandatory: true },
  { countryCode: 'NZ', label: 'New Zealand', isPostCodeMandatory: true },
  { countryCode: 'NI', label: 'Nicaragua', isPostCodeMandatory: true },
  { countryCode: 'NE', label: 'Niger', isPostCodeMandatory: true },
  { countryCode: 'NG', label: 'Nigeria', isPostCodeMandatory: true },
  { countryCode: 'NU', label: 'Niue', isPostCodeMandatory: true },
  { countryCode: 'NF', label: 'Norfolk Islands', isPostCodeMandatory: true },
  { countryCode: 'KP', label: 'North Korea', isPostCodeMandatory: true },
  { countryCode: 'MP', label: 'North Mariana Islands', isPostCodeMandatory: true },
  { countryCode: 'NO', label: 'Norway', isPostCodeMandatory: true },
  { countryCode: 'OM', label: 'Oman', isPostCodeMandatory: true },
  { countryCode: 'PK', label: 'Pakistan', isPostCodeMandatory: true },
  { countryCode: 'PW', label: 'Palau', isPostCodeMandatory: true },
  { countryCode: 'PS', label: 'Palestine', isPostCodeMandatory: true },
  { countryCode: 'PA', label: 'Panama', isPostCodeMandatory: true },
  { countryCode: 'PG', label: 'Papua New Guinea', isPostCodeMandatory: true },
  { countryCode: 'PY', label: 'Paraguay', isPostCodeMandatory: true },
  { countryCode: 'PE', label: 'Peru', isPostCodeMandatory: true },
  { countryCode: 'PH', label: 'Philippines', isPostCodeMandatory: true },
  { countryCode: 'PN', label: 'Pitcairn Islands', isPostCodeMandatory: true },
  { countryCode: 'PL', label: 'Poland', isPostCodeMandatory: true },
  { countryCode: 'PT', label: 'Portugal', isPostCodeMandatory: true },
  { countryCode: 'PR', label: 'Puerto Rico', isPostCodeMandatory: true },
  { countryCode: 'QA', label: 'Qatar', isPostCodeMandatory: true },
  { countryCode: 'CG', label: 'Republic of the Congo', isPostCodeMandatory: true },
  { countryCode: 'RS', label: 'Republik of Serbia', isPostCodeMandatory: true },
  { countryCode: 'RE', label: 'Reunion', isPostCodeMandatory: true },
  { countryCode: 'RO', label: 'Romania', isPostCodeMandatory: true },
  { countryCode: 'RU', label: 'Russian Federation', isPostCodeMandatory: true },
  { countryCode: 'RW', label: 'Rwanda', isPostCodeMandatory: true },
  { countryCode: 'SH', label: 'Saint Helena', isPostCodeMandatory: true },
  { countryCode: 'KN', label: 'Saint Kitts and Nevis', isPostCodeMandatory: true },
  { countryCode: 'BL', label: 'Saint-Barthélemy', isPostCodeMandatory: true },
  { countryCode: 'WS', label: 'Samoa', isPostCodeMandatory: true },
  { countryCode: 'SM', label: 'San Marino', isPostCodeMandatory: true },
  { countryCode: 'ST', label: 'Sao Tome and Principe', isPostCodeMandatory: true },
  { countryCode: 'SA', label: 'Saudi Arabia', isPostCodeMandatory: true },
  { countryCode: 'SN', label: 'Senegal', isPostCodeMandatory: true },
  { countryCode: 'SC', label: 'Seychelles', isPostCodeMandatory: true },
  { countryCode: 'SL', label: 'Sierra Leone', isPostCodeMandatory: true },
  { countryCode: 'SG', label: 'Singapore', isPostCodeMandatory: true },
  { countryCode: 'SX', label: 'Sint Maarten (Dutch part)', isPostCodeMandatory: true },
  { countryCode: 'SK', label: 'Slovakia', isPostCodeMandatory: true },
  { countryCode: 'SI', label: 'Slovenia', isPostCodeMandatory: true },
  { countryCode: 'SB', label: 'Solomon Islands', isPostCodeMandatory: true },
  { countryCode: 'SO', label: 'Somalia', isPostCodeMandatory: true },
  { countryCode: 'ZA', label: 'South Africa', isPostCodeMandatory: true },
  {
    countryCode: 'GS',
    label: 'South Georgia and the Southern Sandwich Islands',
    isPostCodeMandatory: true,
  },
  { countryCode: 'KR', label: 'South Korea', isPostCodeMandatory: true },
  { countryCode: 'SS', label: 'South Sudan', isPostCodeMandatory: true },
  { countryCode: 'ES', label: 'Spain', isPostCodeMandatory: true },
  { countryCode: 'LK', label: 'Sri Lanka', isPostCodeMandatory: true },
  { countryCode: 'LC', label: 'St. Lucia', isPostCodeMandatory: true },
  { countryCode: 'MF', label: 'St. Martin', isPostCodeMandatory: true },
  { countryCode: 'PM', label: 'St. Pierre and Miquelon', isPostCodeMandatory: true },
  { countryCode: 'VC', label: 'St. Vincent and the Grenadines', isPostCodeMandatory: true },
  { countryCode: 'SD', label: 'Sudan', isPostCodeMandatory: true },
  { countryCode: 'SR', label: 'Suriname', isPostCodeMandatory: true },
  { countryCode: 'SJ', label: 'Svalbard', isPostCodeMandatory: true },
  { countryCode: 'SZ', label: 'Swaziland', isPostCodeMandatory: true },
  { countryCode: 'SE', label: 'Sweden', isPostCodeMandatory: true },
  { countryCode: 'CH', label: 'Switzerland', isPostCodeMandatory: true },
  { countryCode: 'SY', label: 'Syria', isPostCodeMandatory: true },
  { countryCode: 'TW', label: 'Taiwan', isPostCodeMandatory: true },
  { countryCode: 'TJ', label: 'Tajikistan', isPostCodeMandatory: true },
  { countryCode: 'TZ', label: 'Tanzania', isPostCodeMandatory: true },
  { countryCode: 'TH', label: 'Thailand', isPostCodeMandatory: true },
  { countryCode: 'TG', label: 'Togo', isPostCodeMandatory: true },
  { countryCode: 'TK', label: 'Tokelau Islands', isPostCodeMandatory: true },
  { countryCode: 'TO', label: 'Tonga', isPostCodeMandatory: true },
  { countryCode: 'TT', label: 'Trinidad and Tobago', isPostCodeMandatory: true },
  { countryCode: 'TN', label: 'Tunisia', isPostCodeMandatory: true },
  { countryCode: 'TR', label: 'Turkey', isPostCodeMandatory: true },
  { countryCode: 'TM', label: 'Turkmenistan', isPostCodeMandatory: true },
  { countryCode: 'TC', label: 'Turks and Caicos Islands', isPostCodeMandatory: true },
  { countryCode: 'TV', label: 'Tuvalu', isPostCodeMandatory: true },
  { countryCode: 'US', label: 'USA', isPostCodeMandatory: true },
  { countryCode: 'UG', label: 'Uganda', isPostCodeMandatory: true },
  { countryCode: 'UA', label: 'Ukraine', isPostCodeMandatory: true },
  { countryCode: 'AE', label: 'United Arab Emirates', isPostCodeMandatory: true },
  { countryCode: 'GB', label: 'United Kingdom', isPostCodeMandatory: true },
  { countryCode: 'UY', label: 'Uruguay', isPostCodeMandatory: true },
  { countryCode: 'UZ', label: 'Uzbekistan', isPostCodeMandatory: true },
  { countryCode: 'VU', label: 'Vanuatu', isPostCodeMandatory: true },
  { countryCode: 'VA', label: 'Vatican City', isPostCodeMandatory: true },
  { countryCode: 'VE', label: 'Venezuela', isPostCodeMandatory: true },
  { countryCode: 'VN', label: 'Vietnam', isPostCodeMandatory: true },
  { countryCode: 'WF', label: 'Wallis and Futuna Islands', isPostCodeMandatory: true },
  { countryCode: 'YE', label: 'Yemen', isPostCodeMandatory: true },
  { countryCode: 'ZM', label: 'Zambia', isPostCodeMandatory: true },
  { countryCode: 'ZW', label: 'Zimbabwe', isPostCodeMandatory: true },
];

export const countryFavorites: FavoriteCountries = {
  shipperCountries: [
    { countryCode: 'CA', label: 'Canada', isPostCodeMandatory: true },
    { countryCode: 'IT', label: 'Italy', isPostCodeMandatory: true },
  ],
  consigneeCountries: [
    { countryCode: 'FR', label: 'France', isPostCodeMandatory: true },
    { countryCode: 'DE', label: 'Germany', isPostCodeMandatory: true },
    { countryCode: 'US', label: 'Canada', isPostCodeMandatory: true },
  ],
};
