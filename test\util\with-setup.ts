import { mount, type VueWrapper } from '@vue/test-utils';
import { defineComponent } from 'vue';

export function withSetup<T>(composable: () => T): [T, VueWrapper] {
  let result: T | undefined;
  const component = defineComponent({
    setup() {
      result = composable();
      return {};
    },
    template: '<span></span>',
  });
  const wrapper = mount(component);

  // @ts-expect-error - result is defined in setup
  return [result, wrapper];
}
