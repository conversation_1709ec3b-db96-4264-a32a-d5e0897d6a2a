import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { HandOverSelection, HandOverSelectionType } from '@/types/hand-over';
import { AirQuoteInformation, SeaQuoteInformation } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { computed, Ref } from 'vue';

const defaultState = {
  default: false,
  alternateAddress: false,
  port: false,
};

export function useHandOverDisabledState() {
  const formStore = useCreateOrderFormStore();
  const addressStore = useCreateOrderAddressesStore();
  const { isAslOrderFromQuote, isImportOrder, isExportOrder, quoteInformation, isAirOrder } =
    storeToRefs(formStore);
  const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);

  const disableShipperPort = computed(
    () => consigneeHandOverSelection.value.selection === HandOverSelection.port,
  );
  const disableConsigneePort = computed(() => {
    if (isImportOrder.value) {
      return true;
    }
    return shipperHandOverSelection.value.selection === HandOverSelection.port;
  });

  function calculateHandOver(
    selectionRef: Ref<{ selection: HandOverSelectionType }>,
    disablePortRef: Ref<boolean>,
    isShipper: boolean,
  ) {
    if (isAslOrderFromQuote.value) {
      const quoteInfo = isAirOrder.value
        ? (quoteInformation.value as AirQuoteInformation | null)
        : (quoteInformation.value as SeaQuoteInformation | null);

      let defaultValue = false;

      if (isExportOrder.value) {
        if (isShipper) {
          defaultValue = quoteInfo?.principalAddress != null;
        } else {
          defaultValue = false;
        }
      } else if (isImportOrder.value) {
        if (isShipper) {
          defaultValue = selectionRef.value.selection === HandOverSelection.port;
        } else {
          defaultValue = true;
        }
      }

      // Enable alternate address if principal address is set
      let alternateAddress =
        quoteInfo?.principalAddress == null &&
        (isShipper ? isExportOrder.value : isImportOrder.value);

      // disable alternate address if non-principal shipper is port
      if (
        isShipper &&
        isImportOrder.value &&
        selectionRef.value.selection === HandOverSelection.port
      ) {
        alternateAddress = true;
      }

      const port = disablePortRef.value || selectionRef.value.selection !== HandOverSelection.port;

      return {
        default: defaultValue,
        alternateAddress,
        port,
      };
    }

    return { ...defaultState, port: disablePortRef.value };
  }
  const shipper = computed(() =>
    calculateHandOver(shipperHandOverSelection, disableShipperPort, true),
  );
  const consignee = computed(() =>
    calculateHandOver(consigneeHandOverSelection, disableConsigneePort, false),
  );

  return {
    shipper,
    consignee,
  };
}
