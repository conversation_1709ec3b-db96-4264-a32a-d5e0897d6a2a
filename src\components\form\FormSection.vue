<template>
  <VForm ref="form" :model-value="modelValue" @update:model-value="validate" @submit.prevent>
    <slot />
  </VForm>
</template>

<script setup lang="ts">
import useResetCreateOrderFormData from '@/composables/createOrder/useResetCreateOrderFormData';
import { useClient } from '@/composables/useClient';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { onBeforeUnmount, ref } from 'vue';
import { VForm } from 'vuetify/components';

const modelValue = defineModel<boolean>();

const { client } = useClient();
const form = ref<VForm | null>(null);
const { clearFormData } = useResetCreateOrderFormData();
const createOrderFormStore = useCreateOrderFormStore();
const { isValidationTriggered } = storeToRefs(createOrderFormStore);

const validate = (isValid: boolean | null) => {
  modelValue.value = isValid ?? true;
};

const resetFormValidation = () => {
  isValidationTriggered.value = false;
  form.value?.resetValidation();
};

const handleCreateOrderValidate = () => {
  form.value?.validate();
};
client?.events.on('createOrderValidate', handleCreateOrderValidate);

const handleClearCreateOrderFormData = () => {
  clearFormData();
  resetFormValidation();
};
client?.events.on('clearCreateOrderFormData', handleClearCreateOrderFormData);

client?.events.on('createOrder', resetFormValidation);
client?.events.on('editOrder', resetFormValidation);

onBeforeUnmount(() => {
  client?.events.off('clearCreateOrderFormData', handleClearCreateOrderFormData);
  client?.events.off('createOrderValidate', handleCreateOrderValidate);
  client?.events.off('createOrder', resetFormValidation);
  client?.events.off('editOrder', resetFormValidation);
});

defineExpose({
  validate: async () => form.value?.validate(),
});
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

:deep(.v-card) {
  border: 1px solid vars.$color-base-grey-200;
}
</style>
