import {
  DfeColumnAlignment,
  DfeTableHeader,
  DfeTableOptions,
  DfeTableSortDirection,
} from '@/components/tableComponent/DfeTableTypes';
import { ref, Ref, shallowRef } from 'vue';
import UnNumberColumn from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberColumn.vue';
import { displayValueWithFallback } from '@/utils/form/valueWithFallback';
import { useI18n } from 'vue-i18n';

const INITIAL_TABLE_OPTIONS_BOOK: DfeTableOptions = {
  totalItems: 0,
  itemsPerPage: 10,
  currentPage: 1,
  totalPages: 0,
  allSelectable: false,
  itemsSelectable: true,
  singleSelect: true,
  firstColumnSticky: false,
  disablePagination: true,
  sortBy: {
    key: 'lastModified',
    order: DfeTableSortDirection.DESC,
  },
};

export const useDangerousGoodsTableData = () => {
  const { t } = useI18n();

  const tableOptions = ref(INITIAL_TABLE_OPTIONS_BOOK);

  const tableHeaders: Ref<DfeTableHeader[]> = ref([
    {
      key: 'unNumber',
      label: t('labels.un_number_short.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
      component: shallowRef(UnNumberColumn),
    },
    {
      key: 'mainDanger',
      label: t('labels.main_danger.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
    },
    {
      key: 'subsidiaryHazardOne',
      label: t('labels.sub_hazard_one.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
      formatting: displayValueWithFallback,
    },
    {
      key: 'subsidiaryHazardTwo',
      label: t('labels.sub_hazard_two.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
      formatting: displayValueWithFallback,
    },
    {
      key: 'subsidiaryHazardThree',
      label: t('labels.sub_hazard_three.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
      formatting: displayValueWithFallback,
    },
    {
      key: 'packingGroup',
      label: t('labels.packaging_group_short.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
    },
    {
      key: 'description',
      label: t('labels.adr_designation.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
    },
    {
      key: 'classificationCode',
      label: t('labels.classification_code.text'),
      visible: true,
      justification: DfeColumnAlignment.LEFT,
    },
  ]);

  return { tableOptions, tableHeaders };
};
