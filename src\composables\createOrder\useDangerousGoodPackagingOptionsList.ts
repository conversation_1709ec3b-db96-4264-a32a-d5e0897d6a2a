import type { Options } from '@dfe/dfe-book-api';
import type { Ref } from 'vue';

export const useDangerousGoodPackagingOptionsList = (
  options: Ref<Options, Options>,
  t: (key: string) => string,
) => {
  let dangerousGoodPackagingOptions = options.value;

  dangerousGoodPackagingOptions = dangerousGoodPackagingOptions
    .map((option) => ({
      ...option,
      translationKey: t(`${String(option.translationKey)}.text`),
    }))
    .sort((a, b) => a.translationKey.localeCompare(b.translationKey));
  return dangerousGoodPackagingOptions;
};
