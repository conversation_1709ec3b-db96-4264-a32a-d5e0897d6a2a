import SelectAddress from '@/components/form/SelectAddress.vue';
import type { CustomerWithAddress } from '@/store/createOrder/data';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { beforeAll, beforeEach } from 'vitest';
import { nextTick } from 'vue';
import { VSelect } from 'vuetify/components';

const label = 'Label';
const requiredChar = '*';

const props = {
  modelValue: '0',
  items: [
    {
      customerNumber: '0',
      label: 'Item 0 label',
      address: {
        name: 'Item 0 name',
        street: 'Item 0 street',
        postcode: 'Item 0 postcode',
        city: 'Item 0 city',
        countryCode: 'DE',
      },
    },
    {
      customerNumber: '1',
      label: 'Item 1 label',
      address: {
        name: 'Item 1 name',
        street: 'Item 1 street',
        postcode: 'Item 1 postcode',
        city: 'Item 1 city',
        countryCode: 'EN',
      },
    },
  ] as CustomerWithAddress[],
};

describe('SelectAddress component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(SelectAddress, {
      props,
      attachTo: document.body,
      global: {
        stubs: ['v-menu'],
      },
    });
  });

  it('emits input event with id on change', () => {
    const select = wrapper.findComponent({ name: 'v-select' });
    select.vm.$emit('update:modelValue', 0);

    const event = wrapper.emitted<[number]>('update:modelValue');
    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([0]);
  });

  it('displays the address label when an item is selected', async () => {
    expect(wrapper.find('.v-select__selection').text()).toEqual(props.items[0].label);
  });

  it('displays full addresses in the dropdown', async () => {
    // await wrapper.getComponent(VSelect).trigger('mouseup');
    wrapper.getComponent(VSelect).vm.menu = true;

    await nextTick();

    const items = wrapper.findAll('.v-list-item-title');

    expect(items).toHaveLength(2);

    for (let i = 0; i < items.length; i++) {
      Object.values(props.items[i].address ?? []).forEach((value) => {
        expect(items.at(i)?.text()).toContain(value);
      });
    }
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    expect(wrapper.get('label').text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ required: true, label });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it("doesn't sort menu items if label is not available", async () => {
    const items = [
      { customerNumber: 2, address: { name: 'name ccc' } },
      { customerNumber: 0, address: { name: 'name aaa' } },
      { customerNumber: 1, address: { name: 'name bbb' } },
    ];
    await wrapper.setProps({ items });
    const menuItems = wrapper.findAll('.v-list-item-title');
    expect(menuItems.at(0)?.text()).toContain('name ccc');
    expect(menuItems.at(1)?.text()).toContain('name aaa');
    expect(menuItems.at(2)?.text()).toContain('name bbb');
  });
  it('should show address name, name2, name3', async () => {
    const items = [
      { customerNumber: 2, address: { name: 'name', name1: 'name2', name2: 'name3' } },
    ];
    await wrapper.setProps({ items, loadingPoints: false });
    const menuItems = wrapper.findAll('.v-list-item-title');
    expect(menuItems.at(0)?.text()).toContain('name, name2, name3');
  });
});
