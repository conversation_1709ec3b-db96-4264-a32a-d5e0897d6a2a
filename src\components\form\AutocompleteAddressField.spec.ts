import AutocompleteAddressField from '@/components/form/AutocompleteAddressField.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import type { Address } from '@dfe/dfe-address-api';
import { beforeAll, beforeEach } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';

const items: Address[] = [
  {
    name: 'name1',
    city: 'city1',
    countryCode: 'DE',
    postcode: '12345',
    street: 'street1',
    individualId: '123456789',
  },
  {
    name: 'name2',
    name2: 'name2-2',
    name3: 'name2-3',
    city: 'city2',
    countryCode: 'EN',
    postcode: '67890',
    street: 'street2',
    individualId: '123456789',
  },
];

const props = {
  items,
};
const mockInput = props.items[1];
const label = 'Label';
const requiredChar = '*';

describe('AutocompleteAddressField compnent', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(AutocompleteAddressField, {
      props,
      attachTo: document.body,
      global: {
        stubs: ['v-menu'],
      },
    });
  });

  it('emits input event on change', async () => {
    const autocomplete = wrapper.findComponent({ name: 'v-autocomplete' });
    await autocomplete.vm.$emit('update:modelValue', mockInput);

    const inputEvents = wrapper.emitted('update:modelValue');

    expect(inputEvents).toHaveLength(1);
    expect(inputEvents?.[0]).toEqual([mockInput]);
  });

  it('shows address values in menu', async () => {
    const el = wrapper.find('.v-input__control [role="combobox"]');
    await el.trigger('click');
    const listItems = wrapper.findAllComponents({
      name: 'v-list-item-content',
    });

    for (let i = 0; i < listItems.length; i++) {
      Object.values(items[i]).forEach((v) => {
        expect(listItems.at(i)?.text()).toContain(v);
      });
    }
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it('shows no-data slot if search has no results', async () => {
    wrapper.findComponent({ name: 'v-autocomplete' }).vm.$emit('update:search', 'test');
    await wrapper.setProps({ loading: true });
    await wrapper.setProps({ items: [], loading: false });
    await wrapper.vm.$nextTick();

    const menu = wrapper.findComponent({ name: 'v-menu' });
    expect(menu.text()).toBe('labels.no_matching_results.text');
  });

  it('should render address correctly for non-Ireland', async () => {
    await wrapper.setProps({
      items: [
        {
          name: 'Elastic GmbH',
          name2: 'Name 2',
          name3: 'Name 3',
          street: 'Teststraße 2',
          street2: null,
          postcode: '2020',
          city: 'Barbados',
          state: null,
          countryCode: 'BE',
          country: null,
          supplement: null,
        },
      ],
    });

    const displayAddress = wrapper.find('.display-address');

    expect(displayAddress.text()).toBe('Teststraße 2, BE 2020 Barbados');
  });

  it('should render address correctly for Ireland', async () => {
    await wrapper.setProps({
      items: [
        {
          name: 'Ireland Test',
          name2: 'Name 2',
          name3: 'Name 3',
          street: 'Teststraße 2',
          street2: null,
          postcode: '2020',
          city: 'Dublin',
          state: null,
          countryCode: 'IE',
          country: null,
          supplement: 'ABC',
        },
      ],
    });

    const displayAddress = wrapper.find('.display-address');

    expect(displayAddress.text()).toBe('Teststraße 2, IE 2020 Dublin, ABC');
  });
});
