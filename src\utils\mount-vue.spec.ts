import { mountVue } from '@/utils/mount-vue';
import { createClientMock } from '@test/util/mock-client';
import LabelPrint from '@/apps/LabelPrint.vue';

describe('mount-app', () => {
  const client = createClientMock();
  vi.spyOn(client.auth, 'getToken').mockImplementation(async () => 'token');

  let mounterFn: ReturnType<typeof mountVue>;
  let mountTarget: HTMLDivElement;

  beforeAll(() => {
    mountTarget = document.createElement('div');
    document.body.appendChild(mountTarget);
  });

  beforeEach(() => {
    mounterFn = mountVue(LabelPrint);
  });

  it('returns a mounting function for a given component', () => {
    expect(typeof mounterFn).toBe('function');
  });

  it('mounts onto HTMLElement', async () => {
    await mounterFn(mountTarget, client);

    expect(document.querySelector('[dfe-book-frontend]')).toBeTruthy();
  });
});
