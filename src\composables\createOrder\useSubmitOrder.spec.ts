import useSubmitOrder from '@/composables/createOrder/useSubmitOrder';
import { initPinia } from '../../../test/util/init-pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderType } from '@dfe/dfe-book-api';
import { mockServer } from '@/mocks/server';
import { submitOrderResult } from '@/mocks/fixtures/submitOrderResult';
import type { DFEClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';
import type { Server } from 'miragejs';
import { validationError } from '@/mocks/fixtures/validationResults';
import { useInit } from '@/composables/useInit';

describe('useSubmitOrder', () => {
  let submitOrder: ReturnType<typeof useSubmitOrder>;
  let server: Server;
  let client: DFEClient<Events>;

  beforeEach(() => {
    server = mockServer({
      environment: 'test',
      fixtures: {
        submitOrderResult,
      },
    });

    initPinia();
    client = useInit().client;
    submitOrder = useSubmitOrder(client);
  });

  afterEach(() => {
    server.shutdown();
  });

  it('should submit an order', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const submitOrderSpy = vi.spyOn(createOrderFormStore, 'submitOrder');
    createOrderFormStore.saveOrderData = {
      orderId: 1,
      orderType: OrderType.RoadForwardingOrder,
    };
    await submitOrder.submitOrder();
    expect(submitOrderSpy).toHaveBeenCalled();
  });

  it('should fail due to validation error', async () => {
    server.shutdown();
    server = mockServer({
      environment: 'test',
      fixtures: {
        submitOrderResult,
        validationError,
      },
    });

    const createOrderFormStore = useCreateOrderFormStore();
    createOrderFormStore.saveOrderData = null;

    const { validationResult } = await submitOrder.submitOrder();
    expect(validationResult).toEqual(validationError);
    expect(createOrderFormStore.saveOrderData).not.toEqual(null);
  });
});
