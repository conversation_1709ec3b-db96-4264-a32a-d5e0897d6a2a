import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useValidationDataStore } from '@/store/validation';
import { storeToRefs } from 'pinia';

export type ValidationResult = boolean | PromiseLike<boolean>;

export type CreateOrderValidationEmits = (
  event: 'createOrderValidate',
  validate: (value: ValidationResult) => void,
) => void;

export function useCreateOrderValidation(emit: CreateOrderValidationEmits) {
  const { formValidationSectionsAir } = storeToRefs(useValidationDataStore());

  const validateContactForm = () => {
    const { isShipperContactDataSet, isConsigneeContactDataSet } = useCreateOrderAddressesStore();

    formValidationSectionsAir.value = {
      ...formValidationSectionsAir.value,
      airOrderAddresses: isShipperContactDataSet && isConsigneeContactDataSet,
    };
  };

  const createOrderValidate = async (callback?: (value: ValidationResult) => void) => {
    const formSectionsValidation = new Promise<boolean>((validate) => {
      emit('createOrderValidate', validate);
    });

    validateContactForm();

    const isValid = await formSectionsValidation;

    if (callback) callback(isValid);

    return isValid;
  };

  return {
    createOrderValidate,
  };
}
