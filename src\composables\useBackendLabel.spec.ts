import { useBackend<PERSON>abel<PERSON>ey } from '@/composables/useBackendLabel';

describe('useBackendLabelKey conversion', () => {
  it('should return the input if not a label', () => {
    const resultOne = useBackendLabelKey('');

    expect(resultOne).toEqual('');
  });
  it('should return the converted value if a label', () => {
    const resultTwo = useBackendLabelKey('label.try_again_later');

    expect(resultTwo).toEqual('labels.try_again_later.text');
  });
  it('should return the converted value if a message', () => {
    const resultTwo = useBackendLabelKey('message.text.6584');

    expect(resultTwo).toEqual('messages.id6584.text');
  });
});
