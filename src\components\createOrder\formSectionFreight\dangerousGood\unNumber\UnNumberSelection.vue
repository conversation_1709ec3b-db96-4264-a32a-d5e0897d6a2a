<template>
  <SpanField class="mr-3 mt-4 un-number_container" :label="t('labels.un_number.text')">
    <div class="h-100 d-flex align-center un-number_display">
      <UnNumberColumn :item="unNumberData" />
      <span class="text-body-2 text--light un-number_description">{{
        unNumberData?.description
      }}</span>
    </div>
  </SpanField>

  <DfeInputTextField
    v-if="unNumberData?.nosRequired"
    v-model="nos"
    v-data-test="'dangerous-goods-nos-field'"
    :input-id="nosId"
    :label="t('labels.dangerous_goods_nos_nag.text')"
    class="un-number_nos-input mr-3 mt-4"
    :required="true"
    :max-length="NOS_MAX_LENGTH"
  />
</template>

<script lang="ts" setup>
import SpanField from '@/components/form/SpanField.vue';
import { useI18n } from 'vue-i18n';
import { DangerousGoodDataItem } from '@dfe/dfe-book-api';
import UnNumberColumn from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberColumn.vue';
import { DfeInputTextField } from '@dfe/dfe-frontend-shared-components';
import { createUuid } from '@/utils/createUuid';

interface Props {
  unNumberData?: DangerousGoodDataItem;
}

const nosId = `create-order-un-number-nos-${createUuid()}`;
const NOS_MAX_LENGTH = 100;

const { unNumberData = undefined } = defineProps<Props>();
const nos = defineModel<string>('nos', { default: undefined });

const { t } = useI18n();
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use '@/styles/base' as base;
@use 'sass:map';

$un-number-display-width: calc(
  #{vars.$form-input-width-lg} + #{base.space(3)} + #{vars.$form-input-width-sm}
);

.un-number_container {
  width: $un-number-display-width;
  min-width: $un-number-display-width;

  .un-number_display {
    max-width: 100%;
  }

  .un-number_description {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }
}

.un-number_nos-input {
  width: 100%;
  max-width: vars.$form-input-width-xxl;
}
</style>
