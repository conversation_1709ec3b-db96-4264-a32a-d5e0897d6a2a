@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

@use 'variablesImports/breakpoints';
@use 'variablesImports/colors';
@use 'variablesImports/typography';
@use 'variablesImports/grid';
@use 'variablesImports/buttons';
@use 'variablesImports/lists';
@use 'variablesImports/forms';
@use 'variablesImports/divider';
@use 'variablesImports/tabs';
@use 'variablesImports/tooltip';
//
//@forward "vuetify/settings" with (
//$material-light: (
//  'text-fields': (
//    'outlined': var(--color-base-grey-400),
//    'outlined-hover': var(--color-base-grey-400),
//  ),
//  'text': (
//    'primary': vars.$color-text-base,
//  ),
//  'icons': (
//    'active': var(--color-base-grey-700),
//    'inactive': var(--color-base-grey-500),
//  ),
//),
//);
$progress-circular-underlay-stroke: var(--color-base-grey-400);
$form-input-width-xxl: 634px;
$form-input-width-xl: 380px;
$form-input-width-lg: 240px;
$form-input-width-md: 128px;
$form-input-width-sm: 110px;
$form-input-width-xs: 86px;

$form-cols-width-xl: 396px;
