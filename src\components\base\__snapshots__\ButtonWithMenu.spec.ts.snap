// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ButtonWithMenu component > check click on chip 1`] = `
<div
  data-v-64dc9b7f=""
>
  
  
  <span
    aria-controls="v-menu-v-0"
    aria-expanded="true"
    aria-haspopup="menu"
    class="v-chip v-chip--link v-theme--light v-chip--density-default v-chip--size-small v-chip--variant-outlined custom-chip default-activator d-inline-block rounded-xl pa-0 my-0 text-label-3 active"
    data-v-64dc9b7f=""
    draggable="false"
    tabindex="0"
  >
    
    <span
      class="v-chip__overlay"
    />
    <span
      class="v-chip__underlay"
    />
    
    <!---->
    <!---->
    <div
      class="v-chip__content"
      data-no-activator=""
    >
      
      
      
      <div
        aria-describedby="v-tooltip-v-2"
        class="d-flex pl-1 py-1 pr-6"
        data-v-64dc9b7f=""
      >
        
        
         label
      </div>
      
      <!--teleport start-->
      <!--teleport end-->
      
      
    </div>
    <!---->
    <!---->
  </span>
  
  <!--teleport start-->
  <!--teleport end-->
  
</div>
`;

exports[`ButtonWithMenu component > matches desktop markup 1`] = `
<div
  data-v-64dc9b7f=""
>
  
  
  <span
    aria-controls="v-menu-v-0"
    aria-expanded="false"
    aria-haspopup="menu"
    class="v-chip v-chip--link v-theme--light v-chip--density-default v-chip--size-small v-chip--variant-outlined custom-chip default-activator d-inline-block rounded-xl pa-0 my-0 text-label-3"
    data-v-64dc9b7f=""
    draggable="false"
    tabindex="0"
  >
    
    <span
      class="v-chip__overlay"
    />
    <span
      class="v-chip__underlay"
    />
    
    <!---->
    <!---->
    <div
      class="v-chip__content"
      data-no-activator=""
    >
      
      
      
      <div
        aria-describedby="v-tooltip-v-2"
        class="d-flex pl-1 py-1 pr-6"
        data-v-64dc9b7f=""
      >
        
        
         label
      </div>
      
      <!--teleport start-->
      <!--teleport end-->
      
      
    </div>
    <!---->
    <!---->
  </span>
  
  <!---->
  
</div>
`;
