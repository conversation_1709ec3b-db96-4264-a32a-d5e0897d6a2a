import type { FurtherAddressTypes } from '@dfe/dfe-book-api';
import { FurtherAddressTypesList } from '@/enums';

export const furtherAddressTypes: FurtherAddressTypes[] = [
  [
    {
      code: FurtherAddressTypesList.coverAddressConsignor,
      description: 'DECKADRESSE ABSENDER',
    },
    {
      code: FurtherAddressTypesList.deviatingFreightPayer,
      description: 'Rechnung durch Empfangsspediteur an',
    },
    {
      code: FurtherAddressTypesList.DC,
      description: 'FRACHTFÜHRER AM EMPFANGSORT',
    },
  ],
  [
    {
      code: FurtherAddressTypesList.customsAgent,
      description: 'ZOLLAGENT',
    },
    {
      code: FurtherAddressTypesList.finalDeliveryAddress,
      description: 'LIEFERANSCHRIFT',
    },
    {
      code: FurtherAddressTypesList.AE,
      description: 'AUSFUHRBESCHEINIGUNG EMPFÄNGER',
    },
    {
      code: FurtherAddressTypesList.DC,
      description: 'FRACHTFÜHRER AM EMPFANGSORT',
    },
  ],
  [
    {
      code: FurtherAddressTypesList.coverAddressConsignor,
      description: 'DECKADRESSE ABSENDER',
    },
  ],
];
