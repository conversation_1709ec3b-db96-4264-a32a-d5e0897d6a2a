import { base64 } from '@/utils/converters/base64';

describe('Base64 Converter', () => {
  describe('toArrayBuffer', () => {
    it('returns ArrayBuffer', () => {
      const str = '23947452';
      const result = base64(str).toArrayBuffer();
      expect(result).toBeInstanceOf(ArrayBuffer);
    });

    it('Returns ArrayBuffer of correct length', () => {
      const str = '23947452';
      const result = base64(str).toArrayBuffer();
      expect(result.byteLength).toBe(6);
    });
  });
});
