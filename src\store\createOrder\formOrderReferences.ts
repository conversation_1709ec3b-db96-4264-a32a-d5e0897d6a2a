import { createUuid } from '@/utils/createUuid';
import { MaxLength } from '@/enums';
import { defineStore } from 'pinia';
import { useCreateOrderFormStore } from './form';
import { useCreateOrderAddressesStore } from './formAddresses';
import type { AirSeaOrderReference } from '@dfe/dfe-book-api';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import type { TranslateResult } from 'vue-i18n';
import { i18n } from '@/plugins/i18n';
import { useValidationRules } from '@/composables/form/useValidationRules';

export interface MultipleReferenceNumber {
  id: string;
  value: string;
  loading?: boolean;
  unloading?: boolean;
}

export interface CreateOrderOrderReferencesFormStore {
  purchaseOrderNumbers: MultipleReferenceNumber[];
  deliveryNoteNumbers: MultipleReferenceNumber[];
  ekaerNumber: string | undefined;
  ekaerNumberNotRequired: boolean;
  deliveryNoteNumberRequiredFields: {
    id: string;
    required: boolean;
  }[];
  shipperReference: AirSeaOrderReference;
  quotationReference: AirSeaOrderReference;
  dailyPriceReference: string | undefined;
  invoiceNumbers: MultipleReferenceNumber[];
  otherNumbers: MultipleReferenceNumber[];
  markAndNumbers: MultipleReferenceNumber[];
  consigneeReferenceNumbers: MultipleReferenceNumber[];
  supplierShipmentNumbers: MultipleReferenceNumber[];
  providerShipmentNumbers: MultipleReferenceNumber[];
  packingListNumbers: MultipleReferenceNumber[];
  commercialInvoiceNumbers: MultipleReferenceNumber[];
  identificationCodeTransport: MultipleReferenceNumber[];
  bookingReference: MultipleReferenceNumber[];
  furtherReferencesOrder: OrderReferenceType[]; // Array contains the references types in correct order
}

function getEmptyReferenceObject(withLoadingData: boolean): MultipleReferenceNumber {
  if (withLoadingData) {
    return {
      id: `id-${createUuid()}`,
      value: '',
      loading: true,
      unloading: true,
    };
  } else
    return {
      id: `id-${createUuid()}`,
      value: '',
    };
}

export const useCreateOrderOrderReferencesFormStore = defineStore(
  'createOrderOrderReferencesFormStore',
  {
    state: (): CreateOrderOrderReferencesFormStore => ({
      purchaseOrderNumbers: [],
      deliveryNoteNumbers: [],
      ekaerNumber: undefined,
      ekaerNumberNotRequired: false,
      deliveryNoteNumberRequiredFields: [],
      shipperReference: {
        referenceValue: '',
        loading: true,
        unloading: true,
      },
      quotationReference: {
        referenceValue: '',
      },
      dailyPriceReference: undefined,
      invoiceNumbers: [],
      otherNumbers: [],
      markAndNumbers: [],
      consigneeReferenceNumbers: [],
      supplierShipmentNumbers: [],
      providerShipmentNumbers: [],
      packingListNumbers: [],
      commercialInvoiceNumbers: [],
      identificationCodeTransport: [],
      bookingReference: [],
      furtherReferencesOrder: [],
    }),
    getters: {
      disablePurchaseOrderNumberButton(state) {
        return state.purchaseOrderNumbers.length >= MaxLength.NumbersArray;
      },
      disableDeliveryNoteNumberButton(state) {
        return state.deliveryNoteNumbers.length >= MaxLength.NumbersArray;
      },
      disableIdentificationCodeTransportButton(state) {
        return state.identificationCodeTransport.length >= MaxLength.NumbersArray;
      },
      disableBookingReferenceButton(state) {
        return state.bookingReference.length >= MaxLength.NumbersArray;
      },
      optionalReferencesObjects(state) {
        const { t } = i18n.global;
        return state.furtherReferencesOrder
          .map((referenceType: OrderReferenceType) => {
            switch (referenceType) {
              case OrderReferenceType.PURCHASE_ORDER_NUMBER:
                return {
                  name: referenceType,
                  items: state.purchaseOrderNumbers,
                  label: t('labels.purchase_order_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.INVOICE_NUMBER:
                return {
                  name: referenceType,
                  items: state.invoiceNumbers,
                  label: t('labels.invoice_number_reference.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.OTHERS:
                return {
                  name: referenceType,
                  items: state.otherNumbers,
                  label: t('labels.other_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.DELIVERY_NOTE_NUMBER:
                return {
                  name: referenceType,
                  items: state.deliveryNoteNumbers,
                  label: t('labels.delivery_note_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.MARKS_AND_NUMBERS:
                return {
                  name: referenceType,
                  items: state.markAndNumbers,
                  label: t('labels.marks_label.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER:
                return {
                  name: referenceType,
                  items: state.consigneeReferenceNumbers,
                  label: t('labels.consignee_reference_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER:
                return {
                  name: referenceType,
                  items: state.supplierShipmentNumbers,
                  label: t('labels.supplier_shipment_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.PROVIDER_SHIPMENT_NUMBER:
                return {
                  name: referenceType,
                  items: state.providerShipmentNumbers,
                  label: t('labels.provider_shipment_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.PACKAGING_LIST_NUMBER:
                return {
                  name: referenceType,
                  items: state.packingListNumbers,
                  label: t('labels.packing_list_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.COMMERCIAL_INVOICE_NUMBER:
                return {
                  name: referenceType,
                  items: state.commercialInvoiceNumbers,
                  label: t('labels.commercial_invoice_number.text'),
                  maxLength: MaxLength.Default,
                };
              case OrderReferenceType.BOOKING_REFERENCE:
                return {
                  name: referenceType,
                  items: state.bookingReference,
                  label: t('labels.booking_reference_label.text'),
                  maxLength: MaxLength.Default,
                  rules: [
                    useValidationRules.regex(
                      new RegExp('^[a-zA-Z\\d\\s:]*$'),
                      'labels.invalid_input.text',
                      '',
                    ),
                  ],
                };
              case OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT:
                return {
                  name: referenceType,
                  items: state.identificationCodeTransport,
                  label: t('labels.identification_code_transport.text'),
                  maxLength: MaxLength.IdentificationCodeTransport,
                  prefix: 'UIT',
                  rules: [
                    useValidationRules.required,
                    useValidationRules.regex(
                      new RegExp('^[a-zA-Z0-9]{16}$'),
                      'labels.validation_invalid_uit.text',
                      '1234AB7890CD3456',
                    ),
                  ],
                };
              default:
                return undefined;
            }
          })
          .filter(Boolean);
      },
      moreReferencesOptions(state): { value: OrderReferenceType; text: TranslateResult }[] {
        const { t } = i18n.global;
        return [
          {
            value: OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
            text: t('labels.commercial_invoice_number.text'),
          },
          {
            value: OrderReferenceType.PACKAGING_LIST_NUMBER,
            text: t('labels.packing_list_number.text'),
          },
          {
            value: OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
            text: t('labels.consignee_reference_number.text'),
          },
          {
            value: OrderReferenceType.MARKS_AND_NUMBERS,
            text: t('labels.marks_label.text'),
          },
          {
            value: OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
            text: t('labels.provider_shipment_number.text'),
          },
          {
            value: OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
            text: t('labels.supplier_shipment_number.text'),
          },
        ].filter((item) => state.furtherReferencesOrder.indexOf(item.value) < 0);
      },
      storeInfosforEkaer() {
        const { shipperAddress, consigneeAddress, isCustomer } = useCreateOrderAddressesStore();

        const countryCodeShipper = shipperAddress.address?.countryCode;
        const countryCodeConsignee = consigneeAddress.address?.countryCode;

        const createOrderFormStore = useCreateOrderFormStore();
        const { selectedCustomer, orderType } = createOrderFormStore;

        return {
          countryCodeShipper,
          countryCodeConsignee,
          countryCodeSelectedCustomer: selectedCustomer.address?.countryCode,
          orderType,
          isCustomer,
        };
      },
    },
    actions: {
      getStoredReferencesArray(referenceType: OrderReferenceType): MultipleReferenceNumber[] {
        switch (referenceType) {
          case OrderReferenceType.INVOICE_NUMBER: {
            return this.invoiceNumbers;
          }
          case OrderReferenceType.PURCHASE_ORDER_NUMBER: {
            return this.purchaseOrderNumbers;
          }
          case OrderReferenceType.DELIVERY_NOTE_NUMBER: {
            return this.deliveryNoteNumbers;
          }
          case OrderReferenceType.OTHERS: {
            return this.otherNumbers;
          }
          case OrderReferenceType.MARKS_AND_NUMBERS: {
            return this.markAndNumbers;
          }
          case OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER: {
            return this.consigneeReferenceNumbers;
          }
          case OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER: {
            return this.supplierShipmentNumbers;
          }
          case OrderReferenceType.PROVIDER_SHIPMENT_NUMBER: {
            return this.providerShipmentNumbers;
          }
          case OrderReferenceType.PACKAGING_LIST_NUMBER: {
            return this.packingListNumbers;
          }
          case OrderReferenceType.COMMERCIAL_INVOICE_NUMBER: {
            return this.commercialInvoiceNumbers;
          }
          case OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT: {
            return this.identificationCodeTransport;
          }
          case OrderReferenceType.BOOKING_REFERENCE: {
            return this.bookingReference;
          }
          default:
            return [];
        }
      },
      cleanReference(referenceType: OrderReferenceType) {
        const storedReferencesArray = this.getStoredReferencesArray(referenceType);
        storedReferencesArray.length = 0;
      },
      addReference(
        referenceType: OrderReferenceType,
        options?: {
          withLoadingData?: boolean;
          required?: boolean;
          setFocus?: boolean;
        },
      ) {
        const optionsWithDefaults = {
          withLoadingData: false,
          required: false,
          setFocus: true,
          ...options,
        };

        const storedReferencesArray = this.getStoredReferencesArray(referenceType);

        const newReferenceObject = getEmptyReferenceObject(optionsWithDefaults.withLoadingData);
        storedReferencesArray.push(newReferenceObject);

        // Add reference type to array (if not already included)
        this.registerActiveReferenceType(referenceType);

        // For delivery note numbers only
        if (referenceType === OrderReferenceType.DELIVERY_NOTE_NUMBER) {
          this.addDeliveryNoteNumberRequiredField(optionsWithDefaults.required);
        }

        if (optionsWithDefaults.setFocus) {
          return this.getLastReferenceId(referenceType);
        }
      },
      addDeliveryNoteNumberRequiredField(required: boolean) {
        const lastId = this.getLastReferenceId(OrderReferenceType.DELIVERY_NOTE_NUMBER);
        if (!lastId) {
          return;
        }
        this.deliveryNoteNumberRequiredFields.push({
          id: lastId,
          required,
        });
      },
      getLastReferenceId(referenceType: OrderReferenceType) {
        const referencesArray = this.getStoredReferencesArray(referenceType);

        return referencesArray.length ? referencesArray[referencesArray.length - 1].id : null;
      },
      registerActiveReferenceType(referenceType: OrderReferenceType): void {
        if (this.furtherReferencesOrder.includes(referenceType)) {
          return;
        }
        this.furtherReferencesOrder.push(referenceType);
      },
      deleteReferenceNumber(
        referenceType: OrderReferenceType,
        itemId: MultipleReferenceNumber['id'],
      ) {
        const storedReferencesArray = this.getStoredReferencesArray(referenceType);
        const idx = storedReferencesArray.findIndex((item) => item.id === itemId);

        if (idx === -1) {
          return;
        }

        storedReferencesArray.splice(idx, 1);

        if (storedReferencesArray.length) {
          return;
        }

        const idx2 = this.furtherReferencesOrder.indexOf(referenceType);

        if (idx2 > -1) {
          this.furtherReferencesOrder.splice(idx2, 1);
        }
      },
    },
  },
);
