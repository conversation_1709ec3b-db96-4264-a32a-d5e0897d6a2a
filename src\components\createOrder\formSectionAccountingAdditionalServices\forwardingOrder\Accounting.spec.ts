import AccountingForwarding from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AccountingForwarding.vue';
import { OrderTypes } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { customers, customersWithAddresses } from '@/mocks/fixtures/customers';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import SelectField from '@/components/form/SelectField.vue';
import { RoadForwardingQuoteInformation } from '@dfe/dfe-book-api';

describe('Accounting component for forwarding orders', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(AccountingForwarding);
    expect(wrapper).toBeDefined();
  });

  it('disables all price related inputs if there is an Order from Quote (road with daily price)', async () => {
    const store = useCreateOrderFormStore();
    const dataStore = useCreateOrderDataStore();
    const addressStore = useCreateOrderAddressesStore();
    const referencesStore = useCreateOrderOrderReferencesFormStore();
    store.orderType = OrderTypes.RoadForwardingOrder;
    store.quoteInformation = {
      orderType: OrderTypes.RoadForwardingOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '********',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as RoadForwardingQuoteInformation;
    store.customerNumber = customers[0].customerNumber ?? '';
    dataStore.customers = customersWithAddresses;
    addressStore.consigneeAddress = {
      address: addresses[0],
    };
    await wrapper.vm.$nextTick();
    await wrapper.setProps({ hasAddresses: true });
    const selectInput = wrapper.findComponent(SelectField);
    expect(selectInput.props('disabled')).toBe(false);

    referencesStore.dailyPriceReference = '060';
    await wrapper.vm.$nextTick();

    expect(selectInput.props('disabled')).toBe(true);
  });
});
