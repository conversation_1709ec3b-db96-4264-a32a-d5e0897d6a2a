<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VSelect
      :id="id"
      v-model="value"
      class="select"
      bg-color="white"
      :items="items"
      :item-title="itemText ? itemText : 'text'"
      :item-value="itemValue ? itemValue : 'value'"
      :placeholder="placeholder"
      :hint="hint"
      :persistent-hint="persistentHint"
      :readonly="readonly"
      single-line
      variant="outlined"
      density="compact"
      hide-details="auto"
      :return-object="returnObject"
      :rules="validationRules"
      :disabled="disabled"
      :menu-props="{ scrollStrategy: 'close', attach: $appRoot }"
    >
      <template v-if="withSmartProposals" #item="{ item, props: slotProps }">
        <VListSubheader v-if="item.raw?.header" color="grey-700" class="text-caption">
          {{ item.raw.header }}
        </VListSubheader>
        <VDivider v-if="item.raw?.divider" />
        <VListItem
          v-if="!item.raw?.divider && !item.raw?.header"
          v-bind="slotProps"
          active-class="bg-blue-50 text-grey-900"
        >
          <template #title>
            <span>{{ item.raw?.[itemText ? (itemText as string) : 'text'] }}</span>
          </template>
        </VListItem>
      </template>
    </VSelect>
  </div>
</template>

<script setup lang="ts">
import type { ValidationRule } from '@/composables/form/useValidationRules';
import { useValidationRules, withMessage } from '@/composables/form/useValidationRules';
import { createUuid } from '@/utils/createUuid';
import type { SelectItemKey } from '@/types/vuetify';
import { computed } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';

interface Props {
  // using another type is difficult because of internal vuetify types
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  items: any[];
  itemValue?: SelectItemKey;
  itemText?: SelectItemKey;
  label?: TranslateResult;
  placeholder?: TranslateResult;
  hint?: string;
  persistentHint?: boolean;
  required?: boolean;
  readonly?: boolean;
  returnObject?: boolean;
  disabled?: boolean;
  rules?: ValidationRule[];
  withSmartProposals?: boolean;
}

const { t } = useI18n();

const props = defineProps<Props>();

const value = defineModel<SelectItemKey>();

const id = `select-field-${createUuid()}`;

const validationRules = computed(() => [
  ...(props.required
    ? [withMessage(useValidationRules.required, t('labels.validation_select_input_required.text'))]
    : []),
  ...(props.rules ?? []),
]);
</script>
