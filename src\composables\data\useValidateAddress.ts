import { useMutation } from '@tanstack/vue-query';
import type { OrderAddress, OrderContactData, ValidationResult } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { useCreateOrderDataStore } from '@/store/createOrder/data';

const isValidationResult = (data: unknown): data is ValidationResult =>
  typeof data === 'object' && data !== null && 'valid' in data;

export const useValidateAddress = () => {
  const createOrderDataStore = useCreateOrderDataStore();

  const { validateAddressResult } = storeToRefs(createOrderDataStore);

  return useMutation({
    mutationFn: async ({
      address,
      contact,
    }: {
      address: OrderAddress;
      contact?: OrderContactData;
    }) => {
      await createOrderDataStore.validateAddress(address, contact);

      if (validateAddressResult.value.valid !== undefined) {
        return validateAddressResult.value;
      }
      throw validateAddressResult.value;
    },
    useErrorBoundary: (error) => !isValidationResult(error),
    onError(err) {
      console.error(err);
    },
  });
};
