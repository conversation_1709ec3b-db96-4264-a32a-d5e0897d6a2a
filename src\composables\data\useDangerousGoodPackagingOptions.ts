import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';

export const useDangerousGoodsPackagingOptions = () => {
  const { api } = useInit();
  const formStore = useCreateOrderFormStore();
  const { orderType } = storeToRefs(formStore);
  return useCustomerQuery('findPackagingOptions', api.book.v1.findPackagingOptions, {
    queryKey: [orderType],
  });
};
