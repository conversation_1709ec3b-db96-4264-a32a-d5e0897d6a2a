import useResetCreateOrderFormData from '@/composables/createOrder/useResetCreateOrderFormData';
import { initPinia } from '../../../test/util/init-pinia';
import { withSetup } from '../../../test/util/with-setup';

describe('useResetCreateOrderFormData composable', () => {
  beforeEach(() => {
    initPinia();
  });

  it('should reset the form', () => {
    const resetOrderFormData = withSetup(useResetCreateOrderFormData)[0];
    const spy = vi.spyOn(resetOrderFormData, 'clearFormData');

    resetOrderFormData.clearFormData();

    expect(spy).toHaveBeenCalledTimes(1);
  });
});
