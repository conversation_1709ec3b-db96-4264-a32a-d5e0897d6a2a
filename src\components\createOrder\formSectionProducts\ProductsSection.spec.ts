import ProductsSection from '@/components/createOrder/formSectionProducts/ProductsSection.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { mockServer } from '@/mocks/server';
import { airProductRoutingConfig } from '@/mocks/fixtures/configs';
import { useConfigStore } from '@/store/config';
import { PortType } from '@dfe/dfe-book-api';
import { airDeliveryProducts } from '@/mocks/fixtures/deliveryProducts';
import { beforeEach, afterEach } from 'vitest';

describe('Products section component', () => {
  let wrapper: VueWrapper,
    addressStore: ReturnType<typeof useCreateOrderAddressesStore>,
    configStore: ReturnType<typeof useConfigStore>;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        airProductRoutingConfig,
        airDeliveryProducts,
      },
    });
  });

  afterAll(() => {
    addressStore.$reset();
    configStore.$reset();
    vi.clearAllMocks();
  });

  beforeEach(() => {
    wrapper = shallowMount(ProductsSection);
    addressStore = useCreateOrderAddressesStore();
    configStore = useConfigStore();
  });

  afterEach(() => {
    wrapper.unmount();
    addressStore.$reset();
    configStore.$reset();
  });

  it('should show products section on valid routing options', async () => {
    await configStore.fetchAirProductRoutingConfig();

    addressStore.fromIATA = {
      code: 'FRA',
      countryCode: 'DE',
      name: 'Frankfurt',
      type: PortType.AIRPORT,
    };
    addressStore.toIATA = {
      code: 'MUC',
      countryCode: 'DE',
      name: 'Munich',
      type: PortType.AIRPORT,
    };

    await wrapper.vm.$nextTick();

    const component = wrapper.findComponent(ProductsSection);

    expect(component.exists()).toBe(true);
    console.log(component);
    expect(component.isVisible).toBeTruthy();
  });

  it('should not show products section on invalid routing option', async () => {
    await configStore.fetchAirProductRoutingConfig();

    addressStore.fromIATA = {
      code: 'LDN',
      countryCode: 'UK',
      name: 'London',
      type: PortType.AIRPORT,
    };
    addressStore.toIATA = {
      code: 'MUC',
      countryCode: 'DE',
      name: 'Munich',
      type: PortType.AIRPORT,
    };

    await wrapper.vm.$nextTick();

    const component = wrapper.findComponent(ProductsSection);
    expect(component.exists()).toBe(true);
    expect(component.isVisible()).toBe(false);
  });

  it('should not show products section on missing routing option', async () => {
    await configStore.fetchAirProductRoutingConfig();

    addressStore.fromIATA = {
      code: 'FRA',
      countryCode: 'DE',
      name: 'Frankfurt',
      type: PortType.AIRPORT,
    };
    addressStore.toIATA = undefined;

    await wrapper.vm.$nextTick();

    const component = wrapper.findComponent(ProductsSection);
    expect(component.exists()).toBe(true);
    expect(component.isVisible()).toBe(false);
  });
});
