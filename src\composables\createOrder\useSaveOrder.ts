import convertCollectionDateToISO from '@/composables/dateTimeUtilities/useCollectionDateToISO';
import useCollectionTimeCustomDefaults from '@/composables/dateTimeUtilities/useCollectionTimeCustomDefaults';
import { useTruncatedDateString } from '@/composables/dateTimeUtilities/useFormatInUTC';
import convertTimeToISO from '@/composables/dateTimeUtilities/useTimeToISO';
import type { FurtherAddressTypeKeys } from '@/enums';
import { DeliveryOptions, FurtherAddressTypesList, OrderTypes } from '@/enums';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { InitialAddressState } from '@/store/createOrder/formAddresses';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import type { MultipleReferenceNumber } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { useTermStore } from '@/store/createOrder/formTerms';
import { useCreateOrderTextsStore } from '@/store/createOrder/formTexts';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import type { OrderLine4Store } from '@/store/createOrder/orderLine';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import { HandOverSelection } from '@/types/hand-over';
import {
  AirExportOrder,
  AirImportOrder,
  AirOrder,
  AirOrderLine,
  AirSeaOrderLineHsCode,
  AirSeaOrderReference,
  BasicOrder,
  ContactData,
  FullContainerLoad,
  OrderAddress,
  OrderReferenceType,
  OrderRequestBody,
  OrderResponseBody,
  OrderText,
  PackingPosition,
  RoadCollectionOrder,
  RoadForwardingOrder,
  RoadOrder,
  RoadOrderLine,
  RoadOrderReference,
  SeaExportOrder,
  SeaImportOrder,
  SeaOrder,
  SeaOrderLine,
  Segment,
} from '@dfe/dfe-book-api';
import { isBoolean, isEqual, omit } from 'lodash';
import { storeToRefs } from 'pinia';
import type { UnwrapRef } from 'vue';
import { isIrelandCountryCode } from '@dfe/dfe-frontend-composables';

const setAddress = (address: OrderAddress): OrderAddress => {
  const addressStore = useCreateOrderAddressesStore();

  const addressToSet = {
    originAddressId: address.originAddressId ?? undefined,
    id: address.id ?? undefined,
    name: address.name,
    name2: address?.name2,
    name3: address?.name3,
    street: address.street,
    street2: address?.street2,
    postcode: address.postcode,
    countryCode: address?.countryCode,
    taxID: address?.taxID,
    // Return undefined explicitly, because BE throws an error if empty string
    gln: address?.gln ? address.gln : undefined,
    contact: address?.contact,
    dropOfLocation: address?.dropOfLocation,
    neutralizeAddress: address?.neutralizeAddress,
  };

  if (isIrelandCountryCode(address.countryCode ?? '')) {
    return {
      ...addressToSet,
      city: addressStore.townCounty?.data.town,
      supplement: addressStore.townCounty?.data.county,
    };
  }

  return {
    ...addressToSet,
    city: address.city,
    supplement: address?.supplement,
  };
};

const setFurtherAddresses = (furtherAddresses: InitialAddressState[]): OrderAddress[] => {
  const { contactDataFurtherAddresses } = storeToRefs(useCreateOrderAddressesStore());

  return furtherAddresses.reduce<OrderAddress[]>((addresses, currentAddress) => {
    const addressType = currentAddress.addressType as FurtherAddressTypeKeys;

    const isNoneAddress =
      (addressType === 'LP' && currentAddress.address.name == null) ||
      currentAddress.address.name == '' ||
      currentAddress.address.name == 'labels.dfe_none.text';

    if (!isEqual(currentAddress.address, orderAddress()) && !isNoneAddress) {
      addresses.push({
        ...setAddress({
          ...currentAddress.address,
          contact: getContactDataOrUndefined(
            contactDataFurtherAddresses?.value[addressType] as ContactData,
          ),
        }),
        addressType: currentAddress.addressType,
      });
    }
    return addresses;
  }, []);
};

function getCustomerNumber(): string | undefined {
  const { customerNumber } = storeToRefs(useCreateOrderFormStore());
  const { selectedCustomer } = storeToRefs(useCreateOrderFormStore());
  const { shipperAddress } = storeToRefs(useCreateOrderAddressesStore());

  if (
    JSON.stringify(selectedCustomer.value.address) === JSON.stringify(shipperAddress.value.address)
  ) {
    return customerNumber.value;
  }
}

function getAddresses() {
  const addressStore = useCreateOrderAddressesStore();
  const {
    shipperAddress,
    consigneeAddress,
    furtherAddresses,
    loadingPoint,
    contactDataShipper,
    contactDataConsignee,
  } = storeToRefs(addressStore);

  return {
    shipperAddress: {
      ...setAddress({
        ...shipperAddress.value.address,
      }),
      addressType: shipperAddress.value.addressType,
      contact: getContactDataOrUndefined(contactDataShipper.value),
      customerNumber: getCustomerNumber(),
    },
    consigneeAddress: {
      ...setAddress({
        ...consigneeAddress.value.address,
      }),
      addressType: consigneeAddress.value.addressType,
      contact: getContactDataOrUndefined(contactDataConsignee.value),
    },
    furtherAddresses: setFurtherAddresses([
      ...furtherAddresses.value,
      {
        address: loadingPoint.value,
        addressType: FurtherAddressTypesList.loadingPoint,
      },
    ]),
  };
}

function getContactDataOrUndefined(
  value: UnwrapRef<ContactData> | UnwrapRef<UnwrapRef<ContactData>>,
) {
  if (!value) return undefined;

  const { name, email, mobile, telephone, fax } = value;

  if (!name && !email && !mobile && !telephone && !fax) {
    return undefined;
  }

  return value;
}

function getNumberWithReferenceCode(number: string, referenceType: OrderReferenceType) {
  return number !== ''
    ? {
        referenceValue: number,
        referenceType,
      }
    : undefined;
}

function getOrderReferencesRoad(): RoadOrderReference[] | undefined {
  const orderReferences = storeToRefs(useCreateOrderOrderReferencesFormStore());
  const {
    purchaseOrderNumbers,
    deliveryNoteNumbers,
    ekaerNumber,
    dailyPriceReference,
    identificationCodeTransport,
    bookingReference,
  } = orderReferences;

  const purchaseOrderNumbersWithRefCode = purchaseOrderNumbers.value.map((number) =>
    getNumberWithReferenceCode(number.value, OrderReferenceType.PURCHASE_ORDER_NUMBER),
  );
  const deliveryNoteNumbersWithRefCode = deliveryNoteNumbers.value.map((number) =>
    getNumberWithReferenceCode(number.value, OrderReferenceType.DELIVERY_NOTE_NUMBER),
  );
  const bookingReferenceWithRefCode = bookingReference.value.map((number) =>
    getNumberWithReferenceCode(number.value, OrderReferenceType.BOOKING_REFERENCE),
  );
  const identificationCodeTransportWithRefCode = identificationCodeTransport.value.map((number) =>
    getNumberWithReferenceCode(
      'UIT:' + number.value,
      OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT,
    ),
  );

  const ekaerNumberWithRefCode = getNumberWithReferenceCode(
    ekaerNumber.value ?? '',
    OrderReferenceType.EKAER_NUMBER,
  );
  const dailyPriceWithRefCode = getNumberWithReferenceCode(
    dailyPriceReference.value ?? '',
    OrderReferenceType.DAILY_PRICE_REFERENCE,
  );

  let returnArray = Array.of(
    ...purchaseOrderNumbersWithRefCode,
    ...deliveryNoteNumbersWithRefCode,
    ...identificationCodeTransportWithRefCode,
    ...bookingReferenceWithRefCode,
  );

  if (returnArray.every((item) => item === undefined || item === null)) {
    returnArray = [];
  }

  if (ekaerNumberWithRefCode?.referenceValue) {
    returnArray.push(ekaerNumberWithRefCode);
  }
  if (dailyPriceWithRefCode?.referenceValue) {
    returnArray.push(dailyPriceWithRefCode);
  }

  return returnArray as RoadOrderReference[];
}

function getOrderReferencesAir(): AirSeaOrderReference[] | undefined {
  const {
    quotationReference,
    shipperReference,
    invoiceNumbers,
    purchaseOrderNumbers,
    deliveryNoteNumbers,
    otherNumbers,
    markAndNumbers,
    consigneeReferenceNumbers,
    supplierShipmentNumbers,
    providerShipmentNumbers,
    packingListNumbers,
    commercialInvoiceNumbers,
  } = storeToRefs(useCreateOrderOrderReferencesFormStore());

  const quotationReferenceValues = {
    id: quotationReference?.value?.id ?? undefined,
    referenceType: OrderReferenceType.QUOTATION_REFERENCE,
    referenceValue: quotationReference?.value?.referenceValue ?? '',
  };
  const shipperReferenceValues = {
    id: shipperReference?.value?.id ?? undefined,
    referenceType: OrderReferenceType.SHIPPERS_REFERENCE,
    referenceValue: shipperReference?.value?.referenceValue ?? '',
    loading: shipperReference.value.loading ?? false,
    unloading: shipperReference.value.unloading ?? false,
  };

  const formatReferenceData = (
    referencesArray: MultipleReferenceNumber[],
    referenceType: OrderReferenceType,
  ) =>
    referencesArray
      .filter((item) => item.value !== '')
      .map(
        (item): AirSeaOrderReference => ({
          id: item.id ? parseInt(item.id) : undefined,
          referenceType,
          referenceValue: item.value,
          loading: item.loading,
          unloading: item.unloading,
        }),
      );

  const invoiceNumbersForRequest = formatReferenceData(
    invoiceNumbers.value,
    OrderReferenceType.INVOICE_NUMBER,
  );

  const purchaseOrderNumbersForRequest = formatReferenceData(
    purchaseOrderNumbers.value,
    OrderReferenceType.PURCHASE_ORDER_NUMBER,
  );

  const deliveryNoteNumbersForRequest = formatReferenceData(
    deliveryNoteNumbers.value,
    OrderReferenceType.DELIVERY_NOTE_NUMBER,
  );

  const otherNumbersForRequest = formatReferenceData(otherNumbers.value, OrderReferenceType.OTHERS);

  const markAndNumbersForRequest = formatReferenceData(
    markAndNumbers.value,
    OrderReferenceType.MARKS_AND_NUMBERS,
  );

  const consigneeReferenceNumbersForRequest = formatReferenceData(
    consigneeReferenceNumbers.value,
    OrderReferenceType.CONSIGNEE_REFERENCE_NUMBER,
  );

  const supplierShipmentNumbersForRequest = formatReferenceData(
    supplierShipmentNumbers.value,
    OrderReferenceType.SUPPLIER_SHIPMENT_NUMBER,
  );

  const providerShipmentNumbersForRequest = formatReferenceData(
    providerShipmentNumbers.value,
    OrderReferenceType.PROVIDER_SHIPMENT_NUMBER,
  );

  const packingListNumbersForRequest = formatReferenceData(
    packingListNumbers.value,
    OrderReferenceType.PACKAGING_LIST_NUMBER,
  );

  const commercialInvoiceNumbersForRequest = formatReferenceData(
    commercialInvoiceNumbers.value,
    OrderReferenceType.COMMERCIAL_INVOICE_NUMBER,
  );

  return [
    quotationReferenceValues,
    shipperReferenceValues,
    ...invoiceNumbersForRequest,
    ...purchaseOrderNumbersForRequest,
    ...deliveryNoteNumbersForRequest,
    ...otherNumbersForRequest,
    ...markAndNumbersForRequest,
    ...consigneeReferenceNumbersForRequest,
    ...supplierShipmentNumbersForRequest,
    ...providerShipmentNumbersForRequest,
    ...packingListNumbersForRequest,
    ...commercialInvoiceNumbersForRequest,
  ];
}

function getEkaerNotRequiredValue(): boolean {
  const { ekaerNumberNotRequired } = storeToRefs(useCreateOrderOrderReferencesFormStore());
  return ekaerNumberNotRequired.value;
}

function filterTexts(getTexts: OrderText[]) {
  return getTexts.filter((text) => text.value !== undefined && text.value !== '');
}

function getOrderLineItems() {
  const { orderLines, getPackingPositionsAllowed, getFullContainerLoadsAllowed } = storeToRefs(
    useCreateOrderOrderLineFormStore(),
  );
  const { isAirAndSeaOrder } = storeToRefs(useCreateOrderFormStore());

  const mappedOrderLines = orderLines.value.map((orderLine: OrderLine4Store) => {
    if (isAirAndSeaOrder.value) {
      orderLine.hsCodes = getHsCodesForOrderLine(orderLine);
    }

    let cleanOrderLine: AirOrderLine & RoadOrderLine = omit(orderLine, 'localId') as AirOrderLine &
      RoadOrderLine;

    if (!orderLine.goodsGroup?.code) {
      cleanOrderLine = omit(cleanOrderLine, 'goodsGroup');
    }

    if (!getPackingPositionsAllowed.value) {
      cleanOrderLine = omit(cleanOrderLine, 'packingPositionId');
    }

    if (!getFullContainerLoadsAllowed.value) {
      cleanOrderLine = omit(cleanOrderLine, 'fullContainerLoadId');
    }

    return cleanOrderLine;
  });

  return mappedOrderLines.sort((a, b) => a.number - b.number);
}

function getPackingPositionItems(cleanOrderLines: OrderResponseBody['orderLineItems']) {
  const { packingPositions } = storeToRefs(useCreateOrderOrderLineFormStore());

  return packingPositions.value.map((packingPosition) => {
    const orderLinesForPackagingPosition = (cleanOrderLines as RoadOrderLine[])?.filter(
      (orderLine) => orderLine.packingPositionId === packingPosition.localId,
    );
    const cleanPackingPosition: PackingPosition = omit(packingPosition, 'localId');

    orderLinesForPackagingPosition.forEach((orderLine) => {
      orderLine.packingPositionId = packingPosition.id;
    });

    cleanPackingPosition.lines = orderLinesForPackagingPosition;
    return cleanPackingPosition;
  });
}

function getFullContainerLoadItems(cleanOrderLines: OrderResponseBody['orderLineItems']) {
  const { fullContainerLoads } = storeToRefs(useCreateOrderOrderLineFormStore());

  return fullContainerLoads.value.map((fullContainerLoad, index) => {
    const orderLinesForFullContainerLoad = (cleanOrderLines as SeaOrderLine[])?.filter(
      (orderLine) => orderLine.fullContainerLoadId === fullContainerLoad.localId,
    );
    const cleanFullContainerLoad: FullContainerLoad = omit(fullContainerLoad, 'localId');

    orderLinesForFullContainerLoad.forEach((orderLine) => {
      orderLine.fullContainerLoadId = fullContainerLoad.id;
    });

    cleanFullContainerLoad.lines = orderLinesForFullContainerLoad;
    cleanFullContainerLoad.sortingPosition = index;
    return cleanFullContainerLoad;
  });
}

function getHsCodesForOrderLine(orderLine: OrderLine4Store): AirSeaOrderLineHsCode[] {
  const hsCodes: AirSeaOrderLineHsCode[] = [];

  orderLine.goodsClassifications?.forEach((goodsClassification) => {
    if (goodsClassification.hsCode?.code || goodsClassification.goods) {
      hsCodes.push({
        id: goodsClassification.id ?? undefined,
        hsCode: goodsClassification.hsCode?.code ?? undefined,
        goods: goodsClassification.goods ?? undefined,
      });
    }
  });

  return hsCodes;
}

function getBasicOrder(): BasicOrder {
  const { orderType, customerNumber, orderNumber, preferredCurrency, orderId } =
    storeToRefs(useCreateOrderFormStore());
  const { collectionTimeSlot, customCollectionTimeSlot, deliveryOption } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );

  const { getTexts } = useCreateOrderTextsStore();
  const { totalValue } = storeToRefs(useCreateOrderOrderLineFormStore());
  const { customsDeclarationRequired, customsDeclarationExecutor } = storeToRefs(
    useCreateOrderFormAccountingAdditionalServices(),
  );

  const addresses = getAddresses();

  function getDocumentIds(): number[] {
    const { documents } = storeToRefs(useCreateOrderDocumentsStore());
    return documents.value?.map((document) => <number>document.documentId);
  }

  function getCollectionTime() {
    if (!collectionTimeSlot.value && !customCollectionTimeSlot.value) {
      const { collectionDate, from, to } = useCollectionTimeCustomDefaults('HH:mm');
      if (collectionDate != null) {
        return {
          collectionDate: convertCollectionDateToISO(collectionDate),
          from: convertTimeToISO(from),
          to: convertTimeToISO(to),
        };
      }
      return {
        from: convertTimeToISO(from),
        to: convertTimeToISO(to),
      };
    }

    if (
      collectionTimeSlot.value?.from === 'Custom' ||
      collectionTimeSlot.value?.to === 'Custom' ||
      (!collectionTimeSlot.value?.from &&
        !collectionTimeSlot.value?.to &&
        !collectionTimeSlot.value?.collectionDate) ||
      (customCollectionTimeSlot.value?.from &&
        customCollectionTimeSlot.value?.to &&
        customCollectionTimeSlot.value?.collectionDate)
    ) {
      return {
        collectionDate: convertCollectionDateToISO(
          customCollectionTimeSlot.value.collectionDate as string,
        ),
        from: convertTimeToISO(customCollectionTimeSlot.value.from as string),
        to: convertTimeToISO(customCollectionTimeSlot.value.to as string),
      };
    } else {
      return {
        collectionDate: collectionTimeSlot.value.collectionDate,
        from: collectionTimeSlot.value.from,
        to: collectionTimeSlot.value.to,
      };
    }
  }

  function getCustomsType() {
    if (customsDeclarationRequired.value) {
      return customsDeclarationExecutor.value;
    }
    return undefined;
  }

  return {
    documentIds: getDocumentIds(),
    orderId: orderId.value ?? undefined,
    customerNumber: customerNumber?.value,
    shipperAddress: addresses.shipperAddress,
    consigneeAddress: addresses.consigneeAddress,
    goodsValue: totalValue.value ?? undefined,
    goodsCurrency: preferredCurrency?.value ?? undefined,
    collectionTime: getCollectionTime(),
    division: undefined,
    deliveryOption: deliveryOption.value ?? undefined,
    texts: filterTexts(getTexts),
    furtherAddresses: addresses.furtherAddresses,
    orderType: orderType.value,
    orderNumber: orderNumber.value ?? undefined,
    customsType: getCustomsType(),
  };
}

function getRoadOrder(): RoadOrder {
  const { selectedFreightTerm, orderGroup, frostProtectionRequired } = storeToRefs(
    useCreateOrderFormAccountingAdditionalServices(),
  );
  const { dateDelivery, tailLiftDelivery, contactDataDelivery, deliveryProduct, deliveryOption } =
    storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());
  const { customsDeclarationRequired } = storeToRefs(
    useCreateOrderFormAccountingAdditionalServices(),
  );

  const { getPackingPositionsAllowed } = storeToRefs(useCreateOrderOrderLineFormStore());

  const cleanOrderLines = getOrderLineItems();
  const unassignedOrderLines = cleanOrderLines.filter((orderLine) => !orderLine.packingPositionId);
  const orderLineItems = getPackingPositionsAllowed.value ? unassignedOrderLines : cleanOrderLines;
  const packingPositions = getPackingPositionsAllowed.value
    ? getPackingPositionItems(cleanOrderLines)
    : undefined;

  return {
    fixDate: getFixDateOrUndefined(<string>dateDelivery?.value),
    tailLiftDelivery: tailLiftDelivery?.value,
    freightTerm: selectedFreightTerm.value ?? undefined,
    orderGroup: orderGroup?.value,
    labelPrinted: false,
    transferListPrinted: false,
    frostProtection: frostProtectionRequired.value,
    ekaerNotRequired: getEkaerNotRequiredValue(),
    references: getOrderReferencesRoad(),
    orderLineItems,
    packingPositions,
    deliveryContact:
      deliveryOption.value == DeliveryOptions.None
        ? { ...contactData() }
        : contactDataDelivery.value,
    product: <string>deliveryProduct.value,
    customsGoods: customsDeclarationRequired.value,
  };
}

function getFixDateOrUndefined(value: string | undefined) {
  if (!value || value === '') {
    return undefined;
  }

  return useTruncatedDateString(value);
}

function getRoadForwardingOrder(): RoadForwardingOrder {
  const { transport, palletLocationsNumber } = storeToRefs(
    useCreateOrderFormAccountingAdditionalServices(),
  );
  const { selfCollection } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());

  const { manualNumberOfLabels, generatedSSccs } = storeToRefs(useCreateOrderOrderLineFormStore());

  const { cashOnDelivery, cashOnDeliveryAmount } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );

  return {
    ...getRoadOrder(),
    ...getBasicOrder(),
    palletLocations: palletLocationsNumber.value,
    selfCollection: selfCollection?.value,
    transportName: transport?.value,
    manualNumberSscc: manualNumberOfLabels.value ?? undefined,
    generatedSsccs: generatedSSccs.value || undefined,
    orderType: 'RoadForwardingOrder',
    cashOnDelivery: cashOnDelivery.value,
    cashOnDeliveryAmount: cashOnDeliveryAmount.value ?? undefined,
  };
}

function getRoadCollectionOrder(): RoadCollectionOrder {
  const addressStore = useCreateOrderAddressesStore();
  const { differentConsigneeAddress, collectionOption } = storeToRefs(addressStore);
  const { contactDataDelivery, collectionInterpreterOption, tailLiftCollection } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );
  const { freightPayer } = storeToRefs(useCreateOrderFormAccountingAdditionalServices());
  return {
    ...getRoadOrder(),
    ...getBasicOrder(),
    deliveryContact: contactDataDelivery?.value,
    differentConsigneeAddress: differentConsigneeAddress.value.address.name
      ? {
          ...setAddress({
            ...differentConsigneeAddress.value.address,
          }),
        }
      : undefined,
    collectionOption: collectionOption.value,
    freightPayer: freightPayer.value,
    interpreter: collectionInterpreterOption?.value?.toUpperCase() as
      | 'FIX'
      | 'BY'
      | 'COLLECTION_NOT_BEFORE'
      | undefined,
    tailLiftCollection: tailLiftCollection?.value,
    orderType: 'RoadCollectionOrder',
  };
}

function getAirOrder(): AirOrder {
  const addressStore = useCreateOrderAddressesStore();
  const { shipperHandOverSelection, consigneeHandOverSelection, fromIATA, toIATA } =
    storeToRefs(addressStore);
  const { selectedAirDeliveryProduct } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );
  return {
    ...getBasicOrder(),
    ...getAslOrder(),
    fromIATA: fromIATA?.value ?? undefined,
    toIATA: toIATA?.value ?? undefined,
    collectFromAirport: consigneeHandOverSelection.value.selection === HandOverSelection.port,
    deliverToAirport: shipperHandOverSelection.value.selection === HandOverSelection.port,
    product: selectedAirDeliveryProduct.value,
  };
}

function getSeaOrder(): SeaOrder {
  const orderLineStore = useCreateOrderOrderLineFormStore();
  const cleanOrderLines = getOrderLineItems();
  const addressStore = useCreateOrderAddressesStore();
  const { shipperHandOverSelection, consigneeHandOverSelection, fromPort, toPort } =
    storeToRefs(addressStore);
  const fullContainerLoads = orderLineStore.getFullContainerLoadsAllowed
    ? getFullContainerLoadItems(cleanOrderLines)
    : undefined;

  return {
    ...getBasicOrder(),
    ...getAslOrder(),
    fromPort: fromPort.value ?? undefined,
    toPort: toPort.value ?? undefined,
    collectFromPort: consigneeHandOverSelection.value.selection === HandOverSelection.port,
    deliverToPort: shipperHandOverSelection.value.selection === HandOverSelection.port,
    fullContainerLoad: orderLineStore.isFullContainerLoad,
    fullContainerLoads,
  };
}

function getAslOrder(): AirOrder | SeaOrder {
  const { selectedIncoTerm } = storeToRefs(useTermStore());
  const { shipperReference } = storeToRefs(useCreateOrderOrderReferencesFormStore());
  const addressStore = useCreateOrderAddressesStore();
  const { shipperHandOverSelection, consigneeHandOverSelection, contactDataCustomer } =
    storeToRefs(addressStore);
  const { tailLiftCollection, requestArrangement } = storeToRefs(
    useCreateOrderFormCollectionAndDeliveryStore(),
  );

  const { stackable, shockSensitive, isFullContainerLoad } = storeToRefs(
    useCreateOrderOrderLineFormStore(),
  );

  return {
    incoTerm: selectedIncoTerm.value ?? undefined,
    references: getOrderReferencesAir(),
    orderLineItems: !isFullContainerLoad?.value ? getOrderLineItems() : [],
    ...(!isFullContainerLoad?.value
      ? {
          stackable: isBoolean(stackable?.value) ? stackable?.value : undefined,
          shockSensitive: isBoolean(shockSensitive?.value) ? shockSensitive?.value : undefined,
        }
      : {}),
    shipperReference: shipperReference.value.referenceValue,
    loading: shipperReference.value.loading,
    unloading: shipperReference.value.unloading,
    customerContactData: getContactDataOrUndefined(contactDataCustomer.value),
    pickupAddress:
      shipperHandOverSelection.value.selection === HandOverSelection.alternateAddress
        ? { ...setAddress(shipperHandOverSelection.value.address) }
        : undefined,
    deliveryAddress:
      consigneeHandOverSelection.value.selection === HandOverSelection.alternateAddress
        ? { ...setAddress(consigneeHandOverSelection.value.address) }
        : undefined,
    requestArrangement: requestArrangement.value,
    tailLiftCollection: tailLiftCollection.value,
  };
}

function useSaveOrder(): OrderRequestBody {
  const { transportType, orderType } = storeToRefs(useCreateOrderFormStore());

  if (transportType.value === Segment.ROAD) {
    if (orderType.value === OrderTypes.RoadForwardingOrder) {
      return getRoadForwardingOrder();
    }
    if (orderType.value === OrderTypes.RoadCollectionOrder) {
      return getRoadCollectionOrder();
    }
  }

  if (transportType.value === Segment.AIR) {
    return getAirOrder() as AirImportOrder | AirExportOrder;
  }

  return getSeaOrder() as SeaImportOrder | SeaExportOrder;
}

export default useSaveOrder;
