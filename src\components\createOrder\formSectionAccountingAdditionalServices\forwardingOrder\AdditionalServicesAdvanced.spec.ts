import AdditionalServicesAdvanced from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AdditionalServicesAdvanced.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount, shallowMount } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { CustomerSettings, RoadForwardingQuoteInformation } from '@dfe/dfe-book-api';
import { Segment } from '@dfe/dfe-book-api';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { OrderTypes } from '@/enums';
import NumberField from '@/components/form/NumberField.vue';
import { customerSettings } from '@/mocks/fixtures/customerSettings';
import { mockServer } from '@/mocks/server';
import { nextTick } from 'vue';
import { orderGroups } from '@/mocks/fixtures/orderGroups';
import SelectField from '@/components/form/SelectField.vue';

describe('Additional Services component', () => {
  let wrapper: VueWrapper;

  const customerSettingsMock: CustomerSettings = customerSettings;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: customerSettingsMock,
        orderGroups,
      },
    });
  });
  beforeEach(() => {
    wrapper = shallowMount(AdditionalServicesAdvanced);
  });

  it('show diabled orderGroups field if there are no order groups', async () => {
    wrapper = mount(AdditionalServicesAdvanced);

    await nextTick();

    expect(wrapper.findComponent({ ref: 'orderGroupSelectField' }).exists()).toBe(true);

    expect(wrapper.findComponent({ ref: 'orderGroupSelectField' }).props('disabled')).toBe(true);
  });

  it('shows orderGroups field if there are order groups', async () => {
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;

    await vi.waitFor(() => {
      expect(wrapper.findComponent({ ref: 'orderGroupSelectField' }).exists()).toBe(true);
    });
  });

  it("doesn't show pallet locations number field when customer settings are set to false", async () => {
    customerSettings.palletLocation = false;

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;

    await nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent(NumberField).exists()).toBe(false);
    });
  });

  it('shows pallet locations number field depending on customer settings', async () => {
    customerSettings.palletLocation = true;

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;

    await nextTick();
    await vi.waitFor(() => {
      expect(wrapper.findComponent(NumberField).exists()).toBe(true);
    });
  });

  it('shows order group selection depending on customer settings', async () => {
    customerSettings.palletLocation = true;

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.ROAD;

    await nextTick();
    await vi.waitFor(() => {
      expect(wrapper.findComponent(SelectField).exists()).toBe(true);
    });
  });

  it('disables all price related inputs if there is an order from Quote (road with daily price)', async () => {
    customerSettings.palletLocation = true;

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;

    await nextTick();

    await vi.waitFor(() => {
      expect(wrapper.findComponent(NumberField).props('disabled')).toBe(false);
    });

    const store = useCreateOrderFormStore();
    const referencesStore = useCreateOrderOrderReferencesFormStore();

    store.orderType = OrderTypes.RoadForwardingOrder;
    store.quoteInformation = {
      orderType: OrderTypes.RoadForwardingOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as RoadForwardingQuoteInformation;
    referencesStore.dailyPriceReference = '060';
    await nextTick();

    expect(wrapper.findComponent(NumberField).props('disabled')).toBe(true);
  });
});
