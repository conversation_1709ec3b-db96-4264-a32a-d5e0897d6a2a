import { onMounted, onUnmounted, Ref } from 'vue';
import { useClient } from '@/composables/useClient';

/**
 * Focuses the initial element when the order form drawer is opened
 * Needed, as the navigation drawer doesn't have a focus trap like the v-dialog component.
 * There is a vuetify issue open for this: https://github.com/vuetifyjs/vuetify/issues/16140
 * @param initialFocusRef the ref of the element to focus
 */
export default function useInitialFocus(initialFocusRef: Ref<{ focus: () => void } | null>) {
  const { client } = useClient();

  const handleInitialFocus = () => {
    /**
     * We need to wait for a bit to ensure the element is rendered
     */
    setTimeout(() => {
      initialFocusRef.value?.focus();
    }, 300);
  };

  onMounted(() => {
    handleInitialFocus();
    client?.events.on('createOrder', handleInitialFocus);
    client?.events.on('editOrder', handleInitialFocus);
  });

  onUnmounted(() => {
    client?.events.off('createOrder', handleInitialFocus);
    client?.events.off('editOrder', handleInitialFocus);
  });
}
