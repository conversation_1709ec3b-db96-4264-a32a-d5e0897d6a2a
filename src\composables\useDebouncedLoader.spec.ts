import { afterEach, beforeAll, describe } from 'vitest';
import { ref } from 'vue';
import { useDebouncedLoader } from '@/composables/useDebouncedLoader';

describe('useDebouncedLoader', () => {
  const isLoading = ref(false);

  const ms = ref(100);

  const showLoader = useDebouncedLoader(isLoading, ms);

  beforeAll(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    isLoading.value = false;
    ms.value = 100;
    vi.runAllTimers();
  });

  it('should return false if isLoading is false', () => {
    expect(showLoader.value).toBe(false);
  });

  it('should return false if isLoading is true and ms is 0', () => {
    isLoading.value = true;
    expect(showLoader.value).toBe(false);
  });

  it('should return true once timer is debounce delay has passed but isLoading is still true', () => {
    isLoading.value = true;
    vi.advanceTimersByTime(100);
    expect(showLoader.value).toBe(true);
  });

  it('should return false instantly if isLoading is false', () => {
    isLoading.value = true;

    expect(showLoader.value).toBe(false);

    vi.advanceTimersByTime(100);
    isLoading.value = false;

    expect(showLoader.value).toBe(false);
  });
});
