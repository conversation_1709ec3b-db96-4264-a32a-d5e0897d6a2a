import type { PortItem } from '@/composables/createOrder/usePortSearch';
import { i18n } from '@/plugins/i18n';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type {
  Address,
  BasicAddresses,
  Countries,
  Country,
  Customer,
  Extensions,
  FavoriteCountries,
  FreightTerms,
  FurtherAddressTypes,
  GeneralProblem,
  IncoTerms,
  Option,
  OrderAddress,
  OrderContactData,
  Ports,
  ValidationResult,
} from '@dfe/dfe-book-api';
import { isGeneralProblem, PortType, Segment } from '@dfe/dfe-book-api';
import { defineStore, storeToRefs } from 'pinia';

export interface CustomerWithAddress extends Customer {
  address?: Address;
}

export interface CreateOrderState {
  customers?: CustomerWithAddress[];
  loadingPoints?: BasicAddresses;
  countries: Countries;
  favoriteCountries: FavoriteCountries;
  furtherAddressTypes: FurtherAddressTypes;
  isThirdCountryConstellation: boolean;
  deliveryOptions: Option[];
  collectionOptions: Option[];
  incoTerms: IncoTerms;
  freightTerms: FreightTerms;
  documentExtensions: Extensions;
  fromPortRouting: PortItem[];
  toPortRouting: PortItem[];
  validateAddressResult: ValidationResult;
  hasInvalidRouting: boolean;
}

export const initialState = {
  customers: [],
  loadingPoints: [],
  countries: [],
  favoriteCountries: {
    shipperCountries: [],
    consigneeCountries: [],
  },
  furtherAddressTypes: [],
  isThirdCountryConstellation: false,
  deliveryOptions: [],
  collectionOptions: [],
  incoTerms: [],
  freightTerms: [],
  documentExtensions: [],
  fromPortRouting: [],
  toPortRouting: [],
  validateAddressResult: {
    valid: false,
    results: [],
  },
  hasInvalidRouting: false,
};

export const useCreateOrderDataStore = defineStore('createOrderData', {
  state: (): CreateOrderState => initialState,
  getters: {
    isFurtherAddressTypeAvailable(state) {
      return (addressType: string) => {
        return state.furtherAddressTypes.map(({ code }) => code).includes(addressType);
      };
    },
    isCustomersAvailable(): boolean {
      return !!this.customers && this.customers?.length > 1;
    },
    getAvailableFurtherAddressTypes(state) {
      return (addressTypes?: string[]) => {
        if (!addressTypes) {
          return state.furtherAddressTypes;
        }

        return addressTypes.reduce((available, addressType) => {
          const value = state.furtherAddressTypes.find(({ code }) => code === addressType);
          return value ? [...available, value] : available;
        }, [] as FurtherAddressTypes);
      };
    },
    isPostcodeOptional(state) {
      const countryCodesWithOptionalPostcode = (state?.countries as Array<Country>)
        .flat()
        .filter((country) => !country.isPostCodeMandatory)
        .map((country) => country.countryCode);

      return (countryCode: string | undefined) => {
        return countryCode ? countryCodesWithOptionalPostcode.includes(countryCode) : true;
      };
    },
  },
  actions: {
    customersSorted(customers: CustomerWithAddress[]) {
      const sortedCustomers = [...customers];
      sortedCustomers.sort((a: CustomerWithAddress, b: CustomerWithAddress) => {
        return (a.label ?? '').localeCompare(b.label ?? '');
      });
      sortedCustomers?.map((address) => {
        let labelParts = '';
        if (address.address?.name) labelParts += address.address?.name;
        if (address.address?.name2) labelParts += ', ' + address.address?.name2;
        if (address.address?.name3) labelParts += ', ' + address.address?.name3;
        address.label = labelParts;
      });

      return sortedCustomers;
    },
    async fetchCustomers(transportType: Segment) {
      try {
        this.customers = [];
        const { data } = await this.api.book.customers.getCustomers({
          customerSegment: transportType,
        });

        let customers: CustomerWithAddress[] = data;

        customers = customers.filter(({ customerNumber }) => !!customerNumber);

        await Promise.all(
          customers.map(async (customer) => {
            if (!customer.customerNumber) {
              return;
            }
            customer.address = await this.getCustomerAddress(
              customer.customerNumber,
              transportType,
            );
          }),
        ).then(() => {
          this.customers = this.customersSorted(customers);
        });
      } catch (error) {
        this.client?.log.error('Failed to fetch customers', 'dfe-book-frontend', error);
        return [];
      }
    },
    async getCustomerAddress(customerNumber: string, customerSegment: Segment) {
      try {
        const { data } = await this.api.book.customers.getCustomerAddress({
          customerNumber,
          customerSegment,
        });
        return data;
      } catch (error) {
        this.client?.log.error('Failed to fetch address of customer', 'dfe-book-frontend', error);
        return undefined;
      }
    },
    async fetchCountries(customerSegment: Segment) {
      try {
        const { data } = await this.api.book.countries.getCountries({
          customerSegment,
        });
        this.countries = data;
      } catch (error) {
        this.client?.log.error('Failed to fetch countries', 'dfe-book-frontend', error);
        return [];
      }
    },
    async fetchFavoriteCountries(customerNumber: string, customerSegment: Segment) {
      try {
        const { data } = await this.api.book.customers.getFavoriteCountries({
          customerNumber,
          customerSegment,
        });
        this.favoriteCountries = data;
      } catch (error) {
        this.client?.log.error(
          'Failed to fetch customers favorite countries',
          'dfe-book-frontend',
          error,
        );
        return {};
      }
    },
    async fetchFurtherAddressTypes(customerNumber: string, transportType: Segment) {
      try {
        const { data } = await this.api.book.customers.getFurtherAddressTypes({
          customerNumber,
          customerSegment: transportType,
        });
        this.furtherAddressTypes = data;
      } catch (error) {
        this.client?.log.error('Failed to fetch further address types', 'dfe-book-frontend', error);
        return [];
      }
    },
    async checkForThirdCountryConstellation(fromCountryCode: string, toCountryCode: string) {
      try {
        const { data } = await this.api.book.thirdCountryConstellation.isThirdCountryConstellation({
          fromCountryCode,
          toCountryCode,
        });
        if (data?.result) {
          this.isThirdCountryConstellation = data.result;
        }
      } catch (error) {
        this.client?.log.error(
          'Failed to check for third country constellation',
          'dfe-book-frontend',
          error,
        );
        return [];
      }
    },
    async fetchCollectionOptions() {
      try {
        const { data } = await this.api.book.collectionOptions.getCollectionOptions();
        this.collectionOptions = data;
      } catch (error) {
        this.client?.log.error('Failed to fetch collection options', 'dfe-book-frontend', error);
        return [];
      }
    },
    async fetchIncoTerms() {
      try {
        const { data } = await this.api.book.incoTerms.getIncoterms();
        this.incoTerms = data ?? [];
      } catch (error) {
        this.client?.log.error('Failed to fetch inco terms', 'dfe-book-frontend', error);
        return [];
      }
    },
    async fetchFreightTerms() {
      try {
        const { data } = await this.api.book.freightTerms.getFreightTerms();
        this.freightTerms = data;
      } catch (error) {
        this.client?.log.error('Failed to fetch freight terms', 'dfe-book-frontend', error);
        return [];
      }
    },
    async fetchExtensions() {
      try {
        const { data } = await this.api.book.extensions.getExtensions();
        this.documentExtensions = data;
      } catch (error) {
        this.client?.log.error('Failed to fetch document extensions', 'dfe-book-frontend', error);
        return [];
      }
    },
    async searchPort(searchFor: string, portType: PortType): Promise<Ports> {
      try {
        const { data } = await this.api.book.ports.getPorts({
          searchFor,
          portType,
        });
        return data;
      } catch (error) {
        this.client?.log.error('Failed to search ports', 'dfe-book-frontend', error);
        return [];
      }
    },
    async fetchPortRouting(fromOrTo: 'from' | 'to', alternateAddress?: OrderAddress) {
      const createOrderFormStore = useCreateOrderFormStore();
      const { transportCountry, transportType, isAirOrderFromQuote, isSeaOrderFromQuote } =
        storeToRefs(createOrderFormStore);
      const { fromPostcode, fromCountry, toPostcode, toCountry } = transportCountry.value;

      const postalCode: string =
        alternateAddress?.postcode ?? (fromOrTo === 'from' ? fromPostcode : toPostcode) ?? '';
      const countryCode: string =
        alternateAddress?.countryCode ?? (fromOrTo === 'from' ? fromCountry : toCountry) ?? '';
      const portType: PortType =
        transportType?.value === Segment.AIR ? PortType.AIRPORT : PortType.SEAPORT;

      if (!countryCode || isAirOrderFromQuote.value || isSeaOrderFromQuote.value) return;
      try {
        const { data } = await this.api.book.v1.getPortRoutings({
          postalCode,
          countryCode,
          portType,
        });
        if (fromOrTo === 'from' && fromCountry) {
          this.fromPortRouting = data;
        } else if (fromOrTo === 'to' && toCountry) {
          this.toPortRouting = data;
        }

        const noPortRouting = this.fromPortRouting.length == 0 || this.toPortRouting.length == 0;
        const hasUserInput = fromPostcode != null && toPostcode != null;

        this.hasInvalidRouting = noPortRouting && hasUserInput;
      } catch (error) {
        this.checkForTechnicalErrorRoutingApi(error);
        this.client?.log.error(
          fromOrTo === 'from'
            ? 'Failed to find port routing from airport'
            : 'Failed to find port routing to airport',
          'dfe-book-frontend',
          error,
        );
        return [];
      }
    },
    async validateAddress(address: OrderAddress, contact?: OrderContactData) {
      const createOrderFormStore = useCreateOrderFormStore();
      const { orderType } = storeToRefs(createOrderFormStore);

      try {
        const { data } = await this.api.book.v1.validateAddress(
          { orderType: orderType.value },
          { ...address, contact },
        );

        this.validateAddressResult = data;
      } catch (error) {
        this.client?.log.error('Failed to validate address', 'dfe-book-frontend', error);
        return [];
      }
    },
    checkForTechnicalErrorRoutingApi(error: unknown) {
      const { isAirOrder, isDraftOrder } = storeToRefs(useCreateOrderFormStore());

      const generalProblem = (error as { error?: GeneralProblem }).error;
      if (isGeneralProblem(generalProblem) && generalProblem?.errorId === 'errRT-01') {
        const { t } = i18n.global;

        const headline = isAirOrder.value ? t('messages.id7129.text') : t('messages.id7130.text');
        const message = isDraftOrder.value ? t('messages.id7131.text') : t('messages.id7132.text');

        this.client?.modal.show('error', { headline, message });
      }
    },
  },
});
