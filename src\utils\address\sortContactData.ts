import { ContactData } from '@dfe/dfe-address-api';

/**
 * Sorts contacts by isMainContact and then by name alphabetically
 * @see DFE-2867
 * @param data - Array of ContactData
 * @returns Sorted new array of ContactData
 */
export function sortContacts(data: ContactData[]): ContactData[] {
  return [...data].sort((a, b) => {
    if (a.isMainContact && !b.isMainContact) return -1;
    if (!a.isMainContact && b.isMainContact) return 1;

    return a.name.localeCompare(b.name);
  });
}
