<template>
  <div v-if="src">
    <VuePdfEmbed ref="pdfRef" :source="`data:application/pdf;base64, ${src}`" />
  </div>
</template>

<script setup lang="ts">
import VuePdfEmbed from 'vue-pdf-embed';
import { ClientKey } from '@/types/client';
import { inject, onBeforeUnmount, ref } from 'vue';
import printJS from 'print-js';

interface Props {
  src: string;
}
const props = defineProps<Props>();
defineEmits(['loading', 'error']);

const client = inject(ClientKey);
const pdfRef = ref<typeof VuePdfEmbed | null>(null);

const handleDoPrintLabels = () => {
  printJS({ printable: props.src, type: 'pdf', base64: true });
};

client?.events.on('doPrintLabels', handleDoPrintLabels);

onBeforeUnmount(() => {
  client?.events.off('doPrintLabels', handleDoPrintLabels);
});
</script>

<style scoped lang="scss">
:deep(.vue-pdf-embed) {
  @media screen {
    .vue-pdf-embed__page {
      margin-bottom: 24px;
    }
  }
}
</style>
