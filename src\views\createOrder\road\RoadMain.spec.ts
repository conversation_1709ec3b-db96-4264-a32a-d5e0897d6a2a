import { OrderTypes } from '@/enums';
import { RoadForwardingQuoteInformation, Segment } from '@dfe/dfe-book-api';
import { customers } from '@/mocks/fixtures/customers';
import { mockServer } from '@/mocks/server';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import FormNavigation from '@/components/form/FormNavigation.vue';
import RoadForwarding from '@/views/createOrder/road/RoadForwarding.vue';
import RoadCollecting from '@/views/createOrder/road/RoadCollecting.vue';
import RoadMain from '@/views/createOrder/road/RoadMain.vue';
import QuoteToBookBanner from '@/components/base/banner/QuoteToBookBanner.vue';

describe('RoadMain view', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        customers,
      },
    });
  });

  beforeEach(() => {
    wrapper = shallowMount(RoadMain);
  });

  it('shows form navigation', () => {
    expect(wrapper.findComponent(FormNavigation).exists()).toBe(true);
  });

  it('shows (only) RoadForwarding view for forwarding orders', async () => {
    const store = useCreateOrderFormStore();
    store.transportType = Segment.ROAD;
    store.orderType = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadForwarding).exists()).toBe(true);
    expect(wrapper.findComponent(RoadCollecting).exists()).toBe(false);
  });

  it('shows (only) RoadCollecting view for forwarding orders', async () => {
    const store = useCreateOrderFormStore();
    store.transportType = Segment.ROAD;
    store.orderType = OrderTypes.RoadCollectionOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(RoadForwarding).exists()).toBe(false);
    expect(wrapper.findComponent(RoadCollecting).exists()).toBe(true);
  });

  it('shows banner if order was created by quote', async () => {
    expect(wrapper.findComponent(QuoteToBookBanner).exists()).toBe(false);

    const formStore = useCreateOrderFormStore();
    formStore.quoteInformation = {
      orderType: OrderTypes.RoadForwardingOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as RoadForwardingQuoteInformation;
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(QuoteToBookBanner).exists()).toBe(true);
  });
});
