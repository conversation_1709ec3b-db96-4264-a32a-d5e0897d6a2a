@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
[dfe-book-frontend] {
:deep() {
  .v-switch {
    &__thumb {
      background-color: vars.$color-base-white;

      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    &__track {
      opacity: 1;
      width: 36px;
      height: 20px;
    }
    
    &.v-input--disabled {
      pointer-events: auto;
      cursor: not-allowed;
    }

    .v-selection-control {
      &__wrapper {
        &:hover .v-switch__thumb {
          color: vars.$color-base-blue-700 !important;
        }

        &:hover .v-switch__track {
          background-color: vars.$color-base-blue-700;
        }
      }

      &--density-default {
        --v-selection-control-size: 24px;
      }

      &--focused {
        .v-switch__track {
          border: 2px solid vars.$color-base-blue-700;
        }
      }

      &--disabled {
        .v-switch__thumb {
          color: vars.$color-base-grey-400 !important;
        }

        .v-switch__track {
          background-color: vars.$color-base-grey-400 !important;
        }
      }

      &--dirty {
        .v-switch__thumb {
          left: -2px;
          width: 16px;
          height: 16px;

          &:after {
            mask: url(@dfe/dfe-frontend-styles/assets/icons/check-16px.svg?inline) no-repeat 50% 50%;
            mask-size: cover;
            background-color: vars.$color-base-blue-500;
          }
        }

        &:not(.v-selection-control--disabled) {
          .v-switch__thumb {
            color: vars.$color-base-blue-500;
          }

          .v-switch__track {
            background-color: vars.$color-base-blue-500;
          }
        }

        .v-selection-control__wrapper:hover {
          .v-switch__thumb {
            color: vars.$color-base-blue-700;
          }

          .v-switch__track {
            background-color: vars.$color-base-blue-700;
          }
        }
      }

      &:not(.v-selection-control--dirty) {
        .v-switch__thumb {
          color: vars.$color-base-grey-600;
          left: 2px;

          &:after {
            mask: url(@dfe/dfe-frontend-styles/assets/icons/close-16px.svg?inline) no-repeat 50% 50%;
            mask-size: cover;
            background-color: vars.$color-base-grey-700;
          }
        }

        &:not(.v-selection-control--disabled) {
          .v-switch__thumb {
            color: vars.$color-base-grey-600;
          }
        }

        .v-switch__track {
          background-color: vars.$color-base-grey-600;
        }

        .v-selection-control__wrapper:hover {
          .v-switch__thumb {
            color: vars.$color-base-grey-800;
          }

          .v-switch__track {
            background-color: vars.$color-base-blue-600;
          }
        }
      }

      &:not(.v-selection-control--disabled) {
        .v-selection-control__wrapper:hover {
          .v-switch__thumb {
            color: vars.$color-base-grey-800;
          }

          .v-switch__track {
            background-color: vars.$color-base-blue-600;
          }
        }
      }
    }
  }
}
}