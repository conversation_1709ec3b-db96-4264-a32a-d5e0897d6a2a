import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import {
  CustomsType,
  FreightPayerType,
  isRoadCollectionOrder,
  isRoadForwardingOrder,
  isRoadOrder,
  OrderResponseBody,
} from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';

export function useSetAccountingAdditionalServicesData(editOrderData: OrderResponseBody) {
  const {
    selectedFreightTerm,
    transport,
    frostProtectionRequired,
    customsDeclarationExecutor,
    customsDeclarationRequired,
    palletLocationsNumber,
    freightPayer,
  } = storeToRefs(useCreateOrderFormAccountingAdditionalServices());

  if (isRoadForwardingOrder(editOrderData)) {
    selectedFreightTerm.value = editOrderData.freightTerm ?? null;
    transport.value = editOrderData.transportName ?? '';
    palletLocationsNumber.value = editOrderData.palletLocations;
  }

  if (isRoadCollectionOrder(editOrderData)) {
    freightPayer.value = editOrderData.freightPayer ?? FreightPayerType.Principal;
  }

  if (editOrderData.customsType != undefined || editOrderData.customsType != null) {
    customsDeclarationExecutor.value = editOrderData.customsType;
  } else {
    customsDeclarationExecutor.value = CustomsType.CUSTOMER;
  }

  frostProtectionRequired.value = isRoadOrder(editOrderData)
    ? editOrderData.frostProtection ?? false
    : false;

  customsDeclarationRequired.value = !!editOrderData.customsType;
}
