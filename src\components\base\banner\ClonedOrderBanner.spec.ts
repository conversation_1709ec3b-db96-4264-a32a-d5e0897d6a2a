import { mount } from '@vue/test-utils';
import ClonedOrderBanner from '@/components/base/banner/ClonedOrderBanner.vue';
import type { TestUtils } from '../../../../test/test-utils';
import { Server } from 'miragejs';
import { mockServer } from '@/mocks/server';
import { airExportOrder } from '@/mocks/fixtures/order';
import { afterAll } from 'vitest';
import { nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderStatus } from '@dfe/dfe-book-api';

describe('ClonedOrderBanner', () => {
  let wrapper: TestUtils.VueWrapper<typeof ClonedOrderBanner>;
  let server: Server;

  beforeAll(() => {
    server = mockServer({
      environment: 'test',
      fixtures: {
        airExportOrder,
      },
    });
  });

  beforeEach(() => {
    wrapper = mount(ClonedOrderBanner);
  });

  afterAll(() => {
    server.shutdown();
  });

  it('should be invisible when the condition is not met - no cloned order', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { orderData } = storeToRefs(createOrderFormStore);
    airExportOrder.clonedOrder = false;

    orderData.value = {
      ...airExportOrder,
    };

    await nextTick();

    expect(wrapper.findComponent(ClonedOrderBanner).isVisible()).toBeFalsy();
  });

  it('should be invisible when the condition is not met- no order in status DRAFT', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { orderData } = storeToRefs(createOrderFormStore);
    airExportOrder.clonedOrder = true;

    if (airExportOrder.orderStatus) {
      airExportOrder.orderStatus.status = OrderStatus.COMPLETE;
    }

    orderData.value = {
      ...airExportOrder,
    };

    await nextTick();

    expect(wrapper.findComponent(ClonedOrderBanner).isVisible()).toBeFalsy();
  });

  it('should be visible when the condition is met', async () => {
    const createOrderFormStore = useCreateOrderFormStore();
    const { orderData } = storeToRefs(createOrderFormStore);
    airExportOrder.clonedOrder = true;

    if (airExportOrder.orderStatus) {
      airExportOrder.orderStatus.status = OrderStatus.DRAFT;
    }

    orderData.value = {
      ...airExportOrder,
    };

    await nextTick();

    expect(wrapper.findComponent(ClonedOrderBanner).isVisible()).toBeTruthy();
    expect(wrapper.text()).toContain('labels.cloned_order_title.text');
  });
});
