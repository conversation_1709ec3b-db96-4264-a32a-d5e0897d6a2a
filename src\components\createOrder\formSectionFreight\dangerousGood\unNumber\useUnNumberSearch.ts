import { useQuery } from '@tanstack/vue-query';
import { computed, Ref, ref, watch } from 'vue';
import { useInit } from '@/composables/useInit';
import { debounce } from 'lodash';
import { Timeouts } from '@/enums';
import { DangerousGoodDataItem, Segment } from '@dfe/dfe-book-api';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useDebouncedLoader } from '@/composables/useDebouncedLoader';
import { useI18n } from 'vue-i18n';

const getQueryKey = (transportType: Segment, searchValue: string) => {
  return ['dangerousGoodsSearch', transportType, searchValue];
};

export const useUnNumberSearch = (searchText: Ref<string>) => {
  const UN_NUMBER_SIZE = 4;
  const DEFAULT_LOADER_DEBOUNCE_MS = 500;
  const QUERY_STALE_TIME = 300000; // 5 minutes
  let cancelToken: symbol;

  const { api } = useInit();
  const { t } = useI18n();
  const formStore = useCreateOrderFormStore();
  const { transportType } = storeToRefs(formStore);
  const queryKey = ref(getQueryKey(transportType.value, searchText.value));

  const isValidUnNumber = computed(() => searchText.value.length === UN_NUMBER_SIZE);
  const isWaitingForDebounce = ref(false);

  watch([searchText, transportType], () => {
    isWaitingForDebounce.value = true;
  });

  watch(
    [searchText, transportType],
    debounce(([searchNew, transportNew]) => {
      isWaitingForDebounce.value = false;
      queryKey.value = getQueryKey(transportNew, searchNew);
    }, Timeouts.SearchInput),
  );

  const { data, isFetching } = useQuery({
    queryKey: queryKey,
    staleTime: QUERY_STALE_TIME,
    retry: false,
    queryFn: async (): Promise<DangerousGoodDataItem[]> => {
      if (cancelToken) {
        api.book.abortRequest(cancelToken);
      }
      cancelToken = Symbol('dangerousGoodsSearch');

      if (!isValidUnNumber.value) {
        return [];
      }

      const { data } = await api.book.v1.findUnNumbers(
        { searchFor: searchText.value, customerSegment: transportType.value },
        { cancelToken },
      );
      return data;
    },
  });

  const searchData = computed(() => data.value ?? []);
  const isLoading = useDebouncedLoader(isFetching, DEFAULT_LOADER_DEBOUNCE_MS);
  const tablePlaceholder = computed(() => {
    if (!isValidUnNumber.value) {
      return {
        heading: t('labels.dangerous_good_enter_correct.text'),
        text: t('labels.dangerous_good_enter_correct_hint.text'),
      };
    }

    if (!isFetching.value && !isWaitingForDebounce.value) {
      return {
        heading: t('labels.dangerous_good_no_results.text'),
        text: t('labels.dangerous_good_no_results_hint.text'),
      };
    }

    return {
      heading: '',
      text: '',
    };
  });

  return {
    data: searchData,
    isValidUnNumber,
    isLoading,
    tablePlaceholder,
  };
};
