<template>
  <div>
    <SelectField
      v-if="!hasAddresses || orderGroupRequired"
      ref="orderGroupSelectField"
      v-model="orderGroup"
      :items="orderGroups ?? []"
      item-text="description"
      item-value="code"
      :label="t('labels.order_group.text')"
      :placeholder="t('labels.select_option.text')"
      class="mb-2"
      :required="orderGroupRequired"
      :disabled="!hasAddresses"
    />
    <NumberField
      v-if="showPalletLocationsNumber"
      v-model="palletLocationsNumber"
      :label="t('labels.pallet_locations_number.text')"
      :hint="`(${t('labels.accounting_hint.text')})`"
      class="size-md"
      :max="99.99"
      :min="0.01"
      :allowed-decimals="2"
      :disabled="isRoadOrderFromQuoteWithDailyPrice"
    />
    <!-- NOTE the final decision on inserting this component is pending: code fragment was left as future structuring reference (DFE-1179) -->
    <!-- <daily-price-select class="mt-2" /> -->
  </div>
</template>

<script setup lang="ts">
import NumberField from '@/components/form/NumberField.vue';
import SelectField from '@/components/form/SelectField.vue';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useOrderGroups } from '@/composables/data/useOrderGroups';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
interface Props {
  hasAddresses?: boolean;
}
defineProps<Props>();
const { t } = useI18n();

const accountingAndAdditionalServicesStore = useCreateOrderFormAccountingAdditionalServices();
const { palletLocationsNumber, orderGroup } = storeToRefs(accountingAndAdditionalServicesStore);

const { data: orderGroups } = useOrderGroups();

const { data: customerSettings } = useCustomerSettings();

const createOrderFormStore = useCreateOrderFormStore();
const { isRoadOrderFromQuoteWithDailyPrice } = storeToRefs(createOrderFormStore);

const showPalletLocationsNumber = computed(() => customerSettings.value?.palletLocation);
const orderGroupRequired = computed(() => {
  if (!orderGroups.value) {
    return false;
  }

  return orderGroups.value.length > 1;
});

watch(orderGroups, (currentOrderGroups) => {
  if (currentOrderGroups?.length === 0) {
    orderGroup.value = undefined;
  }
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
.size-md {
  width: vars.$form-input-width-md;
}

:deep(.v-select) {
  width: vars.$form-cols-width-xl;
}
</style>
