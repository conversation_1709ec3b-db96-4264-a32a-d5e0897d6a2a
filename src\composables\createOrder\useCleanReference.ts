import type { OrderReferenceType } from '@dfe/dfe-book-api';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import { nextTick } from 'vue';

export default async function useCleanReference(referenceType: OrderReferenceType) {
  const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();

  createOrderOrderReferencesFormStore.cleanReference(referenceType);

  await nextTick();
}
