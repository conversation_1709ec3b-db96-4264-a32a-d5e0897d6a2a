import type { Ref } from 'vue';
import { computed } from 'vue';

export function useRequireOneOf<T>(model: Ref<T>, fieldCollection: Ref<(keyof T)[]>) {
  let lastElementWithContent: keyof T | null;
  const requiredFields = computed(() => {
    const elementsWithContent = fieldCollection.value.filter((field) => !!model.value[field]);
    if (lastElementWithContent && !elementsWithContent.includes(lastElementWithContent)) {
      lastElementWithContent = null;
    }

    if (!elementsWithContent.length) {
      return fieldCollection.value;
    }

    if (!lastElementWithContent) {
      lastElementWithContent = elementsWithContent[0];
    }

    return [lastElementWithContent];
  });

  return {
    requiredFields,
  };
}
