import { embargoCountries } from '@/mocks/fixtures/embargo';
import { mockServer } from '@/mocks/server';
import { Events } from '@/types/events';
import { createClient } from '@dfe/dfe-frontend-client';
import { mapActions, storeToRefs } from 'pinia';
import type { Mock } from 'vitest';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { initPinia } from '@test/util/init-pinia';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { AddressCardType, OrderTypes } from '@/enums';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { HandOverSelection } from '@/types/hand-over';

describe('book - embargo store', () => {
  beforeAll(() => {
    initPinia();
    mockServer({
      environment: 'test',
      fixtures: {
        embargoCountries,
      },
    });
  });

  it('sets initial state', () => {
    const store = useEmbargoStore();

    expect(store.$state).toEqual({
      hasEmbargo: false,
    });
  });

  it('resets hasEmbargo value', () => {
    const store = useEmbargoStore();
    store.hasEmbargo = true;

    expect(store.hasEmbargo).toBe(true);

    store.resetHasEmbargo();
    expect(store.hasEmbargo).toBe(false);
  });

  it('fetches embargo for two country codes', async () => {
    const store = useEmbargoStore();

    await store.fetchHasEmbargo('DE', 'FR');
    expect(store.hasEmbargo).toBe(false);

    await store.fetchHasEmbargo('DE', embargoCountries[0]);
    expect(store.hasEmbargo).toBe(true);
  });

  it('returns true for default shipper address in Air and Sea order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.AirExportOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.shipperHandOverSelection.selection = HandOverSelection.default;

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.SHIPPER);
    expect(result).toBe(true);
  });

  it('returns false for alternate shipper address in Air and Sea order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.AirExportOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.shipperHandOverSelection.selection = HandOverSelection.alternateAddress;

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.SHIPPER);
    expect(result).toBe(false);
  });

  it('returns true for default consignee address in Air and Sea order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.AirExportOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.consigneeHandOverSelection.selection = HandOverSelection.default;

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE);
    expect(result).toBe(true);
  });

  it('returns false for alternate consignee address in Air and Sea order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.AirExportOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.consigneeHandOverSelection.selection = HandOverSelection.alternateAddress;

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE);
    expect(result).toBe(false);
  });

  it('returns true for final consignee address in Road Forwarding order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.RoadForwardingOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.getFurtherAddress = vi.fn().mockReturnValue({ address: { name: '' } });

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE);
    expect(result).toBe(true);
  });

  it('returns false for non-final consignee address in Road Forwarding order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.RoadForwardingOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.getFurtherAddress = vi.fn().mockReturnValue({ address: { name: 'Non-final' } });

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE);
    expect(result).toBe(false);
  });

  it('returns true for final consignee address in Road Collection order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.RoadCollectionOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.differentConsigneeAddress = { address: { name: '' } };

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE);
    expect(result).toBe(true);
  });

  it('returns false for non-final consignee address in Road Collection order', () => {
    const embargoStore = useEmbargoStore();
    const store = useCreateOrderFormStore();
    const { orderType } = storeToRefs(store);

    orderType.value = OrderTypes.RoadCollectionOrder;
    const addressesStore = useCreateOrderAddressesStore();
    addressesStore.differentConsigneeAddress = { address: { name: 'Non-final' } };

    const result = embargoStore.checkIfCurrentAddressIsFinalAddress(AddressCardType.CONSIGNEE);
    expect(result).toBe(false);
  });
});

describe('book - embargo store - error handling', () => {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  const actions = mapActions(useEmbargoStore, []);
  type Action = keyof typeof actions;

  function getAction(key: Action) {
    return mapActions(useEmbargoStore, { [key]: key })[key];
  }

  beforeAll(() => {
    initPinia();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it.each([['fetchHasEmbargo', 'de', 'en']])(
    'throws error on data fetching -> %s',
    async (action, ...args) => {
      const store = useEmbargoStore();
      store.client = createClient<Events>({});

      vi.spyOn(globalThis, 'fetch').mockImplementation(
        vi.fn(() => Promise.reject('API is down')) as Mock,
      );

      const event = vi.fn();
      vi.spyOn(store.client.log, 'error').mockImplementation(event);

      // eslint-disable-next-line
      // @ts-ignore: no spread argument
      await getAction(action as Action).bind(this)(...args);

      expect(event).toHaveBeenCalledTimes(1);
    },
  );
});
