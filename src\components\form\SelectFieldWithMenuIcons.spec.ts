import SelectFieldWithMenuIcons from '@/components/form/SelectFieldWithMenuIcons.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { beforeEach } from 'vitest';

const props = {
  items: [
    { value: '0', text: 'Select 0', icon: 'mdi-account' },
    { value: '1', text: 'Select 1', icon: 'mdi-account' },
    { value: '2', text: 'Select 2', icon: 'mdi-account' },
  ],
};
const mockInput = props.items[0];
const label = 'Label';
const requiredChar = '*';

describe('SelectFieldWithMenuIcons component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(SelectFieldWithMenuIcons, {
      props,
    });
  });

  it('emits an update event on change', async () => {
    const select = wrapper.findComponent({ name: 'v-select' });

    select.vm.$emit('update:modelValue', mockInput);

    const event = wrapper.emitted('update:modelValue');

    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([props.items[0]]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });
});
