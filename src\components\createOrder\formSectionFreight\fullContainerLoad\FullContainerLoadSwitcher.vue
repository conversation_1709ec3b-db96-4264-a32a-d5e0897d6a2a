<template>
  <div>
    <VRadioGroup v-model="localFullContainerLoad" inline @update:model-value="onRadioUpdate">
      <div
        class="custom-radio-group border-radio-group-custom d-flex justify-space-between align-center"
      >
        <TooltipWithSlot
          v-data-test="'full-container-load-lcl-button'"
          :tooltip-props="{
            text: t('labels.less_container_load.text'),
            location: 'top',
            attach: $appRoot,
          }"
        >
          <template #content>
            <div
              class="custom-radio d-flex justify-center align-center"
              :class="{ 'custom-radio-selected': !isFullContainerLoad }"
            >
              <VRadio :value="false">
                <template #label>
                  <span
                    class="custom-label"
                    :class="{ 'custom-label-selected': !isFullContainerLoad }"
                    >LCL</span
                  >
                </template>
              </VRadio>
            </div>
          </template>
        </TooltipWithSlot>
        <TooltipWithSlot
          v-data-test="'full-container-load-fcl-button'"
          :tooltip-props="{
            text: t('labels.full_container_load.text'),
            location: 'top',
            attach: $appRoot,
          }"
        >
          <template #content>
            <div
              class="custom-radio d-flex justify-center align-center"
              :class="{ 'custom-radio-selected': isFullContainerLoad }"
            >
              <VRadio :value="true">
                <template #label>
                  <span
                    class="custom-label"
                    :class="{ 'custom-label-selected': isFullContainerLoad }"
                    >FCL</span
                  >
                </template>
              </VRadio>
            </div>
          </template>
        </TooltipWithSlot>
      </div>
    </VRadioGroup>

    <ConfirmPrompt
      v-model="showConfirmSwitchFclLcl"
      :headline="t('labels.switch_full_container_load.text')"
      :confirm-text="t('labels.switch_label.text')"
      :cancel-text="t('labels.cancel_label.text')"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      @close="handleCancel"
    >
      <h5 class="text-body-2 text-grey-darken-4">
        {{ t('labels.full_container_load_switch_desc.text') }}
      </h5>
    </ConfirmPrompt>
  </div>
</template>

<script setup lang="ts">
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import TooltipWithSlot from '@/components/base/tooltips/TooltipWithSlot.vue';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import {
  isEmptyFullContainerLoad,
  isEmptyOrderLine,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { VRadio, VRadioGroup } from 'vuetify/components';

interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<(e: 'update:modelValue', value: boolean) => void>();

const { t } = useI18n();
const orderLineFormStore = useCreateOrderOrderLineFormStore();
const { isFullContainerLoad, fullContainerLoads, orderLines } = storeToRefs(orderLineFormStore);

const showConfirmSwitchFclLcl = ref(false);
const localFullContainerLoad = ref(props.modelValue);

const createOrderDocuments = useCreateOrderDocumentsStore();
const { documents } = storeToRefs(createOrderDocuments);
watch(
  () => props.modelValue,
  (newVal) => {
    localFullContainerLoad.value = newVal;
  },
);

const onRadioUpdate = () => {
  const shouldShowConfirmation = localFullContainerLoad?.value
    ? checkFullContainerLoad()
    : checkEmptyFullContainerLoad();

  documents.value = [];

  if (shouldShowConfirmation) {
    showConfirmSwitchFclLcl.value = true;
  } else {
    makeSwitch(true);
  }
};
const checkFullContainerLoad = () => {
  const firstOrderLineNotEmpty = !isEmptyOrderLine(orderLines.value[0]);
  const multipleOrderLines = orderLines.value.length > 1;
  const notStackable = orderLineFormStore.stackable === false;
  const isShockSensitive = orderLineFormStore.shockSensitive === true;

  return firstOrderLineNotEmpty || multipleOrderLines || notStackable || isShockSensitive;
};

const checkEmptyFullContainerLoad = () => {
  const hasMultipleFullContainerLoads = fullContainerLoads.value.length > 1;
  const hasMultipleOrderLines = orderLines.value.length > 1;

  if (hasMultipleFullContainerLoads || hasMultipleOrderLines) {
    return true;
  }

  const isFirstFullContainerLoadEmpty = isEmptyFullContainerLoad(fullContainerLoads.value[0]);
  const isFirstOrderLineEmpty = isEmptyOrderLine(orderLines.value[0]);

  return !(isFirstFullContainerLoadEmpty && isFirstOrderLineEmpty);
};

const makeSwitch = (isNotFromCancel: boolean) => {
  if (localFullContainerLoad?.value) {
    orderLineFormStore.removeAllOrderLines();
    orderLineFormStore.removeAllFullContainerLoads();
    orderLineFormStore.addFullContainerLoad();
  } else if (isNotFromCancel) {
    orderLineFormStore.removeAllFullContainerLoads();
    orderLineFormStore.addOrderLine();
  }

  orderLineFormStore.stackable = true;
  orderLineFormStore.shockSensitive = false;

  showConfirmSwitchFclLcl.value = false;

  if (isFullContainerLoad) {
    isFullContainerLoad.value = localFullContainerLoad.value;
  }
};
const handleConfirm = () => {
  makeSwitch(true);
  emit('update:modelValue', localFullContainerLoad.value);
};

const handleCancel = () => {
  localFullContainerLoad.value = !localFullContainerLoad.value;
  showConfirmSwitchFclLcl.value = false;
};
</script>

<style lang="scss" scoped>
.custom-radio {
  width: 80px;
  height: 36px;
}

.custom-radio-group {
  border-color: var(--color-base-blue-500) !important;
  border-radius: 4px !important;
}

.custom-radio-selected {
  background-color: var(--color-base-blue-500);
}

.custom-label {
  color: var(--color-base-blue-500) !important;
}

.custom-label-selected {
  color: var(--color-base-white) !important;
}

.border-radio-group-custom {
  border-width: 1.5px !important;
  border-style: solid !important;
}
</style>

<style lang="scss">
.custom-radio-selected svg.icon {
  color: var(--color-base-white) !important;
}
</style>
