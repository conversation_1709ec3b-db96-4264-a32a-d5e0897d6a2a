import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import TimePicker from '@/components/form/TimePicker.vue';
import ComboboxField from '@/components/form/ComboboxField.vue';
import { beforeEach } from 'vitest';

const props = {
  value: '10:00',
  items: [],
};

const label = 'Label';
const requiredChar = '*';

describe('TimePicker component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(TimePicker, {
      props,
    });
  });

  it('emits update event with formatted value on input', async () => {
    const comboboxField = wrapper.findComponent(ComboboxField);

    comboboxField.vm.$emit('update:modelValue', '09:30 AM');

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual(['09:30 AM']);

    comboboxField.vm.$emit('update:modelValue', '09:30');

    expect(emitted['update:modelValue']).toHaveLength(2);
    expect(emitted['update:modelValue'][1]).toEqual(['09:30']);
  });

  it('emits input event with malformed value on input', () => {
    const text = '10 Uhr';
    const comboboxField = wrapper.findComponent(ComboboxField);
    comboboxField.vm.$emit('update:modelValue', text);

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([text]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it('emits input event with default value if it has no value', async () => {
    const defaultValue = '18:30';
    const localWrapper = mount(TimePicker, {
      props: {
        defaultValue,
        items: [],
      },
    });

    const emitted = localWrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([defaultValue]);
  });
});
