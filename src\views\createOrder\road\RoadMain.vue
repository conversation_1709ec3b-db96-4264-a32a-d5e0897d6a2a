<template>
  <div class="mb-6 d-flex">
    <FormNavigation :items="roadNavigationItems" class="d-none d-md-block" />
    <div class="flex-grow-1">
      <QuoteToBookBanner
        v-if="isOrderFromQuote"
        :title="t('labels.order_from_quote_title.text')"
        :order-is-price-dependent="isRoadOrderFromQuoteWithDailyPrice"
        :quote-id="quoteInformation?.quoteRequestId"
      />
      <ClonedOrderBanner />
      <GenericErrorBanner class="mb-4" />
      <CreateOrderRoadForwarding
        v-if="isRoadForwardingOrder"
        @forwarding-order-navigation-items="setNavigationItems($event)"
      />
      <CreateOrderRoadCollecting
        v-if="isRoadCollectionOrder"
        @collecting-order-navigation-items="setNavigationItems($event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import ClonedOrderBanner from '@/components/base/banner/ClonedOrderBanner.vue';
import QuoteToBookBanner from '@/components/base/banner/QuoteToBookBanner.vue';
import GenericErrorBanner from '@/components/createOrder/sharedComponents/GenericErrorBanner.vue';
import FormNavigation from '@/components/form/FormNavigation.vue';
import { OrderTypes } from '@/enums';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import CreateOrderRoadCollecting from './RoadCollecting.vue';
import CreateOrderRoadForwarding from './RoadForwarding.vue';

const { t } = useI18n();

const createOrderFormStore = useCreateOrderFormStore();
const createOrderDataStore = useCreateOrderDataStore();
const {
  orderType,
  isRoadForwardingOrder,
  isRoadCollectionOrder,
  isOrderFromQuote,
  isRoadOrderFromQuoteWithDailyPrice,
  quoteInformation,
} = storeToRefs(createOrderFormStore);

const roadNavigationItems = ref([]);

const setNavigationItems = (items: []) => {
  roadNavigationItems.value = items;
};

onMounted(() => {
  orderType.value = OrderTypes.RoadForwardingOrder;
});

onMounted(async () => {
  await createOrderDataStore.fetchCountries(Segment.ROAD);
});
</script>

<style scoped lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables';

.btn-toggle {
  &.v-btn-toggle:not(.v-btn-toggle--group) .v-btn {
    &.v-btn--outlined.primary--text {
      border-color: currentColor !important;
    }
  }
}
</style>
