{"name": "dfe-book-frontend", "version": "2.19.0", "private": true, "engines": {"node": "^22.12.0"}, "files": ["dist/*"], "type": "module", "scripts": {"serve": "vite", "serve:dist": "npm run build && npx http-server --cors --port 3001 dist", "build:watch": "vite build -w", "build": "npm run vue-tsc && vite build", "build-dev": "npm run vue-tsc && vite build --mode development", "preview": "concurrently \"vite build --watch\" \"vite preview\"", "pretest:unit": "node ./test/prepare-tests.cjs", "test:watch": "npm run test:unit -- --watch", "test:unit": "vitest", "test-snapshot-update": "vitest -u", "test": "npm run test:unit -- --coverage", "test:report": "npm run test:unit -- --coverage", "vue-tsc": "vue-tsc --noEmit", "format-prettier": "prettier --write .", "lint": "eslint --ext .ts,.vue src", "lint:report": "eslint . -f json > reports/eslint-report.json || true", "prepare": "is-ci || husky"}, "dependencies": {"@dfe/dfe-address-api": "^2.6.0", "@dfe/dfe-book-api": "8.3.0", "@dfe/dfe-configserver-api": "^1.0.4", "@dfe/dfe-configserver-api-module": "^1.0.4", "@dfe/dfe-dynamiclabel-api": "^1.1.0", "@dfe/dfe-frontend-client": "^4.0.0", "@dfe/dfe-frontend-composables": "^4.1.1", "@dfe/dfe-frontend-shared-components": "^2.6.0", "@dfe/dfe-frontend-styles": "^2.1.0", "@dfe/dfe-frontend-user-profile": "^5.0.1", "@tanstack/vue-query": "4.39.2", "@vitest/ui": "^3.2.3", "@vueuse/core": "13.3.0", "autoprefixer": "10.4.21", "core-js": "3.43.0", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "lodash": "4.17.21", "pinia": "3.0.3", "prettier": "3.5.3", "print-js": "1.6.0", "sanitize-html": "2.17.0", "uuid": "11.1.0", "vue": "3.5.16", "vue-i18n": "9.14.4", "vue-pdf-embed": "2.1.2", "vue-upload-component": "3.1.17", "vuetify": "3.8.9"}, "devDependencies": {"@dfe/dfe-frontend-i18n": "^1.2.1", "@dfe/eslint-plugin-data-test-prefix": "^1.1.0", "@eslint/js": "9.29.0", "@intlify/unplugin-vue-i18n": "6.0.8", "@originjs/vite-plugin-federation": "1.4.1", "@pinia/testing": "1.0.2", "@types/lodash": "4.17.17", "@types/sanitize-html": "2.16.0", "@typescript-eslint/eslint-plugin": "8.34.1", "@typescript-eslint/parser": "8.34.1", "@vitejs/plugin-vue": "5.2.4", "@vitest/coverage-v8": "3.2.3", "@vue/eslint-config-typescript": "14.5.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "commitlint": "19.8.1", "concurrently": "9.1.2", "eslint": "9.29.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-n": "17.20.0", "eslint-plugin-promise": "7.2.1", "eslint-plugin-vue": "10.2.0", "eslint-plugin-vuetify": "2.5.2", "flush-promises": "1.0.2", "globals": "16.2.0", "husky": "9.1.7", "is-ci": "4.1.0", "jsdom": "26.1.0", "jsdom-testing-mocks": "1.13.1", "lint-staged": "16.1.2", "miragejs": "0.1.48", "postcss-prefix-selector": "2.1.1", "sass": "1.89.2", "sass-loader": "16.0.5", "typescript": "5.8.3", "vite": "6.3.5", "vite-plugin-css-injected-by-js": "3.5.2", "vite-plugin-top-level-await": "1.5.0", "vite-plugin-vue-devtools": "7.7.7", "vite-plugin-vuetify": "2.1.1", "vite-svg-loader": "5.1.0", "vitest": "3.2.3", "vitest-fetch-mock": "0.4.5", "vue-svg-loader": "0.16.0", "vue-tsc": "2.2.10"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.43.0", "@rollup/rollup-win32-x64-msvc": "4.43.0"}}