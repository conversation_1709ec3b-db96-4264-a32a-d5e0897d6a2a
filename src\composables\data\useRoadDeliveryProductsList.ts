import { DeliveryProductsWithFavorites } from '@dfe/dfe-book-api';
import { computed, Ref, watch } from 'vue';
import { TranslateResult } from 'vue-i18n';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { useInit } from '@/composables/useInit';
import { TownCounty, useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useTownCountyList } from '@/composables/data/useTownCountyList';
import { isIrelandCountryCode } from '@dfe/dfe-frontend-composables';

const formatDataWithFavorites = (
  favoritesHeader: TranslateResult | string,
  header: TranslateResult | string,
  data: Ref<DeliveryProductsWithFavorites | undefined>,
) => {
  if (!data.value) {
    return [];
  }

  const { favorites = [], deliveryProducts = [] } = data.value;

  if (favorites?.length) {
    return [
      { header: favoritesHeader },
      ...favorites,
      { divider: true },
      { header },
      ...deliveryProducts,
    ];
  }
  return deliveryProducts;
};

export const useRoadDeliveryProductsList = (
  favoritesHeader: TranslateResult | string,
  header: TranslateResult | string,
) => {
  const { api } = useInit();
  const { shipperAddress, consigneeAddress, townCounty } = storeToRefs(
    useCreateOrderAddressesStore(),
  );
  const { selectableTownCounty } = useTownCountyList();
  const { deliveryProduct } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());
  let cancelToken: symbol;

  const shipperCountryCode = computed(() => shipperAddress.value.address.countryCode ?? '');
  const shipperPostcode = computed(() => shipperAddress.value.address.postcode ?? '');
  const consigneeCountryCode = computed(() => consigneeAddress.value.address.countryCode ?? '');
  const consigneePostcode = computed(() => consigneeAddress.value.address.postcode ?? '');

  const { data } = useCustomerQuery<DeliveryProductsWithFavorites>(
    'roadDeliveryProducts',
    (customerArgs) => {
      if (cancelToken) {
        api.book.abortRequest(cancelToken);
      }

      cancelToken = Symbol('roadDeliveryProducts');

      resolveDachserPlz(
        Array.isArray(selectableTownCounty.value) ? selectableTownCounty.value : [],
        townCounty,
      );

      const shipperPostalCode = getPostalCodeForIE(shipperCountryCode.value, shipperPostcode.value);

      const consigneePostalCode = getPostalCodeForIE(
        consigneeCountryCode.value,
        consigneePostcode.value,
      );

      const request = {
        ...customerArgs,
        shipperCountryCode: shipperCountryCode.value,
        shipperPostalCode: shipperPostalCode,
        consigneeCountryCode: consigneeCountryCode.value,
        consigneePostalCode: consigneePostalCode,
      };

      return api.book.v1.getApplicableRoadDeliveryProducts(request, { cancelToken });
    },
    {
      queryKey: [shipperCountryCode, shipperPostcode, consigneeCountryCode, consigneePostcode],
      keepPreviousData: true,
      enabled: computed(
        () =>
          !!(
            shipperCountryCode.value &&
            shipperPostcode.value &&
            consigneeCountryCode.value &&
            consigneePostcode.value
          ),
      ),
    },
  );

  const selectedDeliveryProduct = computed(
    () =>
      data.value?.deliveryProducts?.find(({ code }) => code === deliveryProduct.value) ??
      data.value?.favorites?.find(({ code }) => code === deliveryProduct.value),
  );

  watch(selectedDeliveryProduct, (selected) => {
    if (!selected) {
      deliveryProduct.value = null;
    }
  });

  return {
    data: computed(() => formatDataWithFavorites(favoritesHeader, header, data)),
    selectedDeliveryProduct,
  };
};

const getPostalCodeForIE = (countryCode: string, fallbackPostcode: string): string => {
  const { townCounty } = storeToRefs(useCreateOrderAddressesStore());
  const dachserPlz = townCounty.value?.data?.dachserPlz;
  return isIrelandCountryCode(countryCode ?? '') && dachserPlz ? dachserPlz : fallbackPostcode;
};
export const resolveDachserPlz = (
  selectableList: TownCounty[],
  townCounty: Ref<TownCounty | undefined>,
): void => {
  const selected = selectableList.find(
    (item) =>
      item.data.town === townCounty.value?.data?.town &&
      item.data.county === townCounty.value?.data?.county,
  );

  if (townCounty.value && selected?.data?.dachserPlz) {
    townCounty.value = {
      ...townCounty.value,
      data: {
        ...townCounty.value.data,
        dachserPlz: selected.data.dachserPlz,
      },
    };
  }
};
