import type { OrderAddress } from '@dfe/dfe-book-api';
import { OrderType } from '@dfe/dfe-book-api';

export const OrderTypes: Record<
  | 'RoadForwardingOrder'
  | 'RoadCollectionOrder'
  | 'AirExportOrder'
  | 'AirImportOrder'
  | 'SeaExportOrder'
  | 'SeaImportOrder',
  OrderType
> = {
  RoadForwardingOrder: OrderType.RoadForwardingOrder,
  RoadCollectionOrder: OrderType.RoadCollectionOrder,
  AirExportOrder: OrderType.AirExportOrder,
  AirImportOrder: OrderType.AirImportOrder,
  SeaExportOrder: OrderType.SeaExportOrder,
  SeaImportOrder: OrderType.SeaImportOrder,
};

export const ContactsType: Record<'Customer' | 'Delivery' | 'Collection', string> = {
  Customer: 'customer',
  Delivery: 'delivery',
  Collection: 'collection',
};

export const CollectionTimeSlots = {
  Custom: 'Custom',
  CustomDefaultFromHours: 8,
  CustomDefaultToHours: 16,
} as const;

export const DocumentTypes = {
  EDN: 'edn',
  GGDGN: 'GGDGN',
  CODDG: 'CODDG',
  GGDGA: 'GGDGA',
  GGETC: 'GGETC',
  GGETA: 'GGETA',
} as const;

export enum CountryCodes {
  RO = 'RO',
  HU = 'HU',
  ES = 'ES',
  PT = 'PT',
}

export enum DangerousGoodType {
  EQ = 'EQ',
  LQ = 'LQ',
  UnNumber = 'UnNumber',
}

export enum DeliveryOptions {
  None = 'NO',
  AutomaticDeliveryBooking = 'AP',
  AutomaticDeliveryNotification = 'AS',
  PhoneDeliveryBooking = 'AT',
  PhoneDeliveryNotification = 'AC',
}

export enum Product {
  TargoOnSite = 'A',
  TargoOnSiteFix = 'U',
  TargoOnSitePlus = 'B',
  TargoOnSitePrenium = 'H',
}

export enum MaxLength {
  Default = 35,
  EkaerNo = 14, // excl. leading "E"
  NumbersArray = 300,
  Texts = 600,
  TextsCommonInstructions = 180,
  DropOfLocation = 120,
  IdentificationCodeTransport = 16,
}
export enum MaxLengthsRoadOrder {
  /** Address */
  Default = 30,
  ZipCode = 9,
  City = 26,
  /** Contact details */
  ContactDataEmail = 150,
  ContactDataPhone = 25,
  /** Incoterm */
  incoTermCode = 3,
  incoTermLocation = 35,
  /** OrderLine */
  Content = 20,
}

export enum MaxLengthsAirAndSeaOrder {
  /** Address */
  Default = 30,
  ZipCode = 9,
  City = 26,
  /** Contact details */
  ContactDataEmail = 150,
  ContactDataPhone = 25,
  /** Incoterm */
  incoTermCode = 3,
  incoTermLocation = 35,
  /** OrderLine */
  GoodsDescription = 1500,
}

export enum MaxLengthCoreSystemAddress {
  Default = 40,
  ZipCode = 12,
}

export enum Timeouts {
  SearchInput = 350,
}

export enum UploadStatus {
  Success = 'success',
  FileMalicious = 'fileMalicious',
  UnspecificError = 'unspecificError',
  ErrorExtensionNotAllowed = 'errorExtensionNotAllowed',
  ErrorInvalidExtension = 'errorInvalidExtension',
  ErrorSize = 'errorSize',
}

export enum TextTypes {
  CollectionInstructions = 'A',
  DeliveryInstructions = 'ZU',
  GoodsDescription = 'WA',
  InvoiceText = 'RE',
  OtherInformation = 'SI',
  SpecialRegulation = 'GS',
}

export enum MaxOrderPeriods {
  Forwarding = 14,
  Collecting = 365,
}

export const Routes: Record<string, string> = {
  QUOTE: '/quote',
  ORDER: '/order-details',
  OVERVIEW: '/shipments',
  TRACK_AND_TRACE: '/trackandtrace',
  BOOK: '/book',
};

/*
TODO Optimize mappings
I didn't find the meanings of those codes anywhere which means this is pretty hard to understand and unnecessary to maintain an enum
when it doesn't improve the namings of the keys themselves.

I asked in Teams about this topic and am waiting for an answer to this.
https://teams.microsoft.com/l/message/19:6aa0b1507a88438db8cdad916481cf8a@thread.tacv2/1697457287086?tenantId=02156bc4-d21d-4066-bb8c-74c422d0e122&groupId=1121020f-5151-4983-8406-f5246423f2ff&parentMessageId=1697457287086&teamName=P-DFE%20(193)&channelName=T01%20Team1%20Dev&createdTime=1697457287086
 */
export const FurtherAddressTypesList = {
  CX: 'CX',
  N1: 'N1',
  N2: 'N2',
  PJ: 'PJ',
  TO: 'TO',
  UP: 'UP',
  customsAgent: 'CB',
  DC: 'DC',
  coverAddressConsignor: 'DA',
  finalDeliveryAddress: 'DP',
  importer: 'IM',
  CC: 'CC',
  deviatingFreightPayer: 'RE',
  OS: 'OS',
  FW: 'FW',
  CT: 'CT',
  loadingPoint: 'LP',
  UK: 'UK',
  RA: 'RA',
  EV: 'EV',
  OY: 'OY',
  OB: 'OB',
  WB: 'WB',
  AE: 'AE',
} as const;

export const FurtherAddressTypesKeyEnum = Object.freeze(Object.values(FurtherAddressTypesList));

export const codeIsFurtherAddressKey = (code: string | undefined): code is FurtherAddressTypeKeys =>
  !!(code && code in FurtherAddressTypesList);

export type FurtherAddressType = typeof FurtherAddressTypesList;
export type FurtherAddressTypeKeys = FurtherAddressType[keyof FurtherAddressType];

export const DisabledFormAddressFields: Record<
  NonNullable<OrderAddress['lockedByQuote']>,
  (keyof OrderAddress)[]
> = {
  full: ['name', 'street', 'postcode', 'city', 'countryCode'],
  partial: ['postcode', 'countryCode'],
};

export enum ConsigneeAddressType {
  PRINCIPALS_ADDRESS,
  DIFFERENT_CONSIGNEE_ADDRESS,
}

export enum AddressCardType {
  SHIPPER,
  CONSIGNEE,
  FINAL_DELIVERY_ADDRESS,
  DIFFERENT_CONSIGNEE_ADDRESS,
}

export enum ErrorCode {
  OrderExpiryErrorCode = 'errSV-04',
  OrderBookingExpiryErrorCode = 'errSE-04',
}

export enum OrderLineType {
  Default = 'default',
  PackingPosition = 'packingPosition',
  FullContainerLoad = 'fullContainerLoad',
}
