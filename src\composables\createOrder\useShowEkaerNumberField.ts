import { CountryCodes, OrderTypes } from '@/enums';
import type { Ref } from 'vue';
import type { OrderType } from '@dfe/dfe-book-api';

export default function (
  dataRef: Ref<{
    countryCodeShipper: string | undefined;
    countryCodeConsignee: string | undefined;
    countryCodeSelectedCustomer: string | undefined;
    orderType: OrderType | null;
    isCustomer: boolean;
  }>,
) {
  const {
    countryCodeShipper,
    countryCodeConsignee,
    countryCodeSelectedCustomer,
    orderType,
    isCustomer,
  } = dataRef.value;

  return (
    (countryCodeSelectedCustomer === CountryCodes.HU &&
      (orderType === OrderTypes.RoadForwardingOrder || isCustomer)) ||
    countryCodeShipper === CountryCodes.HU ||
    countryCodeConsignee === CountryCodes.HU
  );
}
