import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import LinkElement from '@/components/base/LinkElement.vue';

const props = {
  text: 'Link',
};

describe('LinkElement component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(LinkElement, { props });
  });

  it('has link text', async () => {
    expect(wrapper.text()).toBe(props.text);
  });

  it('has icon', async () => {
    await wrapper.setProps({ icon: true, iconPosition: 'start' });

    const icon = wrapper.find('.link-icon');
    expect(icon.exists()).toBe(true);
    expect(icon.classes().includes('link-icon--start')).toBe(true);

    await wrapper.setProps({ iconPosition: 'end' });
    expect(icon.exists()).toBe(true);
    expect(icon.classes().includes('link-icon--end')).toBe(true);
  });

  it('has href', async () => {
    const externalLink = 'www.dachser.de';
    await wrapper.setProps({ href: externalLink });
    expect(wrapper.attributes().href).toBe(externalLink);
  });

  it('can be disabled', async () => {
    await wrapper.setProps({ href: '#', disabled: true });

    expect(wrapper.element.tagName).toBe('SPAN');
  });
});
