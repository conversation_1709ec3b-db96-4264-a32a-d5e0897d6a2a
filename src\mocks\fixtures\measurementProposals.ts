import type { MeasurementProposals } from '@dfe/dfe-book-api';

export const measurementProposals4ConversionTest: MeasurementProposals[] = [
  {
    standard: {
      length: 120,
      width: 80,
    },
    frequentlyUsed: [
      {
        length: 110,
        width: 80,
      },
      {
        length: 120,
        width: 80,
      },
      {
        length: 120,
        width: 100,
      },
      {
        length: 80,
        width: 60,
      },
    ],
  },
  {
    standard: {
      length: 120,
      width: 80,
    },
    frequentlyUsed: [],
  },
  {
    // standard: null;  // TODO: uncomment after API fix
    frequentlyUsed: [
      {
        length: 120,
        width: 100,
      },
      {
        length: 80,
        width: 60,
      },
    ],
  },
];

export const measurementProposals: MeasurementProposals = measurementProposals4ConversionTest[0];
