import { storeToRefs } from 'pinia';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { initPinia } from '../../../test/util/init-pinia';
import convertCollectionDateToISO from '@/composables/dateTimeUtilities/useCollectionDateToISO';

describe('useCollectionDateToISO composable', () => {
  beforeAll(() => {
    initPinia();
  });

  it('should convert today date to ISO when collection date not set', () => {
    const today = new Date();

    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');

    expect(convertCollectionDateToISO('2023-02-14T12:00:00.000Z')).toEqual(
      `${year}-${month}-${day}`,
    );
  });

  it('should convert collection date to ISO when set', () => {
    const { customCollectionTimeSlot } = storeToRefs(
      useCreateOrderFormCollectionAndDeliveryStore(),
    );

    customCollectionTimeSlot.value.collectionDate = '2023-02-14T12:00:00.000Z';

    expect(convertCollectionDateToISO(customCollectionTimeSlot.value.collectionDate)).toEqual(
      '2023-02-14',
    );
  });
});
