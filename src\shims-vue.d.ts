import type { DefineComponent, Vue } from 'vue';

declare module '*.vue' {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type, @typescript-eslint/no-explicit-any
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $appRoot: Element | undefined;
  }
}

declare module '*.svg' {
  export default Vue;
}
