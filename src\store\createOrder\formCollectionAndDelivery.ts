import { defineStore, storeToRefs } from 'pinia';
import { contactData } from '@/store/sharedInitialStates';
import { DeliveryOptions, type FurtherAddressTypeKeys } from '@/enums';
import type { CollectionTimeSlot, CollectionTimeSlots, DeliveryProduct } from '@dfe/dfe-book-api';
import type { ContactData } from '@dfe/dfe-address-api';
import useCollectionTimeSlotOptions from '@/composables/dateTimeUtilities/useCollectionTimeSlotOptions';
import { useCollectionTimeSlots } from '@/composables/dateTimeUtilities/useCollectionTimeSlots';
import type { TimeFormat } from '@/types/createOrder';

export interface CreateCollectionAndDeliveryFormState {
  date: Date | string;
  deliveryProduct: string | null;
  dateDelivery: Date | string;
  deliveryOption: string;
  collectionOption: string;
  productOption: string;
  selfCollection: boolean;
  tailLiftCollection: boolean;
  tailLiftDelivery: boolean;
  cashOnDelivery: boolean;
  cashOnDeliveryAmount: number | null;
  contactDataDelivery: ContactData;
  contactDataCollection: ContactData;
  contactDataFurtherAddresses: { [key in FurtherAddressTypeKeys]?: ContactData };
  collectionTimeSlot: CollectionTimeSlot | null;
  customCollectionTimeSlot: CollectionTimeSlot;
  collectionInterpreterOption: '' | 'FIX' | 'BY' | 'COLLECTION_NOT_BEFORE';
  timeSlotOptions: CollectionTimeSlots;
  requestArrangement: boolean;
  selectedAirDeliveryProduct: DeliveryProduct | undefined;
}

export const useCreateOrderFormCollectionAndDeliveryStore = defineStore(
  'createOrderFormCollectionAndDeliveryLine',
  {
    state: (): CreateCollectionAndDeliveryFormState => ({
      date: '',
      dateDelivery: '',
      deliveryProduct: null,
      deliveryOption: DeliveryOptions.None,
      collectionOption: '',
      productOption: '',
      selfCollection: false,
      tailLiftDelivery: false,
      cashOnDelivery: false,
      cashOnDeliveryAmount: null,
      tailLiftCollection: false,
      contactDataDelivery: { ...contactData() },
      contactDataCollection: { ...contactData() },
      contactDataFurtherAddresses: {},
      collectionTimeSlot: null,
      customCollectionTimeSlot: {
        collectionDate: '',
        from: '',
        to: '',
      },
      collectionInterpreterOption: 'FIX',
      timeSlotOptions: [],
      requestArrangement: false,
      selectedAirDeliveryProduct: undefined,
    }),
    getters: {
      getTimeSlotOptions(): (
        timeFormat: TimeFormat,
      ) => ReturnType<typeof useCollectionTimeSlotOptions> {
        return (timeFormat: TimeFormat) => {
          const createOrderFormCollectionAndDeliveryStore =
            useCreateOrderFormCollectionAndDeliveryStore();
          const { date } = storeToRefs(createOrderFormCollectionAndDeliveryStore);
          const { data: collectionTimeSlots } = useCollectionTimeSlots(date);
          return useCollectionTimeSlotOptions(collectionTimeSlots, timeFormat);
        };
      },
    },
  },
);
