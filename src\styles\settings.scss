@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use "base" as base;


@forward 'vuetify/settings' with (
  $green: (
    'base': vars.$color-base-green-500,
    'lighten-1': vars.$color-base-green-400,
    'lighten-2': vars.$color-base-green-300,
    'lighten-3': vars.$color-base-green-200,
    'lighten-4': vars.$color-base-green-100,
    'darken-1': vars.$color-base-green-600,
    'darken-2': vars.$color-base-green-700,
    'darken-3': vars.$color-base-green-800,
    'darken-4': vars.$color-base-green-900,
  ),
  $blue: (
    'base': vars.$color-base-blue-500,
    'lighten-1': vars.$color-base-blue-400,
    'lighten-2': vars.$color-base-blue-300,
    'lighten-3': vars.$color-base-blue-200,
    'lighten-4': vars.$color-base-blue-100,
    'lighten-5': vars.$color-base-blue-50,
    'darken-1': vars.$color-base-blue-600,
    'darken-2': vars.$color-base-blue-700,
    'darken-3': vars.$color-base-blue-800,
    'darken-4': vars.$color-base-blue-900,
  ),
  $red: (
    'base': vars.$color-base-red-500,
    'lighten-1': vars.$color-base-red-400,
    'lighten-2': vars.$color-base-red-300,
    'lighten-3': vars.$color-base-red-200,
    'lighten-4': vars.$color-base-red-100,
    'darken-1': vars.$color-base-red-600,
    'darken-2': vars.$color-base-red-700,
    'darken-3': vars.$color-base-red-800,
    'darken-4': vars.$color-base-red-900,
  ),
  $grey: (
    'base': vars.$color-base-grey-500,
    'lighten-1': vars.$color-base-grey-400,
    'lighten-2': vars.$color-base-grey-300,
    'lighten-3': vars.$color-base-grey-200,
    'lighten-4': vars.$color-base-grey-100,
    'lighten-5': vars.$color-base-grey-50,
    'darken-1': vars.$color-base-grey-600,
    'darken-2': vars.$color-base-grey-700,
    'darken-3': vars.$color-base-grey-800,
    'darken-4': vars.$color-base-grey-900,
  ),

  $button-border-radius: vars.$button-border-radius,
  $button-content-transition: vars.$button-animation-transition,
  $button-transition-property: box-shadow,
  $button-text-transform: vars.$button-text-transform,
  $button-text-letter-spacing: vars.$button-text-letter-spacing,
  $button-height: vars.$button-size-base,
  $button-font-size: vars.$button-text-size-action,
  $button-card-actions-margin: 12px,

  $btn-toggle-selected-opacity: vars.$button-toggle-opacity,

  $grid-breakpoints: (
    'xs': vars.$breakpoint-xs,
    'sm': vars.$breakpoint-sm,
    'md': vars.$breakpoint-md,
    'lg': vars.$breakpoint-lg,
    'xl': vars.$breakpoint-xl,
  ),

  $divider-opacity: 1,
  $divider-border-color: var(--color-base-grey-400),
  $grid-gutter: vars.$spacing-grid-gutter-base,
  $grid-gutters: (
    'xs': vars.$spacing-grid-gutter-xs,
    'sm': vars.$spacing-grid-gutter-sm,
    'md': vars.$spacing-grid-gutter-md,
    'lg': vars.$spacing-grid-gutter-lg,
    'xl': vars.$spacing-grid-gutter-xl,
  ),
  $container-padding-x: vars.$spacing-grid-container-base,
  $list-item-padding: vars.$list-item-spacing-y vars.$list-item-spacing-x,
  $list-item-min-height: vars.$list-item-height-base,
  $navigation-drawer-scrim-opacity: 0.75,
  $snackbar-z-index: 1000,

  $table-column-padding: 16px 16px 16px 16px,

  $toolbar-content-padding-y: 0,
  $toolbar-content-padding-x: vars.$spacing-grid-container-md,

  $tooltip-background-color: vars.$color-base-grey-900,
  $tooltip-text-color: vars.$color-base-white,
  $tooltip-line-height: vars.$line-height-body-2,
  $tooltip-border-radius: 8px,
  $tooltip-padding: 8px 12px,

  $field-control-padding-start: 12px,
  $field-control-padding-end: 12px,
  $field-input-padding-top: 6px,
  $field-input-padding-bottom: 6px,
  $field-font-size: vars.$font-size-body-2,
  $input-line-height:  base.getRelativeLineHeight(vars.$font-size-body-2, vars.$line-height-body-2),
  $body-font-family: vars.$font-family-base,
  $heading-font-family: vars.$font-family-base,
  $font-size-root: vars.$font-size-base,
  $typography: (
    'h1': (
      'size': vars.$font-size-heading-1,
      'weight': vars.$font-weight-heading,
      'line-height': vars.$line-height-heading-1,
      'letter-spacing': vars.$letter-spacing-heading-1,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'h2': (
      'size': vars.$font-size-heading-2,
      'weight': vars.$font-weight-heading,
      'line-height': vars.$line-height-heading-2,
      'letter-spacing': vars.$letter-spacing-heading-2,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'h3': (
      'size': vars.$font-size-heading-3,
      'weight': vars.$font-weight-heading,
      'line-height': vars.$line-height-heading-3,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'h4': (
      'size': vars.$font-size-heading-4,
      'weight': vars.$font-weight-heading,
      'line-height': vars.$line-height-heading-4,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'h5': (
      'size': vars.$font-size-heading-5,
      'weight': vars.$font-weight-heading,
      'line-height': vars.$line-height-heading-5,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'h6': (
      'size': vars.$font-size-heading-6,
      'weight': vars.$font-weight-heading,
      'line-height': vars.$line-height-heading-6,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'body-1': (
      'size': vars.$font-size-body-1,
      'weight': vars.$font-weight-body,
      'line-height': vars.$line-height-body-1,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'body-2': (
      'size': vars.$font-size-body-2,
      'weight': vars.$font-weight-body,
      'line-height': vars.$line-height-body-2,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'body-3': (
      'size': vars.$font-size-body-3,
      'weight': vars.$font-weight-body,
      'line-height': vars.$line-height-body-3,
      'letter-spacing': normal,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'caption': (
      'size': vars.$font-size-caption,
      'weight': vars.$font-weight-body,
      'line-height': vars.$line-height-caption,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'label-1': (
      'size': vars.$font-size-label-1,
      'weight': vars.$font-weight-label,
      'line-height': vars.$line-height-label-1,
      'letter-spacing': normal,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'label-2': (
      'size': vars.$font-size-label-2,
      'weight': vars.$font-weight-label,
      'line-height': vars.$line-height-label-2,
      'letter-spacing': normal,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
    'label-3': (
      'size': vars.$font-size-label-3,
      'weight': vars.$font-weight-label,
      'line-height': vars.$line-height-label-3,
      'letter-spacing': normal,
      'font-family': vars.$font-family-base,
      'text-transform': none,
    ),
  )
);
