import { AirSeaOrderReference, OrderReferenceType, RoadOrderReference } from '@dfe/dfe-book-api';
import {
  formatReferenceData,
  getReferenceNumbers,
} from '@/composables/createOrder/editOrder/useSetReferencesData';

const references: (RoadOrderReference & AirSeaOrderReference)[] = [
  {
    id: 1,
    referenceType: OrderReferenceType.EKAER_NUMBER,
    referenceValue: '123',
  },
  {
    id: 2,
    referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
    referenceValue: '456',
  },
  {
    id: 3,
    referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
    referenceValue: '789',
    loading: true,
    unloading: false,
  },
  {
    id: 31,
    referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
    referenceValue: '789',
    loading: true,
  },
  {
    id: 32,
    referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
    referenceValue: '789',
    unloading: false,
  },
  {
    referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
    referenceValue: '666',
    unloading: false,
  },
  {
    id: 34,
    referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
    referenceValue: '',
    unloading: false,
  },
  {
    id: 4,
    referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
    referenceValue: '888',
  },
  {
    id: 5,
    referenceType: OrderReferenceType.DELIVERY_NOTE_NUMBER,
    referenceValue: '444',
  },
  {
    id: 42,
    referenceType: OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT,
    referenceValue: 'UIT:1234AB7890CD3442',
  },
  {
    id: 43,
    referenceType: OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT,
    referenceValue: 'UIT:1234AB7890CD3456',
  },
  {
    id: 6,
    referenceType: OrderReferenceType.BOOKING_REFERENCE,
    referenceValue: 'Aze123456',
  },
  {
    id: 7,
    referenceType: OrderReferenceType.BOOKING_REFERENCE,
    referenceValue: 'poi456',
  },
];

describe('formatReferenceData', () => {
  it.each([
    [references[1], { id: '2', value: '456' }],
    [references[2], { id: '3', value: '789', loading: true, unloading: false }],
    [references[3], { id: '31', value: '789', loading: true, unloading: false }],
    [references[4], { id: '32', value: '789', loading: false, unloading: false }],
    [references[5], undefined],
    [references[6], undefined],
  ])('formats reference data for store storage -> %s', (reference, result) => {
    const formattedReference = formatReferenceData(reference);
    expect(formattedReference).toEqual(result);
  });
});

describe('getReferenceNumbers', () => {
  it('should return undefined when references is empty or undefined', () => {
    expect(getReferenceNumbers([], OrderReferenceType.EKAER_NUMBER)).toBeUndefined();

    expect(getReferenceNumbers(undefined, OrderReferenceType.EKAER_NUMBER)).toBeUndefined();
  });

  it('should return ekaer_number', () => {
    expect(getReferenceNumbers(references, OrderReferenceType.EKAER_NUMBER)).toEqual('123');
  });

  it('should return references for purchase_order_number', () => {
    expect(getReferenceNumbers(references, OrderReferenceType.PURCHASE_ORDER_NUMBER)).toEqual([
      { id: '2', value: '456' },
      { id: '3', value: '789', loading: true, unloading: false },
      { id: '31', value: '789', loading: true, unloading: false },
      { id: '32', value: '789', loading: false, unloading: false },
    ]);
  });

  it('should return references for delivery_note_number', () => {
    expect(getReferenceNumbers(references, OrderReferenceType.DELIVERY_NOTE_NUMBER)).toEqual([
      { id: '4', value: '888' },
      { id: '5', value: '444' },
    ]);
  });

  it('should return references for identification_code_transport', () => {
    expect(
      getReferenceNumbers(references, OrderReferenceType.IDENTIFICATION_CODE_TRANSPORT),
    ).toEqual([
      { id: '42', value: '1234AB7890CD3442' },
      { id: '43', value: '1234AB7890CD3456' },
    ]);
  });

  it('should return references for booking_reference', () => {
    expect(getReferenceNumbers(references, OrderReferenceType.BOOKING_REFERENCE)).toEqual([
      { id: '6', value: 'Aze123456' },
      { id: '7', value: 'poi456' },
    ]);
  });
});
