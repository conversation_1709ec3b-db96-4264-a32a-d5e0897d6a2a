import { describe, expect, it, beforeEach, beforeAll } from 'vitest';
import { useCollectionInterpreterOptions } from '@/composables/data/useCollectionInterpreterOptions';
import { nextTick } from 'vue';
import { mockServer } from '@/mocks/server';
import { collectionInterpreterOptions } from '@/mocks/fixtures/collectionInterpreterOptions';
import { initPinia } from '@test/util/init-pinia';
import { withSetup } from '@test/util/with-setup';
import { Option } from '@dfe/dfe-book-api';

describe('useCollectionInterpreterOptions', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        collectionInterpreterOptions,
      },
    });
  });

  beforeEach(() => {
    initPinia();
  });

  it('returns formatted collection interpreter options', async () => {
    const { selectableCollectionInterpreterOptions } = withSetup(() =>
      useCollectionInterpreterOptions(),
    )[0];

    await nextTick();

    collectionInterpreterOptions.forEach((option) => {
      option.code = option.code?.toUpperCase();
    });

    await vi.waitFor(() => {
      expect(selectableCollectionInterpreterOptions.value).toEqual(collectionInterpreterOptions);
    });
  });

  it('handles empty collection interpreter options', async () => {
    mockServer({
      environment: 'test',
      fixtures: {
        collectionInterpreterOptions: [],
      },
    });

    const { selectableCollectionInterpreterOptions } = withSetup(() =>
      useCollectionInterpreterOptions(),
    )[0];

    await nextTick();

    await vi.waitFor(() => {
      expect(selectableCollectionInterpreterOptions.value).toEqual([]);
    });
  });

  it('handles undefined collection interpreter options', async () => {
    mockServer({
      environment: 'test',
      fixtures: {
        collectionInterpreterOptions: undefined,
      },
    });

    const { selectableCollectionInterpreterOptions } = withSetup(() =>
      useCollectionInterpreterOptions(),
    )[0];

    await nextTick();

    await vi.waitFor(() => {
      expect(selectableCollectionInterpreterOptions.value).toEqual([]);
    });
  });

  it('handles collection interpreter options with missing fields', async () => {
    mockServer({
      environment: 'test',
      fixtures: {
        collectionInterpreterOptions: [{ code: 'opt1' }, { description: 'Option 2' }] as Option[],
      },
    });

    const { selectableCollectionInterpreterOptions } = withSetup(() =>
      useCollectionInterpreterOptions(),
    )[0];

    await nextTick();

    await vi.waitFor(() => {
      expect(selectableCollectionInterpreterOptions.value).toEqual([
        { code: 'OPT1', description: '' },
        { code: '', description: 'Option 2' },
      ]);
    });
  });

  it('handles collection interpreter options with special characters', async () => {
    mockServer({
      environment: 'test',
      fixtures: {
        collectionInterpreterOptions: [
          { code: 'opt@1', description: 'Option @1' },
          { code: 'opt#2', description: 'Option #2' },
        ],
      },
    });

    const { selectableCollectionInterpreterOptions } = withSetup(() =>
      useCollectionInterpreterOptions(),
    )[0];

    await nextTick();

    await vi.waitFor(() => {
      expect(selectableCollectionInterpreterOptions.value).toEqual([
        { code: 'OPT@1', description: 'Option @1' },
        { code: 'OPT#2', description: 'Option #2' },
      ]);
    });
  });

  it('handles collection interpreter options with numeric codes', async () => {
    mockServer({
      environment: 'test',
      fixtures: {
        collectionInterpreterOptions: [
          { code: '123', description: 'Option 123' },
          { code: '456', description: 'Option 456' },
        ],
      },
    });

    const { selectableCollectionInterpreterOptions } = withSetup(() =>
      useCollectionInterpreterOptions(),
    )[0];

    await nextTick();

    await vi.waitFor(() => {
      expect(selectableCollectionInterpreterOptions.value).toEqual([
        { code: '123', description: 'Option 123' },
        { code: '456', description: 'Option 456' },
      ]);
    });
  });
});
