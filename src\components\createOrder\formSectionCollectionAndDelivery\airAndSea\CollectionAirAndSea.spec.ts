import CollectionAirAndSea from '@/components/createOrder/formSectionCollectionAndDelivery/airAndSea/CollectionAirAndSea.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { storeToRefs } from 'pinia';
import FormCollection from '@/components/createOrder/formSectionCollectionAndDelivery/FormCollection.vue';

describe('Collection Air component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(CollectionAirAndSea);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('mounts', () => {
    expect(wrapper.exists()).toBeTruthy();
  });

  it('should show form-collection by default', async () => {
    wrapper = mount(CollectionAirAndSea);

    expect(wrapper.findComponent(FormCollection).exists()).toBeTruthy();
  });

  it("should show request arrangement info, when user toggles on 'Request arrangement' switch", async () => {
    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();

    const { requestArrangement } = storeToRefs(createOrderFormCollectionAndDeliveryStore);

    requestArrangement.value = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'form-collection' }).exists()).toBeFalsy();
    expect(wrapper.find('.request-arrangement-info').exists()).toBeTruthy();
  });

  it("should empty customCollectionInfo , when user toggles on 'Request arrangement' switch", async () => {
    const createOrderFormCollectionAndDeliveryStore =
      useCreateOrderFormCollectionAndDeliveryStore();

    const { requestArrangement } = storeToRefs(createOrderFormCollectionAndDeliveryStore);

    requestArrangement.value = true;

    await wrapper.vm.$nextTick();

    expect(createOrderFormCollectionAndDeliveryStore.customCollectionTimeSlot.collectionDate).toBe(
      '',
    );
    expect(createOrderFormCollectionAndDeliveryStore.customCollectionTimeSlot.to).toBe('');
    expect(createOrderFormCollectionAndDeliveryStore.customCollectionTimeSlot.from).toBe('');
  });
});
