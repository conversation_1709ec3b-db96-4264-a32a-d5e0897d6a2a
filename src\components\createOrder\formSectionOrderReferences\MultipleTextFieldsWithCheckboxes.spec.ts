import MultipleTextFieldsWithCheckboxes from '@/components/createOrder/formSectionOrderReferences/MultipleTextFieldsWithCheckboxes.vue';
import { OrderReferenceType } from '@dfe/dfe-book-api';
import type { MultipleReferenceNumber } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import TextField from '@/components/form/TextField.vue';
import AddButton from '@/components/createOrder/AddButton.vue';
import { beforeEach } from 'vitest';
import { nextTick } from 'vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';

const props: {
  referenceType: OrderReferenceType;
  items: MultipleReferenceNumber[];
  label: string;
} = {
  referenceType: OrderReferenceType.PURCHASE_ORDER_NUMBER,
  items: [
    {
      id: '0',
      value: '0',
    },
    {
      id: '1',
      value: '1',
    },
  ],
  label: 'label',
};

describe('Order references - MultipleTextFieldsWithCheckboxes component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(MultipleTextFieldsWithCheckboxes, {
      props,
    });
  });

  it('shows label for first text field and checkboxes', () => {
    // Label for text fields
    const textFields = wrapper.findAllComponents(TextField);
    expect(textFields).toHaveLength(2);
    expect(textFields.at(0)?.props('label')).toBe('label');
    expect(textFields.at(1)?.props('label')).toBeUndefined();

    // Label for checkboxes
    const checkboxesContainers = wrapper.findAll('.checkboxes-container');
    expect(checkboxesContainers).toHaveLength(2);
    expect(checkboxesContainers.at(0)?.find('.applicable-label').text()).toBe(
      'labels.applicable_for.text',
    );
    expect(checkboxesContainers.at(1)?.find('.applicable-label').exists()).toBe(false);
  });

  it('does not set duplicate error for unique values', async () => {
    const inputs = wrapper.findAll('input');
    expect(inputs[0].attributes('error')).toBeUndefined();
    expect(inputs[1].attributes('error')).toBeUndefined();
  });

  it('sets duplicate error when two fields have the same value', async () => {
    const inputs = wrapper.findAllComponents(TextField);

    const input1 = inputs.at(0);
    const input2 = inputs.at(1);

    input1?.setValue('H54YR28');
    input1?.vm.$emit('input');

    input2?.setValue('H54YR28');
    input2?.vm.$emit('input');

    await nextTick();

    expect(input1?.vm.error).toBe(true);
    expect(input2?.vm.error).toBe(true);
  });

  it('skips validation for empty or null values', async () => {
    const inputs = wrapper.findAllComponents(TextField);

    const input1 = inputs.at(0);
    const input2 = inputs.at(1);

    input1?.setValue('');
    input1?.vm.$emit('input');

    input2?.setValue('');
    input2?.vm.$emit('input');

    await nextTick();

    expect(input1?.vm.error).toBe(false);
    expect(input2?.vm.error).toBe(false);
  });

  it('displays the DfeBanner when duplicate entries are detected and hides when resolved', async () => {
    const inputs = wrapper.findAllComponents(TextField);

    const input1 = inputs.at(0);
    const input2 = inputs.at(1);

    input1?.setValue('H54YR28');
    input1?.vm.$emit('input');

    input2?.setValue('H54YR28');
    input2?.vm.$emit('input');

    await nextTick();

    const banner = wrapper.findComponent(DfeBanner);
    expect(banner.exists()).toBe(true);

    input1?.setValue('1111');
    input1?.vm.$emit('input');
    await nextTick();

    const updatedBanner = wrapper.findComponent(DfeBanner);
    expect(updatedBanner.exists()).toBe(false);
  });

  it('shows one delete button per text field – one within each checkboxes-container', () => {
    const deleteButtons = wrapper.findAll('.delete-button');
    expect(deleteButtons).toHaveLength(2);

    const checkboxesContainers = wrapper.findAll('.checkboxes-container .delete-button');
    expect(checkboxesContainers).toHaveLength(2);

    // Does not show delete button as a separate grid item
    const deleteButtonsAsGridItem = wrapper.findAll('.grid-item.delete-button');
    expect(deleteButtonsAsGridItem).toHaveLength(0);
  });

  it.each([
    [OrderReferenceType.INVOICE_NUMBER],
    [OrderReferenceType.OTHERS],
    [OrderReferenceType.PURCHASE_ORDER_NUMBER],
    [OrderReferenceType.DELIVERY_NOTE_NUMBER],
  ])('calls action on clicking add button -> %s', async (referenceType) => {
    const store = useCreateOrderOrderReferencesFormStore();

    await wrapper.setProps({ referenceType });
    await wrapper.vm.$nextTick();

    await wrapper.findComponent(AddButton).trigger('click');
    await wrapper.vm.$nextTick();

    expect(store.addReference).toHaveBeenCalledTimes(1);
    expect(store.addReference).toHaveBeenCalledWith(referenceType, {
      withLoadingData: true,
    });

    store.$reset();
    vi.clearAllMocks();
  });
});
