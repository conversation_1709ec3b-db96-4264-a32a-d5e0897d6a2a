import type { Port, Ports, PortType } from '@dfe/dfe-book-api';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { computed, ref, watch } from 'vue';
import { debounce } from 'lodash';

type PortSearchOptions = {
  portType?: PortType;
  debounce?: number;
};

export interface PortItem extends Port {
  displayName?: string;
}

export function usePortSearch(options: PortSearchOptions) {
  const { searchPort } = useCreateOrderDataStore();

  const searchTerm = ref<string>('');
  const response = ref<Ports>([]);
  const isLoading = ref<boolean>(false);

  const updateResults = debounce(async (value: string) => {
    if (value && options.portType) {
      isLoading.value = true;
      response.value = await searchPort(value, options.portType);
      isLoading.value = false;
    }
  }, options.debounce ?? 0);

  watch(() => searchTerm.value, updateResults);

  const ports = computed<PortItem[]>(() => {
    return response.value.map((port) => ({
      ...port,
      displayName: `${port.name} (${port.code})`,
    }));
  });

  return {
    searchTerm,
    ports,
    isLoading: computed(() => isLoading.value),
  };
}
