<template>
  <VTooltip :attach="$appRoot" :max-width="`${maxWidth}px`" :location="location">
    <template #activator="{ props: tooltipProps }">
      <VBtn
        width="36"
        variant="plain"
        v-bind="{ ...tooltipProps, ...$attrs }"
        class="info-button px-0"
        size="16"
      >
        <MaterialSymbol size="16">
          <InfoIcon />
        </MaterialSymbol>
      </VBtn>
    </template>
    <span class="text-body-2">{{ label }}</span>
  </VTooltip>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import InfoIcon from '@dfe/dfe-frontend-styles/assets/icons/info-16px.svg';
import { Anchor } from '@/types/vuetify';

withDefaults(
  defineProps<{
    label?: string;
    maxWidth?: number;
    location?: Anchor;
  }>(),
  {
    label: '',
    maxWidth: 200,
    location: undefined,
  },
);
</script>

<style lang="scss" scoped>
.info-button {
  vertical-align: text-top;
  padding-top: 2px;
  padding-bottom: 2px;

  &:focus {
    .material-symbol {
      color: var(--color-base-grey-900);
      background: var(--color-base-blue-100);
      -webkit-box-shadow: inset 0 0 0 2px var(--color-base-blue-500);
      box-shadow: inset 0 0 0 2px var(--color-base-blue-500);
      border-radius: 100%;
    }
  }
  &:hover {
    .material-symbol {
      color: var(--color-base-grey-900);
      background: var(--color-base-blue-100);
      border-radius: 100%;
    }
  }
}
</style>
