import { defineStore, storeToRefs } from 'pinia';
import {
  ADRDangerousGood,
  LQDangerousGood,
  EQDangerousGood,
  AirSeaOrderLineHsCode,
  Option,
  Options,
  DangerousGoodDataItem,
} from '@dfe/dfe-book-api';
import { createUuid } from '@/utils/createUuid';
import { isEqual, omit, sumBy } from 'lodash';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { toRaw } from 'vue';
import { DangerousGoodType } from '@/enums';

interface GoodsClassification {
  id?: number;
  hsCode?: Option;
  goods: string;
  loading?: boolean;
  search?: Options;
}

export interface EQDangerousGood4Store extends EQDangerousGood {
  localId: number;
}

export interface ADRDangerousGood4Store extends ADRDangerousGood {
  localId: number;
}

export interface LQDangerousGood4Store extends LQDangerousGood {
  localId: number;
}

export interface PackingPosition4Store {
  localId: number;
  id: number | undefined;
  quantity: number;
  packagingType?: Option;
}

export interface FullContainerLoad4Store {
  localId: number;
  id: number | undefined;
  containerType?: Option;
  containerNumber: string;
  verifiedGrossMass?: number;
  sortingPosition: number;
}

export interface OrderLine4Store {
  localId: number;
  id: number | undefined;
  number: number;
  quantity: number;
  packaging?: Option;
  content?: string;
  weight: number | null;
  length: number | null;
  width: number | null;
  height: number | null;
  volume: number | null;
  loadingMeter?: number;
  goodsGroup:
    | {
        code: string;
        quantity: number | undefined;
      }
    | undefined;
  markAndNumbers?: string;
  goodsClassifications?: GoodsClassification[];
  hsCodes?: AirSeaOrderLineHsCode[];
  packingPositionId?: number | null;
  dangerousGoods: (LQDangerousGood4Store | EQDangerousGood4Store | ADRDangerousGood4Store)[];
  fullContainerLoadId?: number | null;
}

export interface CreateOrderOrderLineFormState {
  orderLines: OrderLine4Store[];
  packingPositions: PackingPosition4Store[];
  fullContainerLoads: FullContainerLoad4Store[];
  packagingAidPosition?: boolean;
  isFullContainerLoadAllowed: boolean;
  isFullContainerLoad?: boolean;
  totalValue: number | undefined;
  manualNumberOfLabels: number | undefined;
  manualNumberOfLabelsOnLoad: number;
  stackable: boolean;
  shockSensitive: boolean;
  originalPackaging: Option | undefined;
  generatedSSccs: string[];
  generatedSSccsOnLoad: number;
}

export function getEmptyPackingPosition(): PackingPosition4Store {
  return {
    localId: createUuid(),
    id: undefined,
    quantity: 0,
    packagingType: undefined,
  };
}

export function getEmptyFullContainerLoad(): FullContainerLoad4Store {
  return {
    localId: createUuid(),
    id: undefined,
    containerType: undefined,
    containerNumber: '',
    verifiedGrossMass: undefined,
    sortingPosition: 0,
  };
}

export function getEmptyOrderLine(): OrderLine4Store {
  return {
    localId: createUuid(),
    id: undefined,
    number: 0,
    quantity: 0,
    packaging: undefined,
    content: undefined,
    weight: null,
    length: null,
    width: null,
    height: null,
    volume: null,
    loadingMeter: undefined,
    goodsGroup: {
      code: '',
      quantity: undefined,
    },
    markAndNumbers: undefined,
    goodsClassifications: [getEmptyGoodsClassificationItem()],
    hsCodes: [],
    packingPositionId: undefined,
    dangerousGoods: [],
    fullContainerLoadId: undefined,
  };
}

function getEmptyGoodsClassificationItem(): GoodsClassification {
  return {
    hsCode: undefined,
    goods: '',
    loading: false,
    search: [],
  };
}

export function getEmptyEQDangerousGood(sortingPosition: number): EQDangerousGood4Store {
  return {
    id: undefined,
    localId: createUuid(),
    sortingPosition: sortingPosition,
    dangerousGoodType: 'EQDangerousGood',
    noOfPackages: 0,
    packaging: undefined,
  };
}

export function getEmptyLQDangerousGood(sortingPosition: number): LQDangerousGood4Store {
  return {
    id: undefined,
    localId: createUuid(),
    sortingPosition: sortingPosition,
    grossMass: undefined,
    nos: '',
    dangerousGoodType: 'LQDangerousGood',
    dangerousGoodDataItem: undefined,
  };
}

export function getEmptyUnNumberDangerousGood(
  sortingPosition: number,
  dangerousGoodDataItem?: DangerousGoodDataItem,
): ADRDangerousGood4Store {
  return {
    id: undefined,
    localId: createUuid(),
    sortingPosition: sortingPosition,
    dangerousGoodType: 'ADRDangerousGood',
    dangerousGoodDataItem,
    nos: '',
    grossMass: undefined,
    noOfPackages: 0,
    packaging: undefined,
    environmentallyHazardous: false,
  };
}

export function isEmptyOrderLine(orderLine: OrderLine4Store) {
  const emptyOrderLine = omit(getEmptyOrderLine(), [
    'localId',
    'id',
    'number',
    'goodsGroup',
    'fullContainerLoadId',
  ]);
  const orderLineForComparison = omit(toRaw(orderLine), [
    'localId',
    'id',
    'number',
    'goodsGroup',
    'fullContainerLoadId',
  ]);

  return isEqual(emptyOrderLine, orderLineForComparison);
}

export function isEmptyFullContainerLoad(fullContainerLoad: FullContainerLoad4Store) {
  const emptyFullContainerLoad = omit(getEmptyFullContainerLoad(), ['localId', 'id']);
  const fullContainerLoadForComparison = omit(toRaw(fullContainerLoad), ['localId', 'id']);

  return isEqual(emptyFullContainerLoad, fullContainerLoadForComparison);
}

let hsCodeCancelToken: symbol | string | number;

export const useCreateOrderOrderLineFormStore = defineStore('createOrderOrderLineForm', {
  state: (): CreateOrderOrderLineFormState => ({
    orderLines: [getEmptyOrderLine()],
    packingPositions: [],
    fullContainerLoads: [],
    packagingAidPosition: false,
    isFullContainerLoadAllowed: false,
    isFullContainerLoad: false,
    totalValue: undefined,
    manualNumberOfLabels: undefined,
    manualNumberOfLabelsOnLoad: 0,
    stackable: true,
    shockSensitive: false,
    originalPackaging: undefined,
    generatedSSccs: [],
    generatedSSccsOnLoad: 0,
  }),
  getters: {
    totalWeight(): number {
      return sumBy(this.orderLines, 'weight') || 0;
    },
    totalVolume(): number {
      return sumBy(this.orderLines, 'volume') || 0;
    },
    totalVGM(): number {
      return sumBy(this.fullContainerLoads, 'verifiedGrossMass') || 0;
    },
    numberOfLabels(): number {
      if (!this.getPackingPositionsAllowed) {
        return sumBy(this.orderLines, 'quantity') || 0;
      }

      return (
        (sumBy(this.getUnassignedOrderLines, 'quantity') || 0) +
        (sumBy(this.packingPositions, 'quantity') || 0)
      );
    },
    hasManualNumberOfLabelsDecreased(): boolean {
      if (!this.manualNumberOfLabels) {
        return this.hasManualNumberOfLabelsDecreasesOnUpdate;
      }
      return (
        this.manualNumberOfLabels < this.manualNumberOfLabelsOnLoad ||
        this.hasManualNumberOfLabelsDecreasesOnUpdate
      );
    },
    hasManualNumberOfLabelsDecreasesOnUpdate(): boolean {
      if (!this.manualNumberOfLabels) {
        return false;
      }
      return this.manualNumberOfLabels < this.generatedSSccs.length;
    },
    differenceInManualNumberOfLabels(): number {
      if (!this.manualNumberOfLabels) {
        return 0;
      }
      return Math.abs(this.manualNumberOfLabels - this.generatedSSccs.length);
    },
    hasGeneratedNumberOfLabelsDecreased(): boolean {
      return (
        this.numberOfLabels < this.generatedSSccsOnLoad ||
        this.hasGeneratedNumberOfLabelsDecreasesOnUpdate
      );
    },
    hasGeneratedNumberOfLabelsDecreasesOnUpdate(): boolean {
      return this.numberOfLabels < this.generatedSSccs.length;
    },
    differenceInGeneratedNumberOfLabels(): number {
      return Math.abs(this.numberOfLabels - this.generatedSSccs.length);
    },
    getOrderLine() {
      return (localId: number): OrderLine4Store => {
        return (
          this.orderLines.find((orderLine) => orderLine.localId === localId) ?? getEmptyOrderLine()
        );
      };
    },
    getUnassignedOrderLines(): OrderLine4Store[] {
      return this.orderLines
        .filter((orderLine) => !orderLine.packingPositionId && orderLine.packingPositionId !== 0)
        .filter(
          (orderLine) => !orderLine.fullContainerLoadId && orderLine.fullContainerLoadId !== 0,
        )
        .sort((a, b) => a.number - b.number);
    },
    getGoodsClassificationItem() {
      return (localId: number, index: number) => {
        const orderLine = this.getOrderLine(localId);
        return orderLine?.goodsClassifications?.[index];
      };
    },
    getPackingPositionsAllowed(): boolean {
      const { isRoadOrder } = storeToRefs(useCreateOrderFormStore());

      return isRoadOrder.value && !!this.packagingAidPosition;
    },
    getFullContainerLoadsAllowed(): boolean {
      const { isSeaOrder } = storeToRefs(useCreateOrderFormStore());

      return isSeaOrder.value && this.isFullContainerLoadAllowed;
    },
    isSeaLclOrder(): boolean {
      const { isSeaOrder } = storeToRefs(useCreateOrderFormStore());

      return isSeaOrder.value && !this.isFullContainerLoad;
    },
  },
  actions: {
    addOrderLine() {
      const newOrderLine = getEmptyOrderLine();
      newOrderLine.number = this.getNextOrderLineNumber();
      this.orderLines.push(newOrderLine);
    },
    addOrderLineToPackingPosition(packingPositionId?: number) {
      const newOrderLine = getEmptyOrderLine();
      newOrderLine.packingPositionId = packingPositionId;
      newOrderLine.number = this.getNextOrderLineNumber();
      this.orderLines.push(newOrderLine);
    },
    addOrderLineToFullContainerLoad(fullContainerLoadId?: number) {
      const newOrderLine = getEmptyOrderLine();
      newOrderLine.fullContainerLoadId = fullContainerLoadId;
      newOrderLine.number = this.getNextOrderLineNumber();
      this.orderLines.push(newOrderLine);
    },
    getNextOrderLineNumber() {
      return (
        this.orderLines.reduce((number, current) => {
          return number < current.number ? current.number : number;
        }, 0) + 1
      );
    },
    updateOrderLine(updatedOrderLine: OrderLine4Store) {
      const lineIndexToUpdate = this.orderLines.findIndex(
        (orderLine) => orderLine.localId === updatedOrderLine.localId,
      );
      this.orderLines[lineIndexToUpdate] = updatedOrderLine;
    },
    removeSSccs(ssccsToRemove: string[]) {
      this.generatedSSccs = this.generatedSSccs.filter((sscc) => !ssccsToRemove.includes(sscc));
      this.generatedSSccsOnLoad = this.generatedSSccs.length;
      this.manualNumberOfLabelsOnLoad = this.generatedSSccs.length;
    },
    removeOrderLine(localId: number) {
      if (this.isFullContainerLoad) {
        const orderLine = this.orderLines.find((orderLine) => orderLine.localId === localId);

        const orderLinesFromFullContainerLoad = this.orderLines.filter(
          (ol) => ol.fullContainerLoadId === orderLine?.fullContainerLoadId,
        );

        if (
          orderLine?.fullContainerLoadId != null &&
          orderLinesFromFullContainerLoad.length === 1
        ) {
          this.addOrderLineToFullContainerLoad(orderLine.fullContainerLoadId);
        }
      }

      this.orderLines = this.orderLines.filter((orderLine) => orderLine.localId !== localId);

      if (!this.orderLines.length && !this.isFullContainerLoad) {
        this.addOrderLine();
      }
    },
    removeAllOrderLines() {
      // Should only be used when working with full container loads
      this.orderLines = [];
    },
    removeAllFullContainerLoads() {
      this.fullContainerLoads = [];
      this.removeAllOrderLines();
    },

    addPackingPosition(skipOrderLine?: boolean) {
      const newPackingPosition = getEmptyPackingPosition();
      this.packingPositions.push(newPackingPosition);

      if (skipOrderLine) {
        return;
      }

      if (
        this.orderLines.length === 1 &&
        this.packingPositions.length === 1 &&
        isEmptyOrderLine(this.orderLines[0])
      ) {
        this.orderLines[0].packingPositionId = newPackingPosition.localId;
        return;
      }

      this.addOrderLineToPackingPosition(newPackingPosition.localId);
    },
    removePackingPosition(localId: number) {
      const orderLinesToRemove = this.orderLines.filter(
        (orderLine) => orderLine.packingPositionId === localId,
      );

      orderLinesToRemove.forEach((orderLine) => {
        this.removeOrderLine(orderLine.localId);
      });

      this.packingPositions = this.packingPositions.filter((packingPosition) => {
        return packingPosition.localId !== localId;
      });
    },
    addFullContainerLoad(skipOrderLine?: boolean) {
      const newFullContainerLoad = getEmptyFullContainerLoad();
      this.fullContainerLoads.push(newFullContainerLoad);

      if (skipOrderLine) {
        return;
      }

      if (
        this.orderLines.length === 1 &&
        this.fullContainerLoads.length === 1 &&
        isEmptyOrderLine(this.orderLines[0])
      ) {
        this.orderLines[0].fullContainerLoadId = newFullContainerLoad.localId;
        return;
      }

      this.addOrderLineToFullContainerLoad(newFullContainerLoad.localId);
    },
    removeFullContainerLoad(localId: number) {
      const orderLinesToRemove = this.orderLines.filter(
        (orderLine) => orderLine.fullContainerLoadId === localId,
      );

      orderLinesToRemove.forEach((orderLine) => {
        this.removeOrderLine(orderLine.localId);
      });

      this.fullContainerLoads = this.fullContainerLoads.filter((fullContainerLoad) => {
        return fullContainerLoad.localId !== localId;
      });
    },
    addGoodsClassificationItem(orderLineLocalId: number) {
      const orderLine = this.getOrderLine(orderLineLocalId);
      if (orderLine) {
        orderLine.goodsClassifications?.push(getEmptyGoodsClassificationItem());
      }
    },
    removeGoodsClassificationItem(orderLineLocalId: number, index: number) {
      const orderLine = this.getOrderLine(orderLineLocalId);

      if (orderLine) {
        orderLine.goodsClassifications?.splice(index, 1);

        if (!orderLine.goodsClassifications?.length) {
          this.addGoodsClassificationItem(orderLineLocalId);
        }
      }
    },
    async searchHsCodeOptions(orderLineLocalId: number, index: number, value: string | null) {
      const goodsClassificationItem = this.getGoodsClassificationItem(orderLineLocalId, index);
      if (goodsClassificationItem) {
        this.api.book.abortRequest(hsCodeCancelToken);
        goodsClassificationItem.loading = true;

        const cancelToken = this.createCancelToken();
        hsCodeCancelToken = cancelToken;

        if (value) {
          const { data } = await this.api.book.hsCodeOptions.getHsCodeOptions(
            { searchFor: value },
            { cancelToken },
          );
          if (data.length > 0) {
            goodsClassificationItem.search = data;
          }
        } else {
          goodsClassificationItem.search = [];
        }

        if (cancelToken === hsCodeCancelToken) {
          goodsClassificationItem.loading = false;
        }
      }
    },
    async loadAndSetHsCode(orderLineLocalId: number, index: number, hsCodeValue: string) {
      const goodsClassificationItem = this.getGoodsClassificationItem(orderLineLocalId, index);
      if (goodsClassificationItem && hsCodeValue) {
        const cancelToken = this.createCancelToken();
        hsCodeCancelToken = cancelToken;
        const { data } = await this.api.book.hsCodeOptions.getHsCodeOptions(
          { searchFor: hsCodeValue },
          { cancelToken },
        );
        if (data.length === 1) {
          goodsClassificationItem.hsCode = data[0];
          goodsClassificationItem.search = data;
        }
        if (cancelToken === hsCodeCancelToken) {
          goodsClassificationItem.loading = false;
        }
      }
    },
    addDangerousGood(
      type: DangerousGoodType,
      localId: number,
      dangerousGoodDataItem?: DangerousGoodDataItem,
    ) {
      const orderLine = this.orderLines.find((orderLine) => orderLine.localId === localId);
      switch (type) {
        case DangerousGoodType.EQ:
          orderLine?.dangerousGoods.push(
            getEmptyEQDangerousGood(orderLine.dangerousGoods.length + 1),
          );
          break;
        case DangerousGoodType.LQ:
          orderLine?.dangerousGoods.push(
            getEmptyLQDangerousGood(orderLine.dangerousGoods.length + 1),
          );
          break;
        case DangerousGoodType.UnNumber:
          orderLine?.dangerousGoods.push(
            getEmptyUnNumberDangerousGood(
              orderLine.dangerousGoods.length + 1,
              dangerousGoodDataItem,
            ),
          );
          break;
      }
    },
    deleteDangerousGood(localId: number, localEqId: number) {
      const orderLine = this.orderLines.find((orderLine) => orderLine.localId === localId);
      if (orderLine) {
        orderLine.dangerousGoods = orderLine.dangerousGoods?.filter(
          (item) => item.localId !== localEqId,
        );
      }
    },
    updateDangerousGood(
      localId: number,
      index: number,
      dangerousGood: LQDangerousGood4Store | EQDangerousGood4Store | ADRDangerousGood4Store,
    ) {
      const orderLine = this.orderLines.find((orderLine) => orderLine.localId === localId);
      if (orderLine) {
        orderLine.dangerousGoods[index] = dangerousGood;
      }
    },
  },
});
