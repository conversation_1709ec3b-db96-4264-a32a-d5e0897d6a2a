<template>
  <label
    v-if="$slots.default"
    :for="props.for"
    class="text-label-3 mb-1"
    :class="tooltip ? 'd-flex' : 'd-block'"
  >
    <slot />
    <span v-if="props.required" class="label-indicator">*</span>
    <InfoButtonWithTooltip v-if="tooltip" class="ml-2" :label="tooltip" location="right" />
  </label>
</template>

<script lang="ts" setup>
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import { type TranslateResult } from 'vue-i18n';

interface Props {
  for: string;
  required?: boolean;
  tooltip?: TranslateResult;
}

const props = defineProps<Props>();
</script>
