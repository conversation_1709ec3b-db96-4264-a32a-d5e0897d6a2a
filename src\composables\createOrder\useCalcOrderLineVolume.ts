export default function (
  quantity: number | null,
  length: number | null,
  width: number | null,
  height: number | null,
) {
  if (quantity && length && width && height) {
    const volume = quantity * length * width * height * 1e-6; // convert to m3
    return parseFloat(volume.toFixed(3));
  } else {
    return null;
  }
}

export function calculateVolume(quantity: number, length: number, width: number, height: number) {
  const volume = quantity * length * width * height * 1e-6; // convert to m3
  return parseFloat(volume.toFixed(3));
}
