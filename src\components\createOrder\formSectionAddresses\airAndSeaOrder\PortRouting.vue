<template>
  <div>
    <h4 class="text-h4 my-6 d-flex align-center">
      {{ t('labels.routing_title.text') }}
    </h4>
    <DfeBanner v-if="showInvalidRoutingBanner" type="error" class="mb-3">
      <span class="text-h5">{{ t('messages.id7124.text') }}</span>
    </DfeBanner>
    <VCard class="routing-card" variant="outlined">
      <VRow>
        <VCol>
          <h5>{{ departurePortHeading }}</h5>
          <AutocompleteField
            v-if="hasMultipleDeparturePorts && !isShipperHandOverSelectionPort"
            :value="fromPortValue"
            :items="fromPortRouting"
            item-title="displayName"
            item-value="code"
            :return-object="true"
            :menu-icon="ArrowDropDownIcon"
          />
          <p v-else-if="fromPortValue && fromDisplayName" class="departure-port">
            {{ fromDisplayName }}
          </p>
          <p v-else class="info-text">
            {{ departurePortDescription }}
          </p>
        </VCol>
        <VCol cols="1" class="d-flex justify-center align-center">
          <MaterialSymbol
            class="forward-arrow | mr-0 | mb-3 mb-xl-0 mb-md-0"
            color="grey-lighten-1"
          >
            <IconArrowForward />
          </MaterialSymbol>
        </VCol>
        <VCol class="destination-column">
          <div class="d-flex flex-column flex-grow-1">
            <h5>
              {{ destinationPortHeading }}
            </h5>
            <AutocompleteField
              v-if="hasMultipleDestinationPorts && !isConsigneeHandOverSelectionPort"
              :items="toPortRouting"
              :model-value="toPortValue"
              item-title="displayName"
              item-value="code"
              :return-object="true"
              :menu-icon="ArrowDropDownIcon"
            />
            <p v-else-if="toPortValue && toDisplayName" class="destination-port">
              {{ toDisplayName }}
            </p>
            <p v-else class="info-text">
              {{ destinationPortDescription }}
            </p>
          </div>
        </VCol>
      </VRow>
    </VCard>
  </div>
</template>
<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import IconArrowForward from '@dfe/dfe-frontend-styles/assets/icons/arrow_forward-24px.svg';
import { storeToRefs } from 'pinia';
import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

interface Props {
  departurePortHeading: string;
  departurePortDescription: string;
  destinationPortHeading: string;
  destinationPortDescription: string;
}

defineProps<Props>();

const { isSeaOrder } = storeToRefs(useCreateOrderFormStore());
const { hasInvalidRouting, fromPortRouting, toPortRouting } =
  storeToRefs(useCreateOrderDataStore());
const {
  isShipperHandOverSelectionPort,
  isConsigneeHandOverSelectionPort,
  fromPort,
  fromIATA,
  toPort,
  toIATA,
  fromAirportDisplayName,
  fromSeaportDisplayName,
  toAirportDisplayName,
  toSeaportDisplayName,
} = storeToRefs(useCreateOrderAddressesStore());

const showInvalidRoutingBanner = computed(() => hasInvalidRouting.value);

const hasMultipleDeparturePorts = computed(() => {
  return fromPortRouting.value.length > 1;
});

const hasMultipleDestinationPorts = computed(() => {
  return toPortRouting.value.length > 1;
});

const fromPortValue = computed(() => {
  return isSeaOrder.value ? fromPort.value : fromIATA.value;
});

const toPortValue = computed(() => {
  return isSeaOrder.value ? toPort.value : toIATA.value;
});

const fromDisplayName = computed(() => {
  return isSeaOrder.value ? fromSeaportDisplayName.value : fromAirportDisplayName.value;
});

const toDisplayName = computed(() => {
  return isSeaOrder.value ? toSeaportDisplayName.value : toAirportDisplayName.value;
});

watch(fromPortRouting, (ports) => {
  ports.forEach((port) => {
    port.displayName = `${port.name} (${port.code})`;
  });
});

watch(toPortRouting, (ports) => {
  ports.forEach((port) => {
    port.displayName = `${port.name} (${port.code})`;
  });
});
</script>

<style scoped lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables';

h5 {
  margin-bottom: 10px;
}

p {
  font-size: 14px;
  margin-bottom: 0 !important;
}

.routing-card {
  border: 1px solid var(--color-base-grey-400);
  padding: 16px;
  gap: 20vw;

  @media only screen and (max-width: 640px) {
    text-align: center;
    flex-direction: column;
    gap: 0;
  }
}

.destination-column {
  position: relative;
}

.info-text {
  color: var(--color-base-grey-600);
}
</style>
