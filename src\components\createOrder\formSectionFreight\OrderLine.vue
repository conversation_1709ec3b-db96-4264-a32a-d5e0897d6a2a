<template>
  <div
    v-data-test="'order-line'"
    class="order-line | mb-6"
    :class="{
      'no-border':
        orderLineType == OrderLineType.PackingPosition ||
        (orderLineType == OrderLineType.FullContainerLoad && lineCounter === 1),
    }"
  >
    <div class="d-flex align-center | mb-n2">
      <div class="order-line--title | text-body-3 text-grey-darken-2">
        {{ t('labels.order_line_header.text') }} {{ computedLineCount }}
      </div>
      <div class="delete-button ml-auto">
        <IconButton
          class="ml-md-0 align-self-end"
          :tooltip="t('labels.delete_label.text')"
          :disabled="isDisabledForPriceRelevantChanges"
          @click="removeOrderLine()"
        >
          <DeleteIcon />
        </IconButton>
      </div>
    </div>
    <VRow v-if="showPackagingChangedBanner">
      <VCol cols="12" xl="8">
        <DfeBanner type="info" class="mt-6 mb-4">
          <span class="text-h5">{{ $t('labels.packaging_changed_headline.text') }}</span>
          <div class="text-body-2 mt-2">
            {{ $t('labels.packaging_changed_description.text') }}
          </div>
        </DfeBanner>
      </VCol>
    </VRow>
    <div class="d-flex flex-wrap">
      <div class="d-flex flex-grow-1 flex-sm-grow-0 flex-wrap flex-md-nowrap align-start">
        <CounterField
          v-data-test="'order-line-quantity'"
          :model-value="orderLine.quantity"
          :label="t('labels.quantity_label.text')"
          :required="!optionalMode"
          :max="999"
          :min="0"
          class="size-sm mr-3 mt-4"
          :rules="getQuantityRules()"
          :disabled="isDisabledForPriceRelevantChanges"
          @update:model-value="updateQuantity"
        />
        <AutocompleteField
          v-model="orderLine.packaging"
          v-data-test="'order-line-packaging'"
          :items="packagingOptionsList"
          item-title="description"
          item-value="code"
          :label="t('labels.packaging_label.text')"
          :placeholder="t('labels.select_option.text')"
          :required="!optionalMode"
          :multiline-menu="false"
          return-object
          :message="t('labels.validation_select_input_required.text')"
          :disabled="isDisabledForPriceRelevantChanges"
          class="size-lg full-width-xs mr-0 mr-sm-3 mt-4"
          :menu-icon="ArrowDropDownIcon"
          @update:model-value="updateValue('packaging', $event)"
        />
        <TextField
          v-if="isAirOrder || isSeaOrder"
          v-model="orderLine.markAndNumbers"
          :label="t('labels.marks_label.text')"
          class="size-xl full-width-xs mr-0 mr-sm-3 mt-4"
          :rules="[useValidationRules.maxChars(35)]"
          @update:model-value="updateValue('markAndNumbers', $event)"
        />

        <SelectFullContainerLoad
          v-if="showSelectFullContainerLoad"
          v-data-test="'order-line-position-fcl'"
          class="size-md mt-4 mr-sm-3"
          :model-value="orderLine.fullContainerLoadId"
          :disabled="isDisabledForPriceRelevantChanges"
          @update:model-value="updateFullContainerLoad($event)"
        />
      </div>

      <div class="d-flex flex-wrap align-start">
        <div class="d-flex flex-wrap justify-space-between align-start flex-grow-1 flex-sm-grow-0">
          <ComboboxMeasurementFields
            v-data-test="'order-line-length'"
            :model-value="orderLine.length"
            :label="t('labels.length_label.text')"
            :required="!optionalMode"
            class="size-xs mt-4 mr-3"
            append="cm"
            :items="measurementsProposalsMenu"
            item-html-text="menuText"
            :max="999"
            :min="1"
            :disabled="isDisabledForPriceRelevantChanges"
            :rules="[useValidationRules.integer]"
            @update:model-value="updateLength"
            @input-multiple="updateMultipleValues"
          />
          <NumberField
            v-data-test="'order-line-width'"
            :model-value="orderLine.width"
            :label="t('labels.width_label.text')"
            :required="!optionalMode"
            class="size-xs mt-4 mr-3"
            append="cm"
            :max="999"
            :min="1"
            :rules="[useValidationRules.integer]"
            :disabled="isDisabledForPriceRelevantChanges"
            :validate-on-blur="widthValidateOnBlur"
            @update:model-value="updateWidth"
          />
          <NumberField
            ref="heightMeasurementField"
            v-data-test="'order-line-height'"
            :model-value="orderLine.height"
            :label="t('labels.height_label.text')"
            :required="!optionalMode"
            class="size-xs mr-0 mr-sm-3 mt-4"
            append="cm"
            :max="999"
            :min="1"
            :rules="[useValidationRules.integer]"
            :disabled="isDisabledForPriceRelevantChanges"
            @update:model-value="updateHeight"
          />
        </div>
        <div class="full-width-xs | d-flex d-sm-none justify-center mt-2 mb-n2">
          <MaterialSymbol color="grey-lighten-1">
            <IconArrowDown />
          </MaterialSymbol>
        </div>
        <div class="d-flex flex-wrap align-start flex-grow-1 flex-sm-grow-0">
          <div class="d-none d-sm-block mt-10">
            <MaterialSymbol class="forward-arrow | mr-3">
              <IconArrowForward />
            </MaterialSymbol>
          </div>

          <NumberField
            ref="volumeField"
            v-model="orderLine.volume"
            :label="t('labels.volume_label.text')"
            :required="!optionalMode"
            class="size-sm mr-3 mt-4"
            append="m<sup>3</sup>"
            :max="999.999"
            :min="0.001"
            :disabled="isDisabledForPriceRelevantChanges"
            :allowed-decimals="3"
            :validate-on-blur="validateOnBlurVolumeField"
            @update:model-value="updateValue('volume', $event)"
          />
          <ComboboxMeasurementFields
            v-data-test="'order-line-weight'"
            :model-value="orderLine.weight"
            :label="t('labels.weight_line_label.text')"
            :required="isWeightRequired && !optionalMode"
            class="size-sm mr-3 mt-4"
            append="kg"
            :max="isAirOrder || isSeaOrder ? 99999.999 : 99999"
            :disabled="isDisabledForPriceRelevantChanges"
            :allowed-decimals="isAirOrder ? 1 : isSeaOrder ? 3 : undefined"
            :rules="getWeightRule()"
            @update:model-value="updateValue('weight', $event)"
          />
          <NumberField
            v-if="isRoadForwardingOrder || isRoadCollectionOrder"
            ref="loadingMeterField"
            v-model="orderLine.loadingMeter"
            :disabled="isDisabledForPriceRelevantChanges"
            :label="t('labels.loading_meter.text')"
            :required="false"
            class="size-xs mt-4"
            append="m"
            :max="99.9"
            :min="0.1"
            :allowed-decimals="1"
            @update:model-value="updateValue('loadingMeter', $event)"
          />
        </div>
      </div>
    </div>

    <div v-if="isRoadForwardingOrder || isRoadCollectionOrder" class="d-flex flex-wrap align-start">
      <AutocompleteField
        ref="ContentTextField"
        v-model="orderLine.content"
        v-data-test="'order-line-content'"
        :items="natureOfGoods ?? []"
        item-title="code"
        item-value="code"
        :label="t('labels.goods_label.text')"
        :required="!optionalMode"
        :multiline-menu="false"
        class="size-xl full-width-xs mr-0 mr-sm-3 mt-4"
        :max-length="MaxLengthsRoadOrder.Content"
        :allow-manual-input="true"
      />

      <div v-if="isRoadForwardingOrder && goodsGroups?.length" class="d-flex">
        <SelectField
          :model-value="orderLine.goodsGroup?.code"
          :label="t('labels.goods_group.text')"
          :placeholder="t('labels.select_option.text')"
          :items="goodsGroups ?? []"
          item-value="code"
          item-text="description"
          :required="!optionalMode"
          class="size-lg full-width-xs mr-0 mr-sm-3 mt-4"
          :rules="[useValidationRules.required]"
          @update:model-value="updateValue('goodsGroup.code', $event)"
        />

        <NumberField
          v-if="showGoodsGroupQuantity && orderLine.goodsGroup"
          v-model="orderLine.goodsGroup.quantity"
          :label="t('labels.quantity_label.text')"
          :required="!optionalMode"
          class="size-md mt-4 mr-3"
          :max="99999"
          :min="0"
          :rules="[useValidationRules.required]"
        />
      </div>

      <SelectPackingPosition
        v-if="showSelectPackingPosition"
        v-data-test="'order-line-position'"
        class="size-md mt-4"
        :model-value="orderLine.packingPositionId"
        :disabled="isDisabledForPriceRelevantChanges"
        @update:model-value="updatePackingPosition($event)"
      />
    </div>
    <AddButton
      v-if="showDangerousGood && !haveDangerousGood"
      v-data-test="'add-dangerous-goods'"
      class="mt-4"
      :label="t('labels.add_dangerous_goods.text')"
      variant="text"
      @add-new-item="addDangerousGoods()"
    />

    <DangerousGood
      v-if="haveDangerousGood"
      :dangerous-goods="orderLine.dangerousGoods"
      :computed-line-count="String(computedLineCount)"
      :local-id="localId"
      @delete-dangerous-goods="deleteDangerousGoods"
    />

    <template v-if="isAirOrder || isSeaOrder">
      <div
        v-for="(goodsClassification, i) in orderLine.goodsClassifications"
        :key="`goodsClassification${i}`"
        class="d-flex flex-wrap align-start"
      >
        <AutocompleteField
          v-model="goodsClassification.goods"
          v-data-test="'order-line-goods'"
          :items="natureOfGoods ?? []"
          item-title="code"
          item-value="code"
          :label="t('labels.goods_label.text')"
          :required="!optionalMode"
          :multiline-menu="false"
          class="size-xl full-width-xs mr-0 mr-sm-3 mt-4"
          :max-length="MaxLengthsAirAndSeaOrder.GoodsDescription"
          :allow-manual-input="true"
        />

        <AutocompleteField
          v-model="goodsClassification.hsCode"
          :label="t('labels.hs_code_label.text')"
          :items="goodsClassification.search ?? []"
          item-title="description"
          :placeholder="t('labels.search_number_description.text')"
          item-value="code"
          :multiline-menu="true"
          :loading="goodsClassification.loading"
          :show-loader-after-ms="1000"
          :return-object="true"
          no-filter
          :message="t('labels.validation_select_input_required.text')"
          class="size-xl full-width-xs mt-4 hs-code mr-0 mr-sm-3"
          :menu-icon="ArrowDropDownIcon"
          @search-input="(value) => onHsCodeSearchInput(i, value)"
        >
          <template #icon>
            <SearchIcon v-if="!goodsClassification.hsCode" ref="searchIcon" />
            <CloseIcon
              v-else
              ref="closeIcon"
              class="cursor-pointer"
              @click="deleteHsCodeSelection(i)"
            />
          </template>
        </AutocompleteField>

        <DfeIconButton
          v-if="(orderLine.goodsClassifications?.length ?? 0) > 1"
          class="mt-10 mr-0 mr-sm-2"
          :tooltip="t('labels.delete_label.text')"
          density="compact"
          @click="removeGoodsClassificationItem(i)"
        >
          <MaterialSymbol size="24">
            <DeleteIcon />
          </MaterialSymbol>
        </DfeIconButton>
        <div
          v-if="i === (orderLine.goodsClassifications?.length ?? 0) - 1"
          class="full-width-md-and-down"
        >
          <VBtn
            variant="text"
            size="small"
            elevation="0"
            class="text-primary text-label-2 mt-md-10 mt-2"
            @click="addGoodsClassificationItem()"
          >
            <MaterialSymbol class="mr-2" left size="16">
              <AddIcon />
            </MaterialSymbol>
            {{ t('labels.add_another.text') }}
          </VBtn>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import IconButton from '@/components/base/buttons/IconButton.vue';
import SelectFullContainerLoad from '@/components/createOrder/formSectionFreight/fullContainerLoad/SelectFullContainerLoad.vue';
import SelectPackingPosition from '@/components/createOrder/formSectionFreight/packingPosition/SelectPackingPosition.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import ComboboxMeasurementFields from '@/components/form/ComboboxMeasurementFields.vue';
import CounterField from '@/components/form/CounterField.vue';
import NumberField from '@/components/form/NumberField.vue';
import SelectField from '@/components/form/SelectField.vue';
import TextField from '@/components/form/TextField.vue';
import useCalcOrderLineVolume from '@/composables/createOrder/useCalcOrderLineVolume';
import useMeasurementProposalsMenu from '@/composables/createOrder/useMeasurementProposalsMenu';
import useOptionDisplayText from '@/composables/createOrder/useOptionDisplayText';
import { usePackagingOptionsList } from '@/composables/createOrder/usePackagingOptionsList';
import { useGoodsGroups } from '@/composables/data/useGoodsGroups';
import { useNatureOfGoods } from '@/composables/data/useNatureOfGoods';
import { usePackagingMeasurementsProposals } from '@/composables/data/usePackagingMeasurementsProposals';
import { usePackagingOptions } from '@/composables/data/usePackagingOptions';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { MaxLengthsAirAndSeaOrder, MaxLengthsRoadOrder, OrderLineType } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import {
  getEmptyOrderLine,
  OrderLine4Store,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import type { MeasurementProposals } from '@dfe/dfe-book-api';
import AddIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
import IconArrowDown from '@dfe/dfe-frontend-styles/assets/icons/arrow_down-24px.svg';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import IconArrowForward from '@dfe/dfe-frontend-styles/assets/icons/arrow_forward-24px.svg';
import CloseIcon from '@dfe/dfe-frontend-styles/assets/icons/close-16px.svg';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import SearchIcon from '@dfe/dfe-frontend-styles/assets/icons/search-16px.svg';
import { debounce, update } from 'lodash';
import { storeToRefs } from 'pinia';
import type { ComponentPublicInstance } from 'vue';
import { computed, nextTick, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { SelectItemKey } from '@/types/vuetify';
import AddButton from '../AddButton.vue';
import DangerousGood from './dangerousGood/DangerousGood.vue';

interface Props {
  localId: number;
  isWeightRequired?: boolean;
  lineCounter: number;
  packingPositionCounter?: number;
  fullContainerLoadCounter?: number;
  optionalMode?: boolean;
}

const orderLine = defineModel<OrderLine4Store>({ default: getEmptyOrderLine() });

const props = defineProps<Props>();
const { t } = useI18n();

const { data: packagingOptions } = usePackagingOptions();

const widthValidateOnBlur = ref(true);
const validateOnBlurVolumeField = ref(true);

const dangerousGoodAdded = ref(false);

const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
const heightMeasurementField = ref<ComponentPublicInstance<HTMLElement> | null>(null);

const { data: goodsGroups, isGoodsGroupQuantityActivated } = useGoodsGroups();

const { data: natureOfGoods } = useNatureOfGoods();

const { data: customerSettings } = useCustomerSettings();

const {
  getGoodsClassificationItem,
  originalPackaging,
  packingPositions,
  fullContainerLoads,
  getPackingPositionsAllowed,
  getFullContainerLoadsAllowed,
  isFullContainerLoad,
} = storeToRefs(createOrderOrderLineFormStore);
const createOrderFormStore = useCreateOrderFormStore();
const {
  isRoadForwardingOrder,
  isRoadCollectionOrder,
  isAirOrder,
  isSeaOrder,
  isAirOrderFromQuote,
  isDisabledForPriceRelevantChanges,
} = storeToRefs(createOrderFormStore);

const updateValue = (key: string, value?: string | number | null | SelectItemKey) => {
  orderLine.value = update({ ...orderLine.value }, key, () => value);
};

const showSelectPackingPosition = computed(() => {
  return !!packingPositions.value.length && getPackingPositionsAllowed.value;
});

const addDangerousGoods = () => {
  dangerousGoodAdded.value = true;
};

const deleteDangerousGoods = () => {
  dangerousGoodAdded.value = false;
  orderLine.value.dangerousGoods = [];
};

const haveDangerousGood = computed(() => {
  return dangerousGoodAdded.value || orderLine.value.dangerousGoods.length > 0;
});

const showDangerousGood = computed(
  () =>
    customerSettings.value?.dangerousGoods &&
    (isRoadForwardingOrder.value || isRoadCollectionOrder.value),
);

const isPackingPositionOrderLine = computed(() => {
  return (
    !!orderLine.value.packingPositionId &&
    orderLine.value.packingPositionId !== 0 &&
    getPackingPositionsAllowed.value
  );
});

const showSelectFullContainerLoad = computed(() => {
  return fullContainerLoads.value.length > 1 && getFullContainerLoadsAllowed.value;
});

const isFullContainerLoadOrderLine = computed(() => {
  return (
    !!orderLine.value.fullContainerLoadId &&
    orderLine.value.fullContainerLoadId !== 0 &&
    getFullContainerLoadsAllowed.value
  );
});

const orderLineType = computed(() => {
  if (isPackingPositionOrderLine.value) {
    return OrderLineType.PackingPosition;
  } else if (isFullContainerLoadOrderLine.value) {
    return OrderLineType.FullContainerLoad;
  } else {
    return OrderLineType.Default;
  }
});

const computedLineCount = computed(() => {
  switch (orderLineType.value) {
    case OrderLineType.PackingPosition:
      return `${props.packingPositionCounter}.${props.lineCounter}`;
    case OrderLineType.FullContainerLoad:
      return `${props.fullContainerLoadCounter}.${props.lineCounter}`;
    case OrderLineType.Default:
    default:
      return props.lineCounter;
  }
});

const showGoodsGroupQuantity = computed(() =>
  isGoodsGroupQuantityActivated(orderLine.value?.goodsGroup?.code),
);

const packagingOptionsList = computed(() => {
  if (!packagingOptions.value) {
    return [];
  }
  return usePackagingOptionsList(
    t('labels.frequently_used.text'),
    t('labels.more_packagings.text'),
    packagingOptions,
  );
});

const onHsCodeSearchInput = debounce((index: number, value: string | null) => {
  const item = getGoodsClassificationItem.value(props.localId, index);
  if (item) {
    if (value && value.length > 2) {
      if (useOptionDisplayText(item?.hsCode) !== value) {
        createOrderOrderLineFormStore.searchHsCodeOptions(props.localId, index, value);
      }
    } else {
      item.search = [];
    }
  }
}, 350);

const deleteHsCodeSelection = (index: number) => {
  const item = getGoodsClassificationItem.value(props.localId, index);

  if (item) {
    item.hsCode = undefined;
    item.search = undefined;
  }
};
const proposals = usePackagingMeasurementsProposals(
  computed(() => orderLine.value.packaging?.code ?? ''),
).data;
const orderLinePackaging = computed<MeasurementProposals>(() => {
  return proposals.value ? proposals.value : ([] as MeasurementProposals);
});

const measurementsProposalsMenu = computed(() =>
  useMeasurementProposalsMenu(
    t('labels.frequently_used.text'),
    t('labels.standard_label.text'),
    orderLinePackaging,
  ),
);

const removeOrderLine = () => createOrderOrderLineFormStore.removeOrderLine(props.localId);

const addGoodsClassificationItem = () =>
  createOrderOrderLineFormStore.addGoodsClassificationItem(props.localId);

const removeGoodsClassificationItem = (index: number) => {
  createOrderOrderLineFormStore.removeGoodsClassificationItem(props.localId, index);
};

const showPackagingChangedBanner = computed(() => {
  return (
    isAirOrderFromQuote.value && originalPackaging.value?.code !== orderLine.value.packaging?.code
  );
});

const updateQuantity = async (quantity: number) => {
  updateValue('quantity', quantity);
  await nextTick();
  calculateOrderLineVolume();
};

const updateMultipleValues = async (value: Partial<OrderLine4Store>) => {
  widthValidateOnBlur.value = false;
  orderLine.value = { ...orderLine.value, ...value };

  await nextTick();
  widthValidateOnBlur.value = true;
  calculateOrderLineVolume();
  focusHeightInput();
};

const focusHeightInput = () => {
  const inputheight = heightMeasurementField.value?.$el.querySelector(
    '.v-text-field input',
  ) as HTMLInputElement;
  return inputheight?.focus();
};

const updatePackingPosition = async (packingPositionId: number | null) => {
  orderLine.value = {
    ...orderLine.value,
    number: createOrderOrderLineFormStore.getNextOrderLineNumber(),
    packingPositionId,
  };
};

const updateFullContainerLoad = async (fullContainerLoadId: number | null) => {
  orderLine.value = {
    ...orderLine.value,
    number: createOrderOrderLineFormStore.getNextOrderLineNumber(),
    fullContainerLoadId: fullContainerLoadId,
  };
};

const updateLength = async (length: number) => {
  updateValue('length', length);
  await nextTick();
  calculateOrderLineVolume();
};

const updateWidth = async (width?: number | null) => {
  updateValue('width', width);
  await nextTick();
  calculateOrderLineVolume();
};

const updateHeight = async (height?: number | null) => {
  updateValue('height', height);
  await nextTick();
  calculateOrderLineVolume();
};

function getWeightRule() {
  return isAirOrder.value || isSeaOrder.value
    ? [useValidationRules.positiveNumber]
    : [useValidationRules.positiveNumber, useValidationRules.integer];
}

function getQuantityRules() {
  const validationRules = [
    useValidationRules.integer,
    useValidationRules.min(0),
    useValidationRules.max(999),
  ];

  if (!isFullContainerLoad?.value) {
    validationRules.push(useValidationRules.positiveNumber);
  }

  return validationRules;
}

function calculateOrderLineVolume() {
  if (isPackingPositionOrderLine.value) {
    return;
  }

  updateValue(
    'volume',
    useCalcOrderLineVolume(
      orderLine.value.quantity,
      orderLine.value.length ?? null,
      orderLine.value.width ?? null,
      orderLine.value.height ?? null,
    ),
  );
}

watch(
  [() => orderLine.value.length, () => orderLine.value.width, () => orderLine.value.height],
  ([length, width, height]) => {
    validateOnBlurVolumeField.value = !(length && width && height);
  },
);

watch(goodsGroups, (currentOrderGroups) => {
  if (currentOrderGroups?.length === 0) {
    orderLine.value.goodsGroup = undefined;
  }
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use 'sass:map';

.full-width-xs {
  @media #{map.get(settings.$display-breakpoints, 'xs')} {
    width: 100%;
  }
}

.full-width-lg-and-down {
  @media #{map.get(settings.$display-breakpoints, 'lg-and-down')} {
    width: 100%;
  }
}

.forward-arrow {
  padding-bottom: 6px;

  color: var(--color-base-grey-400);
}

.order-line {
  border-top: 1px solid var(--color-base-grey-400);
  padding-top: 8px;

  &.no-border {
    border: none;
    padding-top: 0;
  }
}

.delete-button {
  margin-right: 2px;
}

.hs-code :deep(.v-autocomplete__menu-icon) {
  display: none;
}
</style>
