import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useTermStore } from '@/store/createOrder/formTerms';
import { TimeFormat } from '@/types/createOrder';
import { AirOrder, IncoTerm, OrderResponseBody, OrderType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { useSetAccountingAdditionalServicesData } from './useSetAccountingAdditionalServicesData';
import { useSetAddressData } from './useSetAddressData';
import { useSetAirSeaAddressData } from './useSetAirSeaAddressData';
import { useSetCollectionDeliveryData } from './useSetCollectionDeliveryData';
import { useSetOrderLineData } from './useSetOrderLineData';
import { useSetReferencesData } from './useSetReferencesData';
import { useSetPackingPositionData } from '@/composables/createOrder/editOrder/useSetPackingPositionData';
import { useSetFullContainerLoadData } from '@/composables/createOrder/editOrder/useSetFullContainerLoadData';
import { useSetOrderTextData } from '@/composables/createOrder/editOrder/useSetOrderTextData';

export type LoadOrderOptions = {
  timeFormat: TimeFormat;
  locale: string;
};

export function useEditOrder() {
  const createOrderFormStore = useCreateOrderFormStore();
  const { customerNumber, orderType, quoteInformation } = storeToRefs(createOrderFormStore);
  const { selectedIncoTerm } = storeToRefs(useTermStore());

  const loadOrder = async (
    editOrderData: OrderResponseBody | null,
    { timeFormat, locale }: LoadOrderOptions,
  ) => {
    if (!editOrderData) return;

    if (editOrderData.orderType) {
      orderType.value = <OrderType>editOrderData.orderType;
    }

    if (editOrderData.orderId) {
      createOrderFormStore.orderId = editOrderData.orderId;
    }

    // Customer
    customerNumber.value = editOrderData.customerNumber ?? '';

    // Quote to book
    quoteInformation.value = editOrderData.quoteInformation ?? null;
    if (quoteInformation.value && editOrderData.quoteInformation) {
      quoteInformation.value.quoteRequestId = editOrderData.quoteInformation.quoteRequestId;
    }

    // Adresses
    useSetAddressData(editOrderData);

    // Air & Sea Addresses
    await useSetAirSeaAddressData(editOrderData);

    // Order lines
    useSetOrderLineData(editOrderData);

    // Packing positions
    useSetPackingPositionData(editOrderData);

    // Full container loads
    useSetFullContainerLoadData(editOrderData);

    // IncoTerm
    selectedIncoTerm.value = (<AirOrder>editOrderData).incoTerm ?? ({} as IncoTerm);

    // Collection and delivery
    useSetCollectionDeliveryData(editOrderData, { timeFormat, locale });

    // Accounting and additional services
    useSetAccountingAdditionalServicesData(editOrderData);

    // References
    useSetReferencesData(editOrderData);

    // Texts
    useSetOrderTextData(editOrderData);
  };

  return {
    loadOrder,
  };
}
