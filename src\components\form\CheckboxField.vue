<template>
  <div>
    <VCheckbox
      :model-value="value"
      hide-details="auto"
      density="compact"
      :rules="validationCustomCheck"
      :error-messages="errorMessages"
      :class="classStyles"
      :error="error"
      :disabled="disabled"
      @update:model-value="onCheckboxChange"
    >
      <template #label>
        <span v-if="label" class="text-body-2 text-grey-darken-4">
          {{ label }}<span v-if="required" class="label-indicator">*</span>
        </span>
      </template>
    </VCheckbox>
  </div>
</template>

<script setup lang="ts">
import { useValidationRules } from '@/composables/form/useValidationRules';
import { computed } from 'vue';

interface Props {
  label?: string;
  required?: boolean;
  classStyles?: string;
  disabled?: boolean;
  error?: boolean;
  errorMessages?: string[] | string;
}
const props = withDefaults(defineProps<Props>(), {
  label: '',
  required: false,
  classStyles: '',
  disabled: false,
  error: false,
  errorMessages: undefined,
});

const emit = defineEmits(['update:modelValue']);
const value = defineModel<boolean>();

const onCheckboxChange = (value: boolean | null) => {
  emit('update:modelValue', value ?? false);
};

const validationCustomCheck = computed(() => [
  ...(props.required ? [useValidationRules.requiredCheckbox] : []),
]);
</script>

<style lang="scss" scoped>
:deep(.v-input .v-selection-control) {
  --v-selection-control-size: 16px;
  --v-input-control-height: 16px;
}

.v-input:hover :deep(.v-icon.v-theme--light .icon) {
  color: var(--color-base-blue-700);
}
</style>
