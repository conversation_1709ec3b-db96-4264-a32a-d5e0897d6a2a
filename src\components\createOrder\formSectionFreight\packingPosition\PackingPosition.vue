<template>
  <div v-data-test="'packing-position'" class="packing-position | mb-4">
    <div class="packing-position__header | pa-4">
      <div class="d-flex align-center | mb-4">
        <div class="text-h3">{{ t('labels.packing_position_header.text') }} {{ lineCounter }}</div>
        <InfoButtonWithTooltip :label="t('labels.packing_position_hint.text')" class="ml-2" />
        <div class="delete-button ml-auto">
          <IconButton
            v-data-test="'packing-position-delete-button'"
            class="ml-md-0 align-self-end"
            :tooltip="t('labels.delete_label.text')"
            :disabled="isDisabledForPriceRelevantChanges"
            @click="removePackingPosition()"
          >
            <DeleteIcon />
          </IconButton>
        </div>
      </div>

      <div class="d-flex flex-grow-1 flex-sm-grow-0 align-start">
        <CounterField
          v-data-test="'packing-position-quantity'"
          :model-value="packingPosition.quantity"
          :label="t('labels.quantity_label.text')"
          :required="true"
          :max="999"
          :min="0"
          class="size-sm mr-3"
          :rules="[
            useValidationRules.integer,
            useValidationRules.positiveNumber,
            useValidationRules.min(0),
            useValidationRules.max(999),
          ]"
          :disabled="isDisabledForPriceRelevantChanges"
          @update:model-value="updateValue('quantity', $event)"
        />
        <AutocompleteField
          v-model="packingPosition.packagingType"
          v-data-test="'packing-position-packaging'"
          :items="packagingOptionsList"
          item-title="description"
          item-value="code"
          :label="t('labels.packaging_label.text')"
          :placeholder="t('labels.select_option.text')"
          :required="true"
          :multiline-menu="false"
          return-object
          :message="t('labels.validation_select_input_required.text')"
          :disabled="isDisabledForPriceRelevantChanges"
          class="size-lg full-width-xs"
          :menu-icon="ArrowDropDownIcon"
          @update:model-value="updateValue('packagingType', $event)"
        />
      </div>
    </div>

    <div class="packing-position__body | pa-4">
      <div v-if="!!orderLinesForPackingPosition.length">
        <OrderLine
          v-for="({ localId: lineLocalId }, i) in orderLinesForPackingPosition"
          :key="`orderLine${lineLocalId}`"
          :model-value="orderLinesForPackingPosition[i]"
          :local-id="lineLocalId"
          :is-weight-required="i === 0"
          :optional-mode="false"
          :line-counter="i + 1"
          :packing-position-counter="lineCounter"
          @update:model-value="updateOrderLine($event)"
        />
      </div>

      <VDivider class="my-4"></VDivider>
      <VBtn
        v-data-test="'add-order-line-packing-position-button'"
        :disabled="isDisabledForPriceRelevantChanges"
        variant="text"
        size="small"
        color="primary"
        @click="addNewOrderLineToPackingPosition"
      >
        <MaterialSymbol class="mr-2" left size="16">
          <AddIcon />
        </MaterialSymbol>
        {{ t('labels.add_line_packing_position.text') }}
      </VBtn>
    </div>

    <ConfirmPrompt
      v-model="showConfirmDeleteModal"
      :headline="t('labels.remove_packing_position.text')"
      :confirm-text="t('labels.delete_label.text')"
      :cancel-text="t('labels.cancel_label.text')"
      @confirm="removePackingPosition"
      @cancel="showConfirmDeleteModal = false"
      @close="showConfirmDeleteModal = false"
    >
      <h5 class="text-body-2 text-grey-darken-4">
        {{ t('labels.remove_packing_position_info.text') }}
      </h5>
    </ConfirmPrompt>
  </div>
</template>

<script setup lang="ts">
import IconButton from '@/components/base/buttons/IconButton.vue';
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import ConfirmPrompt from '@/components/base/modal/ConfirmPrompt.vue';
import OrderLine from '@/components/createOrder/formSectionFreight/OrderLine.vue';
import AutocompleteField from '@/components/form/AutocompleteField.vue';
import CounterField from '@/components/form/CounterField.vue';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import { usePackagingOptionsList } from '@/composables/createOrder/usePackagingOptionsList';
import { usePackagingOptions } from '@/composables/data/usePackagingOptions';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import {
  getEmptyPackingPosition,
  PackingPosition4Store,
  useCreateOrderOrderLineFormStore,
} from '@/store/createOrder/orderLine';
import AddIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
import ArrowDropDownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-24px.svg';
import { update } from 'lodash';
import { storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  localId: number;
  lineCounter: number;
}

const packingPosition = defineModel<PackingPosition4Store>({ default: getEmptyPackingPosition() });

const props = defineProps<Props>();

const { t } = useI18n();
const createOrderFormStore = useCreateOrderFormStore();
const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
const { isDisabledForPriceRelevantChanges } = storeToRefs(createOrderFormStore);
const { orderLines } = storeToRefs(createOrderOrderLineFormStore);
const { updateOrderLine } = createOrderOrderLineFormStore;

const { data: packagingOptions } = usePackagingOptions();

const showConfirmDeleteModal = ref<boolean>(false);

const packagingOptionsList = computed(() => {
  if (!packagingOptions.value) {
    return [];
  }
  return usePackagingOptionsList(
    t('labels.frequently_used.text'),
    t('labels.more_packagings.text'),
    packagingOptions,
  );
});

const orderLinesForPackingPosition = computed(() => {
  return orderLines.value
    .filter((orderLine) => orderLine.packingPositionId === props.localId)
    .sort((a, b) => a.number - b.number);
});

const updateValue = (key: string, value: string | number | null) => {
  packingPosition.value = update({ ...packingPosition.value }, key, () => value);
};

const removePackingPosition = () => {
  if (!showConfirmDeleteModal.value && !!orderLinesForPackingPosition.value.length) {
    showConfirmDeleteModal.value = true;
    return;
  }

  showConfirmDeleteModal.value = false;

  createOrderOrderLineFormStore.removePackingPosition(props.localId);
};

const addNewOrderLineToPackingPosition = () => {
  createOrderOrderLineFormStore.addOrderLineToPackingPosition(props.localId);
};
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfe-variables;
@use '@/styles/settings';
@use '@/styles/base' as base;
@use 'sass:map';

.packing-position__header {
  background-color: dfe-variables.$color-base-grey-50;
  border: 1px solid dfe-variables.$color-base-grey-400;
  border-top-left-radius: base.space(1);
  border-top-right-radius: base.space(1);
}

.packing-position__body {
  border: 1px solid dfe-variables.$color-base-grey-400;
  border-top: none;
  border-bottom-left-radius: base.space(1);
  border-bottom-right-radius: base.space(1);
}

.size {
  &-lg {
    width: vars.$form-input-width-lg;
  }

  &-sm {
    width: vars.$form-input-width-sm;
  }
}

.full-width-xs {
  @media #{map.get(settings.$display-breakpoints, 'xs')} {
    width: 100%;
  }
}
</style>
