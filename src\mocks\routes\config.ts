import type { Server } from 'miragejs';
import type { Fixtures, Models } from '../server';

const url = process.env.VITE_APP_DFE_CONFIG_API_URL ?? '';
function withURL(path: string) {
  return `${url}${path}`;
}

export function useRoutes(
  server: Server,
  fixtures?: Fixtures<Models>,
  environment = 'development',
) {
  // Mock routes in test environment only
  if (environment === 'test') {
    server.get(withURL('/v1/config/incoterms.json'), () => {
      return fixtures?.incotermConfig ?? {};
    });
    server.get(withURL('/v1/config/air-product-routing.json'), () => {
      return fixtures?.airProductRoutingConfig ?? {};
    });
  }
}
