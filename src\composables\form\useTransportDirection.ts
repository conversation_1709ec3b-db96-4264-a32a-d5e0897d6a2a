import { ConsigneeAddressType, FurtherAddressTypesList } from '@/enums';
import { useEmbargoStore } from '@/store/createOrder/embargoStore';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData, orderAddress } from '@/store/sharedInitialStates';
import { HandOverSelection } from '@/types/hand-over';
import { OrderType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

const DirectionOpposites = {
  [OrderType.RoadCollectionOrder]: OrderType.RoadForwardingOrder,
  [OrderType.RoadForwardingOrder]: OrderType.RoadCollectionOrder,

  [OrderType.AirImportOrder]: OrderType.AirExportOrder,
  [OrderType.AirExportOrder]: OrderType.AirImportOrder,

  [OrderType.SeaImportOrder]: OrderType.SeaExportOrder,
  [OrderType.SeaExportOrder]: OrderType.SeaImportOrder,
};

export const useTransportDirection = () => {
  const { orderType, selectedCustomer } = storeToRefs(useCreateOrderFormStore());
  const addressStore = useCreateOrderAddressesStore();
  const {
    shipperAddress,
    shipperHandOverSelection,
    consigneeAddress,
    consigneeHandOverSelection,
    contactDataShipper,
    contactDataConsignee,
    differentConsigneeAddress,
    consigneeAddressType,
    port,
  } = storeToRefs(addressStore);

  const toggle = () => {
    orderType.value = DirectionOpposites[orderType.value];
    updateAddresses();
  };

  const isImportOrder = computed(() =>
    [OrderType.RoadCollectionOrder, OrderType.AirImportOrder, OrderType.SeaImportOrder].includes(
      orderType.value,
    ),
  );
  const isExportOrder = computed(() =>
    [OrderType.RoadForwardingOrder, OrderType.AirExportOrder, OrderType.SeaExportOrder].includes(
      orderType.value,
    ),
  );

  async function updateAddresses() {
    const preShipperAddress = { ...shipperAddress.value };
    const preContactDataShipper = { ...contactDataShipper.value };
    const preConsigneeAddress = { ...consigneeAddress.value };
    const preContactDataConsignee = { ...contactDataConsignee.value };

    // Reset port routing
    addressStore.resetPortRoutingForSelection(
      shipperHandOverSelection.value,
      'from',
      HandOverSelection.default,
    );

    addressStore.resetPortRoutingForSelection(
      consigneeHandOverSelection.value,
      'to',
      HandOverSelection.default,
    );

    addressStore.removeFurtherAddress(FurtherAddressTypesList.coverAddressConsignor);

    addressStore.removeFurtherAddress(FurtherAddressTypesList.finalDeliveryAddress);

    // Reset addresses
    if (!selectedCustomer.value?.address) {
      consigneeAddress.value = { address: { ...orderAddress() } };
      contactDataConsignee.value = { ...contactData() };
      shipperAddress.value = { address: { ...orderAddress() } };
      contactDataShipper.value = { ...contactData() };
      return;
    }

    addressStore.setCustomerAddressToPrincipal();

    // Swap addresses
    if (isImportOrder.value) {
      consigneeAddress.value = shipperAddress.value;
      contactDataConsignee.value = contactDataShipper.value;
      if (consigneeHandOverSelection.value.selection === HandOverSelection.port) {
        port.value = undefined;
        consigneeHandOverSelection.value = {
          selection: HandOverSelection.default,
        };
      }
      shipperAddress.value = preConsigneeAddress;
      contactDataShipper.value = preContactDataConsignee;
    } else if (isExportOrder.value) {
      shipperAddress.value = consigneeAddress.value;
      contactDataShipper.value = contactDataConsignee.value;
      consigneeAddress.value = preShipperAddress;
      contactDataConsignee.value = preContactDataShipper;
    }

    if (orderType.value === OrderType.RoadForwardingOrder) {
      differentConsigneeAddress.value = { address: { ...orderAddress() } };
      consigneeAddressType.value = ConsigneeAddressType.PRINCIPALS_ADDRESS;
    }

    await useEmbargoStore().fetchHasEmbargo(
      shipperAddress.value.address.countryCode,
      consigneeAddress.value.address.countryCode,
    );
  }

  return { toggle, isImportOrder, isExportOrder };
};
