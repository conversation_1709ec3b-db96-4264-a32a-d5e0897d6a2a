import CardMain from '@/components/createOrder/formSectionCustomer/forwardingOrder/CardMain.vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { OrderStatus, OrderType } from '@dfe/dfe-book-api';

const mockCustomer = {
  customerNumber: '00000001',
};

describe('Customer ForwardingOrder CardMain component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(CardMain);
    expect(wrapper.exists()).toBeTruthy();
  });

  it('is empty if there are less than two customers', async () => {
    const store = useCreateOrderDataStore();

    expect(wrapper.html()).not.toContain('.card-forwarding-order');
    expect(wrapper.findComponent({ name: 'v-card' }).exists()).toBe(false);

    store.customers = [mockCustomer];
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'v-card' }).exists()).toBe(false);
  });

  it('shows card component if there are at least two customers', async () => {
    const store = useCreateOrderDataStore();
    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'v-card' }).exists()).toBe(true);
  });

  it('principal address should be disabled if order is status complete ', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const createOrderStore = useCreateOrderFormStore();
    const store = useCreateOrderDataStore();
    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(false);

    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.RoadForwardingOrder;
    createOrderStore.orderData = {
      orderType: OrderType.RoadForwardingOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(true);
  });

  it('Principal selection should NOT be disabled and should NOT show tooltip, if principal is NOT locked', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const createOrderStore = useCreateOrderFormStore();
    const store = useCreateOrderDataStore();

    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.RoadForwardingOrder;
    createOrderStore.orderData = {
      orderType: OrderType.RoadForwardingOrder,
      principalLocked: false,
    };

    await wrapper.vm.$nextTick();

    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('labels.status_complete_tooltip.text');
    expect(tooltipHTML).toContain('display: none;');
    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(false);
  });

  it('Principal selection should be disabled and show a tooltip if principal is locked', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const createOrderStore = useCreateOrderFormStore();
    const store = useCreateOrderDataStore();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(false);

    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.RoadForwardingOrder;
    createOrderStore.orderData = {
      orderType: OrderType.RoadForwardingOrder,
      principalLocked: true,
    };

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(true);
    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('labels.cannot_edit_order.text');
  });

  it('Principal selection should NOT be disabled, if OrderStatus is undefined', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const store = useCreateOrderDataStore();
    const { orderData } = storeToRefs(useCreateOrderFormStore());

    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.AirExportOrder;

    orderData.value = {
      orderType: OrderType.AirExportOrder,
      orderId: undefined,
    };

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(false);

    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('labels.status_complete_tooltip.text');
    expect(tooltipHTML).toContain('display: none;');
  });

  it('(AIR) Principal selection should be disabled and show a tooltip if order has been saved (Order status not undefined)', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const store = useCreateOrderDataStore();
    const { orderData } = storeToRefs(useCreateOrderFormStore());

    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.AirExportOrder;

    orderData.value = {
      orderType: OrderType.AirExportOrder,
      orderId: 123,
    };

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(true);
    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('labels.cannot_change_saved_order.text');
  });

  it('(ROAD) Principal selection should be disabled and show a tooltip if order has been saved (Order status not undefined)', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const store = useCreateOrderDataStore();
    const { orderData } = storeToRefs(useCreateOrderFormStore());

    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.RoadForwardingOrder;

    orderData.value = {
      orderType: OrderType.RoadForwardingOrder,
      orderId: 123,
    };

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(true);
    const tooltipHTML = document.body.querySelector('.v-overlay__content')?.outerHTML;
    expect(tooltipHTML).toContain('labels.cannot_change_saved_order.text');
  });
});
