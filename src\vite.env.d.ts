/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_DFE_BOOK_API_URL: string;
  readonly VITE_APP_DFE_ADDRESS_API_URL: string;
  readonly VITE_APP_DFE_CONFIG_API_URL: string;
  readonly VITE_APP_DFE_PLATFORM_API_URL: string;
  readonly VITE_APP_DFE_LOGGING_API_URL: string;
  readonly VITE_APP_DFE_DYNAMICLABEL_API_URL: string;
  readonly VITE_APP_DFE_ENVIRONMENT: string;
  readonly VITE_APP_KEYCLOAK_URL: string;
  readonly VITE_APP_KEYCLOAK_REALM: string;
  readonly VITE_APP_KEYCLOAK_CLIENT_ID: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
