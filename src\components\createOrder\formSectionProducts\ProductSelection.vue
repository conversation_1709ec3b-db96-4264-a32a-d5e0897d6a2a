<template>
  <VRow>
    <VCol cols="12" xl="8">
      <VRadioGroup
        class="d-flex flex-wrap radio-group"
        :model-value="modelValue?.code"
        :rules="validationRules"
        :disabled="isDisabled"
        @update:model-value="onChange"
      >
        <VRow>
          <VCol
            v-for="product in products"
            :key="product.code"
            class="py-0 radio-group__item"
            cols="12"
            lg="6"
          >
            <SelectCard
              :label="product.description"
              :description="product.hint"
              :value="product.code"
              name="product-selection"
            />
          </VCol>
        </VRow>
      </VRadioGroup>
    </VCol>
  </VRow>
</template>

<script setup lang="ts">
import SelectCard from '@/components/form/SelectCard.vue';
import { useValidationRules } from '@/composables/form/useValidationRules';
import type { DeliveryProduct, DeliveryProducts } from '@dfe/dfe-book-api';
import type { PropType } from 'vue';
import { computed } from 'vue';

const props = defineProps({
  products: {
    type: Array as PropType<DeliveryProducts>,
    required: true,
  },
  required: {
    type: Boolean,
    required: false,
    default: false,
  },
  isDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
});
const modelValue = defineModel<DeliveryProduct | null>({ default: null });

const onChange = (code: DeliveryProduct['code'] | null) => {
  modelValue.value = props.products.find?.((product) => product.code === code) ?? null;
};

const validationRules = computed(() => [
  props.required ? useValidationRules.requiredWithoutText : () => true,
]);
</script>

<style scoped lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables' as dfevars;
@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use '@/styles/base' as base;
@use 'sass:map';

:deep(.v-radio) {
  margin-bottom: 8px;
}

.radio-group {
  gap: 12px;
}

.radio-group__item {
  .select-card {
    margin-top: base.space(2);
  }

  &:first-child {
    .select-card {
      margin-top: 0;
    }
  }

  @media #{map.get(settings.$display-breakpoints, 'lg')} {
    &:nth-child(2) {
      .select-card {
        margin-top: 0;
      }
    }
  }
}
</style>
