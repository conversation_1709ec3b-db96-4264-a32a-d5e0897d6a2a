<template>
  <AddButton
    v-data-test="'add-un-number'"
    :class="props.buttonClass"
    :label="t('labels.un_number.text')"
    variant="text"
    @add-new-item="showModal = true"
  />

  <ModalWrapper
    v-if="showModal"
    v-model="showModal"
    :headline="t('labels.select_un.text')"
    size="xl"
    :no-body-padding="true"
    @close="closeModal"
  >
    <template #body>
      <div ref="tableContainer" class="dfe-table-container">
        <DfeDataTable
          ref="unNumberSearchResultTable"
          v-model:table-options="tableOptions"
          v-model:headers="tableHeaders"
          :items="items"
          :cell-attach="tableContainer"
          :is-loading="isLoading"
          @select-items="setSelectedItem"
        >
          <template #caption>
            <div class="pa-6 un-number_caption text-left">
              <DfeInputTextField
                v-model="searchText"
                v-data-test="'dangerous-goods-search-field'"
                :input-id="searchFieldId"
                :label="t('labels.search_label.text')"
                :placeholder="t('labels.un_number.text')"
                :append-icon="SearchIcon"
                class="size-xl"
              />
            </div>
          </template>
          <template #emptyTable>
            <h2 class="text-h2 mb-4">{{ tablePlaceholder.heading }}</h2>
            <p class="text-body-2 text-grey-darken-2">{{ tablePlaceholder.text }}</p>
          </template>
        </DfeDataTable>
      </div>
    </template>
    <template #footer>
      <VBtn
        size="small"
        color="primary"
        :disabled="!selectedItem"
        @click.prevent="confirmSelectedItem"
      >
        {{ t('labels.select_label.text') }}
      </VBtn>
      <VBtn size="small" color="primary" variant="text" @click="closeModal">
        {{ t('labels.cancel_label.text') }}
      </VBtn>
    </template>
  </ModalWrapper>
</template>
<script setup lang="ts">
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';
import { ref, watch } from 'vue';
import DfeDataTable from '@/components/tableComponent/DfeDataTable.vue';
import { useDangerousGoodsTableData } from '@/components/tableComponent/useDangerousGoodsTableData';
import { useI18n } from 'vue-i18n';
import { DangerousGoodDataItem } from '@dfe/dfe-book-api';
import { useUnNumberSearch } from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/useUnNumberSearch';
import SearchIcon from '@dfe/dfe-frontend-styles/assets/icons/search-16px.svg';
import { DfeInputTextField } from '@dfe/dfe-frontend-shared-components';
import { createUuid } from '@/utils/createUuid';
import AddButton from '@/components/createOrder/AddButton.vue';
import { DfeDataTableExpose } from '@/components/tableComponent/DfeTableTypes';

interface Props {
  buttonClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  buttonClass: '',
});
const emit = defineEmits(['select-item']);

const showModal = ref(false);
const searchText = ref('');
const selectedItem = ref<DangerousGoodDataItem | null>(null);
const tableContainer = ref<HTMLElement | undefined>(undefined);
const unNumberSearchResultTable = ref<DfeDataTableExpose | null>(null);
const searchFieldId = `dgsearch-${createUuid()}`;

const { t } = useI18n();
const { tableOptions, tableHeaders } = useDangerousGoodsTableData();
const { data: items, isLoading, tablePlaceholder } = useUnNumberSearch(searchText);

watch(items, () => {
  clearSelectedItem();
});

const setSelectedItem = (items: DangerousGoodDataItem[]) => {
  selectedItem.value = items[0];
};

const confirmSelectedItem = () => {
  if (selectedItem.value) {
    emit('select-item', selectedItem.value);
  }
  closeModal();
};

const clearSelectedItem = () => {
  selectedItem.value = null;
  unNumberSearchResultTable.value?.onClearSelection();
};

const closeModal = () => {
  searchText.value = '';
  clearSelectedItem();
  showModal.value = false;
};
</script>

<style lang="scss" scoped>
@use '@/styles/settings';
@use 'sass:map';

$un-number-field-xl: 429px;

.dfe-table-container {
  position: relative;
  height: 100%;

  .un-number_caption {
    .size-xl {
      width: $un-number-field-xl;
      max-width: $un-number-field-xl;

      @media #{map.get(settings.$display-breakpoints, 'md-and-down')} {
        width: 100%;
      }
    }
  }
}
</style>
