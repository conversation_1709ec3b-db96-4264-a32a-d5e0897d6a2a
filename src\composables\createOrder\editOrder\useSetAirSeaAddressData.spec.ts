import { OrderTypes } from '@/enums';
import { HandOverSelection } from '@/types/hand-over';
import { AirExportOrder, RoadForwardingOrder, SeaExportOrder } from '@dfe/dfe-book-api';
import {
  getConsigneeHandOverSelection,
  getShipperHandOverSelection,
} from './useSetAirSeaAddressData';
import { addresses } from '@/mocks/fixtures/addresses';

const airOrder = { orderType: OrderTypes.AirExportOrder } as AirExportOrder;

const seaOrder = { orderType: OrderTypes.SeaExportOrder } as SeaExportOrder;

const roadOrder = { orderType: OrderTypes.RoadForwardingOrder } as RoadForwardingOrder;

describe('getHandOverSelection', () => {
  it('should return HandOverSelection for air orders', () => {
    // shipper
    expect(getShipperHandOverSelection({ ...airOrder, pickupAddress: addresses[0] })).toBe(
      HandOverSelection.alternateAddress,
    );
    expect(getShipperHandOverSelection({ ...airOrder, deliverToAirport: true })).toBe(
      HandOverSelection.port,
    );
    expect(getShipperHandOverSelection(airOrder)).toBe(HandOverSelection.default);

    // consignee
    expect(getConsigneeHandOverSelection({ ...airOrder, deliveryAddress: addresses[0] })).toBe(
      HandOverSelection.alternateAddress,
    );
    expect(getConsigneeHandOverSelection({ ...airOrder, collectFromAirport: true })).toBe(
      HandOverSelection.port,
    );
    expect(getConsigneeHandOverSelection(airOrder)).toBe(HandOverSelection.default);
  });

  it('should return HandOverSelection for sea orders', () => {
    // shipper
    expect(getShipperHandOverSelection({ ...seaOrder, deliverToPort: true })).toBe(
      HandOverSelection.port,
    );
    expect(getShipperHandOverSelection(seaOrder)).toBe(HandOverSelection.default);

    // consignee
    expect(getConsigneeHandOverSelection({ ...seaOrder, collectFromPort: true })).toBe(
      HandOverSelection.port,
    );
    expect(getConsigneeHandOverSelection(seaOrder)).toBe(HandOverSelection.default);
  });

  it('should return HandOverSelection for road orders', () => {
    // shipper
    expect(getShipperHandOverSelection(roadOrder)).toBe(HandOverSelection.default);

    // consignee
    expect(getConsigneeHandOverSelection(roadOrder)).toBe(HandOverSelection.default);
  });
});
