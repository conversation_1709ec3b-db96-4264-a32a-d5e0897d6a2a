// Backend uses label.try_again_later where as frontend uses labels.try_again_later.text for the same label in translations tool. This method converts the key syntax
export const useBackendLabelKey = (label: string) => {
  if (/^label\./.exec(label)) {
    return label.replace('label', 'labels').concat('.text');
  }
  if (/^message\./.exec(label)) {
    return label.replace('message.text.', 'messages.id').concat('.text');
  }
  return label;
};
