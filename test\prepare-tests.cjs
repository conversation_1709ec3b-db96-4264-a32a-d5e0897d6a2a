/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const fs = require('fs');

// TODO: DFE-2703 Remove this file when the API is stable
const mockLocale = path.resolve('./src/i18n/locales/de.json');
if (!fs.existsSync(mockLocale)) {
  fs.mkdirSync(path.dirname(mockLocale), { recursive: true });
  fs.writeFileSync(
    mockLocale,
    JSON.stringify({
      labels: { foo: { text: 'foo' } },
      messages: { bar: { text: 'bar' } },
    }),
  );
}
