import { Customers, Division } from '@dfe/dfe-book-api';
import { addresses } from './addresses';

export const customers: Customers = [
  {
    customerNumber: '00000006',
    label: 'Dyson Ireland Ltd.',
    division: Division.EUROPEAN_LOGISTICS,
  },
  {
    customerNumber: '00000002',
    label: 'Flink CPS',
    division: Division.EUROPEAN_LOGISTICS,
    cashOnDelivery: false,
  },
  {
    customerNumber: '00000001',
    label: 'Flink CPS, Nationwide CO. LTD',
    division: Division.FOOD_LOGISTICS,
    cashOnDelivery: true,
  },
  {
    customerNumber: '00000004',
    label: 'Ikea2, Brunnthal',
    division: Division.EUROPEAN_LOGISTICS,
  },
  {
    customerNumber: '00000005',
    label: 'Ikea3',
    division: Division.EUROPEAN_LOGISTICS,
  },
];

export const customersWithAddresses = customers.map((customer) => {
  return {
    ...customer,
    address: addresses.find((address) => address.id === +(customer?.customerNumber ?? 0)),
  };
});
