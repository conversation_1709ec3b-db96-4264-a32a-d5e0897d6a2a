import DatePicker from '@/components/form/DatePicker.vue';
import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';

const date = new Date(`1999-11-11T00:00:00`);
const dateISO = '1999-11-11';

const props = {
  value: date,
};

const label = 'Label';
const requiredChar = '*';

describe('Date picker component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = shallowMount(DatePicker, {
      props,
    });
  });

  it('emits update event on change in date picker', async () => {
    wrapper.vm.$emit('update:modelValue', dateISO);

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'].at(0)).toEqual([dateISO]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });
});
