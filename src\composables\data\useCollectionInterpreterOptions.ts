import { useQuery } from '@tanstack/vue-query';
import { useInit } from '@/composables/useInit';
import { computed } from 'vue';

export const useCollectionInterpreterOptions = () => {
  const { api } = useInit();
  const query = useQuery({
    queryKey: ['collectionInterpreterOptions'],
    async queryFn() {
      const result = await api.book.collectionInterpreterOptions.getCollectionInterpreterOptions();
      return result.data;
    },
    staleTime: Infinity,
  });

  const selectableCollectionInterpreterOptions = computed(() => {
    return query.data?.value?.map((option) => ({
      code: option?.code?.toUpperCase() || '',
      description: option?.description || '',
    })) || [];
  });

  return {
    selectableCollectionInterpreterOptions,
    ...query,
  };
};
