<template>
  <div class="d-flex flex-column mt-2 mb-3">
    <VDivider class="my-2" />
    <div class="d-flex align-center">
      <slot />
      <InfoButtonWithTooltip :class="classStyle" :label="label" location="right" :max-width="260" />
    </div>
  </div>
</template>
<script setup lang="ts">
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';

interface Props {
  label: string;
  classStyle?: string;
}

withDefaults(defineProps<Props>(), {
  label: '',
  classStyle: '',
});
</script>
