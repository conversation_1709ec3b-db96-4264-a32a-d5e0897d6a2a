import NumberField from '@/components/form/NumberField.vue';
import * as validation from '@/composables/form/useValidationRules';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { beforeEach } from 'vitest';

const label = 'Label';
const requiredChar = '*';
const hint = 'Hint';
const placeholder = 'Placeholder';

const inputSpy = vi.spyOn(window.HTMLInputElement.prototype, 'validity', 'get');
const mockInputValidity = {
  valid: true,
  badInput: false,
  customError: false,
  patternMismatch: false,
  rangeOverflow: false,
  rangeUnderflow: false,
  stepMismatch: false,
  tooLong: false,
  tooShort: false,
  typeMismatch: false,
  valueMissing: false,
};

const spyOnGetMessages = vi
  .spyOn(validation, 'getMessages' as never)
  .mockReturnValue('error message');

describe('NumberField component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(NumberField);
  });

  it('emits input event with numberic value', () => {
    const textField = wrapper.findComponent({ name: 'v-text-field' });
    textField.vm.$emit('update:modelValue', '1');

    const emitted = wrapper.emitted();
    expect(emitted['update:modelValue']).toBeTruthy();
    expect(emitted['update:modelValue']).toHaveLength(1);
    expect(emitted['update:modelValue'][0]).toEqual([1]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });

  it('displays hint depending on prop', async () => {
    expect(wrapper.find('.v-messages__message').exists()).toBe(false);

    await wrapper.setProps({ hint });

    const hintEl = wrapper.find('.v-messages__message');
    expect(hintEl.exists()).toBe(true);
    expect(hintEl.text()).toEqual(hint);
  });

  it('displays placeholder depending on prop', async () => {
    expect(wrapper.find('input').attributes('placeholder')).toBe(undefined);

    await wrapper.setProps({ placeholder });

    expect(wrapper.find('input').attributes('placeholder')).toBe(placeholder);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it('shows manual error message based on input validity', async () => {
    const textField = wrapper.findComponent({ name: 'v-text-field' });
    const input = textField.find('input');
    await wrapper.setProps({ required: false });

    expect(wrapper.find('.v-input__details').exists()).toBe(false);

    inputSpy.mockReturnValueOnce({ ...mockInputValidity, valid: false });
    textField.vm.$emit('update:modelValue', 'e');
    await textField.vm.$emit('update:focused', false);
    await wrapper.vm.$nextTick();
    expect(spyOnGetMessages).toHaveBeenCalled();
    expect(textField.find('.v-input__details').exists()).toBe(true);

    await textField.vm.$emit('update:focused', true);
    await wrapper.vm.$nextTick();
    expect(textField.find('.v-input__details').exists()).toBe(false);

    inputSpy.mockReturnValueOnce({ ...mockInputValidity, valid: true });
    textField.vm.$emit('update:modelValue', '123');
    await input.trigger('blur');
    await textField.vm.$emit('update:focused', false);
    expect(textField.find('.v-input__details').exists()).toBe(false);
  });
});
