export function base64(data: string) {
  const binaryString = window.atob(data);
  return {
    toArrayBuffer() {
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes.buffer;
    },
    toBlob() {
      return new Blob([new Uint8Array(this.toArrayBuffer())]);
    },
  };
}
