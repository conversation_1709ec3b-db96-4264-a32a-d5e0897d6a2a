import useOptionDisplayText from '@/composables/createOrder/useOptionDisplayText';
import { Option } from '@dfe/dfe-book-api';

describe('useOptionDisplayText composable', () => {
  it('returns empty string if port is null or empty', () => {
    expect(useOptionDisplayText(null)).toBe('');
    expect(useOptionDisplayText({} as Option)).toBe('');
  });

  it('returns code or description only', () => {
    expect(useOptionDisplayText({ code: 'code12' })).toBe('code12');
    expect(useOptionDisplayText({ description: 'description34' } as Option)).toBe('description34');
  });

  it('returns code and description combined', () => {
    const option = {
      code: 'code12',
      description: 'description34',
    };
    expect(useOptionDisplayText(option)).toBe('code12 – description34');
  });
});
