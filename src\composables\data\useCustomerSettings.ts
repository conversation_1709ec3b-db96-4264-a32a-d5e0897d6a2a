import { useInit } from '@/composables/useInit';
import { useCustomerQuery } from '@/composables/data/useCustomerQuery';
import { computed, watch } from 'vue';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useClient } from '@/composables/useClient';

export const useCustomerSettings = () => {
  const { api } = useInit();
  const collectionAndDeliveryStore = useCreateOrderFormCollectionAndDeliveryStore();
  const accountingAdditionalServicesStore = useCreateOrderFormAccountingAdditionalServices();
  const orderLineStore = useCreateOrderOrderLineFormStore();
  const { client } = useClient();

  const formStore = useCreateOrderFormStore();
  const { customerNumber, transportType } = storeToRefs(formStore);

  const q = useCustomerQuery('customerSettings', api.book.customers.getCustomerSettings, {
    enabled: computed(() => !!customerNumber.value && !!transportType.value),
  });

  watch(
    q.data,
    (settings) => {
      if (!settings) return;
      const {
        manualNumberOfLabels,
        palletLocation,
        selfCollection,
        frostProtection,
        packagingAidPosition,
      } = settings;

      if (!frostProtection) {
        accountingAdditionalServicesStore.frostProtectionRequired = false;
      }
      if (!palletLocation) {
        accountingAdditionalServicesStore.palletLocationsNumber = undefined;
      }
      if (!selfCollection) {
        collectionAndDeliveryStore.selfCollection = false;
      }
      if (!manualNumberOfLabels) {
        orderLineStore.manualNumberOfLabels = undefined;
      }
      orderLineStore.packagingAidPosition = packagingAidPosition;

      // TODO: Replace once PAM setting is available
      orderLineStore.isFullContainerLoadAllowed = !client?.env.isProduction;
    },
    { immediate: true },
  );

  return q;
};
