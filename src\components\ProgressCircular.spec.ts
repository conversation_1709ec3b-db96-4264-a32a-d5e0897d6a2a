import ProgressCircular from '@/components/ProgressCircular.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { beforeAll, beforeEach } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';

const props = {
  color: 'primary',
  size: 24,
};

describe('ProgressCircular component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(ProgressCircular, {
      props,
    });
  });

  it('adds width class depending on size prop', async () => {
    await wrapper.setProps({ size: 24 });
    expect(wrapper.classes()).toContain('width-2');

    await wrapper.setProps({ size: 48 });
    expect(wrapper.classes()).toContain('width-4');
  });

  it('adds indeterminate class depending on prop', async () => {
    await wrapper.setProps({ indeterminate: true });
    expect(wrapper.classes()).toContain('indeterminate');
  });
});
