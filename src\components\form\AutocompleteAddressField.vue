<template>
  <div>
    <label v-if="label" :for="id" class="text-label-3 d-block mb-1">
      {{ label }}<span v-if="required" class="label-indicator">*</span>
    </label>
    <VAutocomplete
      :id="id"
      v-model:search="searchText"
      :items="menuItems"
      :placeholder="placeholder"
      :item-title="itemTextKey"
      :item-value="itemValueKey"
      :single-line="true"
      variant="outlined"
      item-props.color="grey darken-4"
      :menu-props="{ scrollStrategy: 'close', attach: $appRoot, maxWidth: '100%' }"
      :hide-no-data="!showNoDataSlot"
      bg-color="white"
      :custom-filter="filter"
      :no-filter="staticMenu || showLoader"
      hide-details="auto"
      :return-object="true"
      :rules="rules"
      @update:search="onSearchInput"
      @focus="onFocus"
      @update:model-value="onChange"
      @keydown.enter.prevent
    >
      <template v-if="!showLoader" #item="{ item, props: slotProps }">
        <VListItem v-bind="slotProps" title="">
          <p v-if="showCustomerAddressesTitle" class="text-body text-caption">
            {{ t('labels.principal_title.text') }}
          </p>
          <VListItemTitle v-if="item" class="text-wrap">
            <!-- eslint-disable vue/no-v-html -->
            <div
              v-if="item.raw.individualId"
              class="text-body-3 text-grey-darken-2"
              v-html="setHighlight(displayIndividualId(item.raw))"
            />
            <div class="text-label-2" v-html="setHighlight(displayNames(item.raw))" />
            <div
              class="text-body-3 text-grey-darken-2 display-address"
              v-html="setHighlight(displayAddress(item.raw))"
            />
            <!--eslint-enable-->
          </VListItemTitle>
        </VListItem>
      </template>
      <template v-else #item>
        <VListItem class="d-flex justify-center align-center">
          <progress-circular color="primary" size="24" indeterminate />
        </VListItem>
      </template>
      <template #append-inner>
        <span class="text-grey-darken-2 mt-1">
          <SearchIcon />
        </span>
      </template>
      <template #no-data>
        <div v-data-test="'no-data-slot'" class="text-body-2 text-grey-500 px-3 py-2">
          {{ $t('labels.no_matching_results.text') }}
        </div>
      </template>
    </VAutocomplete>
  </div>
</template>

<script setup lang="ts">
import ProgressCircular from '@/components/ProgressCircular.vue';
import useHighlight from '@/composables/createOrder/useHighlight';
import { useValidationRules, type ValidationRule } from '@/composables/form/useValidationRules';
import { useDebouncedLoader } from '@/composables/useDebouncedLoader';
import { SanitizeFallback, SanitizeKey } from '@/types/sanitize';
import type { FilterFunction } from '@/types/vuetify';
import { isAddress } from '@/utils/address/isAddress';
import { createUuid } from '@/utils/createUuid';
import type { Address } from '@dfe/dfe-address-api';
import { isIrelandCountryCode } from '@dfe/dfe-frontend-composables';
import SearchIcon from '@dfe/dfe-frontend-styles/assets/icons/search-16px.svg';
import { computed, inject, ref, toRefs, watch } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';

interface Props {
  items: Address[];
  itemText?: string;
  itemValue?: string;
  maxLength?: number;
  label?: TranslateResult;
  required?: boolean;
  staticMenu?: boolean;
  loading?: boolean;
  showLoaderAfterMs?: number;
  placeholder?: TranslateResult;
  filter?: FilterFunction;
  rules?: ValidationRule[];
  showCustomerAddressesTitle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  maxLength: 100,
  required: false,
  staticMenu: false,
  loading: false,
  showLoaderAfterMs: 500,
  returnObject: true,
  showCustomerAddressesTitle: false,
  itemText: '',
  itemValue: '',
  label: '',
  placeholder: '',
  filter: undefined,
  rules: undefined,
});

const address = defineModel<Partial<Address>>();

const isSearchPending = ref(false);
const { loading, showLoaderAfterMs } = toRefs(props);
const showLoader = useDebouncedLoader(loading, showLoaderAfterMs);

const emit = defineEmits(['focus', 'search-input']);

const { t } = useI18n();

const sanitize = inject(SanitizeKey, SanitizeFallback);
const id = `autocomplete-${createUuid()}`;
const searchText = ref('');

const onChange = (value: Address | string | null) => {
  if (isAddress(value)) {
    address.value = value;
  }
};

const onFocus = (event: Event) => {
  const target = event.target as HTMLInputElement;
  target.click();
  emit('focus');
};

const onSearchInput = (value: string | null) => {
  searchText.value = value ?? '';
  emit('search-input', value);

  if (searchText.value.length > 2) {
    isSearchPending.value = true;
  }
};

const menuItems = computed(() => (showLoader.value ? [''] : props.items));

const itemTextKey = computed(() => props.itemText ?? 'text');
const itemValueKey = computed(() => props.itemValue ?? 'value');

const setHighlight = (text: string) => {
  return sanitize(useHighlight(text, searchText.value), {
    allowedAttributes: { mark: ['class'] },
  });
};

const displayIndividualId = (item: Address) => {
  return item.individualId ?? '';
};

const displayNames = (item: Address) => {
  return [item.name, item.name2, item.name3].filter(Boolean).join(', ');
};

const displayAddress = (item: Address) => {
  const address = isIrelandCountryCode(item.countryCode)
    ? [item.street, `${item.countryCode} ${item.postcode} ${item.city}, ${item.supplement}`]
    : [item.street, `${item.countryCode} ${item.postcode} ${item.city}`];
  return address.filter(Boolean).join(', ');
};

const showNoDataSlot = computed(() => {
  return searchText.value.length > 2 && !isSearchPending.value;
});

watch(loading, (isLoading) => {
  if (!isLoading) {
    isSearchPending.value = false;
  }
});

const rules = computed(() => [
  ...(props.maxLength ? [useValidationRules.maxChars(props.maxLength)] : []),
  ...(props.rules ?? []),
]);
</script>

<style lang="scss" scoped>
:deep(.v-autocomplete__menu-icon) {
  // Important can be used here as this icon must never be visible
  display: none !important;
}
</style>
