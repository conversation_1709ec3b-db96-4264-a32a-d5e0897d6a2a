import { mount, VueWrapper } from '@vue/test-utils';
import { beforeAll, beforeEach } from 'vitest';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import AutocompleteMultipleContactsField from '@/components/form/AutocompleteMultipleContactsField.vue'; // Assuming contactsMock is defined elsewhere

const props = {
  items: [
    { id: 1, name: '<PERSON>' },
    { id: 2, name: '<PERSON>' },
    { id: 3, name: '<PERSON>' },
  ],
};

const label = 'Label';
const requiredChar = '*';

describe('AutocompleteMultipleContactsField.vue', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(AutocompleteMultipleContactsField, {
      props,
      global: {
        stubs: ['v-menu'],
      },
    });
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.exists()).toBe(true);
    expect(labelEl.text()).toEqual(label);
  });
});
