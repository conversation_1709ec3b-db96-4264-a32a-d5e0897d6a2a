import useCollectionTimeSlotOptions from '@/composables/dateTimeUtilities/useCollectionTimeSlotOptions';
import { useCreateOrderFormCollectionAndDeliveryStore } from '@/store/createOrder/formCollectionAndDelivery';
import {
  isAirOrder,
  isRoadCollectionOrder,
  isRoadForwardingOrder,
  isRoadOrder,
  OrderResponseBody,
} from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
import { LoadOrderOptions } from './useEditOrder';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { useFormatInUTC, useTimeFromDate } from '@/composables/dateTimeUtilities/useFormatInUTC';

export function useSetCollectionDeliveryData(
  editOrderData: OrderResponseBody,
  { timeFormat, locale }: LoadOrderOptions,
) {
  const {
    deliveryProduct,
    deliveryOption,
    selectedAirDeliveryProduct,
    collectionTimeSlot,
    timeSlotOptions,
    dateDelivery,
    tailLiftDelivery,
    tailLiftCollection,
    selfCollection,
    date,
    customCollectionTimeSlot,
    requestArrangement,
    collectionInterpreterOption,
    cashOnDelivery,
    cashOnDeliveryAmount,
  } = storeToRefs(useCreateOrderFormCollectionAndDeliveryStore());
  const { orderGroup } = storeToRefs(useCreateOrderFormAccountingAdditionalServices());

  if (isRoadOrder(editOrderData)) {
    deliveryProduct.value = editOrderData.product ?? '';
    tailLiftDelivery.value = editOrderData.tailLiftDelivery ?? false;

    if (editOrderData.fixDate) {
      dateDelivery.value = useFormatInUTC(editOrderData.fixDate, locale);
    }
  }

  if (isRoadCollectionOrder(editOrderData)) {
    collectionInterpreterOption.value = editOrderData.interpreter ?? '';
    tailLiftCollection.value = editOrderData.tailLiftCollection ?? false;
  }

  if (isRoadForwardingOrder(editOrderData)) {
    orderGroup.value = editOrderData.orderGroup ?? '';
    selfCollection.value = editOrderData.selfCollection ?? false;
    cashOnDelivery.value = editOrderData.cashOnDelivery ?? false;
    cashOnDeliveryAmount.value = editOrderData.cashOnDeliveryAmount ?? null;
  } else {
    orderGroup.value = '';
  }

  if (isAirOrder(editOrderData)) {
    selectedAirDeliveryProduct.value = editOrderData.product;
    requestArrangement.value = editOrderData.requestArrangement ?? false;
    tailLiftCollection.value = editOrderData.tailLiftCollection ?? false;
  }

  deliveryOption.value = editOrderData.deliveryOption ?? '';

  const collectionTimeSlots = useCollectionTimeSlotOptions(
    ref(editOrderData.collectionTime ? [editOrderData.collectionTime] : undefined),
    timeFormat,
  );
  collectionTimeSlot.value = collectionTimeSlots[0];
  timeSlotOptions.value = collectionTimeSlots;

  if (editOrderData.collectionTime?.collectionDate) {
    date.value = useFormatInUTC(editOrderData?.collectionTime?.collectionDate, locale);

    customCollectionTimeSlot.value.collectionDate = useFormatInUTC(
      editOrderData?.collectionTime.collectionDate,
      locale,
    ).toISOString();

    customCollectionTimeSlot.value.from = useTimeFromDate(
      editOrderData?.collectionTime.from ?? '',
      timeFormat,
    );
    customCollectionTimeSlot.value.to = useTimeFromDate(
      editOrderData?.collectionTime.to ?? '',
      timeFormat,
    );
  }
}
