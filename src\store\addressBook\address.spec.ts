import { initPinia } from '../../../test/util/init-pinia';
import { mockServer } from '@/mocks/server';
import { useCreateAddressDataStore } from '@/store/addressBook/address';
import { addresses } from '@/mocks/fixtures/addresses';
import { contactDataMock, contactDataUpdated } from '@/mocks/fixtures/contactData';

describe('createAddressData store', () => {
  beforeAll(() => {
    initPinia();

    mockServer({
      environment: 'test',
      fixtures: {
        addresses,
        contactData: contactDataMock,
        contactDataUpdated,
      },
    });
  });

  it('should add contact to address and return a new contact data id', async () => {
    const store = useCreateAddressDataStore();

    const contactDataId = await store.addContactToAddress(0, contactDataMock);

    expect(contactDataId).toEqual(1);
  });

  it('searches name in address book', async () => {
    const store = useCreateAddressDataStore();

    await store.searchNameInAddressBook('Ikea');

    expect(store.addresses.search).toHaveLength(3);
  });

  it('gets address contacts', async () => {
    const store = useCreateAddressDataStore();

    await store.getAddressContacts(1, 'delivery');
    expect(store.contactsDelivery).toEqual([contactDataMock]);

    await store.getAddressContacts(1, 'collection');
    expect(store.contactsCollection).toEqual([contactDataMock]);

    await store.getAddressContacts(1, 'customer');
    expect(store.contactsCustomer).toEqual([contactDataMock]);
  });

  it('updates an existing contact and returns its updated object', async () => {
    const store = useCreateAddressDataStore();

    const newContact = {
      id: 1,
      name: 'Walt Disney',
      email: '<EMAIL>',
      telephone: '9876555',
      mobile: '786697607',
    };
    await store.updateAddressContact(1, 1, newContact);
    expect(contactDataUpdated).toEqual(newContact);
  });

  it('should upsertAddress correctly', () => {
    const addressDataStore = useCreateAddressDataStore();
    const addAddressSpy = vi.spyOn(addressDataStore.api.address.v1, 'addAddressV1');
    const updateAddressSpy = vi.spyOn(addressDataStore.api.address.v1, 'updateAddressV1');

    addressDataStore.upsertAddress({
      name: 'Name 1',
      name2: 'name 2',
      name3: 'name',
      street: 'TestStreet 2',
      postcode: '1234',
      city: 'TestCity',
      countryCode: 'FR',
    });

    expect(addAddressSpy).toHaveBeenCalled();
    expect(updateAddressSpy).not.toHaveBeenCalled();

    addAddressSpy.mockClear();
    updateAddressSpy.mockClear();

    addressDataStore.upsertAddress({
      id: 123,
      originAddressId: 123,
      name: 'Name 1',
      name2: 'name 2',
      name3: 'name',
      street: 'TestStreet 2',
      postcode: '1234',
      city: 'TestCity',
      countryCode: 'FR',
    });

    expect(addAddressSpy).not.toHaveBeenCalled();
    expect(updateAddressSpy).toHaveBeenCalled();

    addAddressSpy.mockClear();
    updateAddressSpy.mockClear();

    addressDataStore.upsertAddress({
      id: 123,
      originAddressId: 123,
      name: 'Name 1',
      name2: 'name 2',
      name3: 'name',
      street: 'TestStreet 2',
      city: 'TestCity',
      countryCode: 'FR',
    });

    expect(addAddressSpy).not.toHaveBeenCalled();
    expect(updateAddressSpy).toHaveBeenCalled();
  });
});
