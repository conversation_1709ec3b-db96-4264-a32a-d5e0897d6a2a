import { FurtherAddressTypesList, OrderTypes } from '@/enums';
import { addresses } from '@/mocks/fixtures/addresses';
import { customers } from '@/mocks/fixtures/customers';
import { airExportOrder, seaExportOrder } from '@/mocks/fixtures/order';
import { ports } from '@/mocks/fixtures/ports';
import { isThirdCountryConstellation } from '@/mocks/fixtures/thirdCountryConstellation';
import { mockServer } from '@/mocks/server';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData, loadingPointAddress } from '@/store/sharedInitialStates';
import { HandOverSelection } from '@/types/hand-over';
import type { AirExportQuoteInformation, OrderAddress } from '@dfe/dfe-book-api';
import { PortType, Segment } from '@dfe/dfe-book-api';
import { UserInformation } from '@dfe/dfe-frontend-client';
import { initPinia } from '@test/util/init-pinia';
import { storeToRefs } from 'pinia';

const mockAddress = {
  id: undefined,
  name: '',
  name2: '',
  name3: '',
  countryCode: undefined,
  street: '',
  street2: '',
  postcode: '',
  city: '',
  supplement: '',
  gln: undefined,
};

describe('createOrderFormAddresses store', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        isThirdCountryConstellation,
        addresses,
        customers,
        airExportOrder,
        ports,
      },
    });
  });

  beforeEach(() => {
    initPinia();
  });

  it('returns fromAirportDisplayName', () => {
    const store = useCreateOrderAddressesStore();
    store.fromIATA = {
      code: 'ZRH',
      name: 'Zurich',
      countryCode: 'CH',
      type: PortType.AIRPORT,
    };

    expect(store.fromAirportDisplayName).toEqual('ZRH - Zurich');
  });

  it('returns toPortDisplayName', () => {
    const store = useCreateOrderAddressesStore();
    store.toIATA = {
      code: 'ZRH',
      name: 'Zurich',
      countryCode: 'CH',
      type: PortType.AIRPORT,
    };

    expect(store.toAirportDisplayName).toEqual('ZRH - Zurich');
  });

  it('adds new further address', () => {
    const store = useCreateOrderAddressesStore();

    store.addNewFurtherAddress('test');

    expect(store.$state.furtherAddresses).toEqual([
      {
        address: mockAddress,
        addressType: 'test',
      },
    ]);
  });

  it('removes further address', () => {
    const store = useCreateOrderAddressesStore();

    store.addNewFurtherAddress('test');
    store.addNewFurtherAddress('test2');
    store.removeFurtherAddress('test');

    expect(store.$state.furtherAddresses).toEqual([
      {
        address: mockAddress,
        addressType: 'test2',
      },
    ]);
  });

  it('returns further address by type', () => {
    const store = useCreateOrderAddressesStore();

    store.addNewFurtherAddress(FurtherAddressTypesList.importer);

    expect(store.getFurtherAddress(FurtherAddressTypesList.importer)?.address).toEqual(mockAddress);
  });

  it('sets customer address to loading point', async () => {
    const store = useCreateOrderAddressesStore();
    const dataStore = useCreateOrderDataStore();
    const formStore = useCreateOrderFormStore();
    const { customerNumber } = storeToRefs(formStore);

    await dataStore.fetchCustomers(Segment.ROAD);
    customerNumber.value = String(customers[1].customerNumber);

    expect(store.loadingPoint).toEqual(loadingPointAddress());

    store.setCustomerAddressToLoadingPoint();
    expect(store.loadingPoint).not.toEqual(loadingPointAddress());
    expect(store.loadingPoint).toEqual(addresses[1]);
  });

  it('resets loading point', () => {
    const store = useCreateOrderAddressesStore();

    store.loadingPoint = {
      name: 'test',
      city: 'test',
      countryCode: 'test',
      street: 'test',
      postcode: 'test',
    };

    expect(store.loadingPoint).not.toEqual(loadingPointAddress());
    store.resetLoadingPoint();
    expect(store.loadingPoint).toEqual(loadingPointAddress());
  });

  it('updates isThirdCountryConstellation correctly', async () => {
    const store = useCreateOrderAddressesStore();
    const apiSpy = vi.spyOn(
      store.api.book.thirdCountryConstellation,
      'isThirdCountryConstellation',
    );

    // Testing the initial state; should always be false
    expect(store.isThirdCountryAddress).toBe(false);

    await store.updateThirdCountryState();

    // Updating with the initial state must not alter the result
    expect(store.isThirdCountryAddress).toBe(false);

    store.shipperAddress.address.countryCode = 'DE';
    store.consigneeAddress.address.countryCode = '';

    await store.updateThirdCountryState();

    // shipper address has been filled
    expect(store.isThirdCountryAddress).toBe(false);

    store.shipperAddress.address.countryCode = '';
    store.consigneeAddress.address.countryCode = 'DE';

    await store.updateThirdCountryState();

    // consignee address has been filled instead
    expect(store.isThirdCountryAddress).toBe(false);

    store.shipperAddress.address.countryCode = 'DE';
    store.consigneeAddress.address.countryCode = 'DE';

    await store.updateThirdCountryState();

    // The same country has been chosen (the api should've gotten a request but returned false)
    expect(store.isThirdCountryAddress).toBe(false);
    expect(apiSpy).toHaveBeenCalledTimes(1);

    store.shipperAddress.address.countryCode = 'DE';
    store.consigneeAddress.address.countryCode = 'HU';

    await store.updateThirdCountryState();

    // Now a different country has been selected, now the api must return true
    expect(store.isThirdCountryAddress).toBe(true);
    expect(apiSpy).toHaveBeenCalledTimes(2);
    apiSpy.mockReset();

    store.shipperAddress.address.countryCode = 'DE';
    store.consigneeAddress.address.countryCode = '';

    await store.updateThirdCountryState();

    expect(store.isThirdCountryAddress).toBe(false);
    expect(apiSpy).not.toHaveBeenCalled();
  });

  it('should update routing from airport', async () => {
    const store = useCreateOrderAddressesStore();
    const { shipperAddress, shipperHandOverSelection, fromIATA } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.AirExportOrder;
    transportType.value = Segment.AIR;

    shipperAddress.value.address = <OrderAddress>airExportOrder.shipperAddress;

    fromIATA.value = undefined;

    await store.updateRoutingForSelection(
      shipperHandOverSelection.value,
      'from',
      HandOverSelection.default,
    );

    expect(fromIATA.value).toEqual(ports[0]);
  });

  it('should update routing to airport', async () => {
    const store = useCreateOrderAddressesStore();
    const { consigneeAddress, consigneeHandOverSelection, toIATA } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.AirExportOrder;
    transportType.value = Segment.AIR;

    consigneeAddress.value.address = <OrderAddress>airExportOrder.consigneeAddress;

    toIATA.value = undefined;

    await store.updateRoutingForSelection(
      consigneeHandOverSelection.value,
      'to',
      HandOverSelection.default,
    );

    expect(toIATA.value).toEqual(ports[0]);
  });

  it('should not update routing to airport when order is from Quote', async () => {
    const store = useCreateOrderAddressesStore();
    const formStore = useCreateOrderFormStore();
    const { consigneeAddress, consigneeHandOverSelection, toIATA } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { orderType, transportType } = storeToRefs(formStore);

    orderType.value = OrderTypes.AirExportOrder;
    transportType.value = Segment.AIR;
    formStore.quoteInformation = {
      quoteRequestId: 123,
      orderType: OrderTypes.AirExportOrder,
    } as AirExportQuoteInformation;

    consigneeAddress.value.address = <OrderAddress>airExportOrder.consigneeAddress;

    await store.updateRoutingForSelection(
      consigneeHandOverSelection.value,
      'to',
      HandOverSelection.default,
    );

    expect(toIATA.value).toEqual(undefined);
  });

  it('should update routing from port', async () => {
    const store = useCreateOrderAddressesStore();
    const { shipperAddress, shipperHandOverSelection, fromPort } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.SeaExportOrder;
    transportType.value = Segment.SEA;

    shipperAddress.value.address = <OrderAddress>seaExportOrder.shipperAddress;

    fromPort.value = undefined;

    await store.updateRoutingForSelection(
      shipperHandOverSelection.value,
      'from',
      HandOverSelection.default,
    );

    expect(fromPort.value).toEqual(ports[4]);
  });

  it('should update routing to port', async () => {
    const store = useCreateOrderAddressesStore();
    const { consigneeAddress, consigneeHandOverSelection, toPort } = storeToRefs(
      useCreateOrderAddressesStore(),
    );
    const { orderType, transportType } = storeToRefs(useCreateOrderFormStore());

    orderType.value = OrderTypes.SeaExportOrder;
    transportType.value = Segment.SEA;

    consigneeAddress.value.address = <OrderAddress>seaExportOrder.consigneeAddress;

    toPort.value = undefined;

    await store.updateRoutingForSelection(
      consigneeHandOverSelection.value,
      'to',
      HandOverSelection.default,
    );

    expect(toPort.value).toEqual(ports[4]);
  });

  it('returns the original address type of an air export order from quote based on the handover selection', () => {
    const addressStore = useCreateOrderAddressesStore();
    const formStore = useCreateOrderFormStore();

    expect(addressStore.quoteAirAddressesOrigin).toEqual({
      shipper: null,
      consignee: null,
    });

    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '********',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;

    expect(addressStore.quoteAirAddressesOrigin).toEqual({
      shipper: HandOverSelection.default,
      consignee: HandOverSelection.default,
    });

    addressStore.shipperHandOverSelection.selection = HandOverSelection.alternateAddress;
    addressStore.consigneeHandOverSelection.selection = HandOverSelection.port;

    expect(addressStore.quoteAirAddressesOrigin).toEqual({
      shipper: HandOverSelection.alternateAddress,
      consignee: HandOverSelection.port,
    });
  });

  it('should return true if shipperHandOverSelection is airport', () => {
    const { shipperHandOverSelection, isShipperHandOverSelectionPort } = storeToRefs(
      useCreateOrderAddressesStore(),
    );

    shipperHandOverSelection.value.selection = HandOverSelection.default;

    expect(isShipperHandOverSelectionPort.value).toBe(false);

    shipperHandOverSelection.value.selection = HandOverSelection.alternateAddress;

    expect(isShipperHandOverSelectionPort.value).toBe(false);

    shipperHandOverSelection.value.selection = HandOverSelection.port;

    expect(isShipperHandOverSelectionPort.value).toBe(true);
  });

  it('should return true if consigneeHandOverSelection is airport', () => {
    const { consigneeHandOverSelection, isConsigneeHandOverSelectionPort } = storeToRefs(
      useCreateOrderAddressesStore(),
    );

    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    expect(isConsigneeHandOverSelectionPort.value).toBe(false);

    consigneeHandOverSelection.value.selection = HandOverSelection.alternateAddress;

    expect(isConsigneeHandOverSelectionPort.value).toBe(false);

    consigneeHandOverSelection.value.selection = HandOverSelection.port;

    expect(isConsigneeHandOverSelectionPort.value).toBe(true);
  });

  it('should set isShipperAddressDisabled to true, when setCustomerAddressToPrincipal is called', async () => {
    const store = useCreateOrderAddressesStore();
    const dataStore = useCreateOrderDataStore();
    const formStore = useCreateOrderFormStore();
    const { customerNumber } = storeToRefs(formStore);

    await dataStore.fetchCustomers(Segment.ROAD);
    customerNumber.value = String(customers[0].customerNumber);

    store.setCustomerAddressToPrincipal();
    expect(store.isShipperAddressDisabled).toBe(true);
  });

  it('setDefaultContactDataShipper only if no contact is set yet', () => {
    const store = useCreateOrderAddressesStore();
    const testContact: UserInformation = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    };
    store.setDefaultContactDataShipper(testContact, {
      mobileNumber: '',
      phoneNumber: '',
    });
    expect(store.contactDataShipper).toMatchObject({
      ...contactData(),
      email: testContact.email,
      name: `${testContact.firstName} ${testContact.lastName}`,
    });

    store.setDefaultContactDataShipper(
      {
        email: '<EMAIL>',
      },
      {
        mobileNumber: '',
        phoneNumber: '',
      },
    );
    expect(store.contactDataShipper.email).toEqual(testContact.email);
  });

  it('setDefaultContactDataConsignee only if no contact is set yet', () => {
    const store = useCreateOrderAddressesStore();
    const testContact: UserInformation = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    };
    store.setDefaultContactDataConsignee(testContact, {
      mobileNumber: '',
      phoneNumber: '',
    });
    expect(store.contactDataConsignee).toMatchObject({
      ...contactData(),
      email: testContact.email,
      name: `${testContact.firstName} ${testContact.lastName}`,
    });

    store.setDefaultContactDataConsignee(
      {
        email: '<EMAIL>',
      },
      {
        mobileNumber: '',
        phoneNumber: '',
      },
    );
    expect(store.contactDataConsignee.email).toEqual(testContact.email);
  });
  it('sets townCounty if the principal address is an irish address with wrong Eircode (Dachser code)', async () => {
    const addressStore = useCreateOrderAddressesStore();
    const dataStore = useCreateOrderDataStore();
    const { townCounty } = storeToRefs(useCreateOrderAddressesStore());
    const { shipperAddress } = storeToRefs(addressStore);
    const { orderType, customerNumber } = storeToRefs(useCreateOrderFormStore());

    await dataStore.fetchCustomers(Segment.ROAD);

    orderType.value = OrderTypes.RoadForwardingOrder;
    customerNumber.value = String(customers[0].customerNumber);

    addressStore.setCustomerAddressToPrincipal();

    expect(townCounty.value?.data.town).toEqual(shipperAddress.value.address.city);
  });

  it('sets contact name to firstName and lastName, if length is lower or equal to 30', () => {
    const store = useCreateOrderAddressesStore();
    const testContact: UserInformation = {
      email: '<EMAIL>',
      firstName: 'Maximiliano',
      lastName: 'Alexander',
    };

    store.setDefaultContactDataShipper(testContact, {
      mobileNumber: '',
      phoneNumber: '',
    });

    expect(store.contactDataShipper).toMatchObject({
      ...contactData(),
      email: testContact.email,
      name: `${testContact.firstName} ${testContact.lastName}`,
    });
  });

  it('sets contact name to lastName, if length is greater than 30', () => {
    const store = useCreateOrderAddressesStore();
    const testContact: UserInformation = {
      email: '<EMAIL>',
      firstName: 'Maximiliano',
      lastName: 'Alexander von Hohenberg',
    };

    store.setDefaultContactDataShipper(testContact, {
      mobileNumber: '',
      phoneNumber: '',
    });

    expect(store.contactDataShipper).toMatchObject({
      ...contactData(),
      email: testContact.email,
      name: `${testContact.lastName}`,
    });
  });
});
