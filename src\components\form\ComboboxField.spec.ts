import { mount } from '@vue/test-utils';
import ComboboxField from '@/components/form/ComboboxField.vue';
import { beforeAll, beforeEach } from 'vitest';
import { nextTick } from 'vue';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import type { TestUtils } from '../../../test/test-utils';

const props = {
  items: ['Select 0', 'Select 1', 'Select 2'],
};
const mockInput = props.items[0];
const mockSearch = 'Search';
const label = 'Label';
const requiredChar = '*';

describe('ComboboxField component', () => {
  let wrapper: TestUtils.VueWrapper<typeof ComboboxField>;

  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    wrapper = mount(ComboboxField, {
      props,
      attachTo: document.body,
      global: {
        stubs: ['v-menu'],
      },
    });
  });

  it('emits input event on change', async () => {
    await wrapper.findComponent({ name: 'v-combobox' }).vm.$emit('update:modelValue', mockInput);

    const event = wrapper.emitted('update:modelValue');

    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([mockInput]);
  });

  it('emit search event', async () => {
    await wrapper.findComponent({ name: 'v-combobox' }).vm.$emit('update:search', mockSearch);

    const event = wrapper.emitted('search-input');
    expect(event).toHaveLength(1);
    expect(event?.[0]).toEqual([mockSearch]);
  });

  it('displays label depending on prop', async () => {
    expect(wrapper.find('label').exists()).toBe(false);

    await wrapper.setProps({ label });

    const labelEl = wrapper.find('label');
    expect(labelEl.text()).toEqual(label);
  });

  it('marks component as required', async () => {
    await wrapper.setProps({ label, required: true });

    await nextTick();

    expect(wrapper.find('label').text()).toContain(requiredChar);
  });
});
