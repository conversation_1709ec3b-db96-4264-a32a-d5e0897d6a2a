import CardMain from '@/components/createOrder/formSectionCollectionAndDelivery/CardMain.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import CollectionAndDeliveryRoad from '@/components/createOrder/formSectionCollectionAndDelivery/road/CollectionAndDeliveryRoad.vue';
import CollectionAirAndSea from '@/components/createOrder/formSectionCollectionAndDelivery/airAndSea/CollectionAirAndSea.vue';

describe('Collection & Delivery - CardMain component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(CardMain);
  });

  it("shows 'collection-and-delivery-road' and hide 'collection-air-and-sea'", async () => {
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.ROAD;
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(CollectionAndDeliveryRoad).exists()).toBe(true);
    expect(wrapper.findComponent(CollectionAirAndSea).exists()).toBe(false);
  });

  it("shows 'collection-air-and-sea' and hide 'collection-and-delivery-road'", async () => {
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;
    formStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(CollectionAirAndSea).exists()).toBe(true);
    expect(wrapper.findComponent(CollectionAndDeliveryRoad).exists()).toBe(false);

    formStore.transportType = Segment.SEA;
    formStore.orderType = OrderTypes.SeaExportOrder;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(CollectionAirAndSea).exists()).toBe(true);
    expect(wrapper.findComponent(CollectionAndDeliveryRoad).exists()).toBe(false);
  });
});
