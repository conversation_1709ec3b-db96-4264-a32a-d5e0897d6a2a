import { afterAll, afterEach, beforeEach, describe, expect } from 'vitest';
import { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import AccountingForwarding from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AccountingForwarding.vue';
import LinkElement from '@/components/base/LinkElement.vue';
import { storeToRefs } from 'pinia';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import SelectField from '@/components/form/SelectField.vue';
import { mockServer } from '@/mocks/server';
import { freightTerms } from '@/mocks/fixtures/freightTerms';
import { useCreateOrderDataStore } from '@/store/createOrder/data';

describe('AccountForwarding', () => {
  let wrapper: VueWrapper;
  let server: ReturnType<typeof mockServer>;
  const orderAddressesStore = useCreateOrderAddressesStore();

  beforeAll(() => {
    server = mockServer({
      environment: 'test',
      fixtures: {
        freightTerms,
      },
    });
  });

  beforeEach(() => {
    wrapper = shallowMount(AccountingForwarding);
  });

  afterEach(() => {
    wrapper.unmount();
    orderAddressesStore.$reset();
  });

  afterAll(() => {
    server.shutdown();
  });

  it('should have an incoterm link', () => {
    const link = wrapper.findComponent(LinkElement);

    expect(link.exists()).toBeTruthy();
  });

  it('should not have freight terms if countries are not set', () => {
    const freightTermSelect = wrapper.findComponent(SelectField);
    expect(freightTermSelect.props('items')).toHaveLength(0);
  });

  it('should fetch freight terms on mount if countries are set', async () => {
    const { shipperAddress, consigneeAddress } = storeToRefs(orderAddressesStore);

    shipperAddress.value.address = { countryCode: 'NL' };
    consigneeAddress.value.address = { countryCode: 'DE' };

    const wrapperWithData = shallowMount(AccountingForwarding);
    await wrapperWithData.vm.$nextTick();
    const freightTermSelect = wrapperWithData.findComponent(SelectField);

    await vi.waitFor(() => {
      expect(freightTermSelect.props('items')).toHaveLength(3);
    });
  });

  it('should fetch freight terms if countries change', async () => {
    const { shipperAddress, consigneeAddress } = storeToRefs(orderAddressesStore);
    const { freightTerms } = storeToRefs(useCreateOrderDataStore());
    freightTerms.value = [];
    const freightTermSelect = wrapper.findComponent(SelectField);

    await vi.waitFor(() => {
      expect(freightTermSelect.props('items')).toHaveLength(0);
    });

    shipperAddress.value.address = { countryCode: 'NL' };
    consigneeAddress.value.address = { countryCode: 'DE' };

    await vi.waitFor(() => {
      expect(freightTermSelect.props('items')).toHaveLength(3);
    });
  });
});
