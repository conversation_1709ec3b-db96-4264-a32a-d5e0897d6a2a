import useLoadingPointsWithLabel from '@/composables/createOrder/useLoadingPointsWithLabel';
import { customersWithAddresses } from '@/mocks/fixtures/customers';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { loadingPointAddress } from '@/store/sharedInitialStates';
import type { Address } from '@dfe/dfe-book-api';
import { ref } from 'vue';
import { initPinia } from '../../../test/util/init-pinia';

const mockLoadingPoints = ref<Address[]>([
  {
    id: 0,
    name: 'Flink CPS',
    name2: 'Test name2',
    name3: 'Test name3',
    street: 'Hauptstraße 12',
    city: 'Rottenburg am Neckar',
    postcode: '72108',
    countryCode: 'DE',
  },
]);

const mockInvalidLoadingPoint: Address = {
  id: 123,
  name: 'foo',
  name2: 'name2',
  name3: 'name3',
  city: 'city',
  street: 'street',
  countryCode: '',
  postcode: '12345',
};

const mockCustomer = customersWithAddresses[0];
console.log(mockCustomer);
describe('useLoadingPoints composable', () => {
  beforeAll(() => {
    initPinia();
  });

  it('execute create loadingPointsWithLabel', () => {
    const expectedResult = [
      {
        id: 0,
        label: 'Flink CPS, Test name2, Test name3, Rottenburg am Neckar',
        address: {
          id: 0,
          name: 'Flink CPS',
          name2: 'Test name2',
          name3: 'Test name3',
          street: 'Hauptstraße 12',
          city: 'Rottenburg am Neckar',
          postcode: '72108',
          countryCode: 'DE',
        },
      },
    ];

    const { loadingPointsWithLabel } = useLoadingPointsWithLabel(mockLoadingPoints);
    expect(loadingPointsWithLabel.value).toEqual(expectedResult);
  });

  it('sets loading point', () => {
    const dataStore = useCreateOrderDataStore();
    dataStore.customers = customersWithAddresses;
    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = mockCustomer.customerNumber as string;

    const addressStore = useCreateOrderAddressesStore();
    const loadingPoints = ref<Address[]>([]);
    const { setLoadingPoint } = useLoadingPointsWithLabel(loadingPoints);

    // No loading points
    setLoadingPoint();
    expect(addressStore.loadingPoint).toEqual(loadingPointAddress());

    // Has loading points
    loadingPoints.value = mockLoadingPoints.value;
    setLoadingPoint();
    expect(addressStore.loadingPoint).toEqual(mockCustomer.address);

    // Selected loading point not in list
    addressStore.loadingPoint = mockInvalidLoadingPoint;
    setLoadingPoint();
    expect(addressStore.loadingPoint).toEqual(loadingPointAddress());
  });
});
