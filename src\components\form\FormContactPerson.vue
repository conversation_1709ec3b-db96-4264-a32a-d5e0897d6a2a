<template>
  <VCard v-data-test="'contact-form'" elevation="0">
    <AutocompleteMultipleContactsField
      ref="autocompleteContactField"
      v-model="contactValue"
      v-data-test="'contact-form-name'"
      :items="contacts"
      :placeholder="contacts?.length != 0 ? t('labels.select_enter_contact.text') : ''"
      item-value="name"
      item-text="name"
      :return-object="true"
      :label="t('labels.contact_name.text')"
      class="mt-8"
      :required="requiredFields?.includes('name')"
      :rules="contactNameRules"
      :hide-no-data="!contacts?.length"
      @update:model-value="updateContact($event)"
    />

    <TextField
      v-data-test="'contact-form-email'"
      :model-value="contactValue.email"
      :label="t('labels.email_label.text')"
      :required="requiredFields ? requiredFields.includes('email') : false"
      :max-length="
        isAirAndSeaOrder
          ? MaxLengthsAirAndSeaOrder.ContactDataEmail
          : MaxLengthsRoadOrder.ContactDataEmail
      "
      class="mt-3"
      :rules="[useValidationRules.email]"
      @update:model-value="updateValue('email', $event)"
    />
    <DfePhoneInput
      v-data-test="'contact-form-telephone'"
      :model-value="contactValue.telephone"
      :default-country="getDefaultCountry(String(contactValue.telephone))"
      input-id="form-telephone"
      :label="t('labels.phone_number.text')"
      :required="requiredFields ? requiredFields.includes('telephone') : false"
      :max-length="
        isAirAndSeaOrder
          ? MaxLengthsAirAndSeaOrder.ContactDataPhone
          : MaxLengthsRoadOrder.ContactDataPhone
      "
      class="mt-3"
      :country-attach="$appRoot"
      @update:model-value="updateValue('telephone', $event)"
    />
    <DfePhoneInput
      v-data-test="'contact-form-mobile'"
      :model-value="contactValue.mobile"
      :default-country="getDefaultCountry(String(contactValue.mobile))"
      input-id="form-mobile"
      :label="t('labels.mobile_number.text')"
      :required="requiredFields ? requiredFields.includes('mobile') : false"
      :max-length="
        isAirAndSeaOrder
          ? MaxLengthsAirAndSeaOrder.ContactDataPhone
          : MaxLengthsRoadOrder.ContactDataPhone
      "
      :class="{
        'mb-3': hint,
      }"
      class="mt-3"
      :country-attach="$appRoot"
      @update:model-value="updateValue('mobile', $event)"
    />
    <span v-if="hint" class="hint d-block text-body-2 text-grey-darken-2 mt-3 mb-1">{{
      hint
    }}</span>
  </VCard>
</template>

<script setup lang="ts">
import AutocompleteMultipleContactsField from '@/components/form/AutocompleteMultipleContactsField.vue';
import TextField from '@/components/form/TextField.vue';
import { useValidationRules } from '@/composables/form/useValidationRules';
import { ContactsType, MaxLengthsAirAndSeaOrder, MaxLengthsRoadOrder } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { contactData } from '@/store/sharedInitialStates';
import type { multipleContactDataKeyValueObject } from '@/types/createOrder';
import type { ContactData } from '@dfe/dfe-address-api';
import { DfePhoneInput } from '@dfe/dfe-frontend-shared-components';
import { update } from 'lodash';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref, watch } from 'vue';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';

interface Props {
  contacts?: ContactData[];
  addressId?: number;
  type?: (typeof ContactsType)[keyof typeof ContactsType];
  requiredFields?: Partial<keyof ContactData>[];
  hint?: TranslateResult;
  defaultCountry?: string;
}

const contactValue = defineModel<ContactData>({
  default: contactData(),
});

const { t } = useI18n();

const props = withDefaults(defineProps<Props>(), {
  requiredFields: () => [],
  contacts: undefined,
  addressId: undefined,
  type: undefined,
  hint: undefined,
  defaultCountry: undefined,
});

const createOrderFormStore = useCreateOrderFormStore();
const { isAirAndSeaOrder } = storeToRefs(createOrderFormStore);

const createOrderAddressesStore = useCreateOrderAddressesStore();
const { hasUnsavedAddressChanges } = storeToRefs(createOrderAddressesStore);

const initialContactData = ref<ContactData>({ ...contactValue.value });

const contactNameRules = computed(() => [
  ...(props.requiredFields.includes('name') ? [useValidationRules.required] : []),
  useValidationRules.maxCharsProperty<ContactData>(
    isAirAndSeaOrder.value ? MaxLengthsAirAndSeaOrder.Default : MaxLengthsRoadOrder.Default,
    'name',
  ),
]);

const updateHasUnsavedChanges = () => {
  hasUnsavedAddressChanges.value =
    JSON.stringify(contactValue.value) !== JSON.stringify(initialContactData.value);
};

const updateContact = (value?: ContactData | string) => {
  if (value && typeof value === 'object') {
    contactValue.value = value;
  } else {
    updateValue('name', value);
  }
};

const updateValue = (key: string, value?: string | number) => {
  contactValue.value = { ...contactValue.value, [key]: value };
};

const updateMultipleValues = (value: multipleContactDataKeyValueObject) => {
  let storeObjCopy = { ...contactValue.value };

  for (const [k, v] of Object.entries(value ?? contactData())) {
    if (k in storeObjCopy) {
      storeObjCopy = update(storeObjCopy, k, () => v);
    }
  }
  contactValue.value = storeObjCopy;
};

const setDefaultContact = () => {
  if (props.contacts && props.contacts.length >= 1) {
    const indexOfMainContact = props.contacts.findIndex((value) => value.isMainContact);
    if (indexOfMainContact !== -1) {
      updateMultipleValues(props.contacts[indexOfMainContact]);
    }
  }
};

function getDefaultCountry(phoneValue: string) {
  if (!phoneValue || !phoneValue.includes('+')) {
    if (props.defaultCountry) {
      return props.defaultCountry;
    }
    return navigator.language.split('-')[1];
  }
  return '';
}

onMounted(() => {
  if (props.addressId) {
    setDefaultContact();
  }
});

watch(
  () => props.contacts,
  () => {
    setDefaultContact();
  },
);

watch(
  () => contactValue.value,
  () => {
    updateHasUnsavedChanges();
  },
);
</script>
