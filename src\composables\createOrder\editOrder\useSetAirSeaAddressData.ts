import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import {
  HandOverSelection,
  HandOverSelectionType,
  HandOverSelectionValue,
} from '@/types/hand-over';
import {
  AirOrder,
  isAirOrder,
  isRoadOrder,
  isSeaOrder,
  OrderAddress,
  OrderResponseBody,
  SeaOrder,
} from '@dfe/dfe-book-api';
import { orderAddress } from '@/store/sharedInitialStates';
import { storeToRefs } from 'pinia';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { PortItem } from '@/composables/createOrder/usePortSearch';
import { Ref } from 'vue';

function getHandOverSelection(
  isAlternateSelection?: OrderAddress,
  isAirportSelection?: boolean,
): HandOverSelectionType {
  if (isAlternateSelection) {
    return HandOverSelection.alternateAddress;
  }

  if (isAirportSelection) {
    return HandOverSelection.port;
  }

  return HandOverSelection.default;
}

export function getShipperHandOverSelection(
  editOrderData: OrderResponseBody | null,
): HandOverSelectionType {
  if (isAirOrder(editOrderData)) {
    const isAlternateSelection = editOrderData.pickupAddress;
    const isAirportSelection = editOrderData.deliverToAirport;

    return getHandOverSelection(isAlternateSelection, isAirportSelection);
  } else if (isSeaOrder(editOrderData)) {
    const isPortSelection = editOrderData.deliverToPort;

    return getHandOverSelection(undefined, isPortSelection);
  }
  return getHandOverSelection();
}

export function getConsigneeHandOverSelection(
  editOrderData: OrderResponseBody | null,
): HandOverSelectionType {
  if (isAirOrder(editOrderData)) {
    const isAlternateSelection = editOrderData.deliveryAddress;
    const isAirportSelection = editOrderData.collectFromAirport;

    return getHandOverSelection(isAlternateSelection, isAirportSelection);
  } else if (isSeaOrder(editOrderData)) {
    const isPortSelection = editOrderData.collectFromPort;

    return getHandOverSelection(undefined, isPortSelection);
  }
  return getHandOverSelection();
}

async function setAlternateAddressSelectionValue(
  editOrderData: AirOrder | SeaOrder,
  fromOrTo: 'from' | 'to',
  handOverSelection: Ref<HandOverSelectionValue>,
) {
  if (handOverSelection.value.selection === HandOverSelection.alternateAddress) {
    handOverSelection.value.address =
      fromOrTo === 'from'
        ? editOrderData.pickupAddress ?? orderAddress()
        : editOrderData.deliveryAddress ?? orderAddress();

    await useCreateOrderDataStore().fetchPortRouting(fromOrTo, handOverSelection.value.address);
  }
}

export async function useSetAirSeaAddressData(editOrderData: OrderResponseBody) {
  const {
    port,
    fromIATA,
    toIATA,
    fromPort,
    toPort,
    shipperHandOverSelection,
    consigneeHandOverSelection,
  } = storeToRefs(useCreateOrderAddressesStore());

  if (isRoadOrder(editOrderData)) return;

  if (isAirOrder(editOrderData)) {
    fromIATA.value = editOrderData.fromIATA;
    toIATA.value = editOrderData.toIATA;

    if (editOrderData.deliverToAirport) {
      port.value = fromIATA.value;
    }

    if (editOrderData.collectFromAirport) {
      port.value = toIATA.value;
    }
  }

  if (isSeaOrder(editOrderData)) {
    fromPort.value = editOrderData.fromPort;
    toPort.value = editOrderData.toPort;

    if (editOrderData.deliverToPort) {
      port.value = fromPort.value;
    }

    if (editOrderData.collectFromPort) {
      port.value = toPort.value;
    }
  }

  if (port.value) {
    port.value.displayName = `${port.value?.name} (${port.value?.code})`;
  }

  shipperHandOverSelection.value.selection = getShipperHandOverSelection(editOrderData);

  await setAlternateAddressSelectionValue(editOrderData, 'from', shipperHandOverSelection);

  if (shipperHandOverSelection.value.selection === HandOverSelection.port) {
    shipperHandOverSelection.value.port = port.value as PortItem;
  }

  consigneeHandOverSelection.value.selection = getConsigneeHandOverSelection(editOrderData);

  await setAlternateAddressSelectionValue(editOrderData, 'to', consigneeHandOverSelection);

  if (consigneeHandOverSelection.value.selection === HandOverSelection.port) {
    consigneeHandOverSelection.value.port = port.value as PortItem;
  }
}
