<template>
  <div dfe-book-frontend class="w-100 overflow-visible">
    <VThemeProvider theme="light">
      <VLayout>
        <div class="w-100">
          <div class="v-application v-application--is-ltr" data-app>
            <VMain class="bg-grey-lighten-4">
              <div v-if="isLocaleLoaded" ref="bookFrontendContent" class="content" tabindex="0">
                <DeleteOverflowSSCCs />
                <CreateOrderRoad v-if="transportType === Segment.ROAD" class="pt-8" />
                <CreateOrderAir v-else-if="transportType === Segment.AIR" class="pt-8" />
                <CreateOrderSea v-else-if="transportType === Segment.SEA" class="pt-8" />
                <LoaderOverlay :model-value="!!isLoading"></LoaderOverlay>
              </div>
            </VMain>
          </div>
        </div>
      </VLayout>
    </VThemeProvider>
  </div>
</template>

<script setup lang="ts">
import DeleteOverflowSSCCs from '@/components/createOrder/sharedComponents/deleteOverflowSSCCs/DeleteOverflowSSCCs.vue';
import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import { useEditOrder } from '@/composables/createOrder/editOrder/useEditOrder';
import useInitialFocus from '@/composables/createOrder/useInitialFocus';
import useResetCreateOrderFormData from '@/composables/createOrder/useResetCreateOrderFormData';
import useValidateOrder from '@/composables/form/useValidateOrder';
import { useClient } from '@/composables/useClient';
import { OrderTypes } from '@/enums';
import { useLocale } from '@/plugins/i18n';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { useCreateOrderDocumentsStore } from '@/store/createOrder/orderDocuments';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { fullContainerLoadStorage } from '@/store/createOrder/useFullContainerLoad';
import { CreateOrder, EditBookOrderEvent } from '@/types/events';
import CreateOrderAir from '@/views/createOrder/air/AirMain.vue';
import CreateOrderRoad from '@/views/createOrder/road/RoadMain.vue';
import CreateOrderSea from '@/views/createOrder/sea/SeaMain.vue';
import { BasicOrder, OrderStatus, Segment } from '@dfe/dfe-book-api';
import { usePreferences } from '@dfe/dfe-frontend-composables';
import { storeToRefs } from 'pinia';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { client } = useClient();
const { t } = useI18n();
const { locale, timeFormat, mobileNumber, phoneNumber } = usePreferences();
const isLocaleLoaded = ref(false);
const bookFrontendContent = ref<HTMLDivElement | null>(null);

const createOrderFormStore = useCreateOrderFormStore();
const {
  customerNumber,
  transportType,
  isSaveLoading,
  isValidateLoading,
  isSubmitLoading,
  isEditMode,
  isOrderLoaded,
} = storeToRefs(createOrderFormStore);
const addressStore = useCreateOrderAddressesStore();
const labelStore = useLabelStore();
const { isFetchLabelsLoading } = storeToRefs(labelStore);
const isLoading = computed(() => {
  return (
    isSaveLoading.value ||
    isValidateLoading.value ||
    isSubmitLoading.value ||
    isFetchLabelsLoading.value
  );
});
const createOrderDataStore = useCreateOrderDataStore();
const { customers } = storeToRefs(createOrderDataStore);
const orderLineStore = useCreateOrderOrderLineFormStore();
const { isFullContainerLoad } = storeToRefs(orderLineStore);
const { loadOrder } = useEditOrder();

const { clearFormData } = useResetCreateOrderFormData();

useInitialFocus(bookFrontendContent);

const fetchCustomers = async () => {
  bookFrontendContent.value?.focus();
  await createOrderDataStore.fetchCustomers(createOrderFormStore.transportType);

  if (!createOrderFormStore.customerNumber) {
    customerNumber.value = customers?.value?.[0]?.customerNumber?.toString() ?? '';
  }
};

const resetOrderTypeBasedOnTransportType = (transportType: Segment) => {
  const { orderType } = storeToRefs(useCreateOrderFormStore());

  switch (transportType) {
    case Segment.ROAD:
      orderType.value = OrderTypes.RoadForwardingOrder;
      break;
    case Segment.AIR:
      orderType.value = OrderTypes.AirExportOrder;
      break;
    case Segment.SEA:
      orderType.value = OrderTypes.SeaExportOrder;
      break;
    default:
  }
};

const scrollToDocumentsSection = () => {
  const documentsSection = document.getElementById('documentsSection');

  if (documentsSection) {
    documentsSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  }
};
onMounted(async () => {
  await useLocale(locale);
  isLocaleLoaded.value = true;

  const clientCreateEvent = client?.events.queue.get('createOrder') as CreateOrder | undefined;
  const clientEditEvent = client?.events.queue.get('editOrder') as EditBookOrderEvent | undefined;
  if (clientCreateEvent) {
    createOrder(clientCreateEvent);
  } else if (clientEditEvent) {
    editOrder(clientEditEvent);
  }
  await createOrderFormStore.fetchCreationPreferences();
});

const createOrder = async (payload: CreateOrder) => {
  if (!client) return;
  clearFormData();

  if (payload.transportType) {
    createOrderFormStore.transportType = payload.transportType;
    resetOrderTypeBasedOnTransportType(payload.transportType);
    if (payload.transportType === Segment.SEA && isFullContainerLoad !== undefined) {
      isFullContainerLoad.value = fullContainerLoadStorage.value;

      if (isFullContainerLoad.value) {
        orderLineStore.removeAllOrderLines();
        orderLineStore.addFullContainerLoad(false);
      }
    }
  }
  await fetchCustomers();

  const userInformation = client.auth.getUserInformation();
  if (userInformation) {
    addressStore.setDefaultContactDataShipper(userInformation, {
      mobileNumber: mobileNumber.value,
      phoneNumber: phoneNumber.value,
    });
  }

  client?.events.emit('formLoaded');

  /**
   * The timeout is needed, because often the fromIATA attribute is not set yet when the order is loaded.
   * Then it will show the discard changes dialog when closing the form, even if the user didn't
   * do any changes. This is the only workaround we found so far.
   *
   * Ticket: (https://dil-itd.atlassian.net/browse/DFE-2756)
   */
  setTimeout(() => {
    const { order } = useValidateOrder();
    createOrderFormStore.setInitialOrder(order.value);
  }, 1000);
};

const editOrder = async (payload: EditBookOrderEvent) => {
  isEditMode.value = true;

  await createOrderFormStore.getOrder(payload.orderId);

  const { orderData } = storeToRefs(createOrderFormStore);

  if (!orderData.value) {
    return;
  }

  client?.events.emit('currentOrderStatus', {
    status: getStatus(orderData.value.orderStatus?.status),
  });

  transportType.value = getTransportType(orderData.value);

  await loadOrder(orderData.value, { timeFormat: timeFormat.value, locale: locale.value });
  await fetchCustomers();

  await useCreateOrderDocumentsStore().getOrderDocuments(payload.orderId);
  isOrderLoaded.value = true;
  if (payload.scrollToDocuments) {
    scrollToDocumentsSection();
  }
  const { order } = useValidateOrder();
  createOrderFormStore.setInitialOrder(order.value);

  client?.events.emit('formLoaded');
};

const handleCreateOrder = (evt: CreateOrder) => {
  createOrder(evt);
};
const handleEditOrder = (evt: EditBookOrderEvent) => {
  editOrder(evt);
};

client?.events.on('createOrder', handleCreateOrder);
client?.events.on('editOrder', handleEditOrder);

const getTransportType = (orderData: BasicOrder): Segment => {
  const orderType = orderData.orderType;

  if (!orderType || orderType.includes('Road')) {
    return Segment.ROAD;
  }
  if (orderType.includes('Air')) {
    return Segment.AIR;
  }
  if (orderType.includes('Sea')) {
    return Segment.SEA;
  }

  return Segment.ROAD;
};

const getStatus = (status: OrderStatus | undefined): string | undefined => {
  let statusTrad = '';
  if (!status) {
    return undefined;
  }

  switch (status) {
    case OrderStatus.LABEL_PENDING:
      statusTrad = t('labels.tab_labels_pending.text');
      break;
    case OrderStatus.COMPLETE:
      statusTrad = t('labels.order_complete.text');
      break;
    default:
      statusTrad = t(`labels.${status.toLowerCase()}.text`);
  }

  return statusTrad;
};

onBeforeUnmount(() => {
  client?.events.off('createOrder', handleCreateOrder);
  client?.events.off('editOrder', handleEditOrder);
});
</script>

<style lang="scss" scoped>
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';
@use '@/styles/overrides';
@use '@/styles/colorVars.scss';
[dfe-book-frontend] {
  overflow: auto;
}

.content {
  padding-left: vars.$spacing-grid-container-xs;
  padding-right: vars.$spacing-grid-container-xs;
  @media screen and (min-height: 560px) {
    margin-bottom: calc(100vh - 560px);
  }

  @media #{map.get(settings.$display-breakpoints, 'sm-and-up')} {
    padding-left: vars.$spacing-grid-container-sm;
    padding-right: vars.$spacing-grid-container-sm;
  }

  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    padding-left: vars.$spacing-grid-container-xl;
    padding-right: vars.$spacing-grid-container-xl;
  }

  :focus-visible {
    outline: none;
  }
}
</style>

<style lang="scss">
@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use 'sass:map';
.nav-scroll-section {
  scroll-margin: 32px;
}

:deep(.v-footer__inner) {
  background: var(--color-base-white);
  border-top: 1px solid var(--color-base-grey-400);
}

.size {
  &-xxl {
    width: vars.$form-input-width-xxl;

    @media #{map.get(settings.$display-breakpoints, 'md-and-down')} {
      max-width: 319px;
    }
  }

  &-xl {
    @media #{map.get(settings.$display-breakpoints, 'lg-and-up')} {
      width: vars.$form-input-width-xl;
    }
    @media #{map.get(settings.$display-breakpoints, 'md-and-up')} {
      width: 45%;
      max-width: 319px;
    }
    @media #{map.get(settings.$display-breakpoints, 'md-and-down')} {
      width: 40%;
      max-width: 319px;
    }
  }

  &-lg {
    width: vars.$form-input-width-lg;
  }

  &-md {
    width: vars.$form-input-width-md;
  }

  &-sm {
    width: vars.$form-input-width-sm;
    min-width: vars.$form-input-width-sm;
  }

  &-xs {
    width: vars.$form-input-width-xs;
    @media #{map.get(settings.$display-breakpoints, 'xs')} {
      flex: 1 0 0;
    }
  }
}
</style>
