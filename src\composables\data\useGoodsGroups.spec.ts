import { goodsGroupResponse } from '@/mocks/fixtures/goodsGroupResponse';
import { useGoodsGroups } from '@/composables/data/useGoodsGroups';
import { initPinia } from '../../../test/util/init-pinia';
import { mockServer } from '@/mocks/server';
import { withSetup } from '../../../test/util/with-setup';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';

describe('useGoodsGroups', () => {
  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        goodsGroupResponse,
      },
    });
  });

  beforeEach(() => {
    initPinia();
  });

  it.each([
    ['1', true],
    ['2', false],
    ['3', false],
  ])('returns boolean for %s if goods group has quantity activated', async (code, expected) => {
    const { isGoodsGroupQuantityActivated } = withSetup(() => useGoodsGroups())[0];

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;
    await vi.waitFor(() => {
      expect(isGoodsGroupQuantityActivated(code)).toBe(expected);
    });
  });
});
