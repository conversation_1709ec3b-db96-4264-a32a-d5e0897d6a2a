<template>
  <ModalWrapper
    v-model="isOpen"
    data-test-content="edit-modal"
    :headline="headline"
    :size="
      (isRoadForwardingOrder || isDifferentConsigneeAddress) && !isShipperAddress ? 'md' : 'lg'
    "
    @close="close"
  >
    <template #body>
      <slot></slot>
    </template>
    <template #footer>
      <div class="d-flex flex-wrap align-center">
        <VBtn
          v-data-test="'edit-modal-confirm-button'"
          variant="tonal"
          theme="dark"
          size="small"
          color="white"
          elevation="0"
          class="confirm-btn text-label-2"
          @click.prevent="confirm"
          >{{ _confirmText }}
        </VBtn>
        <VBtn
          v-data-test="'edit-modal-cancel-button'"
          size="small"
          variant="text"
          class="cancel-btn mr-3 text-primary text-label-2"
          @click="close"
        >
          {{ _cancelText }}
        </VBtn>
        <div class="edit-modal-footer-slot mb-3 mb-sm-0">
          <slot name="footer" />
        </div>
      </div>
    </template>
  </ModalWrapper>
</template>

<script setup lang="ts">
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';

import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

interface Props {
  headline: string;
  confirmText?: string;
  cancelText?: string;
  isShipperAddress?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  confirmText: '',
  cancelText: '',
});

const createOrderFormStore = useCreateOrderFormStore();
const { isRoadForwardingOrder } = storeToRefs(createOrderFormStore);

const addressStore = useCreateOrderAddressesStore();
const { isDifferentConsigneeAddress } = storeToRefs(addressStore);

const _confirmText = computed(() => props.confirmText ?? t('labels.apply_label.text'));
const _cancelText = computed(() => props.cancelText ?? t('labels.cancel_label.text'));

const isOpen = defineModel<boolean>();
const emit = defineEmits(['close', 'confirm']);

const close = () => {
  emit('close');
};
const confirm = () => {
  emit('confirm');
};
</script>
<style lang="scss" scoped>
@use 'sass:map';
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
@use '@/styles/settings';
:deep(.v-overlay__content) {
  display: flex;
  max-height: calc(100vh - 32px);
}

:deep(.v-card) {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1 1 100%;
}

:deep(.v-card__text) {
  backface-visibility: hidden;
  flex: 1 1 auto;
  overflow-y: auto;
}

:deep(.v-card-title),
:deep(.v-card-actions) {
  flex: 0 0 auto;
}

.confirm-btn {
  background-color: vars.$color-text-primary;
}

@media #{map.get(settings.$display-breakpoints, 'sm-and-down')} {
  :deep(.v-overlay__content) {
    align-self: flex-end;
    width: 100% !important;
  }

  :deep(.v-card) {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
}

@media #{map.get(settings.$display-breakpoints, 'xs')} {
  :deep(.edit-modal-footer-slot) {
    order: 1;
    width: 100%;
  }

  :deep(.confirm-btn) {
    order: 2;
  }

  :deep(.cancel-btn) {
    order: 3;
  }
}
</style>
