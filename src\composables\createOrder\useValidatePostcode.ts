import { useQuery } from '@tanstack/vue-query';
import { useInit } from '@/composables/useInit';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';

export const useValidatePostcode = () => {
  const addressStore = useCreateOrderAddressesStore();
  const { formAddress } = storeToRefs(addressStore);
  const { api } = useInit();
  const queryKey = ref(['validatePostcode', formAddress.value.countryCode]);
  let cancelToken: symbol;

  watch(
    formAddress,
    (newValue, oldValue) => {
      if (oldValue.countryCode !== newValue.countryCode) {
        queryKey.value = ['validatePostcode', newValue.countryCode];
      }
    },
    { deep: true },
  );

  return useQuery({
    queryKey: queryKey,
    enabled: false, // Is disabled because the validation happens not directly on blur, but on click on the "apply" button within the EditModal.
    queryFn: async () => {
      if (cancelToken) {
        api.book.abortRequest(cancelToken);
      }
      cancelToken = Symbol('validatePostcode');
      const result = await api.book.v1.validatePostcode(
        {
          postalCode: formAddress.value.postcode ?? '',
          countryCode: formAddress.value.countryCode ?? '',
        },
        { cancelToken },
      );
      return result.data;
    },
  });
};
