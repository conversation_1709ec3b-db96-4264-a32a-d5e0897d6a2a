import sanitizeNumbers from '@/utils/sanitizeInput';

describe('sanitizeNumbers', () => {
  it("should remove '-' from numbers", () => {
    const stringToTest = '-100';

    expect(sanitizeNumbers(stringToTest)).toBe(100);
  });

  it('should remove non-digit characters from numbers', () => {
    const stringToTest = '1a2b3c';

    expect(sanitizeNumbers(stringToTest)).toBe(123);
  });

  it("should allow decimal numbers when 'decimalAllowed' is true", () => {
    const stringToTest = '3.14';

    expect(sanitizeNumbers(stringToTest, true)).toBe(3.14);
  });
});
