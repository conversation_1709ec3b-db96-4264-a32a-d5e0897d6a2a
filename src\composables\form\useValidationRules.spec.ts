import {
  getMessages,
  hasValue,
  useValidationRules,
  withMessage,
} from '@/composables/form/useValidationRules';
import { i18n } from '@/plugins/i18n';

describe('useValidationRules composable', () => {
  describe('function hasValue', () => {
    it('returns true when value is a string', () => {
      expect(hasValue('value')).toBe(true);
    });

    it('returns true when value is a number', () => {
      expect(hasValue(123)).toBe(true);
    });

    it('returns false when value is an empty string', () => {
      expect(hasValue('')).toBe(false);
    });

    it('returns false when value is undefined', () => {
      expect(hasValue(undefined)).toBe(false);
    });

    it('returns false when value is null', () => {
      expect(hasValue(null)).toBe(false);
    });

    it('returns true when value is a Date', () => {
      expect(hasValue(new Date())).toBe(true);
    });
  });

  describe('useValidationRules', () => {
    it('returns correct error message for required validation', () => {
      expect(useValidationRules.required('')).not.toBe(true);
      expect(useValidationRules.required('value')).toBe(true);
    });

    it('returns correct error message for requiredCheckbox', () => {
      expect(useValidationRules.requiredCheckbox(false)).not.toBe(true);
      expect(useValidationRules.requiredCheckbox(true)).toBe(true);
    });

    it('returns correct error message for email validation', () => {
      expect(useValidationRules.email('invalidemail')).not.toBe(true);
      expect(useValidationRules.email('<EMAIL>')).toBe(true);
    });

    it('returns correct error message for phone validation', () => {
      expect(useValidationRules.phone('invalidphone')).not.toBe(true);
      expect(useValidationRules.phone('+************')).toBe(true);
    });

    it('returns correct error message for numbers validation', () => {
      expect(useValidationRules.numbers('invalidnumber')).not.toBe(true);
      expect(useValidationRules.numbers('123')).toBe(true);
    });

    it('returns correct error message for date validation', () => {
      expect(useValidationRules.date('dd.MM.yyyy')('invaliddate')).not.toBe(true);
      expect(useValidationRules.date('dd.MM.yyyy')('31.12.2020')).toBe(true);
      expect(useValidationRules.date('dd/MM/yyyy')('31/12/2020')).toBe(true);
    });

    it('returns correct error message for time validation', () => {
      expect(useValidationRules.time('HH:mm')('invalidtime')).not.toBe(true);
      expect(useValidationRules.time('HH:mm')('23:59')).toBe(true);
      expect(useValidationRules.time('hh:mm a')('11:59 pm')).toBe(true);
    });

    it('returns correct error message for gln validation', () => {
      expect(useValidationRules.gln('invalidgln')).not.toBe(true);
      expect(useValidationRules.gln('1234567890123')).toBe(true);
    });

    it('returns correct error message for ekaer validation', () => {
      expect(useValidationRules.ekaer('invalidekaer')).not.toBe(true);
      expect(useValidationRules.ekaer('1A23456789012A')).toBe(true);
    });
    it('returns correct error message for positive number', () => {
      expect(useValidationRules.positiveNumber('1')).toBe(true);
      expect(useValidationRules.positiveNumber('-1')).not.toBe(true);
      expect(useValidationRules.positiveNumber('0')).not.toBe(true);
      expect(useValidationRules.positiveNumber('value')).not.toBe(true);
      expect(useValidationRules.positiveNumber('')).toBe(true);
      expect(useValidationRules.positiveNumber('0.0001')).toBe(true);
    });
    it('returns correct error message for integer', () => {
      expect(useValidationRules.integer('1.234')).not.toBe(true);
      expect(useValidationRules.integer('1')).toBe(true);
    });
    it('returns correct error message for max chars', () => {
      const max = useValidationRules.maxChars(5);
      expect(max('hello world')).not.toBe(true);
      expect(max('hello')).toBe(true);
    });

    it('returns correct error message for max chars property', () => {
      type MaxCharsPropertyTest = {
        name?: string | number;
        address?: string;
      };
      const max = useValidationRules.maxCharsProperty<MaxCharsPropertyTest>(5, 'name') as (
        v?: MaxCharsPropertyTest,
      ) => string | boolean;
      expect(max()).toBe(true);
      expect(max({ address: 'hello' })).toBe(true);
      expect(max({ name: 111 })).toBe(true);
      expect(max({ name: 'hello world' })).not.toBe(true);
      expect(max({ name: 'hello' })).toBe(true);
    });

    it('returns correct error message for min', () => {
      const min = useValidationRules.min(5);
      expect(min('3')).not.toBe(true);
      expect(min('6')).toBe(true);
    });

    it('returns correct error message for max', () => {
      const max = useValidationRules.max(5);
      expect(max('6')).not.toBe(true);
      expect(max('3')).toBe(true);
    });

    it('returns correct error message for whitespace validation', () => {
      expect(useValidationRules.noWhitespace('')).not.toBe(true);
      expect(useValidationRules.noWhitespace(' ')).not.toBe(true);
      expect(useValidationRules.noWhitespace('\t')).not.toBe(true);
      expect(useValidationRules.noWhitespace(' content ')).toBe(true);
      expect(useValidationRules.noWhitespace('content')).toBe(true);
    });

    it('returns boolean if conditions is (not) fullfilled', () => {
      expect(useValidationRules.isConditionFulfilled(true)).toEqual(true);
      expect(useValidationRules.isConditionFulfilled(false)).toEqual(false);
    });

    it('returns correct error message for date range validation', () => {
      expect(
        useValidationRules.dateRange(
          '15.05.2023',
          'dd.MM.yyyy',
          '2023-05-30T11:23:52+02:00',
          '2023-05-30T11:23:52+02:00',
        )('invaliddate'),
      ).not.toBe(true);
    });

    it('returns correct error message for decimal digits validation', () => {
      const rule = useValidationRules.decimalWithMaxPlaces(3);
      expect(rule('100')).toBe(true);
      expect(rule('100.123')).toBe(true);
      expect(rule('100.12345')).toEqual(
        i18n.global.t('labels.validation_decimal_with_max_places.text', { 0: 3 }),
      );
    });

    it('returns correct error message for regex validation', () => {
      const rule = useValidationRules.regex(/^[0-9]{5}$/, 'invalid_digit', '5 digits');
      expect(rule('12345')).toBe(true);
      expect(rule('1234')).toEqual(i18n.global.t('invalid_digit', ['5 digits']));
    });
  });

  describe('withMessage', () => {
    const message = 'custom message';

    it('returns true if rule returns true', () => {
      const rules = [
        withMessage(useValidationRules.required, message),
        withMessage(() => true, message),
      ];

      rules.forEach((rule) => {
        expect(rule).toEqual(expect.any(Function));
        if (typeof rule === 'function') {
          expect(rule('foo')).toBe(true);
        }
      });
    });

    it('returns custom message if rule does not return true', () => {
      const rules = [
        withMessage(useValidationRules.required, message),
        withMessage(() => false, message),
      ];

      rules.forEach((rule) => {
        expect(rule).toEqual(expect.any(Function));
        if (typeof rule === 'function') {
          expect(rule('')).toEqual(message);
        }
      });
    });
  });

  describe('getMessages', () => {
    const rules = [
      useValidationRules.required,
      useValidationRules.numbers,
      useValidationRules.max(5),
    ];

    it('returns empty array if all rules are valid', () => {
      expect(getMessages(rules, 3)).toEqual([]);
    });

    it('returns array of error messages of every invalid rule', () => {
      expect(getMessages(rules, 10)).toEqual([
        i18n.global.t('labels.validation_number_maximum.text', [5]),
      ]);

      expect(getMessages(rules, 'foo')).toEqual([
        i18n.global.t('labels.validation_numbers_only.text'),
        i18n.global.t('labels.validation_number_maximum.text', [5]),
      ]);

      expect(getMessages(rules, '')).toEqual([
        i18n.global.t('labels.validation_input_required.text'),
      ]);
    });
  });
});
