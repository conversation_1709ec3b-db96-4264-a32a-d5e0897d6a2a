import { useCreateAddressDataStore } from '@/store/addressBook/address';

const useSearchAddress = async (value: string) => {
  const createAddressDataStore = useCreateAddressDataStore();

  if (value === '' || value.length <= 2) {
    createAddressDataStore.addresses.search = [];
    return;
  }

  await createAddressDataStore.searchNameInAddressBook(value.toLowerCase());
};

export { useSearchAddress };
