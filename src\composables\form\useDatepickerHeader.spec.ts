import useDatepickerHeader from '@/composables/form/useDatepickerHeader';

describe('useDatepickerHeader composable', () => {
  it('returns year or month-year depending on string passed', async () => {
    expect(useDatepickerHeader('2023', 'de')).toBe('2023');
    expect(useDatepickerHeader('2023', 'en')).toBe('2023');

    expect(useDatepickerHeader('2023-05', 'de')).toBe('Mai 2023');
    expect(useDatepickerHeader('2023-05', 'en')).toBe('May 2023');
  });
});
