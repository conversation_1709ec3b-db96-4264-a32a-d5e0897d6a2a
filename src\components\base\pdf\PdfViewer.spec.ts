import PdfViewer from '@/components/base/pdf/PdfViewer.vue';
import { ClientKey } from '@/types/client';
import { Events } from '@/types/events';
import { createClient } from '@dfe/dfe-frontend-client';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import printJS from 'print-js';
import { beforeEach } from 'vitest';

declare global {
  interface Window {
    printJS: typeof printJS;
  }
}

const props = {
  src: 'someFileString',
};

describe('PdfViewer', () => {
  let wrapper: VueWrapper;
  const client = createClient<Events>({});

  beforeEach(() => {
    window.URL.createObjectURL = vi.fn();
  });

  beforeAll(() => {
    wrapper = mount(PdfViewer, {
      attachTo: document.body,
      props,
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });
  });

  it("calls component's sprint function - on client doPrintLabels event", async () => {
    vi.mock('print-js', () => ({
      default: vi.fn(),
    }));

    const pdfViewer = wrapper.findComponent({ ref: 'pdfRef' });

    expect(pdfViewer.exists()).toBe(true);

    await wrapper.vm.$nextTick();

    client.events.emit('doPrintLabels');

    expect(printJS).toHaveBeenCalledTimes(1);
  });
});
