<template>
  <DfeBanner v-if="hasDuplicateErrors" type="error" class="mt-4">
    <span class="text-h5">{{ t('labels.unique_order_reference.text') }}</span>
  </DfeBanner>

  <div class="mt-6">
    <div v-for="(item, index) in items" :key="item.id" class="grid-container-form--new mt-3">
      <div class="grid-item text-field">
        <TextField
          :id="item.id"
          v-model="item.value"
          :label="index === 0 ? label : undefined"
          class="grid-item"
          :max-length="maxLength"
          :error="hasDuplicateError(item)"
        />
      </div>
      <div class="checkboxes-container grid-item checkboxes">
        <label v-if="index === 0" class="applicable-label d-flex text-label-3 mb-1">
          {{ t('labels.applicable_for.text') }}
        </label>
        <div class="d-flex align-center container__applicable">
          <div class="d-flex flex-wrap align-center">
            <CheckboxField
              v-model="item.loading"
              v-data-test="'bo-air-' + referenceType + '-applicable-for-loading'"
              :data-test-details="
                'bo-air-' + referenceType + '-applicable-for-loading-' + item.loading
              "
              class="pt-0 mt-0 mr-3"
              :label="t('labels.loading_label.text')"
            />
            <CheckboxField
              v-model="item.unloading"
              v-data-test="'bo-air-' + referenceType + '-applicable-for-unloading'"
              :data-test-details="
                'bo-air-' + referenceType + '-applicable-for-unloading-' + item.unloading
              "
              class="pt-0 mt-0"
              :label="t('labels.unloading_label.text')"
            />
          </div>
          <div v-if="$vuetify.display.mdAndUp" class="inline-item delete-button ml-8">
            <DfeIconButton
              :tooltip="t('labels.delete_label.text')"
              density="compact"
              @click="deleteItem(item.id)"
            >
              <MaterialSymbol size="24">
                <DeleteIcon />
              </MaterialSymbol>
            </DfeIconButton>
          </div>
        </div>
      </div>
      <div v-if="$vuetify.display.smAndDown" class="grid-item delete-button d-flex align-end ml-6">
        <DfeIconButton
          :tooltip="t('labels.delete_label.text')"
          density="compact"
          @click="deleteItem(item.id)"
        >
          <MaterialSymbol size="24">
            <DeleteIcon />
          </MaterialSymbol>
        </DfeIconButton>
      </div>
    </div>

    <div class="mt-3">
      <AddButton
        :label="t('labels.add_another.text')"
        variant="text"
        class="mr-1"
        @add-new-item="
          useAddReference(referenceType, {
            withLoadingData: true,
          })
        "
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { DfeIconButton } from '@dfe/dfe-frontend-shared-components';
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import AddButton from '@/components/createOrder/AddButton.vue';
import CheckboxField from '@/components/form/CheckboxField.vue';
import TextField from '@/components/form/TextField.vue';
import useAddReference from '@/composables/createOrder/useAddReference';
import { useDuplicateValidation } from '@/composables/createOrder/useDuplicateValidation';
import type { MultipleReferenceNumber } from '@/store/createOrder/formOrderReferences';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { OrderReferenceType } from '@dfe/dfe-book-api';
import DeleteIcon from '@dfe/dfe-frontend-styles/assets/icons/delete-16px.svg';
import type { TranslateResult } from 'vue-i18n';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

interface Props {
  referenceType: OrderReferenceType;
  items: MultipleReferenceNumber[];
  label?: TranslateResult;
  required?: boolean;
  maxLength?: number;
}

const props = defineProps<Props>();

const createOrderOrderReferencesFormStore = useCreateOrderOrderReferencesFormStore();

const deleteItem = (itemId: MultipleReferenceNumber['id']) => {
  createOrderOrderReferencesFormStore.deleteReferenceNumber(props.referenceType, itemId);
};

const { hasDuplicateErrors, hasDuplicateError } = useDuplicateValidation(props.items);
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;
@use '@/styles/settings';
@use 'sass:map';
.container__applicable {
  height: 34px; // input height - border width to center align with the input field left of it
}

:deep(.grid-container-form--new) {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: auto;
  grid-gap: 0.75em;

  @media #{map.get(settings.$display-breakpoints, 'md-and-up')} {
    grid-template-columns: repeat(3, 1fr);
  }

  @media #{map.get(settings.$display-breakpoints, 'xl')} {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-item.text-field {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
  }

  .grid-item.checkboxes {
    grid-column: 1 / 3;
    grid-row: 2 / 3;

    @media #{map.get(settings.$display-breakpoints, 'md-and-up')} {
      grid-column: 2 / 4;
      grid-row: 1 / 2;
    }

    @media #{map.get(settings.$display-breakpoints, 'lg')} {
      grid-column: 2 / 5;
    }
  }

  .grid-item.delete-button {
    grid-column: 2 / 3;
    grid-row: 1 / 2;

    @media #{map.get(settings.$display-breakpoints, 'md-and-up')} {
      display: none !important;
    }
  }
}
</style>
