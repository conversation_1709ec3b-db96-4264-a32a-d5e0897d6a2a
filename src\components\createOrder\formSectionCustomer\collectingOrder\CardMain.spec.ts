import CardMain from '@/components/createOrder/formSectionCustomer/collectingOrder/CardMain.vue';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes } from '@/enums';
import SelectAddress from '@/components/form/SelectAddress.vue';
import { AirExportQuoteInformation, OrderStatus, OrderType } from '@dfe/dfe-book-api';
import { storeToRefs } from 'pinia';

const mockCustomer = {
  customerNumber: '00000001',
};

describe('Customer CollectingOrder CardMain component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(CardMain);
  });

  it('is empty if there is no customer', () => {
    expect(wrapper.html()).not.toContain('.card-collecting-order');
    expect(wrapper.findComponent({ name: 'v-card' }).exists()).toBe(false);
  });

  it('shows card component if there is at least one customer', async () => {
    const store = useCreateOrderDataStore();
    store.customers = [mockCustomer];

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'v-card' }).exists()).toBe(true);
  });

  it('shows card component if there is at least one customer - select', async () => {
    const store = useCreateOrderDataStore();
    const formStore = useCreateOrderFormStore();
    store.customers = [mockCustomer];
    formStore.customerNumber = '00000001';

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent({ name: 'v-card' }).exists()).toBe(true);
  });

  it('disables select principal input if there is an air order from Quote', async () => {
    const store = useCreateOrderDataStore();
    store.customers = [mockCustomer, mockCustomer];
    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(SelectAddress).props('disabled')).toBe(false);
    const createOrderStore = useCreateOrderFormStore();

    createOrderStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;
    createOrderStore.orderType = OrderTypes.AirExportOrder;

    await wrapper.vm.$nextTick();
    expect(wrapper.findComponent(SelectAddress).props('disabled')).toBe(true);
  });

  it('principal address should be disabled if order is status complete ', async () => {
    const { orderType } = storeToRefs(useCreateOrderFormStore());
    const createOrderStore = useCreateOrderFormStore();
    const store = useCreateOrderDataStore();
    store.customers = [mockCustomer, mockCustomer];

    await wrapper.vm.$nextTick();

    orderType.value = OrderType.RoadCollectionOrder;
    createOrderStore.orderData = {
      orderType: OrderType.RoadCollectionOrder,
      orderStatus: { status: OrderStatus.COMPLETE },
    };
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ name: 'v-select' }).props('disabled')).toBe(true);
  });
});
