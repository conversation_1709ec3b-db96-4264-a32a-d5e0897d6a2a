import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { unNumberSearchResults } from '@/mocks/fixtures/unNumberSearchResults';
import type { TestUtils } from '@test/test-utils';
import UnNumberColumn from '@/components/createOrder/formSectionFreight/dangerousGood/unNumber/UnNumberColumn.vue';
import { DfeChip } from '@dfe/dfe-frontend-shared-components';
import { mockResizeObserver } from 'jsdom-testing-mocks';

describe('UnNumberColumn.vue', () => {
  let wrapper: TestUtils.VueWrapper<typeof UnNumberColumn>;

  beforeEach(() => {
    mockResizeObserver();
    wrapper = mount(UnNumberColumn, {
      props: {
        item: unNumberSearchResults[0],
      },
      attachTo: document.body,
    });
  });

  afterEach(() => {
    wrapper.unmount();
    vi.clearAllMocks();
  });

  it('renders content', () => {
    expect(wrapper.text()).toContain('1203');
  });

  it('opens display layer on click', async () => {
    expect(document.body.textContent).not.toContain('BENZIN\nKraftstoff\nleicht entzündlich');

    const chip = wrapper.findComponent(DfeChip);
    await chip.trigger('click');

    expect(document.body.textContent).toContain('BENZIN\nKraftstoff\nleicht entzündlich');
  });
});
