import { useDisabledFormAddressFields } from '@/composables/createOrder/addresses/useDisabledFormAddressFields';
import { DisabledFormAddressFields } from '@/enums';
import type { OrderAddress } from '@dfe/dfe-book-api';
import { orderAddress } from '@/store/sharedInitialStates';
import { type Ref, ref } from 'vue';

const testAddress: Ref<OrderAddress> = ref({ ...orderAddress() });
const keys = Object.keys(testAddress.value) as (keyof OrderAddress)[];

function getTestCases(
  type: keyof typeof DisabledFormAddressFields,
): [keyof OrderAddress, boolean][] {
  return keys.map((key) => [key, DisabledFormAddressFields[type].includes(key)]);
}

describe('useDisabledFormAddressFields composable', () => {
  it.each(getTestCases('full'))(
    `returns whether the field is disabled if it is fully locked -> %s %s`,
    (key, expected) => {
      testAddress.value.lockedByQuote = 'full';
      const { isDisabled } = useDisabledFormAddressFields(testAddress);

      expect(isDisabled(key).value).toBe(expected);
    },
  );

  it.each(getTestCases('partial'))(
    `returns whether the field is disabled if it is partially locked -> %s %s`,
    (key, expected) => {
      testAddress.value.lockedByQuote = 'partial';
      const { isDisabled } = useDisabledFormAddressFields(testAddress);

      expect(isDisabled(key).value).toBe(expected);
    },
  );

  it.each(keys)('returns false if the address is not locked', (key) => {
    testAddress.value.lockedByQuote = undefined;
    const { isDisabled } = useDisabledFormAddressFields(testAddress);

    expect(isDisabled(key).value).toBe(false);
  });
});
