import { ref } from 'vue';
import { usePackagingOptionsList } from '@/composables/createOrder/usePackagingOptionsList';
import type { PackagingOptionsWithFavorites } from '@dfe/dfe-book-api';

const favoritesHeader = 'Favorites';
const header = 'Header';

const emptyPackagingOptions = ref([] as PackagingOptionsWithFavorites);
const packagingOptions = [
  { code: 'EU', description: 'Euro-Pallet' },
  { code: 'HE', description: 'Half EU pallet' },
];
const favorites = [
  { code: 'CRT', description: 'Carton' },
  { code: 'CON', description: 'Container' },
];

describe('usePackagingOptionsList composable', () => {
  it('returns an empty array if no packaging options are available', () => {
    expect(usePackagingOptionsList(favoritesHeader, header, emptyPackagingOptions));
  });

  it('returns packaging options', () => {
    expect(usePackagingOptionsList(favoritesHeader, header, ref({ packagingOptions }))).toEqual(
      packagingOptions,
    );
  });

  it('returns packaging options with favorites and headers', () => {
    expect(
      usePackagingOptionsList(favoritesHeader, header, ref({ packagingOptions, favorites })),
    ).toEqual([
      { header: favoritesHeader },
      ...favorites,
      { divider: true },
      { header },
      ...packagingOptions,
    ]);
  });
});
