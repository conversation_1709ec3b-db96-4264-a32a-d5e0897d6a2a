import CardMain from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/CardMain.vue';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import AdditionalServices from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AdditionalServices.vue';
import AccountingForwarding from '@/components/createOrder/formSectionAccountingAdditionalServices/forwardingOrder/AccountingForwarding.vue';

describe('Accounting & Additional Services component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(CardMain);
  });

  it('shows additional services and specific accounting', async () => {
    expect(wrapper.findComponent(AdditionalServices).exists()).toBe(true);

    expect(wrapper.findComponent(AccountingForwarding).exists()).toBe(true);
  });
});
