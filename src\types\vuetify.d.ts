/**
 * Since vuetify3 doesn't export types we had in use since vuetify2,
 * this file is for some types that are copied from vuetify3.
 */

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SelectItemKey<T = Record<string, any>> =
  | boolean
  | null
  | undefined
  | string
  | readonly (string | number)[]
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | ((item: T, fallback?: any) => any);

export type VariantProp = NonNullable<
  'flat' | 'text' | 'elevated' | 'tonal' | 'outlined' | 'plain'
>;

export type FilterFunction = (value: string, query: string, item?: InternalItem) => FilterMatch;

// Anchor type used for tooltips
// @see https://github.com/vuetifyjs/vuetify/blob/2c726a52d479c4333df832290917fd1775f7b0f1/packages/vuetify/src/util/anchor.ts#L8-L14
const block = ['top', 'bottom'] as const;
const inline = ['start', 'end', 'left', 'right'] as const;
type Tblock = (typeof block)[number];
type Tinline = (typeof inline)[number];
export type Anchor =
  | Tblock
  | Tinline
  | 'center'
  | 'center center'
  | `${Tblock} ${Tinline | 'center'}`
  | `${Tinline} ${Tblock | 'center'}`;
