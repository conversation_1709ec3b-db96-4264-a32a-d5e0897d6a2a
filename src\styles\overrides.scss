@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;

@use 'overridesImports/svg';
@use 'overridesImports/list';
@use 'overridesImports/button';
@use 'overridesImports/card';
@use 'overridesImports/tabs';
@use 'overridesImports/autocomplete';
@use 'overridesImports/checkbox';
@use 'overridesImports/radiobox';
@use 'overridesImports/switch';
@use 'overridesImports/textField';
@use 'overridesImports/menu';
@use 'overridesImports/datePicker';
@use 'overridesImports/dialog';
@use 'overridesImports/alert';
@use 'overridesImports/select';
[dfe-book-frontend] {
:deep(.text--light) {
  color: var(--color-base-grey-700);
}

:deep(.theme--light.v-divider) {
  border-color: var(--color-base-grey-400);
}

:deep(.label-indicator) {
  margin-left: 2px;
  font-size: 14px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.v-input--selection-controls__input:hover :deep(.v-input--selection-controls__ripple:before),
.v-input--is-focused :deep(.v-input--selection-controls__ripple:before) {
  transition: none;
  opacity: 0;
}

.v-input__append-inner {
  z-index: 0;
}
}
