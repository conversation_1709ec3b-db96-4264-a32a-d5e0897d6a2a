import { createApp } from 'vue';
import DevHeader from '@/dev/DevHeaderLabelPrint.vue';
import { createClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';
import { ClientKey } from '@/types/client';
import { mount } from '@/bootstrap-label-print';
import { KEYCLOAK_CLIENT_ID, KEYCLOAK_REALM, KEYCLOAK_URL } from '@/env';
import { i18n } from '@/plugins/i18n';
import vuetify from '@/plugins/vuetify';
import { createComposablePlugin } from '@dfe/dfe-frontend-composables';

if (import.meta.env.VITE_DFE_ENVIRONMENT === 'development') {
  const headerEl = document.querySelector('#__dfe-book-frontend-dev-header-root');
  const labelsPrintEl = document.querySelector('#__dfe-book-frontend-labels-print-dev-root');

  if (headerEl && labelsPrintEl) {
    const client = createClient<Events>({
      api: {
        'dfe-book': import.meta.env.VITE_APP_DFE_BOOK_API_URL ?? '',
        'dfe-address': import.meta.env.VITE_APP_DFE_ADDRESS_API_URL ?? '',
        'dfe-platform': import.meta.env.VITE_APP_DFE_PLATFORM_API_URL ?? '',
        'dfe-config': import.meta.env.VITE_APP_DFE_CONFIG_API_URL ?? '',
        'dfe-dynamiclabel': import.meta.env.VITE_APP_DFE_DYNAMICLABEL_API_URL ?? '',
      },
      logger: {
        baseUrl: import.meta.env.VITE_APP_DFE_LOGGING_API_URL ?? '',
        printLogs: true,
      },
      environment: 'development',
      availableLanguages: import.meta.env.VITE_APP_DFE_AVAILABLE_LANGUAGES,
    });

    client.auth.init({
      url: KEYCLOAK_URL,
      realm: KEYCLOAK_REALM,
      clientId: KEYCLOAK_CLIENT_ID,
      async onAuth() {
        await client.preferences.fetch();

        await mount(labelsPrintEl, client);

        const devHeader = createApp(DevHeader);
        devHeader.use(i18n);
        devHeader.use(vuetify);
        devHeader.use(createComposablePlugin({ client }));
        devHeader.provide(ClientKey, client);
        devHeader.mount(headerEl);
      },
    });
  }
}
