import { useUTCTimeFromDate } from '@/composables/dateTimeUtilities/useTimeFromDate';
import { useTimeFromDate } from '@/composables/dateTimeUtilities/useFormatInUTC';

describe('useTimeFromDate composables', () => {
  it('returns formatted UTC time from date', () => {
    const date = '2022-11-18T22:00:00Z';

    expect(useUTCTimeFromDate(date, 'hh:mm a')).toEqual('10:00 PM');
    expect(useUTCTimeFromDate(date, 'HH:mm')).toEqual('22:00');

    const dateWithTimezone = '2022-11-18T22:00:00+02:00';
    expect(useUTCTimeFromDate(dateWithTimezone, 'hh:mm a')).toEqual('08:00 PM');
    expect(useUTCTimeFromDate(dateWithTimezone, 'HH:mm')).toEqual('20:00');
  });

  it("returns the input value if it's an invalid date", () => {
    expect(useUTCTimeFromDate('today', 'hh:mm a')).toEqual('today');
  });

  it('returns formatted time from date', () => {
    const date = '2022-11-25T15:15:10.478Z';

    expect(useTimeFromDate(date, 'HH:mm')).toEqual('15:15');
  });

  it('returns formatted time in 12-hour format with AM/PM', () => {
    const date = '2022-11-25T15:15:10.478Z';

    expect(useTimeFromDate(date, 'hh:mm a')).toEqual('03:15 PM');
  });

  it('returns formatted time in 24-hour format', () => {
    const date = '2022-11-25T03:15:10.478Z';

    expect(useTimeFromDate(date, 'HH:mm')).toEqual('03:15');
  });
});
