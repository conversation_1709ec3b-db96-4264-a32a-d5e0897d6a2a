import useParsedDate from '@/composables/dateTimeUtilities/useParsedDate';

const refDate = new Date('2022-09-10T00:00:00');
const expectedDate = new Date('2022-09-19T00:00:00');
const expectedDateTime = new Date('2022-09-10T19:45:00');
const formats = ['dd.MM.yy', 'dd.MM.yyyy', 'dd.MM.', 'dd.'];

describe('useParsedDate composable', () => {
  it('returns the input value if either of the parameters is empty', () => {
    expect(useParsedDate('', [])).toBe('');
    expect(useParsedDate('19.09.2022', [])).toBe('19.09.2022');
    expect(useParsedDate('', ['dd.MM.yyyy'])).toBe('');
  });

  it('returns a parsed date for a specific format', () => {
    expect(useParsedDate('19.09.2022', 'dd.MM.yyyy', refDate)).toEqual(expectedDate);
    expect(useParsedDate('19/09/2022', 'dd/MM/yyyy', refDate)).toEqual(expectedDate);
    expect(useParsedDate('9/19/22', 'M/d/yy', refDate)).toEqual(expectedDate);
    expect(useParsedDate('19. September 2022', 'd. MMMM yyyy', refDate)).toEqual(expectedDate);
  });

  it('returns a parsed date for a time format', () => {
    expect(useParsedDate('19:45', 'HH:mm', refDate)).toEqual(expectedDateTime);
    expect(useParsedDate('07:45 PM', 'hh:mm a', refDate)).toEqual(expectedDateTime);
  });

  it('returns a parsed date for the first format that matches', () => {
    const values = ['19.09.2022', '19.09.22', '19.09.', '19.9.', '19.'];

    values.forEach((value) => {
      expect(useParsedDate(value, formats, refDate)).toEqual(expectedDate);
    });
  });

  it("returns the input value if it doesn't match any format", () => {
    const values = ['19/09/2022', '19. September 2022', '19. 9.', '09-2022', '19.09.2e22', 'today'];

    values.forEach((value) => {
      expect(useParsedDate(value, formats, refDate)).toBe(value);
    });
  });
});
