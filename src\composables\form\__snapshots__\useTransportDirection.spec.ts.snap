// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Composable - useTransportDirection > Watch order type > should reset addresses when order type changes to export 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "consigneeAddressType": 0,
  "consigneeHandOverSelection": {
    "port": {
      "code": "Airport",
      "type": "AIRPORT",
    },
    "selection": "portSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "differentConsigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperAddress": {
    "address": {
      "city": "Stuttgart",
      "countryCode": "DE",
      "id": 2,
      "name": "Flink CPS",
      "name2": "",
      "name3": "",
      "postcode": "70469",
      "street": "Sieglestraße 25",
    },
    "addressType": "",
    "customerNumber": undefined,
  },
  "shipperHandOverSelection": {
    "port": {
      "code": "Airport",
      "type": "AIRPORT",
    },
    "selection": "portSelection",
  },
}
`;

exports[`Composable - useTransportDirection > Watch order type > should reset addresses when order type changes to import 1`] = `
{
  "consigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
    "customerNumber": undefined,
  },
  "consigneeAddressType": 0,
  "consigneeHandOverSelection": {
    "selection": "defaultSelection",
  },
  "contactDataConsignee": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "contactDataShipper": {
    "email": "",
    "id": 0,
    "mobile": "",
    "name": "",
    "telephone": "",
  },
  "differentConsigneeAddress": {
    "address": {
      "city": "",
      "contact": undefined,
      "countryCode": undefined,
      "dropOfLocation": undefined,
      "gln": undefined,
      "id": undefined,
      "lockedByQuote": undefined,
      "name": "",
      "name2": "",
      "name3": "",
      "neutralizeAddress": undefined,
      "originAddressId": undefined,
      "postcode": "",
      "street": "",
      "street2": "",
      "supplement": "",
      "taxID": undefined,
    },
    "addressType": "",
  },
  "shipperAddress": {
    "address": {
      "city": "Stuttgart",
      "countryCode": "DE",
      "id": 2,
      "name": "Flink CPS",
      "name2": "",
      "name3": "",
      "postcode": "70469",
      "street": "Sieglestraße 25",
    },
    "addressType": "",
  },
  "shipperHandOverSelection": {
    "port": {
      "code": "Airport",
      "type": "AIRPORT",
    },
    "selection": "portSelection",
  },
}
`;
