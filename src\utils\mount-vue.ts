import { createApp } from 'vue';
import type { DFEClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';
import { i18n } from '@/plugins/i18n';
import pinia from '@/plugins/pinia';
import { createApi, createCancelToken, requestOriginHeader } from '@/services/api';
import type { API } from '@/composables/useInit';
import { setInit } from '@/composables/useInit';
import { VueQueryPlugin } from '@tanstack/vue-query';
import { ClientKey } from '@/types/client';
import sanitizeHtml from 'sanitize-html';
import { SanitizeKey } from '@/types/sanitize';
import vuetify from '@/plugins/vuetify';
import { createUserProfilePlugin } from '@dfe/dfe-frontend-user-profile';
import { createComposablePlugin, getDataTestDirective } from '@dfe/dfe-frontend-composables';

export function mountVue(rootComponent: object) {
  return async (el: Element, client: DFEClient<Events>) => {
    const app = createApp(rootComponent);

    app.directive<HTMLElement, string>('data-test', getDataTestDirective('book'));

    const api: API = {
      book: createApi('Book', {
        baseUrl: client.api.getUrl('dfe-book'),
        getToken: client.auth.getToken,
        getLanguage: () => i18n.global.locale.value,
        getRequestOrigin: () => requestOriginHeader,
      }),
      address: createApi('Address', {
        baseUrl: client.api.getUrl('dfe-address'),
        getToken: client.auth.getToken,
        getLanguage: () => i18n.global.locale.value,
        getRequestOrigin: () => requestOriginHeader,
      }),
      config: createApi('Config', {
        baseUrl: client.api.getUrl('dfe-config'),
        getToken: client.auth.getToken,
        getLanguage: () => i18n.global.locale.value,
        getRequestOrigin: () => requestOriginHeader,
      }),
      dynamicLabel: createApi('DynamicLabel', {
        baseUrl: client.api.getUrl('dfe-dynamiclabel'),
        getToken: client.auth.getToken,
        getLanguage: () => i18n.global.locale.value,
        getRequestOrigin: () => requestOriginHeader,
      }),
    };

    setInit(client, api);

    pinia.use(() => ({
      api,
      createCancelToken,
      client,
    }));

    app.use(i18n);
    app.use(VueQueryPlugin);
    app.use(pinia);
    app.use(vuetify);
    app.use(createUserProfilePlugin({ client }));
    app.use(createComposablePlugin({ client }));
    app.provide(ClientKey, client);
    app.provide(SanitizeKey, sanitizeHtml);
    app.config.globalProperties.$appRoot = undefined;

    const mountedApp = app.mount(el);

    app.config.globalProperties.$appRoot = mountedApp.$root?.$el;

    return () => undefined;
  };
}
