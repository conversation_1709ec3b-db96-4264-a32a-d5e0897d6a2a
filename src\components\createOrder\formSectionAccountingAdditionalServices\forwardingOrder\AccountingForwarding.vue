<template>
  <div class="account-forwarding-wrapper">
    <SelectField
      v-model="selectedFreightTerm"
      v-data-test="'freight-term-field'"
      :disabled="!hasAddresses || isRoadOrderFromQuoteWithDailyPrice"
      :items="freightTerms"
      :item-text="itemTextFunction"
      item-value="code"
      :label="t('labels.term_label.text')"
      :placeholder="t('labels.select_option.text')"
      :return-object="true"
      :required="true"
      :rules="[useValidationRules.required]"
    />
    <LinkElement
      :text="t('labels.incoterm_details.text')"
      external-link="https://www.dachser.de/de/mediaroom/downloads/Corporate/Marktinfo/Incoterms2020.pdf"
      class="text-label-2"
      icon
    >
      <template #icon>
        <CustomFilePdf />
      </template>
    </LinkElement>
  </div>
</template>

<script setup lang="ts">
import SelectField from '@/components/form/SelectField.vue';
import { onMounted, watch } from 'vue';
import { useCreateOrderFormAccountingAdditionalServices } from '@/store/createOrder/accountingAdditionalServices';
import { storeToRefs } from 'pinia';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useValidationRules } from '@/composables/form/useValidationRules';

import type { FreightTerm } from '@dfe/dfe-book-api';
import { useI18n } from 'vue-i18n';
import CustomFilePdf from '@dfe/dfe-frontend-styles/assets/icons/custom_file_pdf-16px.svg';
import LinkElement from '@/components/base/LinkElement.vue';

const { t } = useI18n();

const accountingAndAdditionalServicesStore = useCreateOrderFormAccountingAdditionalServices();
const { selectedFreightTerm } = storeToRefs(accountingAndAdditionalServicesStore);
const createOrderDataStore = useCreateOrderDataStore();
const { freightTerms } = storeToRefs(createOrderDataStore);
const createOrderFormStore = useCreateOrderFormStore();
const { transportCountry, isRoadOrderFromQuoteWithDailyPrice } = storeToRefs(createOrderFormStore);

interface Props {
  hasAddresses?: boolean;
}
defineProps<Props>();

onMounted(() => {
  const { fromCountry, toCountry } = transportCountry.value;
  if (fromCountry && toCountry) {
    createOrderDataStore.fetchFreightTerms();
  }
});

watch(
  () => transportCountry.value,
  ({ fromCountry, toCountry }) => {
    if (fromCountry && toCountry) {
      createOrderDataStore.fetchFreightTerms();
    }
  },
);

const itemTextFunction = ({ headline, incoTermKey }: FreightTerm) => `${incoTermKey} – ${headline}`;
</script>
<style lang="scss">
@use '@/styles/variables' as projectVars;

.account-forwarding-wrapper {
  width: projectVars.$form-cols-width-xl;
}
</style>
