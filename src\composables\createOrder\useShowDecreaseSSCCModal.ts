import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { OrderStatus } from '@dfe/dfe-book-api';

export function showDecreaseSSCCsModal(
  calledByCallback: boolean,
  manualNumberOfLabelsActivated: boolean | undefined,
): boolean {
  const createOrderFormStore = useCreateOrderFormStore();
  const { isRoadOrder, currentOrderStatus } = storeToRefs(createOrderFormStore);
  const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
  const { hasGeneratedNumberOfLabelsDecreased, hasManualNumberOfLabelsDecreased } = storeToRefs(
    createOrderOrderLineFormStore,
  );

  // 1. Check if it is a road order
  if (!isRoadOrder.value) {
    return false;
  }

  const isStatusComplete = currentOrderStatus.value === OrderStatus.COMPLETE;

  // 2. Check if the order status is complete
  if (!isStatusComplete) {
    return false;
  }

  // 3. Check if the number of labels has decreased
  const hasDecreased = manualNumberOfLabelsActivated
    ? hasManualNumberOfLabelsDecreased.value
    : hasGeneratedNumberOfLabelsDecreased.value;

  // 4. Check if the function was not called by a callback
  const notCalledByCallback = !calledByCallback;

  return hasDecreased && notCalledByCallback;
}
