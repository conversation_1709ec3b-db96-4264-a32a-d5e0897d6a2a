<template>
  <VRadio
    :key="value"
    :value="value"
    :disabled="disabled"
    :ripple="false"
    class="max-content-width"
  >
    <template #label>
      <VTooltip v-if="hasTooltip" location="top">
        <template #activator="{ props: radioProps }">
          <span v-bind="radioProps" class="text-body-2 pointer-events-auto">
            {{ label }}
          </span>
        </template>
        {{ tooltip }}
      </VTooltip>
      <span v-else class="text-body-2">{{ label }}</span>
    </template>
  </VRadio>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  label?: TranslateResult;
  disabled?: boolean;
  tooltip?: TranslateResult;
}

const props = defineProps<Props>();

const value = defineModel<string | number | symbol | undefined>();

const hasTooltip = computed(() => !!props.tooltip);
</script>

<style scoped lang="scss">
@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
.v-radio {
  max-height: 18px;
  line-height: vars.$size-line-height-sm;
}

.v-radio:hover :deep(.v-icon.v-theme--light .icon) {
  color: var(--color-base-blue-500);
}

.max-content-width {
  width: max-content;
}
.pointer-events-auto {
  pointer-events: auto;
}
</style>
