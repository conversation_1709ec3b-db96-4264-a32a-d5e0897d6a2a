import { useValidationDataStore } from '@/store/validation';
import { storeToRefs } from 'pinia';
import { initPinia } from '../../test/util/init-pinia';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';

describe('useValidationStore', () => {
  let validationStore: ReturnType<typeof useValidationDataStore>;

  beforeAll(() => {
    initPinia();

    validationStore = useValidationDataStore();
  });

  it('should return true, if all sections have no validation error', () => {
    const { getFormSectionsValid } = storeToRefs(validationStore);
    expect(getFormSectionsValid.value).toBeTruthy();
  });

  it('should return false, if one or more road sections have a validation error', () => {
    const {
      formValidationSectionsSea,
      formValidationSectionsAir,
      formValidationSectionsRoad,
      getFormSectionsValid,
    } = storeToRefs(validationStore);

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.ROAD;
    formStore.orderType = OrderTypes.RoadForwardingOrder;

    formValidationSectionsRoad.value.roadOrderAddresses = false;

    expect(getFormSectionsValid.value).toBeFalsy();

    formValidationSectionsRoad.value.roadOrderAddresses = true;
    formValidationSectionsAir.value.airOrderIncoterms = false;
    formValidationSectionsSea.value.seaOrderCustomers = false;

    expect(getFormSectionsValid.value).toBeTruthy();
  });

  it('should return false, if one or more air sections have a validation error', () => {
    const {
      formValidationSectionsSea,
      formValidationSectionsAir,
      formValidationSectionsRoad,
      getFormSectionsValid,
    } = storeToRefs(validationStore);

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.AIR;
    formStore.orderType = OrderTypes.AirExportOrder;

    formValidationSectionsAir.value.airOrderIncoterms = false;
    formValidationSectionsRoad.value.roadOrderAddresses = true;

    expect(getFormSectionsValid.value).toBeFalsy();

    formValidationSectionsAir.value.airOrderIncoterms = true;
    formValidationSectionsSea.value.seaOrderCustomers = false;
    formValidationSectionsRoad.value.roadOrderAddresses = false;

    expect(getFormSectionsValid.value).toBeTruthy();
  });

  it('should return false, if one or more sea sections have a validation error', () => {
    const {
      formValidationSectionsSea,
      formValidationSectionsAir,
      formValidationSectionsRoad,
      getFormSectionsValid,
    } = storeToRefs(validationStore);

    const formStore = useCreateOrderFormStore();
    formStore.customerNumber = '00000001';
    formStore.transportType = Segment.SEA;
    formStore.orderType = OrderTypes.SeaExportOrder;

    formValidationSectionsSea.value.seaOrderCustomers = false;
    formValidationSectionsAir.value.airOrderIncoterms = true;
    formValidationSectionsRoad.value.roadOrderAddresses = true;

    expect(getFormSectionsValid.value).toBeFalsy();

    formValidationSectionsSea.value.seaOrderCustomers = true;
    formValidationSectionsAir.value.airOrderIncoterms = false;
    formValidationSectionsRoad.value.roadOrderAddresses = false;

    expect(getFormSectionsValid.value).toBeTruthy();
  });
});
