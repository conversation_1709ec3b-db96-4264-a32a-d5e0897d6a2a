import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import AddLineButtons from '@/components/createOrder/formSectionFreight/AddLineButtons.vue';

describe('Freight AddLineButtons component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(AddLineButtons);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('renders the Add Order Line button when showOrderLine is true', async () => {
    await wrapper.setProps({ showOrderLine: true });

    const button = wrapper.findComponent('[data-test="book-add-order-line-button"]');
    expect(button.exists()).toBe(true);
    expect(button.text()).toContain('labels.add_line.text');
  });

  it('does not render the Add Order Line button when showOrderLine is false', async () => {
    await wrapper.setProps({ showOrderLine: false });

    const button = wrapper.find('[data-test="book-add-order-line-button"]');
    expect(button.exists()).toBe(false);
  });

  it('renders the Add Packing Line button when showPackingPosition is true', async () => {
    await wrapper.setProps({ showPackingPosition: true });

    const button = wrapper.find('[data-test="book-add-packing-line-button"]');
    expect(button.exists()).toBe(true);
    expect(button.text()).toContain('labels.add_packing_position.text');
  });

  it('does not render the Add Packing Line button when showPackingPosition is false', async () => {
    await wrapper.setProps({ showPackingPosition: false });

    const button = wrapper.find('[data-test="book-add-packing-line-button"]');
    expect(button.exists()).toBe(false);
  });

  it('disables the buttons when disableButtons is true', async () => {
    await wrapper.setProps({
      disableButtons: true,
      showOrderLine: true,
      showPackingPosition: true,
      showFullContainerLoad: true,
    });

    const addOrderLineButton = wrapper.find('[data-test="book-add-order-line-button"]');
    const addPackingLineButton = wrapper.find('[data-test="book-add-packing-line-button"]');
    const addFullContainerLoadButton = wrapper.find(
      '[data-test="book-add-full-container-load-line-button"]',
    );

    expect(addOrderLineButton.attributes('disabled')).toBeDefined();
    expect(addPackingLineButton.attributes('disabled')).toBeDefined();
    expect(addFullContainerLoadButton.attributes('disabled')).toBeDefined();
  });

  it('enables the buttons when disableButtons is false', async () => {
    await wrapper.setProps({
      disableButtons: false,
      showOrderLine: true,
      showPackingPosition: true,
    });

    const addOrderLineButton = wrapper.find('[data-test="book-add-order-line-button"]');
    const addPackingLineButton = wrapper.find('[data-test="book-add-packing-line-button"]');

    expect(addOrderLineButton.attributes('disabled')).toBeUndefined();
    expect(addPackingLineButton.attributes('disabled')).toBeUndefined();
  });

  it('emits addOrderLine event when Add Order Line button is clicked', async () => {
    await wrapper.setProps({ showOrderLine: true });

    const button = wrapper.find('[data-test="book-add-order-line-button"]');
    await button.trigger('click');

    expect(wrapper.emitted('addOrderLine')).toBeTruthy();
    expect(wrapper.emitted('addOrderLine')?.length).toBe(1);
  });

  it('emits addPackingPosition event when Add Packing Line button is clicked', async () => {
    await wrapper.setProps({ showPackingPosition: true });

    const button = wrapper.find('[data-test="book-add-packing-line-button"]');
    await button.trigger('click');

    expect(wrapper.emitted('addPackingPosition')).toBeTruthy();
    expect(wrapper.emitted('addPackingPosition')?.length).toBe(1);
  });

  it('renders the Add Full container load button when showFullContainerLoad is true', async () => {
    await wrapper.setProps({ showFullContainerLoad: true });

    const button = wrapper.find('[data-test="book-add-full-container-load-line-button"]');
    expect(button.exists()).toBe(true);
    expect(button.text()).toContain('labels.add_full_container_load.text');
  });

  it('does not render the Add Full Container Load button when showFullContainerLoad is false', async () => {
    await wrapper.setProps({ showFullContainerLoad: false });

    const button = wrapper.find('[data-test="book-add-full-container-load-line-button"]');
    expect(button.exists()).toBe(false);
  });

  it('emits addFullContainerLoad event when Add Full Container Load button is clicked', async () => {
    await wrapper.setProps({ showFullContainerLoad: true });

    const button = wrapper.find('[data-test="book-add-full-container-load-line-button"]');
    await button.trigger('click');

    expect(wrapper.emitted('addFullContainerLoad')).toBeTruthy();
    expect(wrapper.emitted('addFullContainerLoad')?.length).toBe(1);
  });
});
