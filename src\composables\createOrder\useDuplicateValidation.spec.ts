import { describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useDuplicateValidation } from '@/composables/createOrder/useDuplicateValidation';

describe('useDuplicateValidation', () => {
  it('returns no duplicate errors for unique values', () => {
    const items = ref([
      { id: '1', value: 'Value1' },
      { id: '2', value: 'Value2' },
      { id: '3', value: 'Value3' },
    ]);

    const { hasDuplicateErrors, duplicateFlags } = useDuplicateValidation(items.value);

    expect(hasDuplicateErrors.value).toBe(false);
    expect(duplicateFlags.value).toEqual({
      '1': false,
      '2': false,
      '3': false,
    });
  });

  it('returns duplicate errors for repeated values', () => {
    const items = ref([
      { id: '1', value: 'Duplicate' },
      { id: '2', value: 'Duplicate' },
      { id: '3', value: 'Unique' },
    ]);

    const { hasDuplicateErrors, duplicateFlags } = useDuplicateValidation(items.value);

    expect(hasDuplicateErrors.value).toBe(true);
    expect(duplicateFlags.value).toEqual({
      '1': true,
      '2': true,
      '3': false,
    });
  });

  it('handles empty and null values correctly', () => {
    const items = ref([
      { id: '1', value: null },
      { id: '2', value: '' },
      { id: '3', value: 'Unique' },
    ]);

    const { hasDuplicateErrors, duplicateFlags } = useDuplicateValidation(items.value);

    expect(hasDuplicateErrors.value).toBe(false);
    expect(duplicateFlags.value).toEqual({
      '1': false,
      '2': false,
      '3': false,
    });
  });

  it('updates duplicate flags when values change', async () => {
    const items = ref([
      { id: '1', value: 'Initial' },
      { id: '2', value: 'Value2' },
      { id: '3', value: 'Initial' },
    ]);

    const { hasDuplicateErrors, duplicateFlags } = useDuplicateValidation(items.value);

    expect(hasDuplicateErrors.value).toBe(true);
    expect(duplicateFlags.value).toEqual({
      '1': true,
      '2': false,
      '3': true,
    });

    items.value[2].value = 'Unique';

    expect(hasDuplicateErrors.value).toBe(false);
    expect(duplicateFlags.value).toEqual({
      '1': false,
      '2': false,
      '3': false,
    });
  });

  it('marks duplicates correctly with multiple changes', async () => {
    const items = ref([
      { id: '1', value: 'Value1' },
      { id: '2', value: 'Value2' },
      { id: '3', value: 'Value1' },
    ]);

    const { hasDuplicateErrors, duplicateFlags } = useDuplicateValidation(items.value);

    expect(hasDuplicateErrors.value).toBe(true);
    expect(duplicateFlags.value).toEqual({
      '1': true,
      '2': false,
      '3': true,
    });

    items.value[0].value = 'Value3';

    expect(hasDuplicateErrors.value).toBe(false);
    expect(duplicateFlags.value).toEqual({
      '1': false,
      '2': false,
      '3': false,
    });
  });
});
