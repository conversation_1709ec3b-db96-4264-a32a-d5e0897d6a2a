import useSaveOrder from '@/composables/createOrder/useSaveOrder';
import { isErrorHttpResponse, isOrderProcessResult } from '@/services/type-guards';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import type { Events } from '@/types/events';
import { OrderResponseBody, ValidationResult, type OrderProcessResult } from '@dfe/dfe-book-api';
import type { DFEClient } from '@dfe/dfe-frontend-client';

function useSubmitOrder(client?: DFEClient<Events>) {
  const createOrderFormStore = useCreateOrderFormStore();
  const submitOrder = async (): Promise<OrderProcessResult> => {
    try {
      return await createOrderFormStore.submitOrder(useSaveOrder());
    } catch (error) {
      client?.log.error('Failed to submit order', 'dfe-book-frontend', error);

      createOrderFormStore.checkForTechnicalError(error);

      const validationError = (
        error as { error: { validationResult: ValidationResult; order?: OrderResponseBody } }
      ).error;

      if (validationError.order) {
        createOrderFormStore.saveOrderData = validationError.order;
      }

      if (isErrorHttpResponse(error, isOrderProcessResult)) {
        return error.error;
      } else {
        return error as OrderProcessResult;
      }
    }
  };

  return {
    submitOrder,
  };
}

export default useSubmitOrder;
