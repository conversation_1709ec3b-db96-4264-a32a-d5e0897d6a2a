import type { VueWrapper } from '@vue/test-utils';
import { afterEach, beforeEach, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import TooltipWithSlot from '@/components/base/tooltips/TooltipWithSlot.vue';
import { VTooltip } from 'vuetify/components/VTooltip';
import { mockResizeObserver } from 'jsdom-testing-mocks';

function mountComponent(withTooltipSlot?: boolean) {
  const contentOnly = { content: `<div>test content</div>` };
  const withTooltip = {
    content: `<div>test content</div>`,
    tooltipContent: `<div>test tooltip content</div>`,
  };

  return mount(TooltipWithSlot, {
    props: { tooltipProps: { attach: true } },
    slots: withTooltipSlot ? withTooltip : contentOnly,
  });
}

describe('TooltipWithSlot component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    mockResizeObserver();
    wrapper = mountComponent();
  });

  afterEach(() => {
    wrapper.unmount();
    vi.clearAllMocks();
  });

  it('should render the tooltip with the slot content without tooltip by default', () => {
    expect(wrapper.findComponent(VTooltip).exists()).toBe(false);
    expect(wrapper.html()).toContain('test content');
  });

  it('should render the tooltip with the slot content and tooltip', async () => {
    await wrapper.setProps({ tooltipProps: { text: 'test tooltip', attach: true } });

    expect(wrapper.findComponent(VTooltip).exists()).toBe(true);
    expect(wrapper.html()).toContain('test tooltip');
    expect(wrapper.html()).toContain('test content');
  });

  it('should render the tooltip with the slot content and tooltip slot content', async () => {
    const wrapperWithTooltip = mountComponent(true);

    expect(wrapperWithTooltip.findComponent(VTooltip).exists()).toBe(true);
    expect(wrapperWithTooltip.html()).toContain('test tooltip content');
    expect(wrapperWithTooltip.html()).toContain('test content');

    wrapperWithTooltip.unmount();
  });
});
