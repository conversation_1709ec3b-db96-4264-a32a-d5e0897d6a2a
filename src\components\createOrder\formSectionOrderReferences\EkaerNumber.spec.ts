import EkaerNumber from '@/components/createOrder/formSectionOrderReferences/EkaerNumber.vue';
import { useCreateOrderOrderReferencesFormStore } from '@/store/createOrder/formOrderReferences';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import TextField from '@/components/form/TextField.vue';
import { beforeEach } from 'vitest';

const requiredChar = '*';

describe('Order references - EkaerNumber component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(EkaerNumber);
  });

  it('marks component as required', async () => {
    const store = useCreateOrderOrderReferencesFormStore();
    store.ekaerNumberNotRequired = false;

    expect(wrapper.find('label > span').text()).toContain(requiredChar);
  });

  it("pastes raw text that does start with an 'E'", async () => {
    const pasteEvent = {
      clipboardData: {
        getData: vi.fn().mockReturnValueOnce('E1234567890123456789'),
      },
      preventDefault: () => {
        return false;
      },
    };

    const textField = wrapper.findComponent(TextField);
    textField.vm.$emit('paste', pasteEvent);

    await wrapper.vm.$nextTick();

    const store = useCreateOrderOrderReferencesFormStore();
    expect(store.ekaerNumber).toBe('12345678901234');

    const inputField: HTMLInputElement = textField.find('input').element as HTMLInputElement;
    expect(inputField.value).toBe('12345678901234');
  });

  it('disables the text field when ekaerNumberNotRequired is true', async () => {
    const store = useCreateOrderOrderReferencesFormStore();
    store.ekaerNumberNotRequired = true;

    await wrapper.vm.$nextTick();

    const textField = wrapper.findComponent(TextField);
    expect(textField.props('disabled')).toBe(true);
  });

  it('enables the text field when ekaerNumberNotRequired is false', async () => {
    const store = useCreateOrderOrderReferencesFormStore();
    store.ekaerNumberNotRequired = false;

    await wrapper.vm.$nextTick();

    const textField = wrapper.findComponent(TextField);
    expect(textField.props('disabled')).toBe(false);
  });

  it('clears the ekaerNumber when ekaerNumberNotRequired is checked', async () => {
    const store = useCreateOrderOrderReferencesFormStore();
    store.ekaerNumber = '12345678901234';
    store.ekaerNumberNotRequired = true;

    await wrapper.vm.$nextTick();

    expect(store.ekaerNumber).toBeUndefined();
  });
});
