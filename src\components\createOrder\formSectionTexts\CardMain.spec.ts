import CardMain from '@/components/createOrder/formSectionTexts/CardMain.vue';
import { mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';

describe('Sections Texts CardMain component with filtered Texts', () => {
  let wrapper: VueWrapper;
  beforeEach(() => {
    vi.mock('@/composables/createOrder/useOrderTexts', () => ({
      useOrderTexts() {
        return {
          filteredTexts: [
            {
              label: 'test',
              type: 'WA',
              maxLength: 600,
              visible: true,
              active: true,
            },
          ],
        };
      },
    }));
    wrapper = mount(CardMain);
  });

  it('check if checkboxField Component exist', async () => {
    expect(wrapper.findComponent({ name: 'SectionCard' }).exists()).toBe(true);
    expect(wrapper.findAllComponents({ name: 'checkbox-field' })).toHaveLength(1);

    const checkboxField = wrapper.findComponent({ name: 'checkbox-field' });

    checkboxField.vm.$emit('update:model-value', true);

    await nextTick();

    expect(wrapper.findComponent({ name: 'text-area' }).exists()).toBe(true);
  });

  it('check if TextArea Component exist', async () => {
    const checkboxField = wrapper.findComponent({ name: 'checkbox-field' });

    checkboxField.vm.$emit('update:model-value', true);

    await wrapper.vm.$nextTick();

    expect(wrapper.findAllComponents({ name: 'text-area' })).toHaveLength(1);
  });
});
