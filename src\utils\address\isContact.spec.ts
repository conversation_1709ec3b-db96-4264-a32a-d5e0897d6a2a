import { describe, it, expect } from 'vitest';
import { isContact } from '@/utils/address/isContact';
import { ContactData } from '@dfe/dfe-book-api';

describe('isContact utility function', () => {
  it('returns true for valid contact object', () => {
    const validContact: ContactData = { name: '<PERSON>', email: '<EMAIL>' };
    expect(isContact(validContact)).toBe(true);
  });

  it('returns false for null', () => {
    expect(isContact(null)).toBe(false);
  });

  it('returns false for undefined', () => {
    expect(isContact(undefined)).toBe(false);
  });

  it('returns false for non-object types', () => {
    expect(isContact(123)).toBe(false);
    expect(isContact('string')).toBe(false);
    expect(isContact(true)).toBe(false);
  });

  it('returns false for object missing name property', () => {
    const contactWithoutName = { email: '<EMAIL>' };
    expect(isContact(contactWithoutName)).toBe(false);
  });

  it('returns false for object missing email property', () => {
    const contactWithoutEmail = { name: 'John Doe' };
    expect(isContact(contactWithoutEmail)).toBe(false);
  });

  it('returns false for object with extra properties', () => {
    const contactWithExtra = { name: 'John Doe', email: '<EMAIL>', phone: '1234567890' };
    expect(isContact(contactWithExtra)).toBe(true); // Note: The function checks for minimum required properties, not exclusivity
  });
});
