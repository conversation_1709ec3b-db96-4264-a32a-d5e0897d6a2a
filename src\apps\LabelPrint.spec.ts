import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import LabelPrint from '@/apps/LabelPrint.vue';
import PdfViewer from '@/components/base/pdf/PdfViewer.vue';
import { ClientKey } from '@/types/client';
import SelectFieldWithMenuIcons from '@/components/form/SelectFieldWithMenuIcons.vue';
import { PrintLabelStartPosition } from '@dfe/dfe-book-api';
import { useLabelStore } from '@/store/createOrder/labelStore';
import { createClientMock } from '@test/util/mock-client';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import * as i18n from '@/plugins/i18n';
import { getComputedPreferences, usePreferencesMock } from '@test/util/mock-preferences';
import type { DFEClient } from '@dfe/dfe-frontend-client';
import type { Events } from '@/types/events';

async function triggerRenderAndSelectPosition(
  client: DFEClient<Events>,
  wrapper: VueWrapper,
  orderId: number | number[],
) {
  client.events.emit('renderLabelPreview', {
    file: 'someFileString',
    fileName: 'fileName',
    modalHeadline: 'modalHeadline',
    orderId: orderId,
  });
  await wrapper.vm.$nextTick();

  const selectField = wrapper.findComponent(SelectFieldWithMenuIcons);
  expect(selectField.exists()).toBe(true);

  selectField.vm.$emit('update:model-value', {
    title: 'labels.position_top_right.text',
    value: PrintLabelStartPosition.TOP_RIGHT,
    icon: '$custom_grid_topright-16',
  });
  await wrapper.vm.$nextTick();
}

describe('PrintLabels app', function () {
  let wrapper: VueWrapper;

  const client = createClientMock();
  beforeAll(() => {
    mockResizeObserver();
  });

  beforeEach(() => {
    vi.spyOn(i18n, 'useLocale').mockResolvedValueOnce();
    wrapper = mount(LabelPrint, {
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
        mocks: {
          $t: (text: string) => text,
        },
      },
    });
  });

  it('mounts correctly', () => {
    expect(wrapper).toBeDefined();
  });

  it('shows pdf viewer on client event', async () => {
    expect(wrapper.findComponent(PdfViewer).exists()).toBe(false);

    client.events.emit('renderLabelPreview', {
      file: 'someFileString',
      fileName: 'fileName',
      modalHeadline: 'modalHeadline',
      orderId: 101,
    });
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(PdfViewer).exists()).toBe(true);
  });

  it('emits printLabels event', async () => {
    const labelStore = useLabelStore();

    const fetchLabelsSpy = vi
      .spyOn(labelStore, 'fetchLabelsForExistingOrder')
      .mockResolvedValue({ orderLabel: 'anotherFileString' });

    const event = vi.fn();
    client.events.on('printLabels', event);

    await triggerRenderAndSelectPosition(client, wrapper, 101);

    expect(fetchLabelsSpy).toHaveBeenCalledTimes(1);

    await vi.waitFor(() => {
      expect(event).toHaveBeenCalledTimes(1);
      expect(event).toHaveBeenCalledWith({
        file: 'anotherFileString',
        fileName: 'fileName',
        modalHeadline: 'modalHeadline',
        orderId: 101,
      });
    });
  });

  it('emits printLabels event for bulk', async () => {
    const labelStore = useLabelStore();

    const fetchLabelsSpy = vi
      .spyOn(labelStore, 'fetchLabelsForBulkOrder')
      .mockResolvedValue('anotherFileString');

    const event = vi.fn();
    client.events.on('printLabels', event);

    await triggerRenderAndSelectPosition(client, wrapper, [101, 102]);

    expect(fetchLabelsSpy).toHaveBeenCalledTimes(1);

    await vi.waitFor(() => {
      expect(event).toHaveBeenCalledTimes(1);
      expect(event).toHaveBeenCalledWith({
        file: 'anotherFileString',
        fileName: 'fileName',
        modalHeadline: 'modalHeadline',
        orderId: [101, 102],
      });
    });
  });

  it('should show choose printer starting position only for dinA4_printer', async () => {
    const startingPositionTitle = wrapper.find('.text-h4');
    const startingPositionMessage = wrapper.find('.text-body-2');
    const selectField = wrapper.findComponent(SelectFieldWithMenuIcons);

    expect(startingPositionTitle.exists()).toBe(true);
    expect(startingPositionMessage.exists()).toBe(true);
    expect(selectField.exists()).toBe(true);
  });

  it('should not show choose printer starting position for other printers', async () => {
    usePreferencesMock.mockReturnValue(
      getComputedPreferences({ printerPresetName: 'other_printer' }),
    );

    wrapper = mount(LabelPrint);

    const startingPositionTitle = wrapper.find('.text-h4');
    const startingPositionMessage = wrapper.find('.text-body-2');
    const selectField = wrapper.findComponent(SelectFieldWithMenuIcons);

    expect(startingPositionTitle.exists()).toBe(false);
    expect(startingPositionMessage.exists()).toBe(false);
    expect(selectField.exists()).toBe(false);
  });
});
