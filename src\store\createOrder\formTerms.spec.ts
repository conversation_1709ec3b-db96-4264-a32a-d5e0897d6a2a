import { useTermStore } from '@/store/createOrder/formTerms';
import { useCreateOrderDataStore } from '@/store/createOrder/data';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useConfigStore } from '@/store/config';
import { mockServer } from '@/mocks/server';
import { initPinia } from '../../../test/util/init-pinia';
import { incoTerms } from '@/mocks/fixtures/incoTerms';
import { incotermConfig } from '@/mocks/fixtures/configs';
import { storeToRefs } from 'pinia';
import { HandOverSelection } from '@/types/hand-over';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';

describe('useTermStore', () => {
  let termStore: ReturnType<typeof useTermStore>,
    dataStore: ReturnType<typeof useCreateOrderDataStore>,
    formStore: ReturnType<typeof useCreateOrderFormStore>,
    addressStore: ReturnType<typeof useCreateOrderAddressesStore>,
    configStore: ReturnType<typeof useConfigStore>;

  beforeAll(() => {
    mockServer({
      environment: 'test',
      fixtures: {
        incoTerms,
        incotermConfig,
      },
    });
  });

  beforeEach(() => {
    initPinia();

    termStore = useTermStore();
    dataStore = useCreateOrderDataStore();
    formStore = useCreateOrderFormStore();
    addressStore = useCreateOrderAddressesStore();
    configStore = useConfigStore();
  });

  it('should return incoTerms for air export from_address - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;

    const expectedResult = [
      {
        id: 8,
        code: 'DAP',
        dachserCode: 'DAP',
        description:
          'Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading.',
        label: 'Delivered at Place',
      },
      {
        id: 9,
        code: 'DDP',
        dachserCode: 'DD4',
        description:
          'Duties are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, excl.Tax',
      },
      {
        id: 10,
        code: 'DDP',
        dachserCode: 'DD5',
        description:
          'Duties and Taxes are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, incl.Tax',
      },
      {
        id: 7,
        code: 'DPU',
        dachserCode: 'DPU',
        description: '',
        label: 'Delivered at Place Unloaded',
      },
    ];

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for air export from_address - to_port', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.port;

    const expectedResult = [
      {
        id: 6,
        code: 'CIP',
        dachserCode: 'CIP',
        description:
          'Similar to CPT. In that case, seller pays also the insurance for the transportation.',
        label: 'Carriage Insurance Paid',
      },
      {
        id: 5,
        code: 'CPT',
        dachserCode: 'CPT',
        description: 'The seller pays all fees until the destination location.',
        label: 'Carriage Paid To',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for air export from_port - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;
    orderType.value = OrderTypes.AirExportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 8,
        code: 'DAP',
        dachserCode: 'DAP',
        description:
          'Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading.',
        label: 'Delivered at Place',
      },
      {
        id: 9,
        code: 'DDP',
        dachserCode: 'DD4',
        description:
          'Duties are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, excl.Tax',
      },
      {
        id: 10,
        code: 'DDP',
        dachserCode: 'DD5',
        description:
          'Duties and Taxes are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, incl.Tax',
      },
      {
        id: 7,
        code: 'DPU',
        dachserCode: 'DPU',
        description: '',
        label: 'Delivered at Place Unloaded',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for sea export from_address - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType } = storeToRefs(formStore);

    transportType.value = Segment.SEA;

    const expectedResult = [
      {
        id: 8,
        code: 'DAP',
        dachserCode: 'DAP',
        description:
          'Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading.',
        label: 'Delivered at Place',
      },
      {
        id: 9,
        code: 'DDP',
        dachserCode: 'DD4',
        description:
          'Duties are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, excl.Tax',
      },
      {
        id: 10,
        code: 'DDP',
        dachserCode: 'DD5',
        description:
          'Duties and Taxes are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, incl.Tax',
      },
      {
        id: 7,
        code: 'DPU',
        dachserCode: 'DPU',
        description: '',
        label: 'Delivered at Place Unloaded',
      },
    ];

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for sea export from_address - to_port', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType } = storeToRefs(formStore);

    transportType.value = Segment.SEA;

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.port;

    const expectedResult = [
      {
        id: 12,
        code: 'CFR',
        dachserCode: 'CFR',
        description: '',
        label: 'Carriage Insurance Paid',
      },
      {
        id: 13,
        code: 'CIF',
        dachserCode: 'CIF',
        description: '',
        label: 'Carriage Insurance Paid',
      },
      {
        id: 6,
        code: 'CIP',
        dachserCode: 'CIP',
        description:
          'Similar to CPT. In that case, seller pays also the insurance for the transportation.',
        label: 'Carriage Insurance Paid',
      },
      {
        id: 5,
        code: 'CPT',
        dachserCode: 'CPT',
        description: 'The seller pays all fees until the destination location.',
        label: 'Carriage Paid To',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for air import from_address - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;
    orderType.value = OrderTypes.AirImportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 1,
        code: 'EXW',
        dachserCode: 'EXW',
        description: '',
        label: 'Ex Works',
      },
      {
        id: 2,
        code: 'FCA',
        dachserCode: 'FC1',
        description: '',
        label: 'Free Carrier Pick up Address',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return empty incoTerms for air import from_address - to_port', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;
    orderType.value = OrderTypes.AirImportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.port;

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject([]);
  });

  it('should return incoTerms for air import from_port - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.AIR;
    orderType.value = OrderTypes.AirImportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 4,
        code: 'FCA',
        dachserCode: 'FOA',
        description: '',
        label: 'Free Carrier Departure Airport',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for sea export from_port - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType } = storeToRefs(formStore);

    transportType.value = Segment.SEA;

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 8,
        code: 'DAP',
        dachserCode: 'DAP',
        description:
          'Cargo is delivered (but not unloaded) at destination location. The buyer is in charge of the customs clearance and unloading.',
        label: 'Delivered at Place',
      },
      {
        id: 9,
        code: 'DDP',
        dachserCode: 'DD4',
        description:
          'Duties are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, excl.Tax',
      },
      {
        id: 10,
        code: 'DDP',
        dachserCode: 'DD5',
        description:
          'Duties and Taxes are not part of our quotation. Incoterm refers to the requested service only.',
        label: 'Delivered Duty Paid incl. Duty, incl.Tax',
      },
      {
        id: 7,
        code: 'DPU',
        dachserCode: 'DPU',
        description: '',
        label: 'Delivered at Place Unloaded',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for sea import from_address - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.SEA;
    orderType.value = OrderTypes.SeaImportOrder;

    const expectedResult = [
      {
        id: 1,
        code: 'EXW',
        dachserCode: 'EXW',
        description: '',
        label: 'Ex Works',
      },
    ];

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return empty incoTerms for sea import from_address - to_port', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.SEA;
    orderType.value = OrderTypes.SeaImportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.default;
    consigneeHandOverSelection.value.selection = HandOverSelection.port;

    const { incoTermsOptions } = storeToRefs(termStore);
    const expectedResult = [
      {
        id: 1,
        code: 'EXW',
        dachserCode: 'EXW',
        description: '',
        label: 'Ex Works',
      },
    ];

    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });

  it('should return incoTerms for sea import from_port - to_address', async () => {
    await dataStore.fetchIncoTerms();
    await configStore.fetchIncotermsConfig();

    const { shipperHandOverSelection, consigneeHandOverSelection } = storeToRefs(addressStore);
    const { transportType, orderType } = storeToRefs(formStore);

    transportType.value = Segment.SEA;
    orderType.value = OrderTypes.SeaImportOrder;

    shipperHandOverSelection.value.selection = HandOverSelection.port;
    consigneeHandOverSelection.value.selection = HandOverSelection.default;

    const expectedResult = [
      {
        id: 15,
        code: 'FAS',
        dachserCode: 'FAS',
        description: '',
        label: 'Free Alongside Ship ',
      },
      {
        id: 14,
        code: 'FOB',
        dachserCode: 'FOB',
        description: '',
        label: 'Free on Board',
      },
    ];

    const { incoTermsOptions } = storeToRefs(termStore);
    expect(incoTermsOptions.value).toMatchObject(expectedResult);
  });
});
