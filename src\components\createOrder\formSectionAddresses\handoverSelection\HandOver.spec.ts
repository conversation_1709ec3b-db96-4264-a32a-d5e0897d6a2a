import type { VueWrapper } from '@vue/test-utils';
import { shallowMount } from '@vue/test-utils';
import HandOver from '@/components/createOrder/formSectionAddresses/handoverSelection/HandOver.vue';
import { HandOverSelection } from '@/types/hand-over';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { OrderTypes } from '@/enums';
import { orderAddress } from '@/store/sharedInitialStates';
import { addresses } from '@/mocks/fixtures/addresses';
import { contactDataMock } from '@/mocks/fixtures/contactData';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import { nextTick } from 'vue';
import { afterEach, expect } from 'vitest';
import { storeToRefs } from 'pinia';
import { AirExportQuoteInformation, PortType } from '@dfe/dfe-book-api';
import PortSelection from '@/components/createOrder/formSectionAddresses/airAndSeaOrder/PortSelection.vue';

const props = {
  defaultLabel: 'defaultLabel',
  alternateAddressLabel: 'alternateAddressLabel',
  airportSelectionLabel: 'airportSelectionLabel',
  addressCardLabel: 'addressCardLabel',
  value: { selection: HandOverSelection.default },
};

describe('HandOver component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = shallowMount(HandOver, { props });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('sets AddressCard props isEditable and isDeletable for air export orders accordingly', async () => {
    const addressStore = useCreateOrderAddressesStore();
    const formStore = useCreateOrderFormStore();

    await wrapper.setProps({
      modelValue: { selection: HandOverSelection.alternateAddress },
    });

    let addressCard = wrapper.findComponent(AddressCard);

    expect(addressCard.props('isEditable')).toBe(true);
    expect(addressCard.props('isDeletable')).toBe(true);

    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 123,
    } as AirExportQuoteInformation;

    addressStore.shipperHandOverSelection.selection = HandOverSelection.alternateAddress;
    await wrapper.vm.$nextTick();

    addressCard = wrapper.findComponent(AddressCard);

    expect(addressCard.props('isEditable')).toBe(true);
    expect(addressCard.props('isDeletable')).toBe(false);

    addressStore.shipperHandOverSelection.selection = HandOverSelection.default;
    addressStore.consigneeHandOverSelection.selection = HandOverSelection.alternateAddress;
    await wrapper.vm.$nextTick();

    addressCard = wrapper.findComponent(AddressCard);

    expect(addressCard.props('isEditable')).toBe(true);
    expect(addressCard.props('isDeletable')).toBe(false);
  });

  it('sets AddressCard props isShipperAddress', async () => {
    await wrapper.setProps({ modelValue: { selection: HandOverSelection.alternateAddress } });
    await nextTick();

    let addressCard = wrapper.findComponent(AddressCard);

    expect(addressCard.props('isShipperAddress')).toBe(false);

    await wrapper.setProps({
      modelValue: { selection: HandOverSelection.alternateAddress },
      isShipperAddress: true,
    });

    addressCard = wrapper.findComponent(AddressCard);

    expect(addressCard.props('isShipperAddress')).toBe(true);
  });

  it('emits event if radio selection changes and resets port, if selection is default or alternate address', async () => {
    const { port } = storeToRefs(useCreateOrderAddressesStore());

    port.value = { displayName: 'Munich (MUC)', code: 'MUC', type: PortType.AIRPORT };

    const radioGroup = wrapper.findComponent({ name: 'v-radio-group' });

    // Switch to alternate address
    radioGroup.vm.$emit('update:modelValue', HandOverSelection.alternateAddress);
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:modelValue')).toHaveLength(1);
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([
      {
        selection: HandOverSelection.alternateAddress,
        address: { ...orderAddress() },
      },
    ]);
    expect(port.value).toBeUndefined();

    port.value = { displayName: 'Munich (MUC)', code: 'MUC', type: PortType.AIRPORT };

    // Switch (back) to default
    radioGroup.vm.$emit('update:modelValue', HandOverSelection.default);
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:modelValue')).toHaveLength(2);
    expect(wrapper.emitted('update:modelValue')?.[1]).toEqual([
      {
        selection: HandOverSelection.default,
      },
    ]);
    expect(port.value).toBeUndefined();

    // Switch to airport
    radioGroup.vm.$emit('update:modelValue', HandOverSelection.port);
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:modelValue')).toHaveLength(3);
    expect(wrapper.emitted('update:modelValue')?.[2]).toEqual([
      {
        selection: HandOverSelection.port,
        port: undefined,
      },
    ]);
  });

  it('emits event if alternate address changes', async () => {
    await wrapper.setProps({ modelValue: { selection: HandOverSelection.alternateAddress } });
    await nextTick();

    const alternateAddress = wrapper.findComponent(AddressCard);
    alternateAddress.vm.$emit('update:model-value', addresses[1]);
    await nextTick();

    expect(wrapper.emitted('update:alternate-address')).toHaveLength(1);
    expect(wrapper.emitted('update:alternate-address')?.[0]).toEqual([addresses[1]]);
  });

  it('emits event if alternate contact data changes', async () => {
    await wrapper.setProps({
      modelValue: { selection: HandOverSelection.alternateAddress, address: orderAddress() },
    });
    await nextTick();
    const alternateAddress = wrapper.findComponent(AddressCard);
    alternateAddress.vm.$emit('update-contact', contactDataMock);
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:alternate-contact')).toHaveLength(1);
    expect(wrapper.emitted('update:alternate-contact')?.[0]).toEqual([contactDataMock]);
  });

  it('disables port selection for Air import order', async () => {
    await wrapper.setProps({
      modelValue: {
        selection: HandOverSelection.port,
        port: { displayName: 'Munich (MUC)', code: 'MUC', type: PortType.AIRPORT },
      },
      airportSelectionDisabled: true,
    });

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirImportOrder;

    expect(wrapper.findComponent(PortSelection).props('disabled')).toBe(true);
  });
});
