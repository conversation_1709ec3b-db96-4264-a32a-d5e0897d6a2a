import { fileURLToPath } from 'node:url';
import { mergeConfig } from 'vite';
import { configDefaults, defineConfig } from 'vitest/config';

import viteConfig from './vite.config.js';

export default mergeConfig(
  viteConfig({ command: 'serve', mode: 'test' }),
  defineConfig({
    test: {
      setupFiles: fileURLToPath(new URL('./test/setup-tests.ts', import.meta.url)),
      environment: 'jsdom',
      exclude: [...configDefaults.exclude, 'e2e/*'],
      root: fileURLToPath(new URL('.', import.meta.url)),
      globals: true,
      poolOptions: {
        vmThreads: {
          memoryLimit: '512MB',
        },
      },
      server: {
        deps: {
          inline: [
            'vuetify',
            'vue-pdf-embed',
            'flag-icons',
            'v-phone-input > v-phone-input',
            'v-phone-input',
            '@dfe/dfe-frontend-shared-components',
          ],
        },
      },
      coverage: {
        provider: "v8",
        reporter: ["text", "lcov", "html", "cobertura", "json", "json-summary"],
        reportsDirectory: "./coverage",
        include: ["src/**/*.{ts,js,vue}"],
        exclude: [
          "src/dev/**/*",
          "src/types/**/*",
          "src/**/generated*",
          "src/mocks/**/*",
          "src/*.d.ts",
          "src/**/*.d.ts",
        ],
      },
    },
  }),
);
