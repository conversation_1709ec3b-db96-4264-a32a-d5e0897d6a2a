<template>
  <VMenu location="bottom" :attach="$appRoot">
    <template #activator="{ props }">
      <VBtn
        v-bind="props"
        variant="outlined"
        size="small"
        color="primary"
        class="activator-button"
        :ripple="false"
      >
        <MaterialSymbol left size="24">
          <arrow-dropdown-icon />
        </MaterialSymbol>
        <slot />
      </VBtn>
    </template>
    <VList :items="items" class="text-body-2">
      <VListItem
        v-for="item in items"
        :key="item.value"
        v-data-test="'additional-reference'"
        :data-test-details="'bo-additional-reference-' + item.value"
        @click="selectItem(item.value)"
        >{{ item.text }}
      </VListItem>
    </VList>
  </VMenu>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import ArrowDropdownIcon from '@dfe/dfe-frontend-styles/assets/icons/arrow_drop_down-24px.svg';
import type { TranslateResult } from 'vue-i18n';

interface Props {
  items: {
    value: string;
    text: TranslateResult;
  }[];
}

defineProps<Props>();
const emit = defineEmits(['input']);

const selectItem = (option: string) => {
  emit('input', option);
};
</script>
