<template>
  <div v-if="addresses.items.length">
    <h4 class="text-h4 mt-6 mb-4 d-flex align-center">
      {{ addresses.title }}
    </h4>
    <VRow class="ma-auto">
      <template v-for="{ code } in addresses.items">
        <VCol v-if="showAddress[code]" :key="code" cols="12" sm="6" lg="4">
          <AddressCard
            v-model="getFurtherAddress(code).address"
            :is-customer="false"
            :header="useFurtherAddressTypeLabel(code)"
            :contact-data="contactDataFurtherAddresses[code]"
            :show-delete="true"
            @update-contact="contactDataFurtherAddresses[code] = $event"
            @delete-address="showAddress[code] = false"
          />
        </VCol>
      </template>
    </VRow>
    <div v-if="useShowAddButtons(addresses.items, showAddress)" class="d-flex button-row">
      <div v-for="{ code } in addresses.items" :key="code">
        <AddButton
          v-if="!showAddress[code]"
          v-data-test="'additional-adresses'"
          :label="useFurtherAddressTypeLabel(code)"
          variant="text"
          :data-test-details="'bo-additional-adresses-' + code"
          @add-new-item="addNewFurtherAddress(code)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AddButton from '@/components/createOrder/AddButton.vue';
import AddressCard from '@/components/createOrder/formSectionAddresses/AddressCard.vue';
import {
  useCheckFurtherAddresses,
  useShowAddButtons,
} from '@/composables/createOrder/addresses/useCheckAdditionalAddresses';
import useFurtherAddressTypeLabel from '@/composables/createOrder/addresses/useFurtherAddressTypeLabel';
import type { FurtherAddressType } from '@/enums';
import { FurtherAddressTypesList } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { ClientKey } from '@/types/client';
import { storeToRefs } from 'pinia';
import { computed, inject, onBeforeUnmount, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

type AdditionalAddresses =
  | FurtherAddressType['customsAgent']
  | FurtherAddressType['importer']
  | FurtherAddressType['N1'];

const props = defineProps({
  withContacts: { type: Boolean, required: false, default: false },
});

const client = inject(ClientKey);
const { t } = useI18n();

const showAddress = ref({
  [FurtherAddressTypesList.customsAgent]: false,
  [FurtherAddressTypesList.importer]: false,
  [FurtherAddressTypesList.N1]: false,
});

const addressStore = useCreateOrderAddressesStore();
const { contactDataFurtherAddresses } = storeToRefs(addressStore);
const createOrderFormStore = useCreateOrderFormStore();
const { isOrderLoaded } = storeToRefs(createOrderFormStore);

const { getFurtherAddress } = addressStore;

const addresses = computed(() => {
  return {
    title: t('labels.additional_addresses.text'),
    items: [
      { code: FurtherAddressTypesList.customsAgent },
      { code: FurtherAddressTypesList.importer },
      { code: FurtherAddressTypesList.N1 },
    ] as { code: AdditionalAddresses }[],
  };
});

const addNewFurtherAddress = (addressType: AdditionalAddresses) => {
  addressStore.addNewFurtherAddress(addressType);
  showAddress.value[addressType] = true;
};

if (!props.withContacts) {
  contactDataFurtherAddresses.value = {};
}

const handleClearCreateOrderFormData = () => useCheckFurtherAddresses(showAddress.value);
client?.events.on('clearCreateOrderFormData', handleClearCreateOrderFormData);

onBeforeUnmount(() => {
  client?.events.off('clearCreateOrderFormData', handleClearCreateOrderFormData);
});

watch(
  () => isOrderLoaded.value,
  async () => useCheckFurtherAddresses(showAddress.value),
);
</script>

<style lang="scss" scoped>
@use '@dfe/dfe-frontend-styles/build/scss/variables';
.button-row {
  flex-wrap: wrap;
}
</style>
