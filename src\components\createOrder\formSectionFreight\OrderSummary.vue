<template>
  <div class="bg-grey-lighten-5 rounded pa-4">
    <h4 class="text-h4 mb-4">{{ t('labels.summary_label.text') }}</h4>
    <VRow>
      <VCol v-if="isFullContainerLoad" ref="totalContainerQuantityRef" cols="auto" class="d-flex">
        <span class="text-label-2 d-inline-block mt-2 mr-2">
          {{ t('labels.total_container_quantity.text') }}:
        </span>
        <span class="text-body-2 d-inline-block mt-2">
          {{ fullContainerLoads.length }}
        </span>
      </VCol>
      <VCol cols="auto" class="d-flex">
        <span class="text-label-2 d-inline-block mt-2 mr-2">
          {{ t(getTranslationKeyTotalQuantity()) }}:
        </span>
        <NumberField
          v-if="canEditNumberOfLables"
          v-model="manualNumberOfLabels"
          v-data-test="'order-number-of-labels'"
          :min="1"
          :max="999"
          class="size-sm"
        />
        <span v-else class="text-body-2 d-inline-block mt-2">
          {{ numberOfLabels }}
        </span>
      </VCol>
      <VCol cols="auto">
        <span class="text-label-2 d-inline-block mt-2 mr-2">
          {{ t('labels.total_volume_label.text') }}:
        </span>
        <span class="text-body-2"> {{ totalVolumeLocalized }} m<sup>3</sup> </span>
      </VCol>
      <VCol cols="auto">
        <span class="text-label-2 d-inline-block mt-2 mr-2">
          {{ t('labels.total_weight.text') }}:
        </span>
        <span class="text-body-2 d-inline-block mt-2">
          {{ totalWeightLocalized }}
        </span>
      </VCol>
      <VCol v-if="isFullContainerLoad" ref="totalVGMRef" cols="auto" class="d-flex">
        <span class="text-label-2 d-inline-block mt-2 mr-2">
          {{ t('labels.total_vgm.text') }}:
        </span>
        <span class="text-body-2 d-inline-block mt-2">
          {{ totalVgmLocalized }}
        </span>
      </VCol>
      <VCol v-if="!isAirOrder && !isSeaOrder" ref="goodsValueSection" cols="auto" class="d-flex">
        <span class="text-label-2 d-inline-block mt-2 mr-2">
          {{ t('labels.goods_value.text') }}:
        </span>
        <NumberField
          v-model="totalValue"
          :max="9999999.99"
          :min="0.01"
          :allowed-decimals="2"
          class="size-sm mr-2"
          :disabled="isDisabledForPriceRelevantChanges"
        />
        <SelectField
          v-if="showCurrencySelectForm"
          v-model="preferredCurrency"
          :items="currencyItems"
          class="size-xs min-w-100"
          :disabled="isDisabledForPriceRelevantChanges"
          :placeholder="t('labels.currency_label.text')"
          :required="!!totalValue"
        />
        <span v-else class="text-body-2">{{ preferredCurrency }}</span>
      </VCol>
    </VRow>
  </div>
</template>

<script setup lang="ts">
import NumberField from '@/components/form/NumberField.vue';
import SelectField from '@/components/form/SelectField.vue';
import { useCurrencies } from '@/composables/data/useCurrencies';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { useNumberFormat } from '@dfe/dfe-frontend-composables';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const { data: currencies } = useCurrencies();
const { data: customerSettings } = useCustomerSettings();

const createOrderFormStore = useCreateOrderFormStore();
const {
  isRoadForwardingOrder,
  isRoadCollectionOrder,
  isAirOrder,
  isSeaOrder,
  preferredCurrency,
  isDisabledForPriceRelevantChanges,
} = storeToRefs(createOrderFormStore);

const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
const {
  totalValue,
  totalWeight,
  totalVolume,
  numberOfLabels,
  manualNumberOfLabels,
  totalVGM,
  fullContainerLoads,
  isFullContainerLoad,
} = storeToRefs(createOrderOrderLineFormStore);

const currencyItems = computed(() =>
  (Array.isArray(currencies.value) ? currencies.value : []).map(({ currencyCode }) => ({
    text: currencyCode,
    value: currencyCode,
  })),
);

const showCurrencySelectForm = computed(() => {
  return currencies?.value && currencies?.value?.length > 1;
});

const canEditNumberOfLables = computed(() => {
  return isRoadForwardingOrder.value && customerSettings.value?.manualNumberOfLabels;
});

const totalWeightLocalized = useNumberFormat(totalWeight, { style: 'unit', unit: 'kilogram' });
const totalVgmLocalized = useNumberFormat(
  computed(() => totalVGM.value || 0),
  {
    style: 'unit',
    unit: 'kilogram',
  },
);
const totalVolumeLocalized = useNumberFormat(totalVolume, {
  minimumFractionDigits: 0,
  maximumFractionDigits: 3,
});

const getTranslationKeyTotalQuantity = () => {
  if (isFullContainerLoad?.value) return 'labels.total_quantity_sea.text';
  if (isAirOrder.value || isSeaOrder.value || isRoadCollectionOrder.value)
    return 'labels.total_quantity.text';
  return 'labels.number_of_labels.text';
};
</script>

<style lang="scss" scoped>
@use '@/styles/variables' as vars;

.size-sm {
  width: vars.$form-input-width-sm;
}
.size-xs {
  width: vars.$form-input-width-xs;
}
.min-w-100 {
  min-width: 100px;
}
</style>
