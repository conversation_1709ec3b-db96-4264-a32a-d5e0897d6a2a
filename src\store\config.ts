import type { AirProductRoutingConfig, IncotermConfig } from '@dfe/dfe-configserver-api-module';
import { defineStore } from 'pinia';

export interface ConfigState {
  incoterms?: Partial<IncotermConfig>;
  airProductRouting?: Partial<AirProductRoutingConfig>;
}

export const initialState: ConfigState = {
  incoterms: {},
  airProductRouting: {},
};

export const useConfigStore = defineStore('configStore', {
  state: (): ConfigState => initialState,
  actions: {
    async fetchIncotermsConfig() {
      try {
        const { data } = await this.api.config.incoterms.getIncotermConfigV1();
        this.incoterms = data ?? {};
      } catch (error) {
        this.client?.log.error('Failed to fetch incoterms config', 'dfe-book-frontend', error);
      }
    },
    async fetchAirProductRoutingConfig() {
      try {
        const { data } = await this.api.config.airProductRouting.getAirProductRoutingConfigV1();
        this.airProductRouting = data ?? {};
      } catch (error) {
        this.client?.log.error(
          'Failed to fetch air-product-routing config',
          'dfe-book-frontend',
          error,
        );
      }
    },
  },
});
