@use '@dfe/dfe-frontend-styles/build/scss/variables' as vars;
[dfe-book-frontend] {
:deep(.v-text-field) {
  .v-field--single-line {
    .v-text-field__suffix,
    .v-text-field__prefix {
      color: var(--color-base-grey-700);
      line-height: 20px !important;
      padding: 8px 0;
      opacity: 1;
    }
  }

  .v-field--disabled.v-field--single-line .v-text-field__suffix,
  .v-field--disabled.v-field--single-line .v-text-field__prefix {
    color: var(--color-base-grey-500);
  }

  .v-messages__message {
    color: var(--color-base-grey-700);
    line-height: vars.$size-line-height-xsm;
  }

  .v-select__slot {
    padding-left: 4px;
  }

  &.v-input--is-disabled fieldset {
    background-color: var(--color-base-grey-100);
    border-color: var(--color-base-grey-400) !important;
  }

  &.v-input--is-disabled input {
    color: var(--color-base-grey-500);
    -webkit-text-fill-color: var(--color-base-grey-500); /* required on iOS */
  }

  &.v-input--error .v-messages__message {
    color: var(--color-base-red-500);
  }

  &.v-input--error fieldset {
    border: 1px solid var(--color-base-red-500);
  }

  &:hover fieldset {
    border-color: var(--color-base-grey-700);
  }

  &.v-input--disabled {
    cursor: not-allowed;
    pointer-events: auto;
  }
}

:deep(.theme--light.v-text-field--outlined.v-input--is-focused fieldset) {
  border: 1px solid currentColor;
}

:deep(.v-input--density-compact) {
  --v-input-control-height: #{vars.$form-input-height};
}

input::placeholder {
  color: var(--color-base-grey-700);
}

:deep(.v-input) {
  .v-field__input {
    &::placeholder {
      color: vars.$form-text-color-placeholder-default;
      opacity: 1;
    }
  }
  input::placeholder {
    color: vars.$form-text-color-placeholder-default;
    opacity: 1;
  }

  .v-field__outline {
    --v-field-border-opacity: 1;
    color: vars.$form-border-color-default;
  }

  .v-field__overlay {
    background-color: rgb(var(--v-theme-white));
  }

  .v-field__append-inner,
  .v-field__prepend-inner {
    color: vars.$form-text-color-placeholder-default;
    z-index: 1;
  }
  &.v-text-field {
    &:not(.v-input--disabled):not(.v-field--disabled):hover {
      .v-field__outline {
        color: vars.$form-border-color-hover;
      }
    }
  }

  .v-field--focused {
    .v-field__outline {
      --v-field-border-width: 1px;
      color: vars.$form-border-color-focused;
    }
  }

  .v-field--disabled {
    opacity: 1;

    .v-field__overlay {
      background-color: vars.$form-background-color-disabled;
    }

    .v-field__input {
      color: vars.$form-text-color-disabled;

      &::placeholder, & input::placeholder {
        color: vars.$form-text-color-placeholder-disabled;
      }
    }

    .v-field__outline {
        color: vars.$form-border-color-disabled;
    }

    .v-field__append-inner,
    .v-field__prepend-inner {
      color: vars.$form-text-color-placeholder-disabled;
    }
  }

  &.v-input--error {
    .v-field__outline {
      color: vars.$form-border-color-error;
    }
  }

  .v-input__details {
    color: rgb(var(--v-theme-grey-700));
    letter-spacing: normal;
    padding-left: 0;
    padding-right: 0;

    .v-messages__message {
      transition: none !important;
    }
  }

  &.text-right input {
    text-align: right;
  }

  &.text-center input {
    text-align: center;
  }

  .v-text-field__prefix,
  .v-text-field__suffix {
    opacity: 1;
  }
}
}