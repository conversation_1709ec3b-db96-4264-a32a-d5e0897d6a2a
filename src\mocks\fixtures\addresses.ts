import { Address as AddressBookAddress } from '@dfe/dfe-address-api';
import { OrderAddress } from '@dfe/dfe-book-api';

export const addresses: OrderAddress[] = [
  {
    id: 1,
    name: 'Flink CPS',
    name2: 'Nationwide CO. LTD',
    name3: '',
    countryCode: 'DE',
    street: 'Hauptstraße 12',
    city: 'Rottenburg am Neckar',
    postcode: '72108',
    supplement: '',
    gln: '12345',
    contact: {
      name: 'name',
    },
    neutralizeAddress: true,
  },
  {
    id: 2,
    name: 'Flink CPS',
    name2: '',
    name3: '',
    street: 'Sieglestraße 25',
    city: 'Stuttgart',
    postcode: '70469',
    countryCode: 'DE',
  },
  {
    id: 3,
    name: 'Ikea1',
    name2: 'Eching',
    street: 'Heisenbergstraße 17',
    city: 'München',
    postcode: '85386',
    countryCode: 'DE',
  },
  {
    id: 4,
    name: 'Ikea2',
    name2: '<PERSON><PERSON><PERSON><PERSON>',
    street: 'Brunnthalerstr. 1',
    city: 'Taufkirchen',
    postcode: '82024',
    countryCode: 'DE',
  },
  {
    id: 5,
    name: 'Ikea3',
    street: 'Some Street 4',
    city: 'Budapest',
    postcode: '11111',
    countryCode: 'HU',
  },
  {
    id: 6,
    name: 'Dyson Ireland Ltd.',
    street: 'Black Church Business Park',
    city: ' Rathcoole',
    postcode: '06175',
    countryCode: 'IE',
    supplement: 'Dublin',
  },
];

export const addressBookAddresses: AddressBookAddress[] = [
  {
    id: 1,
    name: 'Flink CPS',
    name2: 'Nationwide CO. LTD',
    name3: '',
    city: 'Rottenburg am Neckar',
    countryCode: 'DE',
    street: 'Hauptstraße 1',
    addressPresets: {
      deliveryInstructions: 'Please deliver to the back door',
      invoiceText: 'Invoice will be sent via email',
    },
  },
  {
    id: 2,
    name: 'Flink CPS',
    city: 'Stuttgart',
    countryCode: 'DE',
    street: 'Sieglestraße 25',
  },
  {
    id: 3,
    name: 'Dyson Ireland Ltd.',
    street: 'Black Church Business Park',
    city: ' Rathcoole',
    postcode: '06175',
    countryCode: 'IE',
    supplement: 'Dublin',
    addressPresets: {
      deliveryInstructions: 'Please deliver to the back door',
      invoiceText: 'Invoice will be sent via email',
    },
  },
];
