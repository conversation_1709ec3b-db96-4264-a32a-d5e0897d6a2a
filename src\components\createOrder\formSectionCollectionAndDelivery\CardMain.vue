<template>
  <CollectionAndDeliveryRoad v-if="isRoadOrder" />
  <CollectionAirAndSea
    v-else-if="
      (isAirOrder || isSeaOrder) && shipperHandOverSelection.selection !== HandOverSelection.port
    "
  />
  <DeliveryAirAndSea v-else-if="shipperHandOverSelection.selection === HandOverSelection.port" />
</template>

<script setup lang="ts">
import CollectionAndDeliveryRoad from '@/components/createOrder/formSectionCollectionAndDelivery/road/CollectionAndDeliveryRoad.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import DeliveryAirAndSea from '@/components/createOrder/formSectionCollectionAndDelivery/airAndSea/DeliveryAirAndSea.vue';
import CollectionAirAndSea from '@/components/createOrder/formSectionCollectionAndDelivery/airAndSea/CollectionAirAndSea.vue';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { HandOverSelection } from '@/types/hand-over';

const createOrderFormStore = useCreateOrderFormStore();
const { isRoadOrder, isAirOrder, isSeaOrder } = storeToRefs(createOrderFormStore);
const addressStore = useCreateOrderAddressesStore();
const { shipperHandOverSelection } = storeToRefs(addressStore);
</script>
