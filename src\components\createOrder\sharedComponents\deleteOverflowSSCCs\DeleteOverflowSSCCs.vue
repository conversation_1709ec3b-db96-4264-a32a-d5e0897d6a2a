<template>
  <ModalWrapper
    v-model="showModal"
    :headline="t('labels.delete_labels.text')"
    size="md"
    @close="cancel"
  >
    <template #body>
      <div class="d-flex flex-wrap">
        <span>{{ t('labels.select_labels_to_delete.text', [numberOfLabelsToDelete]) }}</span>
        <span class="mt-4">{{ t('messages.id6998.text') }}</span>
      </div>

      <DfeBanner :value="showErrorMessageOnSsccMissmatch" type="error" class="mt-4">
        <div class="text-body-2">
          <strong>{{ t('messages.id6999.text') }}</strong>
        </div>
      </DfeBanner>

      <div v-for="sscc in generatedSSccs" :key="sscc" class="mt-4">
        <VCheckbox v-model="selectedSSccs[sscc]" hide-details="auto" density="compact">
          <template #label>
            <span
              >{{ sscc.slice(0, -3) }}<strong>{{ sscc.slice(-3) }}</strong></span
            >
          </template>
        </VCheckbox>
      </div>
    </template>
    <template #footer>
      <VBtn v-data-test="'confirm-btn'" size="small" color="primary" class="mr-1" @click="confirm">
        {{ t('labels.delete_labels.text') }}
      </VBtn>
      <VBtn v-data-test="'cancel-btn'" size="small" color="primary" variant="text" @click="cancel">
        {{ t('labels.cancel_label.text') }}
      </VBtn>
    </template>
  </ModalWrapper>
</template>
<script setup lang="ts">
import DfeBanner from '@/components/base/banner/DfeBanner.vue';
import ModalWrapper from '@/components/base/modal/ModalWrapper.vue';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { ClientKey } from '@/types/client';
import { storeToRefs } from 'pinia';
import { computed, inject, onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const client = inject(ClientKey);

const showModal = defineModel<boolean>({ default: false });
const showErrorMessageOnSsccMissmatch = ref(false);
const createOrderOrderLineFormStore = useCreateOrderOrderLineFormStore();
const { differenceInGeneratedNumberOfLabels, differenceInManualNumberOfLabels, generatedSSccs } =
  storeToRefs(createOrderOrderLineFormStore);
const { data: customerSettings } = useCustomerSettings();

const selectedSSccs = ref<SelectedSSCCsType>({});

defineEmits(['close', 'cancel', 'confirm']);

type SelectedSSCCsType = {
  [key: string]: boolean;
};

let callbackFunctionToCall: (() => void) | null = null;

const cancel = () => {
  showErrorMessageOnSsccMissmatch.value = false;
  showModal.value = false;
  resetSelectedSSccs();
};

const confirm = () => {
  const ssccsToRemove = Object.keys(selectedSSccs.value).filter((key) => selectedSSccs.value[key]);

  if (ssccsToRemove.length !== numberOfLabelsToDelete.value) {
    showErrorMessageOnSsccMissmatch.value = true;
    return;
  }

  createOrderOrderLineFormStore.removeSSccs(ssccsToRemove);

  cancel();
  if (callbackFunctionToCall) {
    callbackFunctionToCall();
  }
};

const numberOfLabelsToDelete = computed(() => {
  return customerSettings.value?.manualNumberOfLabels
    ? differenceInManualNumberOfLabels.value
    : differenceInGeneratedNumberOfLabels.value;
});

const handleShowDeleteOverflowSSCCs = (callbackFunction: (() => void) | null) => {
  showModal.value = true;
  callbackFunctionToCall = callbackFunction;
};

client?.events.on('showDeleteOverflowSSCCs', handleShowDeleteOverflowSSCCs);

onBeforeUnmount(() => {
  client?.events.off('showDeleteOverflowSSCCs', handleShowDeleteOverflowSSCCs);
});

onMounted(() => {
  resetSelectedSSccs();
  showErrorMessageOnSsccMissmatch.value = false;
});

const resetSelectedSSccs = () => {
  Object.keys(selectedSSccs.value).forEach((key) => {
    selectedSSccs.value[key] = false;
  });
};
</script>
