import CardMain from '@/components/createOrder/formSectionAccountingAdditionalServices/collectionOrder/CardMain.vue';
import { useCreateOrderAddressesStore } from '@/store/createOrder/formAddresses';
import { initPinia } from '@test/util/init-pinia';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { beforeEach, describe, expect, it } from 'vitest';
import { ConsigneeAddressType } from '@/enums';
import { storeToRefs } from 'pinia';

describe('Accounting & Additional Services component', () => {
  let wrapper: VueWrapper;

  beforeAll(() => {
    initPinia();
  });

  beforeEach(() => {
    wrapper = mount(CardMain, {
      global: {
        provide: {
          $t: (key: string) => key,
        },
      },
    });
  });

  it('shows select for freight payers and hides freight payer output if consignee is not a customer', async () => {
    const store = useCreateOrderAddressesStore();
    store.isCustomer = false;

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent({ ref: 'freightPayersSelect' }).exists()).toBe(true);
    expect(wrapper.findComponent({ ref: 'freightPayerOutput' }).exists()).toBe(false);
  });

  it('disables select for freight payers if different consignee address is false', async () => {
    const store = useCreateOrderAddressesStore();
    const { consigneeAddressType } = storeToRefs(store);
    consigneeAddressType.value = ConsigneeAddressType.PRINCIPALS_ADDRESS;

    await wrapper.vm.$nextTick();

    const freightPayersSelect = wrapper.findComponent({ ref: 'freightPayersSelect' });
    expect(freightPayersSelect.props('disabled')).toBe(true);
  });

  it('enables select for freight payers if different consignee address is true', async () => {
    const store = useCreateOrderAddressesStore();
    const { consigneeAddressType } = storeToRefs(store);
    consigneeAddressType.value = ConsigneeAddressType.DIFFERENT_CONSIGNEE_ADDRESS;

    await wrapper.vm.$nextTick();

    const freightPayersSelect = wrapper.findComponent({ ref: 'freightPayersSelect' });
    expect(freightPayersSelect.props('disabled')).toBe(false);
  });

  it('does not render tooltip when consignee address is not different', async () => {
    const store = useCreateOrderAddressesStore();
    const { consigneeAddressType } = storeToRefs(store);
    consigneeAddressType.value = ConsigneeAddressType.PRINCIPALS_ADDRESS;

    await wrapper.vm.$nextTick();

    const tooltip = wrapper.findComponent({ ref: 'tooltip' });
    expect(tooltip.exists()).toBe(false);
  });
});
