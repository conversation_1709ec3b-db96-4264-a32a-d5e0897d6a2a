import type { VueWrapper } from '@vue/test-utils';
import InfoTooltipWithSlot from '@/components/form/InfoTooltipWithSlot.vue';
import InfoButtonWithTooltip from '@/components/base/InfoButtonWithTooltip.vue';
import { beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';

describe('InfoTooltipWithSlot component', () => {
  let wrapper: VueWrapper;

  beforeEach(() => {
    wrapper = mount(InfoTooltipWithSlot, {
      slots: {
        default: 'Tooltip content',
      },
    });
  });

  it('should render the tooltip with the slot content', () => {
    const infoButtonWithTooltip = wrapper.findComponent(InfoButtonWithTooltip);

    expect(wrapper.text()).toBe('Tooltip content');
    expect(infoButtonWithTooltip.exists()).toBe(true);
  });
});
