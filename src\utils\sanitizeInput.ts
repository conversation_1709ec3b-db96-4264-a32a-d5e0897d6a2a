const sanitizeNumbers = (input: string, decimalAllowed?: boolean): number => {
  const sanitized = [];
  const regex = decimalAllowed ? /[0-9,.]/ : /\d/;
  for (const char of input) {
    if (regex.test(char)) {
      sanitized.push(char);
    }
  }

  const sanitizedString = sanitized.join('');
  return decimalAllowed
    ? parseFloat(sanitizedString.replace(/,/g, ''))
    : parseInt(sanitizedString, 10);
};

export default sanitizeNumbers;
