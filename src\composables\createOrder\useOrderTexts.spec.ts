import { useOrderTexts } from '@/composables/createOrder/useOrderTexts';
import { describe, expect, it } from 'vitest';
import { withSetup } from '@test/util/with-setup';
import { mockServer } from '@/mocks/server';
import { Server } from 'miragejs';
import { createPinia, setActivePinia } from 'pinia';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { Segment } from '@dfe/dfe-book-api';
import { OrderTypes } from '@/enums';

describe('useOrderTexts', () => {
  beforeAll(() => {
    setActivePinia(createPinia());
  });

  it('return no order texts when customer settings are false - RoadCollectionOrder ', async () => {
    const server: Server = mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: {
          deliveryInstructions: false,
          goodsDescription: false,
          invoiceText: false,
        },
      },
    });
    const { filteredTexts } = withSetup(() => {
      useCustomerSettings();
      const formStore = useCreateOrderFormStore();
      formStore.orderType = OrderTypes.RoadCollectionOrder;
      formStore.transportType = Segment.ROAD;
      formStore.customerNumber = '123';
      return useOrderTexts();
    })[0];

    await vi.waitFor(() => {
      expect(filteredTexts.value.length).toBe(0);
    });
    server.shutdown();
  });

  it('returns only visible texts when some customer settings are true', async () => {
    const server: Server = mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: {
          deliveryInstructions: true,
          goodsDescription: false,
          invoiceText: true,
        },
      },
    });
    const { filteredTexts } = withSetup(() => {
      useCustomerSettings();
      const formStore = useCreateOrderFormStore();
      formStore.orderType = OrderTypes.RoadCollectionOrder;
      formStore.transportType = Segment.ROAD;
      formStore.customerNumber = '123';
      return useOrderTexts();
    })[0];

    await vi.waitFor(() => {
      expect(filteredTexts.value.length).toBe(2);
    });
    server.shutdown();
  });

  it('returns texts excluding forwarding order texts when isRoadForwardingOrder is true', async () => {
    const server: Server = mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: {
          deliveryInstructions: true,
          goodsDescription: true,
          invoiceText: true,
        },
      },
    });
    const { filteredTexts } = withSetup(() => {
      useCustomerSettings();
      const formStore = useCreateOrderFormStore();
      formStore.orderType = OrderTypes.RoadForwardingOrder;
      formStore.transportType = Segment.ROAD;
      formStore.customerNumber = '123';
      return useOrderTexts();
    })[0];

    await vi.waitFor(() => {
      expect(filteredTexts.value.length).toBe(3);
    });
    server.shutdown();
  });

  it('returns texts excluding collection order texts when isRoadForwardingOrder is false', async () => {
    const server: Server = mockServer({
      environment: 'test',
      fixtures: {
        customerSettings: {
          deliveryInstructions: true,
          goodsDescription: true,
          invoiceText: true,
        },
      },
    });
    const { filteredTexts } = withSetup(() => {
      useCustomerSettings();
      const formStore = useCreateOrderFormStore();
      formStore.orderType = OrderTypes.RoadCollectionOrder;
      formStore.transportType = Segment.ROAD;
      formStore.customerNumber = '123';
      return useOrderTexts();
    })[0];

    await vi.waitFor(() => {
      expect(filteredTexts.value.length).toBe(2);
    });
    server.shutdown();
  });
});
