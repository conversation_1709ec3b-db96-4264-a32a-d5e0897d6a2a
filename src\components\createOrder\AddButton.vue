<template>
  <VBtn
    color="primary"
    :disabled="disabled"
    class="align-self-center"
    size="small"
    :variant="variant"
    @click="addNewItem"
  >
    <MaterialSymbol v-if="!withoutIcon" size="16" left class="mr-2">
      <AddIcon />
    </MaterialSymbol>
    {{ label }}
  </VBtn>
</template>

<script setup lang="ts">
import MaterialSymbol from '@/components/MaterialSymbol.vue';
import AddIcon from '@dfe/dfe-frontend-styles/assets/icons/add-16px.svg';
import type { TranslateResult } from 'vue-i18n';
import type { VariantProp } from '@/types/vuetify';

type Props = {
  label: TranslateResult;
  disabled?: boolean;
  withoutIcon?: boolean;
  variant?: VariantProp;
};

withDefaults(defineProps<Props>(), {
  disabled: false,
  withoutIcon: false,
  variant: 'flat',
});

const emit = defineEmits(['add-new-item']);

const addNewItem = () => {
  emit('add-new-item');
};
</script>
