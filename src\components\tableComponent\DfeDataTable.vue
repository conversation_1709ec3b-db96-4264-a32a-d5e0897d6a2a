<template>
  <div
    id="table-wrapper"
    ref="tableWrapper"
    class="h-100"
    :class="items.length ? 'overflow-x-scroll' : 'd-flex flex-column overflow-x-hidden'"
    :style="footerFixed ? 'margin-bottom: 54px' : ''"
  >
    <div id="sizer"></div>

    <table v-if="headers.length" ref="table" class="w-100">
      <caption v-if="slots.caption" id="table-caption" class="position-sticky left-0">
        <slot name="caption" />
      </caption>

      <thead class="position-sticky top-0">
        <tr>
          <th
            v-if="
              (tableOptions.allSelectable || tableOptions.itemsSelectable) &&
              !firstHeaderColumnSticky
            "
            class="position-sticky left-0 select-header-column"
            :style="`width: ${selectColumnWidth ? selectColumnWidth + 'px' : 'auto'}`"
          >
            <div v-if="tableOptions.allSelectable">
              <div v-if="!slots.selectAllColumn" class="d-inline-flex align-center pl-8 pr-5">
                <VTooltip
                  location="top"
                  :text="
                    allSelected
                      ? t('labels.unselect_page_label.text')
                      : t('labels.select_page_label.text')
                  "
                >
                  <template #activator="{ props: tooltipActivator }">
                    <VCheckbox
                      :model-value="allSelected"
                      color="primary"
                      :indeterminate="someSelected && !allSelected"
                      :class="{ 'text-primary': someSelected }"
                      :v-data-test="`${trackingPrefixWithDash}bulk-select-all`"
                      density="compact"
                      hide-details="auto"
                      v-bind="tooltipActivator"
                      @update:model-value="onSelectAll"
                    />
                  </template>
                </VTooltip>
              </div>

              <slot
                v-else
                name="selectAllColumn"
                :model-value="allSelected"
                :some-selected="someSelected"
                @update:model-value="onSelectAll"
                @select-all="onSelectAll"
                @select-none="onClearSelection"
              />
            </div>
          </th>

          <th
            v-for="(header, index) in headers"
            :key="header.key"
            class="position-sticky top-0"
            :class="{
              'left-0': index == 0 && firstHeaderColumnSticky,
            }"
            :style="`min-width: ${header.minWidth ? header.minWidth + 'px' : 'auto'}; width: ${header.fixedWidth ? header.fixedWidth + 'px' : 'auto'}; text-align: ${header.justification ?? DfeColumnAlignment.LEFT}`"
          >
            <div class="ma-4 d-inline-flex align-center">
              <VHover v-slot="{ isHovering, props: hoverActivator }">
                <button
                  :key="header.key"
                  v-data-test="`${trackingPrefixWithDash}${header.key}-sort-${header.sorted}`"
                  class="text-label-2 d-inline-flex align-center"
                  :class="[
                    { sorted: header.sorted },
                    { 'gc-2': header.sortable },
                    { 'flex-row-reverse': header.justification === DfeColumnAlignment.RIGHT },
                    header.sortable && !header.sorted && !isHovering
                      ? header.justification === DfeColumnAlignment.RIGHT
                        ? 'ml-6'
                        : 'mr-6'
                      : '',
                    { 'no-hover': !header.sortable },
                  ]"
                  :tabindex="header.sortable ? 0 : -1"
                  v-bind="hoverActivator"
                  @click="onSortHeader(header)"
                >
                  <span>
                    {{ header.label }}
                  </span>
                </button>
              </VHover>
            </div>
          </th>
        </tr>
      </thead>

      <tbody v-if="items.length">
        <tr v-for="(item, index) in items" :key="index" tabindex="0">
          <td
            v-if="
              (tableOptions.itemsSelectable || tableOptions.allSelectable) &&
              !firstHeaderColumnSticky
            "
            class="position-sticky left-0 select-item-column"
          >
            <slot v-if="slots.selectItemColumn" name="selectItemColumn" />

            <div v-else class="d-flex align-center text-left h-100 ml-6 mr-2">
              <DfeSelectTableItems
                :selected-items="selectedItems"
                :index="index"
                :is-single-select="tableOptions.singleSelect"
                @item-selected="onSelectItem(index)"
              />

              <slot name="itemActionMenu" :item="item" />
            </div>
          </td>

          <template v-for="(header, headerIndex) in headers" :key="header.key">
            <td
              :class="{
                'position-sticky left-0': headerIndex == 0 && firstHeaderColumnSticky,
              }"
            >
              <div
                class="d-flex align-center h-100"
                :style="`text-align: ${header.justification ?? DfeColumnAlignment.LEFT}; padding: ${headerIndex == 0 && firstHeaderColumnSticky ? '' : columnPadding}`"
              >
                <component
                  :is="header.component"
                  v-if="header.component"
                  v-cloak
                  :item="item"
                  :attach="cellAttach"
                />

                <p v-else-if="header.formatting !== undefined">
                  {{ header.formatting(item[header.key as keyof typeof item]) ?? '' }}
                </p>
                <p v-else>{{ item[header.key as keyof typeof item] ?? '' }}</p>
              </div>
            </td>
          </template>
        </tr>
      </tbody>

      <tfoot
        v-if="items.length && !tableOptions.disablePagination"
        class="bottom-0"
        :class="footerFixed ? 'position-fixed right-0 left-0' : 'position-sticky'"
      >
        <tr :class="{ 'position-fixed right-0 left-0': footerFixed }">
          <td :colspan="headers.length" :class="{ 'position-fixed right-0 left-0': footerFixed }">
            <div class="d-flex">
              <div class="d-flex flex-grow-1"></div>

              <div
                class="position-sticky right-0 d-flex h-100 align-center gc-8 pr-8 pl-4 py-2 text-body-2"
              >
                <div v-if="selectedPageSize" class="d-inline-flex align-center gc-4">
                  <label class="text-grey-darken-2" for="rows-per-page-select">
                    {{ t('labels.rows_per_page.text') }}
                  </label>
                  <VSelect
                    id="rows-per-page-select"
                    v-model="selectedPageSize"
                    :items="pageSizeOptions"
                    :clearable="false"
                    :hide-no-data="true"
                    hide-details="auto"
                    @update:model-value="onSelectPageSize"
                  />
                </div>

                <div class="d-flex align-center gc-4">
                  <p id="selected-table-page">
                    {{ currentPageItemsLabel }}
                    <span class="text-grey-darken-2">
                      {{ t('labels.of_label.text') }} {{ tableOptions.totalItems }}
                    </span>
                  </p>

                  <v-pagination
                    :length="tableOptions.totalPages"
                    :model-value="tableOptions.currentPage"
                    :total-visible="0"
                    variant="plain"
                    density="compact"
                    show-first-last-page
                    :first-icon="FirstPageIcon"
                    :last-icon="LastPageIcon"
                    @first="onSelectPage(DfeSelectPage.FIRST)"
                    @prev="onSelectPage(DfeSelectPage.PREV)"
                    @next="onSelectPage(DfeSelectPage.NEXT)"
                    @last="onSelectPage(DfeSelectPage.LAST)"
                  />
                </div>
              </div>
            </div>
          </td>
        </tr>
      </tfoot>
    </table>

    <div
      v-if="!items.length && !isLoading"
      id="empty-table-message"
      class="d-flex flex-column justify-center align-center flex-grow-1"
    >
      <slot name="emptyTable" />
    </div>

    <loader-overlay :is-loading="isLoading" />
  </div>
</template>

<script setup lang="ts" generic="T">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import FirstPageIcon from '@dfe/dfe-frontend-styles/assets/icons/first_page-24px.svg';
import LastPageIcon from '@dfe/dfe-frontend-styles/assets/icons/last_page-24px.svg';
import LoaderOverlay from '@/components/loader/LoaderOverlay.vue';
import {
  DfeColumnAlignment,
  DfeSelectPage,
  DfeTableHeader,
  DfeTableOptions,
  DfeTableSortDirection,
} from '@/components/tableComponent/DfeTableTypes';
import { useI18n } from 'vue-i18n';
import DfeSelectTableItems from '@/components/tableComponent/DfeSelectTableItems.vue';

interface Props {
  items: T[];
  isLoading?: boolean;
  pageSizeOptions?: number[];
  columnPadding?: string;
  allowNoSorting?: boolean;
  trackingPrefix?: string;
  selectColumnWidth?: number;
  cellAttach?: HTMLElement;
}

interface Emits {
  updateOptions: [options: DfeTableOptions];
  selectItems: [items: T[]];
}

interface Slots {
  caption: string;
  selectAllColumn: string;
  selectItemColumn: string;
  itemActionMenu: string;
  emptyTable: string;
}

const emits = defineEmits<Emits>();

const slots = defineSlots<Slots>();

const {
  items = [],
  isLoading = false,
  pageSizeOptions = [10, 20, 50],
  columnPadding = '16px',
  allowNoSorting = false,
  trackingPrefix = '',
  selectColumnWidth = undefined,
  cellAttach = undefined,
} = defineProps<Props>();

const headers = defineModel<DfeTableHeader[]>('headers', { required: true, default: [] });
const tableOptions = defineModel<DfeTableOptions>('tableOptions', { required: true });

defineExpose({ onClearSelection });

const { t } = useI18n();

const tableWrapper = ref<HTMLElement | null>(null);
const table = ref<HTMLTableElement | null>(null);
let wrapperResizeObserver: ResizeObserver | null = null;

const footerFixed = ref(false);

const allSelected = ref(false);
const selectedItems = ref<number[]>([]);
const someSelected = computed(() => selectedItems.value.length > 0 || allSelected.value);

const selectedPageSize = ref(tableOptions.value.itemsPerPage ?? 10);

const firstHeaderColumnSticky = computed(
  () =>
    tableOptions.value.firstColumnSticky &&
    !tableOptions.value.allSelectable &&
    !tableOptions.value.itemsSelectable,
);

const currentPageItemsLabel = computed(() => {
  const start = (tableOptions.value.currentPage - 1) * tableOptions.value.itemsPerPage + 1;
  const end = start + items.length - 1; // this makes sure to also display the correct number of items when the last page is not full

  return `${start}–${end}`;
});

const trackingPrefixWithDash = computed(() =>
  trackingPrefix.endsWith('-') ? trackingPrefix : `${trackingPrefix}-`,
);

onMounted(() => {
  setTableClasses();
  onSelectPageSize(tableOptions.value.itemsPerPage);

  if (tableWrapper.value) {
    wrapperResizeObserver = new ResizeObserver(setTableClasses);
    wrapperResizeObserver.observe(tableWrapper.value);
  }
});

onBeforeUnmount(() => {
  onClearSelection();

  if (wrapperResizeObserver && tableWrapper.value) {
    wrapperResizeObserver.unobserve(tableWrapper.value);
    wrapperResizeObserver.disconnect();
    wrapperResizeObserver = null;
  }
});

watch(tableOptions, (newOptions, oldOptions) => {
  onSelectPageSize(tableOptions.value.itemsPerPage);
  setFooterPosition();

  if (newOptions.currentPage !== oldOptions.currentPage) {
    scrollToTop();
  }
});

watch(
  () => items,
  () => {
    setTableClasses();
  },
);

function onSortHeader(header: DfeTableHeader) {
  if (!header.sortable) return;

  if (!header.sorted) {
    header.sorted = DfeTableSortDirection.DESC;
  } else if (header.sorted === DfeTableSortDirection.DESC) {
    header.sorted = DfeTableSortDirection.ASC;
  } else {
    header.sorted = allowNoSorting ? undefined : DfeTableSortDirection.DESC;
  }

  emits('updateOptions', {
    ...tableOptions.value,
    sortBy: { key: header.key, order: header.sorted },
    currentPage: 1,
  });
}

function onSelectPageSize(value: number | null) {
  if ((value === selectedPageSize.value && value === tableOptions.value.itemsPerPage) || !value)
    return;

  selectedPageSize.value = value ?? 0;
  emits('updateOptions', {
    ...tableOptions.value,
    itemsPerPage: value,
    currentPage: 1,
  });
}

function onSelectAll() {
  allSelected.value = !allSelected.value;
  selectedItems.value = allSelected.value ? items.map((_, index) => index) : [];

  emits('selectItems', allSelected.value ? items : []);
}

function onSelectItem(index: number) {
  if (tableOptions.value.singleSelect) {
    selectedItems.value = [index];
  } else {
    const itemIndex = selectedItems.value.indexOf(index);

    if (itemIndex === -1) {
      selectedItems.value.push(index);
    } else {
      selectedItems.value.splice(itemIndex, 1);
    }

    allSelected.value = selectedItems.value.length === items.length;
  }

  emits(
    'selectItems',
    selectedItems.value.map((index) => items[index]),
  );
}

function onClearSelection() {
  allSelected.value = false;
  selectedItems.value = [];
}

function onSelectPage(option: DfeSelectPage) {
  let nextPage = 1;

  switch (option) {
    case DfeSelectPage.LAST:
      nextPage = tableOptions.value.totalPages;
      break;
    case DfeSelectPage.NEXT:
      nextPage = tableOptions.value.currentPage + 1;
      break;
    case DfeSelectPage.PREV:
      nextPage = tableOptions.value.currentPage - 1;
      break;
    case DfeSelectPage.FIRST:
    default:
      break;
  }

  nextPage = nextPage > 0 ? nextPage : 1;

  emits('updateOptions', {
    ...tableOptions.value,
    currentPage: nextPage,
  });
}

function setTableClasses() {
  setCaptionWidthToViewPortWidth();
  setFooterPosition();

  setTimeout(() => {
    setCaptionWidthToViewPortWidth(); // needed for the right width when opening dev-tools
  }, 100);
}

function setCaptionWidthToViewPortWidth() {
  const sizer = window.document.getElementById('sizer');
  const caption = window.document.getElementById('table-caption');

  if (sizer && caption) {
    caption.style.width = `${sizer.offsetWidth}px`;
  }
}

function scrollToTop() {
  const sizer = document.getElementById('sizer');

  sizer?.scrollTo({ behavior: 'smooth', top: 0, left: 0 });
}

function setFooterPosition() {
  requestAnimationFrame(() => {
    if (!table.value || !tableWrapper.value) {
      footerFixed.value = true;
    } else {
      footerFixed.value = tableWrapper.value.scrollHeight > table.value.scrollHeight;
    }
  });
}
</script>

<style scoped lang="scss">
@use '@/components/tableComponent/DfeDataTable';
</style>
