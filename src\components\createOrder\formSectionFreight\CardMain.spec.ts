import CardMain from '@/components/createOrder/formSectionFreight/CardMain.vue';
import { OrderTypes } from '@/enums';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { useCreateOrderOrderLineFormStore } from '@/store/createOrder/orderLine';
import { AirExportQuoteInformation, Segment } from '@dfe/dfe-book-api';
import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import { expect } from 'vitest';

describe('Freight CardMain component', () => {
  let wrapper: VueWrapper;

  it('mounts', () => {
    wrapper = mount(CardMain);
  });

  it('disables counter Button if there is an air order from Quote', async () => {
    const counterButton = wrapper.findAllComponents({ name: 'v-btn' }).at(1);

    expect(counterButton?.attributes('disabled')).toBeUndefined();

    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.AirExportOrder;
    store.quoteInformation = {
      orderType: OrderTypes.AirExportOrder,
      quoteExpiryDate: '2124-12-31',
      customerNumber: '03011149',
      collectionDate: '2024-12-31',
      termCode: 'CFR',
      quoteRequestId: 1,
    } as AirExportQuoteInformation;
    await wrapper.vm.$nextTick();

    expect(counterButton?.attributes('disabled')).toBeDefined();
  });

  it('shows stackable and shock sensitive if order type is air export', async () => {
    expect(wrapper.find('[data-test=book-stackable-and-shock-sensitive]').exists()).toBe(true);

    const store = useCreateOrderFormStore();
    store.orderType = OrderTypes.RoadForwardingOrder;
    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-stackable-and-shock-sensitive]').exists()).toBe(false);
  });

  it('shows add packing line button', async () => {
    const orderLineStore = useCreateOrderOrderLineFormStore();
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.RoadCollectionOrder;
    formStore.transportType = Segment.ROAD;
    orderLineStore.addPackingPosition();
    orderLineStore.addOrderLineToPackingPosition();
    orderLineStore.packagingAidPosition = true;

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-freight-add-order-line-buttons]').exists()).toBe(true);
    expect(wrapper.find('[data-test=book-freight-add-packing-position-buttons]').exists()).toBe(
      true,
    );
  });

  it('Hide packing position line button for AirOrder', async () => {
    const orderLineStore = useCreateOrderOrderLineFormStore();
    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.AirExportOrder;
    formStore.transportType = Segment.AIR;
    orderLineStore.addPackingPosition();
    orderLineStore.addOrderLineToPackingPosition();

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-freight-add-order-line-buttons]').exists()).toBe(true);
    expect(wrapper.find('[data-test=book-freight-add-packing-position-buttons]').exists()).toBe(
      false,
    );
  });

  it('Hides add container button for SEA LCL order', async () => {
    const orderLineStore = useCreateOrderOrderLineFormStore();
    orderLineStore.isFullContainerLoadAllowed = true;

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.SeaExportOrder;
    formStore.transportType = Segment.SEA;

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-add-full-container-load-line-button]').exists()).toBe(
      false,
    );
  });

  it('Shows add container button for SEA FLC order', async () => {
    const orderLineStore = useCreateOrderOrderLineFormStore();
    orderLineStore.isFullContainerLoadAllowed = true;
    orderLineStore.isFullContainerLoad = true;

    const formStore = useCreateOrderFormStore();
    formStore.orderType = OrderTypes.SeaExportOrder;
    formStore.transportType = Segment.SEA;

    orderLineStore.addOrderLineToFullContainerLoad(1);

    await wrapper.vm.$nextTick();

    expect(wrapper.find('[data-test=book-add-full-container-load-line-button]').exists()).toBe(
      true,
    );
  });
});
