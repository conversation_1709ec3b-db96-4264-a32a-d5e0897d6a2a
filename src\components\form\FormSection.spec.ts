import type { VueWrapper } from '@vue/test-utils';
import { mount } from '@vue/test-utils';
import FormSection from '@/components/form/FormSection.vue';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { createClientMock } from '../../../test/util/mock-client';
import { ClientKey } from '@/types/client';

describe('FormSection component', () => {
  let wrapper: VueWrapper;
  const client = createClientMock();

  beforeEach(() => {
    wrapper = mount(FormSection, {
      global: {
        provide: {
          [ClientKey as symbol]: client,
        },
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('matches markup', () => {
    expect(wrapper.element).toMatchSnapshot();
  });

  it('sets isValidationTriggered to false when resetFormValidation is called', async () => {
    const formStore = useCreateOrderFormStore();
    const { isValidationTriggered } = storeToRefs(formStore);

    isValidationTriggered.value = true;

    client.events.emit('clearCreateOrderFormData');

    await wrapper.vm.$nextTick();

    expect(isValidationTriggered.value).toBe(false);
  });
});
