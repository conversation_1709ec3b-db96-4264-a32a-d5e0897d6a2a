import type { TranslateResult } from 'vue-i18n';
import type { DocumentTypes, Extension } from '@dfe/dfe-book-api';
import { StoreDocument } from '@/store/createOrder/orderDocuments';

export function isValidExtension(extension?: string, supportedExtensions?: Extension[]) {
  if (!extension || !supportedExtensions || supportedExtensions?.length === 0) {
    return true;
  }

  return supportedExtensions.some((supported) =>
    // Check if the extension is actually a mime type, as mime types always have a '/' in them
    extension.includes('/')
      ? supported.description === extension
      : `.${supported.label}` === extension,
  );
}

export default function (
  favoritesHeader: TranslateResult | string,
  options: DocumentTypes,
  recentlyUsedDocumentTypes: DocumentTypes,
  document: StoreDocument,
) {
  const categories: string[] = [];

  const recentDocumentTypes = recentlyUsedDocumentTypes.reduce<DocumentTypes>(
    (previousValue, currentOption) => {
      const { category, description } =
        options.find((o) => o.typeId === currentOption.typeId) ?? {};
      if (
        !description ||
        !category ||
        !isValidExtension(document?.mimeType, currentOption.supportedExtensions)
      ) {
        return previousValue;
      }
      currentOption.description = `${category}: ${description}`;
      return [...previousValue, currentOption];
    },
    [],
  );

  const recentlyUsedDocumentTypesIds = recentlyUsedDocumentTypes.map((option) => option.typeId);

  options.forEach((option) => {
    if (
      option.category &&
      !categories.includes(option.category) &&
      isValidExtension(document?.mimeType, option.supportedExtensions)
    ) {
      categories.push(option.category);
    }
  });

  const categoryOptionsWithHeader = categories
    .map((category) => {
      const categoryOptions = options.filter(
        (option) =>
          option.category === category && !recentlyUsedDocumentTypesIds.includes(option.typeId),
      );
      if (!categoryOptions.length) return undefined;
      return {
        header: category,
        options: categoryOptions,
      };
    })
    .filter((item): item is { header: string; options: DocumentTypes } => !!item);

  const recentlyUsed = recentDocumentTypes[0]?.typeId
    ? [{ header: favoritesHeader }, ...recentDocumentTypes, { divider: true }]
    : [];

  const allOptions = categoryOptionsWithHeader.map((item) => {
    return [{ header: item.header }, ...item.options, { divider: true }];
  });

  return recentlyUsed.concat(...allOptions);
}
