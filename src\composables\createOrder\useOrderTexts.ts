import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useCustomerSettings } from '@/composables/data/useCustomerSettings';
import { useCreateOrderFormStore } from '@/store/createOrder/form';
import { storeToRefs } from 'pinia';
import { MaxLength, TextTypes } from '@/enums';

interface Text {
  label: string;
  type: (typeof TextTypes)[keyof typeof TextTypes];
  maxLength: (typeof MaxLength)[keyof typeof MaxLength];
  exclCollectionOrder?: boolean;
  exclForwardingOrder?: boolean;
  visible?: boolean;
}

export const useOrderTexts = () => {
  const { data: customerSettings } = useCustomerSettings();
  const { t } = useI18n();
  const createOrderFormStore = useCreateOrderFormStore();
  const { isRoadForwardingOrder, isRoadCollectionOrder, isRoadOrder } =
    storeToRefs(createOrderFormStore);

  const texts = computed<Text[]>(() => {
    if (!customerSettings.value) {
      return [];
    }

    return [
      {
        label: t('labels.instructions_collection_order_label.text'),
        type: TextTypes.CollectionInstructions,
        maxLength: MaxLength.TextsCommonInstructions,
        visible: true,
        exclForwardingOrder: true,
      },
      {
        label: t('labels.delivery_instructions.text'),
        type: TextTypes.DeliveryInstructions,
        maxLength: MaxLength.Texts,
        visible: customerSettings.value.deliveryInstructions,
      },
      {
        label: t('labels.goods_description.text'),
        type: TextTypes.GoodsDescription,
        maxLength: MaxLength.Texts,
        exclCollectionOrder: true,
        visible: customerSettings.value.goodsDescription,
      },
      {
        label: t('labels.invoice_text.text'),
        type: TextTypes.InvoiceText,
        maxLength: MaxLength.Texts,
        visible: customerSettings.value.invoiceText && !isRoadCollectionOrder.value,
      },
      {
        label: t('labels.special_regulation.text'),
        type: TextTypes.SpecialRegulation,
        maxLength: MaxLength.Texts,
        visible: customerSettings.value.dangerousGoods && isRoadOrder.value,
      },
    ];
  });

  const filteredTexts = computed(() => {
    if (isRoadForwardingOrder.value) {
      return texts.value.filter((text) => !text.exclForwardingOrder && text.visible);
    }

    return texts.value.filter((text) => !text.exclCollectionOrder && text.visible);
  });

  const isAtLeastOneOrderTextVisible = computed(() => {
    return filteredTexts.value.length > 0;
  });

  return { filteredTexts, isAtLeastOneOrderTextVisible };
};
