import useHighlight from '@/composables/createOrder/useHighlight';

const text = 'Lorem airport ipsum seaport';

describe('useHighlight composable', () => {
  it('returns the text with highlight class', async () => {
    const result1 = useHighlight(text, 'port');
    const match1 = result1.match(/<mark class="v-list-item__mask">port<\/mark>/gm);
    expect(match1).toHaveLength(2);

    const result2 = useHighlight(text, 'frankfurt');
    const match2 = result2.match(/<mark class="v-list-item__mask">frankfurt<\/mark>/gm);
    expect(match2).toEqual(null);
  });
});
