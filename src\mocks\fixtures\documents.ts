import type { DocumentResponse, Document, BooleanResult } from '@dfe/dfe-book-api';

export const documents: Document[] = [
  {
    documentTypeId: 1,
    documentName: 'invoice.xlsx',
    extension: 'xlsx',
    startProcessing: true,
  },
  {
    documentTypeId: 7,
    documentName: 'Certificate.pdf',
    extension: 'pdf',
    startProcessing: true,
  },
  {
    documentTypeId: 5,
    documentName: 'CustomsLicence.pdf',
    extension: 'pdf',
    startProcessing: true,
  },
  {
    documentTypeId: 5,
    documentName: 'CustomsLicence2.pdf',
    extension: 'pdf',
    startProcessing: true,
  },
];

export const documentsResponse: DocumentResponse[] = [
  {
    documentId: 111,
    documentName: 'invoice.xlsx',
    uploadStatus: 'success',
    documentProcessed: false,
    error: 'new',
  },
  {
    documentId: 222,
    documentName: 'Certificate.pdf',
    uploadStatus: 'unspecificError',
    documentProcessed: false,
    error: 'fileMalicious',
  },
  {
    documentId: 333,
    documentName: 'CustomsLicence.pdf',
    uploadStatus: 'fileMalicious',
    documentProcessed: false,
    error: 'errorExtension',
  },
  {
    documentId: 4444,
    documentName: 'CustomsLicence2.pdf',
    uploadStatus: 'unspecificError',
    documentProcessed: false,
    error: 'unspecificError',
  },
];

export const updateDocument: BooleanResult = {
  result: true,
};
