import type { TimeFormat } from '@/types/createOrder';
import { useUTCTimeFromDate } from '@/composables/dateTimeUtilities/useTimeFromDate';

function useTimeOptions(timeFormat: TimeFormat) {
  const options = [];
  const date = new Date();

  for (let i = 0; i < 24; i++) {
    date.setUTCHours(i);
    date.setUTCMinutes(0);
    options.push(useUTCTimeFromDate(date, timeFormat));
    date.setMinutes(30);
    options.push(useUTCTimeFromDate(date, timeFormat));
  }

  return options;
}

export default useTimeOptions;
