import { ref } from 'vue';
import { useFavoriteCountriesList } from '@/composables/createOrder/useFavoriteCountriesList';
import type { Countries, FavoriteCountries } from '@dfe/dfe-book-api';

const favoritesHeader = 'Favorites';
const header = 'Header';

const emptyCountries = ref([] as Countries);
const emptyFavoriteCountries = ref([] as FavoriteCountries);

const countries = [
  { countryCode: 'CA', label: 'Canada', isPostCodeMandatory: true },
  { countryCode: 'DE', label: 'Germany', isPostCodeMandatory: true },
  { countryCode: 'IT', label: 'Italy', isPostCodeMandatory: true },
  { countryCode: 'FR', label: 'France', isPostCodeMandatory: true },
];

const favoriteCountriesShipper = [
  { countryCode: 'BE', label: 'Belgium', isPostCodeMandatory: true },
  { countryCode: 'FR', label: 'France', isPostCodeMandatory: true },
];

const favoriteCountriesConsignee = [
  { countryCode: 'NL', label: 'Netherlands', isPostCodeMandatory: true },
  { countryCode: 'IQ', label: 'Iraq', isPostCodeMandatory: true },
];

const favoriteCountries = {
  shipperCountries: ref(favoriteCountriesShipper),
  consigneeCountries: ref(favoriteCountriesConsignee),
};

describe('usePackagingOptionsList composable', () => {
  it('returns an empty object if no favorite countries are available', () => {
    expect(
      useFavoriteCountriesList(
        favoritesHeader,
        header,
        emptyCountries,
        emptyFavoriteCountries,
        true,
      ),
    );
  });

  it('returns favorite countries for shipper with favorites and headers', () => {
    const countriesWithoutFr = countries.filter((country) => country.countryCode !== 'FR');
    expect(
      useFavoriteCountriesList(
        favoritesHeader,
        header,
        ref(countries),
        ref(favoriteCountries),
        true,
      ),
    ).toEqual([
      { header: favoritesHeader },
      ...favoriteCountriesShipper,
      { divider: true },
      { header },
      ...countriesWithoutFr,
    ]);
  });

  it('returns favorite countries for consignee with favorites and headers', () => {
    expect(
      useFavoriteCountriesList(
        favoritesHeader,
        header,
        ref(countries),
        ref(favoriteCountries),
        false,
      ),
    ).toEqual([
      { header: favoritesHeader },
      ...favoriteCountriesConsignee,
      { divider: true },
      { header },
      ...countries,
    ]);
  });
});
